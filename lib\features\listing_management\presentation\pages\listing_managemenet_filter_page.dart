import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/widgets/filter_last_date_month_picker.dart';
import 'package:leadrat/core_main/common/widgets/filter_first_date_month_picker.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_form_button.dart';
import 'package:leadrat/core_main/common/widgets/min_max_validator_form.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/property_enums/listing_management_filter_key.dart';
import 'package:leadrat/core_main/enums/property_enums/property_date_type.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/lead/presentation/widgets/filter_custom_date_picker_widget.dart';
import 'package:leadrat/features/listing_management/data/models/listing_management_filter_model.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/listing_management_filter_bloc/listing_management_filter_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/item/item_property_listing_filter_model.dart';
import 'package:leadrat/features/properties/presentation/widgets/range_input_widget.dart';

class ListingManagementFilterPage extends LeadratStatefulWidget {
  final Function(ListingManagementFilterModel?) onApplyFilter;

  const ListingManagementFilterPage({
    super.key,
    required this.onApplyFilter,
  });

  @override
  State<ListingManagementFilterPage> createState() => _ListingManagementFilterPageState();
}

class _ListingManagementFilterPageState extends LeadratState<ListingManagementFilterPage> {
  @override
  Widget buildContent(BuildContext context) {
    return BlocConsumer<ListingManagementFilterBloc, ListingManagementFilterState>(
      listener: (context, state) {
        if (state.pageState == PageState.success) {
          widget.onApplyFilter(state.listingManagementFilterModel);
          Navigator.of(context).pop();
        } else if (state.pageState == PageState.failure && state.errorMessage.isNotNullOrEmpty()) {
          LeadratCustomSnackbar.show(context: context, message: state.errorMessage ?? "Something went wrong", type: SnackbarType.error);
        }
      },
      builder: (context, state) {
        final selectedCategoryItem = state.listingManagementFilterCategories[state.selectedCategoryIndex];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            Container(
              decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(ImageResources.imageAppBarPattern), alignment: Alignment.bottomRight)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Text("filter", style: LexendTextStyles.lexend18Bold.copyWith(color: ColorPalette.tertiaryTextColor)),
                  ),
                  GestureDetector(
                      onTap: () => context.read<ListingManagementFilterBloc>().add(ResetFilterEvent()),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Text("reset", style: LexendTextStyles.lexend14Bold.copyWith(color: ColorPalette.tertiaryTextColor)),
                      )),
                ],
              ),
            ),
            Expanded(
              child: Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  //Filter categories
                  Container(
                    color: ColorPalette.primaryDarkColor,
                    width: context.width(35),
                    child: ListView.builder(
                      itemCount: state.listingManagementFilterCategories.length,
                      itemBuilder: (context, index) {
                        final filterCategory = state.listingManagementFilterCategories[index];
                        return GestureDetector(
                          onTap: () => context.read<ListingManagementFilterBloc>().add(FilterCategorySelectEvent(itemListingFilterCategoryModel: filterCategory, selectedCategoryIndex: index)),
                          child: Container(
                            key: ValueKey(index),
                            padding: const EdgeInsets.all(22),
                            decoration: BoxDecoration(
                                color: filterCategory.isSelected ? ColorPalette.primaryDarkColor : ColorPalette.darkToneInk,
                                border: Border(
                                  top: BorderSide(color: index == 0 ? ColorPalette.transparent : ColorPalette.lightBackground, width: .35),
                                  bottom: BorderSide(color: filterCategory.isSelected ? ColorPalette.lightBackground : ColorPalette.lightBackground, width: .35),
                                  right: BorderSide(color: filterCategory.isSelected ? ColorPalette.transparent : ColorPalette.lightBackground, width: .7),
                                )),
                            child: Text(filterCategory.displayName ?? '', style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.tertiaryTextColor, fontWeight: filterCategory.isSelected ? FontWeight.w600 : FontWeight.w400)),
                          ),
                        );
                      },
                    ),
                  ),
                  //Filters
                  if (selectedCategoryItem.hasCustomFilters)
                    _buildFilterCustomView(selectedCategoryItem.filterKey, state,false)
                  else
                    Container(
                      width: context.width(65),
                      padding: const EdgeInsets.only(right: 14, left: 8),
                      color: ColorPalette.primaryDarkColor,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          if (selectedCategoryItem.hasCustomHeaderView) ..._buildCustomHeaderView(context, selectedCategoryItem, state),
                          if (selectedCategoryItem.hasSearch) ..._buildSearchWidget(context, selectedCategoryItem, state),
                          if (selectedCategoryItem.hasSelectAll || selectedCategoryItem.hasMultiSelect) ..._buildSelectAllItem(context, selectedCategoryItem),
                          if (selectedCategoryItem.hasCount)
                            MinMaxValidatorForm(minCountController: selectedCategoryItem.minCountController, maxCountController: selectedCategoryItem.maxCountController)
                          else
                            Expanded(
                              child: state.listingManagementFilterCategories[state.selectedCategoryIndex].isInitialized
                                  ? _buildFilterList(context, state, selectedCategoryItem)
                                  : const Center(
                                  child: CircularProgressIndicator(
                                    color: ColorPalette.primaryGreen,
                                  )),
                            ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.only(top: 15, right: 24, left: 14, bottom: 15),
              decoration: BoxDecoration(color: ColorPalette.primaryDarkColor, border: Border(top: BorderSide(color: ColorPalette.tertiaryTextColor.withOpacity(.3), width: .8))),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(child: LeadratFormButton(onPressed: () => Navigator.of(context).pop(), buttonText: "Close")),
                  const SizedBox(width: 14),
                  Expanded(
                      child: LeadratFormButton(
                        onPressed: () => context.read<ListingManagementFilterBloc>().add(ApplyListingManagementFilterEvent()),
                        buttonText: "Apply",
                      )),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  //Lead Filter ListView With checkbox and radio tile
  Widget _buildFilterList(BuildContext context, ListingManagementFilterState state, ItemListingPropertyFilterCategoryModel category) {
    final selectedCategoryItem = (category.searchController?.text.isEmpty ?? false) ? state.listingManagementFilterCategories[state.selectedCategoryIndex] : state.searchFilteredCategories[state.selectedCategoryIndex];
    final leadFilters = selectedCategoryItem.filters;
    return ListView.builder(
      itemCount: leadFilters?.length ?? 0,
      itemBuilder: (context, index) {
        final categoryFilters = leadFilters!;
        final filter = categoryFilters[index];
        return Column(
          children: [
            if (selectedCategoryItem.hasMultiSelect)
              CheckboxListTile(
                key: ValueKey(index),
                value: filter.isSelected,
                onChanged: (value) {
                  context.read<ListingManagementFilterBloc>().add(SelectFilterEvent(itemListingFilterModel: filter, selectedFilterCategory: selectedCategoryItem));
                },
                visualDensity: const VisualDensity(horizontal: -4, vertical: -2),
                side: const BorderSide(color: ColorPalette.gray500, width: 1),
                checkboxShape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
                title: Row(
                  children: [
                    Expanded(child: Text(filter.displayName, style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.primary, overflow: TextOverflow.ellipsis))),
                    if (!filter.isActive) Text("(disabled)", style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.fadedRed, overflow: TextOverflow.ellipsis)),
                  ],
                ),
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding: EdgeInsets.zero,
                activeColor: ColorPalette.primaryGreen,
              )
            else
              RadioListTile(
                key: ValueKey(index),
                value: filter,
                onChanged: (value) {
                  context.read<ListingManagementFilterBloc>().add(SelectFilterEvent(itemListingFilterModel: filter, selectedFilterCategory: selectedCategoryItem));
                },
                visualDensity: const VisualDensity(horizontal: -4, vertical: -2),
                title: Row(
                  children: [
                    Expanded(child: Text(filter.displayName, style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.primary, overflow: TextOverflow.ellipsis))),
                    if (!filter.isActive) Text("(disabled)", style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.fadedRed, overflow: TextOverflow.ellipsis)),
                  ],
                ),
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding: EdgeInsets.zero,
                activeColor: ColorPalette.primaryGreen,
                groupValue: categoryFilters.firstWhereOrNull((element) => element.isSelected) ?? false,
              ),
            if (filter.hasCustomView && filter.isSelected) _buildFilterCustomView(selectedCategoryItem.filterKey, state,filter.hasMonthFilter),
          ],
        );
      },
    );
  }

  //select all widget
  _buildSelectAllItem(BuildContext context, ItemListingPropertyFilterCategoryModel selectedCategoryItem) {
    return [
      const SizedBox(height: 10),
      CheckboxListTile(
        value: (selectedCategoryItem.isAllSelected) ? null : false,
        onChanged: (value) {
          context.read<ListingManagementFilterBloc>().add(SelectFilterEvent(selectedFilterCategory: selectedCategoryItem, isSelectAll: true));
        },
        tristate: true,
        visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
        side: const BorderSide(color: ColorPalette.gray500, width: 1),
        checkboxShape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        title: Text((selectedCategoryItem.isAllSelected) ? "Deselect all" : "Select all", style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.primary)),
        controlAffinity: ListTileControlAffinity.leading,
        contentPadding: EdgeInsets.zero,
        activeColor: ColorPalette.primaryGreen,
      ),
      const Divider(color: ColorPalette.tertiaryTextColor, thickness: .5, indent: 10, endIndent: 20),
    ];
  }

  _buildSearchWidget(BuildContext context, ItemListingPropertyFilterCategoryModel selectedCategoryItem, ListingManagementFilterState state) {
    return [
      if (selectedCategoryItem.hasSearch)
        SizedBox(
          height: 65,
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 10, 14, 14),
            child: TextField(
              controller: selectedCategoryItem.searchController,
              onChanged: (value) => context.read<ListingManagementFilterBloc>().add(SearchFiltersItemsEvent(searchText: selectedCategoryItem.searchController?.text, filterKey: selectedCategoryItem.filterKey)),
              style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.gray500),
              cursorColor: ColorPalette.primaryGreen,
              decoration: InputDecoration(
                fillColor: ColorPalette.primaryColor,
                filled: true,
                border: const OutlineInputBorder(borderSide: BorderSide(color: ColorPalette.primaryLightColor)),
                enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: const BorderSide(color: ColorPalette.primaryLightColor)),
                focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: const BorderSide(color: ColorPalette.primaryLightColor)),
                contentPadding: const EdgeInsets.fromLTRB(14, 5, 5, 10),
                hintStyle: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.gray700),
                hintText: selectedCategoryItem.searchHintText ?? "type here",
                suffixIcon: const Icon(CupertinoIcons.search),
              ),
            ),
          ),
        ),
      // if (selectedCategoryItem.filterKey == LeadFilterKey.assignedTo) ..._buildCustomAssignedToHeader(state),
      const Divider(color: ColorPalette.tertiaryTextColor, thickness: .5, indent: 10, endIndent: 20),
    ];
  }

  Widget _buildFilterCustomView(ListingManagementFilterKey filterKey, ListingManagementFilterState state, bool isMonthPicker) {
    if (isMonthPicker) {
      return _customMonthPicker();
    }
    if (filterKey == ListingManagementFilterKey.dateRange) {
      return _customDatePicker();
    } else if (filterKey == ListingManagementFilterKey.propertySize) {
      return _buildMinMaxAreaWidget(
        filterKey: filterKey,
        selectableItems: state.areaUnits,
        minValue: state.propertySize?.min,
        maxValue: state.propertySize?.max,
        selectedItem: state.selectedPropertySizeAreaUnit,
        moduleName: 'carpet area',
      );
    } else if (filterKey == ListingManagementFilterKey.saleableArea) {
      return _buildMinMaxAreaWidget(
        filterKey: filterKey,
        selectableItems: state.areaUnits,
        minValue: state.saleableArea?.min,
        maxValue: state.saleableArea?.max,
        selectedItem: state.selectedSaleableAreaUnit,
        moduleName: 'saleable area',
      );
    } else if (filterKey == ListingManagementFilterKey.builtUpArea) {
      return _buildMinMaxAreaWidget(
        filterKey: filterKey,
        selectableItems: state.areaUnits,
        minValue: state.builtUpArea?.min,
        maxValue: state.builtUpArea?.max,
        selectedItem: state.selectedBuiltUpAreaUnit,
        moduleName: 'built up area',
      );
    }
    if (filterKey == ListingManagementFilterKey.carpetArea) {
      return _buildMinMaxAreaWidget(
        filterKey: filterKey,
        selectableItems: state.areaUnits,
        minValue: state.carpetArea?.min,
        maxValue: state.carpetArea?.max,
        selectedItem: state.selectedCarpetAreaUnit,
        moduleName: 'carpet area',
      );
    }
    if (filterKey == ListingManagementFilterKey.netArea) {
      return _buildMinMaxAreaWidget(
          filterKey: filterKey,
          selectableItems: state.areaUnits,
          minValue: state.netArea?.min,
          maxValue: state.netArea?.max,
          selectedItem: state.selectedNetAreaUnit,
          moduleName: 'net area');
    }
    if (filterKey == ListingManagementFilterKey.minBudget) {
      return _buildMinMaxBudgetWidget(
        filterKey: filterKey,
        selectableItems: state.currencies,
        maxValue: state.minBudget?.max,
        minValue: state.minBudget?.min,
        selectedItem: state.selectedCurrency,
      );
    }

    return const SizedBox.shrink();
  }

  Widget _customMonthPicker() {
    return BlocBuilder<ListingManagementFilterBloc, ListingManagementFilterState>(
      builder: (context, state) {
        return Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 10, 14, 10),
              child: FilterFirstDateMonthPicker(
                labelText: 'from',
                selectedDate: state.selectedFromDate,
                maxDate: state.selectedToDate,
                onDateSelected: (selectedDate) {
                  if (selectedDate != null) context.read<ListingManagementFilterBloc>().add(SelectFromDateEvent(selectedDate));
                },
              ),
            ),
            Text("&", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.gray600)),
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 10, 14, 0),
              child: FilterCustomMonthPicker(
                labelText: 'to',
                minDate: state.selectedFromDate,
                selectedDate: state.selectedToDate,
                onDateSelected: (selectedDate) {
                  if (selectedDate != null) context.read<ListingManagementFilterBloc>().add(SelectToDateEvent(selectedDate));
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _customDatePicker() {
    return BlocBuilder<ListingManagementFilterBloc, ListingManagementFilterState>(
      builder: (context, state) {
        return Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 10, 14, 10),
              child: FilterCustomDatePicker(
                labelText: 'from',
                selectedDate: state.selectedFromDate,
                maxDate: state.selectedToDate,
                onDateSelected: (selectedDate) {
                  if (selectedDate != null) context.read<ListingManagementFilterBloc>().add(SelectFromDateEvent(selectedDate));
                },
              ),
            ),
            Text("&", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.gray600)),
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 10, 14, 0),
              child: FilterCustomDatePicker(
                labelText: 'to',
                minDate: state.selectedFromDate,
                selectedDate: state.selectedToDate,
                onDateSelected: (selectedDate) {
                  if (selectedDate != null) context.read<ListingManagementFilterBloc>().add(SelectToDateEvent(selectedDate));
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildMinMaxAreaWidget({required ListingManagementFilterKey filterKey, required List<SelectableItem<MasterAreaUnitsModel>> selectableItems, double? minValue, double? maxValue, SelectableItem<MasterAreaUnitsModel>? selectedItem, required String moduleName}) {
    return Expanded(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: SelectableItemBottomSheet<MasterAreaUnitsModel>(
              title: "select unit",
              canDeselectSingleItem: true,
              selectableItems: selectableItems,
              onItemSelected: (selectedItem) => context.read<ListingManagementFilterBloc>().add(AreaUnitChangedEvent(selectedItem, filterKey: filterKey)),
              selectedItem: selectedItem,
              child: Container(
                height: 40,
                padding: const EdgeInsets.symmetric(horizontal: 10).copyWith(top: 7),
                margin: const EdgeInsets.only(top: 10),
                decoration: BoxDecoration(
                  color: ColorPalette.primaryColor,
                  border: Border.all(width: 1, color: ColorPalette.primaryLightColor),
                  borderRadius: BorderRadius.circular(5),
                ),
                alignment: Alignment.topCenter,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Text(
                        selectedItem?.title ?? 'select unit',
                        style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.white),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const Icon(Icons.keyboard_arrow_down, size: 23, color: ColorPalette.white),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 10),
          RangeInputWidget(
            key: ValueKey(filterKey),
            onRangeChanged: (rangeInput) => context.read<ListingManagementFilterBloc>().add(AreaSizeInputChangeEvent(rangeInput, filterKey: filterKey)),
            initialMaxValue: maxValue,
            initialMinValue: minValue,
            moduleName: moduleName,
          ),
        ],
      ),
    );
  }

  Widget _buildMinMaxBudgetWidget({
    required ListingManagementFilterKey filterKey,
    required List<SelectableItem<String>> selectableItems,
    double? minValue,
    double? maxValue,
    SelectableItem<String>? selectedItem,
  }) {
    return Expanded(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: SelectableItemBottomSheet<String>(
              title: "select Currency",
              selectableItems: selectableItems,
              onItemSelected: (selectedItem) => context.read<ListingManagementFilterBloc>().add(ChangeCurrencyEvent(selectedItem)),
              selectedItem: selectedItem,
              child: Container(
                height: 40,
                padding: const EdgeInsets.symmetric(horizontal: 10).copyWith(top: 7),
                margin: const EdgeInsets.only(top: 10),
                decoration: BoxDecoration(
                  color: ColorPalette.primaryColor,
                  border: Border.all(width: 1, color: ColorPalette.primaryLightColor),
                  borderRadius: BorderRadius.circular(5),
                ),
                alignment: Alignment.topCenter,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Text(
                        selectedItem?.title ?? 'select Currency',
                        style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.white),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const Icon(Icons.keyboard_arrow_down, size: 23, color: ColorPalette.white),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 10),
          RangeInputWidget(
            key: ValueKey(filterKey),
            onRangeChanged: (rangeInput) => ListingManagementFilterKey.minBudget == filterKey ? context.read<ListingManagementFilterBloc>().add(OnMinBudgetChangeEvent(rangeInput)) : context.read<ListingManagementFilterBloc>().add(OnMaxBudgetChangeEvent(rangeInput)),
            initialMaxValue: maxValue,
            initialMinValue: minValue,
          ),
        ],
      ),
    );
  }

  _buildCustomHeaderView(BuildContext context, ItemListingPropertyFilterCategoryModel selectedCategoryItem, ListingManagementFilterState state) {
    return [
      if (selectedCategoryItem.filterKey == ListingManagementFilterKey.dateRange)
        Padding(
          padding: const EdgeInsets.fromLTRB(14, 0, 14, 10),
          child: SelectableItemBottomSheet<PropertyDateType>(
            title: "select date type",
            selectableItems: state.dateTypes,
            onItemSelected: (selectedItem) => context.read<ListingManagementFilterBloc>().add(SelectDateTypeEvent(selectedItem)),
            selectedItem: state.selectedDateType,
            child: Container(
              height: 40,
              padding: const EdgeInsets.symmetric(horizontal: 10).copyWith(top: 7),
              margin: const EdgeInsets.only(top: 10),
              decoration: BoxDecoration(
                color: ColorPalette.primaryColor,
                border: Border.all(width: 1, color: ColorPalette.primaryLightColor),
                borderRadius: BorderRadius.circular(5),
              ),
              alignment: Alignment.topCenter,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Text(
                      state.selectedDateType?.title ?? 'select date type',
                      style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.white),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const Icon(Icons.keyboard_arrow_down, size: 23, color: ColorPalette.white),
                ],
              ),
            ),
          ),
        ),
    ];
  }
}
