import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/base/usecase/use_case.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_amenity_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/repository/global_setting_repository.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_type_model.dart';
import 'package:leadrat/core_main/common/data/master_data/repository/masterdata_repository.dart';
import 'package:leadrat/core_main/common/data/user/models/get_all_users_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/common/date_range.dart';
import 'package:leadrat/core_main/enums/common/no_of_bhk.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/property_type_enum.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/property_enums/completion_status.dart';
import 'package:leadrat/core_main/enums/property_enums/facing.dart';
import 'package:leadrat/core_main/enums/property_enums/feature.dart';
import 'package:leadrat/core_main/enums/property_enums/floors.dart';
import 'package:leadrat/core_main/enums/property_enums/furnish_status.dart';
import 'package:leadrat/core_main/enums/property_enums/listing_status.dart';
import 'package:leadrat/core_main/enums/property_enums/property_date_type.dart';
import 'package:leadrat/core_main/enums/property_enums/property_filter_keys.dart';
import 'package:leadrat/core_main/enums/property_enums/property_size.dart';
import 'package:leadrat/core_main/enums/property_enums/property_status.dart';
import 'package:leadrat/core_main/enums/property_enums/sale_type.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:leadrat/core_main/managers/shared_preference_manager/shared_preference_manager.dart';
import 'package:leadrat/core_main/mapper/property_mapper.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/features/lead/domain/usecase/get_project_name_with_id_use_case.dart';
import 'package:leadrat/features/projects/domain/entities/project_entity.dart';
import 'package:leadrat/features/properties/data/models/property_filter_model.dart';
import 'package:leadrat/features/properties/domain/repository/properties_repository.dart';
import 'package:leadrat/features/properties/domain/usecase/get_community_and_sub_community_usecase.dart';
import 'package:leadrat/features/properties/domain/usecase/get_owner_names_or_addresses_usecase.dart';
import 'package:leadrat/features/properties/presentation/bloc/manage_property_bloc/manage_property_bloc.dart';
import 'package:leadrat/features/properties/presentation/items/item_property_filter_model.dart';
import 'package:leadrat/features/properties/presentation/widgets/range_input_widget.dart';

part 'property_filter_event.dart';
part 'property_filter_state.dart';

class PropertyFilterBloc extends Bloc<PropertyFilterEvent, PropertyFilterState> {
  final GetOwnerNamesOrAddressesUseCase _getOwnerNamesOrAddressesUseCase;
  final MasterDataRepository _masterDataRepository;
  GlobalSettingModel? globalSettings;
  final GlobalSettingRepository _globalSettingRepository;
  SelectableItem<String>? selectedCurrency;
  PropertyFilterState? tempPropertyFilterState;
  final GetProjectNameWithIdUseCase _getProjectNameWithIdUseCase;
  final GetCommunityUseCase _getCommunityUseCase;
  final GetSubCommunityUseCase _getSubCommunityUseCase;

  bool get isPropertyListingEnabled => getIt<LeadratHomeBloc>().globalSettings?.shouldEnablePropertyListing ?? false;

  PropertyFilterBloc(
    this._getOwnerNamesOrAddressesUseCase,
    this._getProjectNameWithIdUseCase,
    this._globalSettingRepository,
    this._masterDataRepository,
    this._getSubCommunityUseCase,
    this._getCommunityUseCase,
  ) : super(const PropertyFilterState()) {
    on<PropertyFilterInitialEvent>(_onPropertyFilterInitialEvent);
    on<InitialiseCurrenciesEvent>(_onInitialiseCurrenciesEvent);
    on<InitialiseDateTypesEvent>(_onInitialiseDateTypesEvent);
    on<PropertyFilterCancelEvent>(_onPropertyFilterCancelEvent);
    on<PropertyFilterApplyEvent>(_onPropertyFilterApplyEvent);
    on<PropertyFilterResetEvent>(_onPropertyFilterResetEvent);
    on<FilterCategorySelectEvent>(_onFilterCategorySelectEvent);
    on<FilterValueMultiSelectEvent>(_onFilterValueMultiSelectEvent);
    on<FilterValueSingleSelectEvent>(_onFilterValueSingleSelectEvent);
    on<UnSelectFilterValueEvent>(_onUnSelectEventFilterValue);
    on<PropertyChangeCurrencyEvent>(_onPropertyChangeCurrencyEvent);
    on<PropertiesSearchFiltersItemsEvent>(_onPropertiesSearchFiltersItemsEvent);
    on<PropertySelectFromOrToDateEvent>(_onPropertySelectFromToDateEvent);
    on<PropertySelectDateTypeEvent>(_onPropertySelectDateTypeEvent);
    on<SelectCarpetAreaEvent>(_onSelectCarpetArea);
    on<SelectPropertyAreaEvent>(_onSelectPropertyArea);
    on<SelectBuiltUpAreaEvent>(_onSelectBuiltUpArea);
    on<SelectSaleableAreaEvent>(_onSelectSaleableArea);
    on<OnPropertySizeChangeEvent>(_onPropertySizeChange);
    on<OnCarpetAreaChangeEvent>(_onCarpetAreaChange);
    on<OnBuiltUpAreaChangeEvent>(_onBuiltUpAreaChange);
    on<OnSaleableAreaChangeEvent>(_onSaleableAreaChange);
    on<AreaValueChanged>(_onAreaValueChanged);
    on<AreaUnitChanged>(_onAreaUnitChanged);
    on<PropertiesChangeCurrencyEvent>(_onPropertiesChangeCurrencyEvent);
    on<OnMinBudgetChangeEvent>(_onOnMinBudgetChangeEvent);
    on<OnMaxBudgetChangeEvent>(_onOnMaxBudgetChangeEvent);
  }

  FutureOr<void> _onPropertyFilterInitialEvent(PropertyFilterInitialEvent event, Emitter<PropertyFilterState> emit) async {
    if (!state.isInitialized) {
      await _initializeFilterCategories(emit: emit, propertyFilterKey: event.propertyFilterKey);
      add(InitialiseCurrenciesEvent());
      add(InitialiseDateTypesEvent());
    }
    tempPropertyFilterState = state;
    final masterAreaUnits = await _masterDataRepository.getAreaUnits();
    final areaUnits = masterAreaUnits?.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit)).toList();
    emit(state.copyWith(areaUnits: areaUnits));
  }

  FutureOr<void> _onPropertyFilterCancelEvent(PropertyFilterCancelEvent event, Emitter<PropertyFilterState> emit) async {
    if (tempPropertyFilterState != null) emit(tempPropertyFilterState!);
    if (tempPropertyFilterState != null && tempPropertyFilterState!.itemPropertyFilterCategoryModels != null && tempPropertyFilterState!.itemPropertyFilterCategoryModels!.where((element) => element.isSelected).isEmpty) {
      add(FilterCategorySelectEvent(itemPropertyFilterModel: tempPropertyFilterState!.itemPropertyFilterCategoryModels!.where((element) => element.propertyFilterKey == PropertyFilterKey.lookingFor).first));
    }
  }

  FutureOr<void> _onPropertyFilterApplyEvent(PropertyFilterApplyEvent event, Emitter<PropertyFilterState> emit) async {
    List<ItemSimpleModel<Map<PropertyFilterKey?, List<ItemSimpleModel>>>> filterItemSimpleModels = [];
    state.itemPropertyFilterCategoryModels?.forEach(
      (element) {
        ItemSimpleModel<Map<PropertyFilterKey?, List<ItemSimpleModel>>>? tempModel = element.toItemSimpleModel(currency: state.selectedCurrency?.value);
        if (tempModel != null) {
          filterItemSimpleModels.add(tempModel);
        }
      },
    );
    if (state.propertySize != null) {
      final title = "${PropertyFilterKey.propertySize.description}"
          "${state.propertySize?.min != null ? " min${state.propertySize?.min ?? ''}" : ''}"
          "${state.propertySize?.max != null ? " - max${state.propertySize?.max ?? ''}" : ''}";

      final values = [ItemSimpleModel(title: title, value: null, isSelected: true, description: '')];
      filterItemSimpleModels.add(
        ItemSimpleModel<Map<PropertyFilterKey?, List<ItemSimpleModel>>>(title: title, value: {PropertyFilterKey.propertySize: values}, description: state.selectedPropertyAreaUnit?.value?.unit ?? ''),
      );
    }
    if (state.builtUpArea != null) {
      final title = "${PropertyFilterKey.builtUpArea.description}"
          "${state.builtUpArea?.min != null ? " min${state.builtUpArea?.min ?? ''}" : ''}"
          "${state.builtUpArea?.max != null ? " - max${state.builtUpArea?.max ?? ''}" : ''}";

      final values = [ItemSimpleModel(title: title, value: null, isSelected: true, description: '')];
      filterItemSimpleModels.add(
        ItemSimpleModel<Map<PropertyFilterKey?, List<ItemSimpleModel>>>(title: title, value: {PropertyFilterKey.builtUpArea: values}, description: state.selectedBuiltUpAreaUnit?.value?.unit ?? ''),
      );
    }
    if (state.carpetArea != null) {
      final title = "${PropertyFilterKey.carpetArea.description}"
          "${state.carpetArea?.min != null ? " min${state.carpetArea?.min ?? ''}" : ''}"
          "${state.carpetArea?.max != null ? " - max${state.carpetArea?.max ?? ''}" : ''}";

      final values = [ItemSimpleModel(title: title, value: null, isSelected: true, description: '')];
      filterItemSimpleModels.add(
        ItemSimpleModel<Map<PropertyFilterKey?, List<ItemSimpleModel>>>(title: title, value: {PropertyFilterKey.carpetArea: values}, description: state.selectedCarpetAreaUnit?.value?.unit ?? ''),
      );
    }
    if (state.saleableArea != null) {
      final title = "${PropertyFilterKey.saleableArea.description}"
          "${state.saleableArea?.min != null ? " min${state.saleableArea?.min ?? ''}" : ''}"
          "${state.saleableArea?.max != null ? " - max${state.saleableArea?.max ?? ''}" : ''}";

      final values = [ItemSimpleModel(title: title, value: null, isSelected: true, description: '')];
      filterItemSimpleModels.add(
        ItemSimpleModel<Map<PropertyFilterKey?, List<ItemSimpleModel>>>(title: title, value: {PropertyFilterKey.saleableArea: values}, description: state.selectedSaleableAreaUnit?.value?.unit ?? ''),
      );
    }
    if (state.managePropertySalableArea != null) {
      final title = "${PropertyFilterKey.saleableArea.description} ${state.managePropertySalableArea ?? ''}";
      final values = [ItemSimpleModel(title: title, value: null, isSelected: true, description: '')];
      filterItemSimpleModels.add(
        ItemSimpleModel<Map<PropertyFilterKey?, List<ItemSimpleModel>>>(title: title, value: {PropertyFilterKey.saleableArea: values}, description: state.selectedSaleableAreaUnit?.value?.unit ?? ''),
      );
    }
    if (state.managePropertyCarpetArea != null) {
      final title = "${PropertyFilterKey.carpetArea.description} ${state.managePropertyCarpetArea ?? ''}";
      final values = [ItemSimpleModel(title: title, value: null, isSelected: true, description: '')];
      filterItemSimpleModels.add(
        ItemSimpleModel<Map<PropertyFilterKey?, List<ItemSimpleModel>>>(title: title, value: {PropertyFilterKey.carpetArea: values}, description: state.selectedCarpetAreaUnit?.value?.unit ?? ''),
      );
    }
    if (state.managePropertyBuiltUpArea != null) {
      final title = "${PropertyFilterKey.builtUpArea.description} ${state.managePropertyBuiltUpArea ?? ''}";
      final values = [ItemSimpleModel(title: title, value: null, isSelected: true, description: '')];
      filterItemSimpleModels.add(
        ItemSimpleModel<Map<PropertyFilterKey?, List<ItemSimpleModel>>>(title: title, value: {PropertyFilterKey.builtUpArea: values}, description: state.selectedBuiltUpAreaUnit?.value?.unit ?? ''),
      );
    }
    if (state.managePropertyArea != null) {
      final title = "${PropertyFilterKey.propertySize.description} ${state.managePropertyArea ?? ''}";
      final values = [ItemSimpleModel(title: title, value: null, isSelected: true, description: '')];
      filterItemSimpleModels.add(
        ItemSimpleModel<Map<PropertyFilterKey?, List<ItemSimpleModel>>>(title: title, value: {PropertyFilterKey.propertySize: values}, description: state.selectedPropertyAreaUnit?.value?.unit ?? ''),
      );
    }
    if (state.minBudget != null) {
      String budgetRange = "${state.minBudget?.min != null ? "min( ${state.minBudget?.min})" : ''}"
          "${state.minBudget?.min != null && state.minBudget?.max != null ? ' - ' : ''}"
          "${state.minBudget?.max != null ? "max( ${state.minBudget?.max})" : ''}";

      final title = "${PropertyFilterKey.minBudget.description} ${state.selectedCurrency?.title ?? ''}";
      final values = [ItemSimpleModel(title: budgetRange, value: null, isSelected: true, description: '')];

      filterItemSimpleModels.add(ItemSimpleModel<Map<PropertyFilterKey?, List<ItemSimpleModel>>>(title: budgetRange, value: {PropertyFilterKey.minBudget: values}, description: state.selectedCurrency?.title ?? ''));
    }
    if (state.maxBudget != null) {
      String budgetRange = "min(${state.selectedCurrency?.title ?? ''} ${state.maxBudget?.min ?? ''})"
          "${state.maxBudget?.max != null ? " - max(${state.selectedCurrency?.title ?? ''} ${state.maxBudget?.max})" : ''}";

      final values = [ItemSimpleModel(title: budgetRange, value: null, isSelected: true, description: '')];

      // filterItemSimpleModels.add(ItemSimpleModel<Map<PropertyFilterKey?, List<ItemSimpleModel>>>(title: budgetRange, value: {PropertyFilterKey.maxBudget: values}, description: state.selectedCurrency?.title ?? ''));
    }

    getIt<ManagePropertyBloc>().add(ManagePropertyInitialEvent(filterItemSimpleModels: filterItemSimpleModels, propertyFilterModel: _getPropertyFilterModel()));
  }

  FutureOr<void> _onPropertySelectFromToDateEvent(PropertySelectFromOrToDateEvent event, Emitter<PropertyFilterState> emit) async {
    emit(state.copyWith(toDate: event.isFromDate ? state.toDate : event.selectedDate, fromDate: !event.isFromDate ? state.fromDate : event.selectedDate));
  }

  FutureOr<void> _onPropertySelectDateTypeEvent(PropertySelectDateTypeEvent event, Emitter<PropertyFilterState> emit) async {
    if (event.selectedDateType.value == PropertyDateType.possessionDate) {
      emit(state.copyWith(selectedDateType: event.selectedDateType, updateToDate: false, updateFromDate: false));
      var propertyFilterValues = PossessionType.values.where((e) => e != PossessionType.none).map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
      _emitInitializedFilterValues(FilterCategorySelectEvent(itemPropertyFilterModel: state.itemPropertyFilterCategoryModels!.where((element) => element.propertyFilterKey == PropertyFilterKey.dateRange).toList()[0]), emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.dateRange);
    } else {
      emit(state.copyWith(selectedDateType: event.selectedDateType, updateToDate: false, updateFromDate: false));
      var propertyFilterValues = DateRange.values.map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
      _emitInitializedFilterValues(FilterCategorySelectEvent(itemPropertyFilterModel: state.itemPropertyFilterCategoryModels!.where((element) => element.propertyFilterKey == PropertyFilterKey.dateRange).toList()[0]), emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.dateRange);
    }
  }

  FutureOr<void> _onPropertyFilterResetEvent(PropertyFilterResetEvent event, Emitter<PropertyFilterState> emit) async {
    PropertyFilterKey? propertyFilterKey = state.itemPropertyFilterCategoryModels?.where((e) => e.isSelected).first.propertyFilterKey;
    emit(state.resetState(itemPropertyFilterCategoryModels: null, selectedCurrency: null, selectedDateType: null, currencies: state.currencies, dateTypes: state.dateTypes, fromDate: null, toDate: null));
    add(PropertyFilterInitialEvent(propertyFilterKey: propertyFilterKey));
  }

  FutureOr<void> _onFilterCategorySelectEvent(FilterCategorySelectEvent event, Emitter<PropertyFilterState> emit) async {
    if (!event.itemPropertyFilterModel.isInitialized) {
      await _initializeFilterValues(event, emit);
    } else {
      if (event.itemPropertyFilterModel.propertyFilterKey == PropertyFilterKey.propertySubType) {
        _makePropertySubTypeFields(emit);
      } else {
        final itemPropertyFilterModels = state.itemPropertyFilterCategoryModels?.map((e) => e.copyWith(isSelected: e.propertyFilterKey == event.itemPropertyFilterModel.propertyFilterKey, resetSearchController: true)).toList();
        emit(state.copyWith(itemPropertyFilterCategoryModels: itemPropertyFilterModels));
        add(PropertiesSearchFiltersItemsEvent(itemPropertyFilterModel: event.itemPropertyFilterModel, searchText: ''));
      }
    }
  }

  FutureOr<void> _onFilterValueMultiSelectEvent(FilterValueMultiSelectEvent event, Emitter<PropertyFilterState> emit) async {
    if (event.isSelectAll && event.valueIndex == null) {
      var values = event.itemPropertyFilterModel.propertyFilterValues?.map((e) => e.copyWith(isSelected: !(event.itemPropertyFilterModel.isAllSelected) && e.visible)).toList();
      var itemPropertyFilterModel = state.itemPropertyFilterCategoryModels?.map((e) => e.propertyFilterKey == event.itemPropertyFilterModel.propertyFilterKey ? e.copyWith(propertyFilterValues: values) : e).toList();
      emit(state.copyWith(itemPropertyFilterCategoryModels: itemPropertyFilterModel));
    } else {
      var values = event.itemPropertyFilterModel.propertyFilterValues?.map((e) => e == event.valueIndex ? e.copyWith(isSelected: e.isSelected == null ? false : !e.isSelected!) : e).toList();
      var itemPropertyFilterModel = state.itemPropertyFilterCategoryModels?.map((e) => e.propertyFilterKey == event.itemPropertyFilterModel.propertyFilterKey ? e.copyWith(propertyFilterValues: values) : e).toList();
      emit(state.copyWith(itemPropertyFilterCategoryModels: itemPropertyFilterModel));
    }
  }

  FutureOr<void> _onPropertyChangeCurrencyEvent(PropertyChangeCurrencyEvent event, Emitter<PropertyFilterState> emit) async {
    emit(state.copyWith(selectedCurrency: event.selectedCurrency));
  }

  FutureOr<void> _onUnSelectEventFilterValue(UnSelectFilterValueEvent event, Emitter<PropertyFilterState> emit) async {
    var itemPropertyFilterModels = state.itemPropertyFilterCategoryModels
        ?.map((e) => e.propertyFilterKey == event.propertyFilterKey
            ? e.copyWith(
                propertyFilterValues: e.propertyFilterValues?.map((value) {
                  return value.copyWith(
                    isSelected: event.value == null
                        ? false
                        : event.value == value
                            ? false
                            : value.isSelected,
                    visible: true,
                    // budgetRangeModel: event.propertyFilterKey == PropertyFilterKey.price ? null : value.budgetRangeModel,
                  );
                }).toList(),
                isSelected: true)
            : e.copyWith(isSelected: false))
        .toList();
    if (event.propertyFilterKey == PropertyFilterKey.leadCount) {
      itemPropertyFilterModels = itemPropertyFilterModels?.map((element) => element.propertyFilterKey == PropertyFilterKey.leadCount ? element.copyWith(resetCounts: true) : element).toList();
    }
    if (event.propertyFilterKey == PropertyFilterKey.prospectCount) {
      itemPropertyFilterModels = itemPropertyFilterModels?.map((element) => element.propertyFilterKey == PropertyFilterKey.prospectCount ? element.copyWith(resetCounts: true) : element).toList();
    }
    emit(state.resetState(
      propertySize: event.propertyFilterKey == PropertyFilterKey.propertySize ? null : state.propertySize,
      selectedPropertyAreaUnit: event.propertyFilterKey == PropertyFilterKey.propertySize ? null : state.selectedPropertyAreaUnit,
      selectedSaleableAreaUnit: event.propertyFilterKey == PropertyFilterKey.saleableArea ? null : state.selectedSaleableAreaUnit,
      selectedCarpetAreaUnit: event.propertyFilterKey == PropertyFilterKey.carpetArea ? null : state.selectedCarpetAreaUnit,
      selectedBuiltUpAreaUnit: event.propertyFilterKey == PropertyFilterKey.builtUpArea ? null : state.selectedBuiltUpAreaUnit,
      managePropertySalableArea: event.propertyFilterKey == PropertyFilterKey.saleableArea ? null : state.managePropertySalableArea,
      managePropertyBuiltUpArea: event.propertyFilterKey == PropertyFilterKey.builtUpArea ? null : state.managePropertyBuiltUpArea,
      managePropertyCarpetArea: event.propertyFilterKey == PropertyFilterKey.carpetArea ? null : state.managePropertyCarpetArea,
      managePropertyArea: event.propertyFilterKey == PropertyFilterKey.propertySize ? null : state.managePropertyArea,
      saleableArea: event.propertyFilterKey == PropertyFilterKey.saleableArea ? null : state.saleableArea,
      builtUpArea: event.propertyFilterKey == PropertyFilterKey.builtUpArea ? null : state.builtUpArea,
      carpetArea: event.propertyFilterKey == PropertyFilterKey.carpetArea ? null : state.carpetArea,
      itemPropertyFilterCategoryModels: itemPropertyFilterModels,
      selectedCurrency: (event.propertyFilterKey == PropertyFilterKey.minBudget) ? selectedCurrency : null,
      selectedDateType: event.propertyFilterKey == PropertyFilterKey.dateRange ? null : state.selectedDateType,
      currencies: state.currencies,
      dateTypes: state.dateTypes,
      fromDate: event.propertyFilterKey == PropertyFilterKey.dateRange ? null : state.fromDate,
      toDate: event.propertyFilterKey == PropertyFilterKey.dateRange ? null : state.toDate,
      // maxBudget: event.propertyFilterKey == PropertyFilterKey.maxBudget ? null : state.maxBudget,
      minBudget: event.propertyFilterKey == PropertyFilterKey.minBudget ? null : state.minBudget,
    ));
    add(PropertyFilterApplyEvent());
  }

  FutureOr<void> _onFilterValueSingleSelectEvent(FilterValueSingleSelectEvent event, Emitter<PropertyFilterState> emit) async {
    if (event.itemPropertyFilterModel.propertyFilterKey == PropertyFilterKey.dateRange && state.dateTypes != null && state.selectedDateType == null) {
      emit(state.copyWith(pageState: PageState.failure, errorMessage: "Please select the dateType."));
      return;
    }
    var values = event.itemPropertyFilterModel.propertyFilterValues?.map((e) => e == event.filterValue ? e.copyWith(isSelected: true) : e.copyWith(isSelected: false)).toList();
    var itemPropertyFilterModel = state.itemPropertyFilterCategoryModels?.map((e) => e.propertyFilterKey == event.itemPropertyFilterModel.propertyFilterKey ? e.copyWith(propertyFilterValues: values) : e).toList();
    emit(state.copyWith(itemPropertyFilterCategoryModels: itemPropertyFilterModel));
  }

  FutureOr<void> _onPropertiesSearchFiltersItemsEvent(PropertiesSearchFiltersItemsEvent event, Emitter<PropertyFilterState> emit) async {
    var searchItemPropertyFilters = event.itemPropertyFilterModel.propertyFilterValues
        ?.map(
          (e) => e.displayName!.toLowerCase().contains(event.searchText.toLowerCase()) ? e.copyWith(visible: true) : e.copyWith(visible: false),
        )
        .toList();
    var itemPropertyFilterModel = state.itemPropertyFilterCategoryModels?.map((e) => e.propertyFilterKey == event.itemPropertyFilterModel.propertyFilterKey ? e.copyWith(propertyFilterValues: searchItemPropertyFilters) : e).toList();
    emit(state.copyWith(itemPropertyFilterCategoryModels: itemPropertyFilterModel));
  }

  Future<void> _initializeFilterCategories({PropertyFilterKey? propertyFilterKey, required Emitter<PropertyFilterState> emit}) async {
    final customViewFilterCategories = [PropertyFilterKey.carpetArea, PropertyFilterKey.saleableArea, PropertyFilterKey.builtUpArea, PropertyFilterKey.propertySize, PropertyFilterKey.minBudget];

    List<ItemPropertyFilterCategoryModel>? itemPropertyFilterModels = PropertyFilterKey.values
        .map((e) => ItemPropertyFilterCategoryModel(
              propertyFilterKey: e,
              propertyFilterValues: [],
              categoryName: e.description,
              hasCustomView: customViewFilterCategories.contains(e),
            ))
        .toList();

    bool checkPermission = getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.view);
    checkPermission = checkPermission ? checkPermission : getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.viewAssigned);
    if (checkPermission) {
      itemPropertyFilterModels.removeWhere((element) => element.propertyFilterKey == PropertyFilterKey.assignTo);
    }
    if (!isPropertyListingEnabled) {
      final propertyListingCategories = [
        PropertyFilterKey.offeringType,
        PropertyFilterKey.completionStatus,
        PropertyFilterKey.listingStatus,
        PropertyFilterKey.community,
        PropertyFilterKey.subCommunity,
      ];
      itemPropertyFilterModels.removeWhere((element) => propertyListingCategories.contains(element.propertyFilterKey));
    } else {
      //Category name changes
      itemPropertyFilterModels = itemPropertyFilterModels.map((e) {
        if (e.propertyFilterKey == PropertyFilterKey.bHK) {
          return e.copyWith(categoryName: "BR");
        }
        if (e.propertyFilterKey == PropertyFilterKey.bHKType) {
          return e.copyWith(categoryName: "BR Type");
        }
        return e;
      }).toList();
    }

    emit(state.copyWith(itemPropertyFilterCategoryModels: itemPropertyFilterModels, isInitialized: true));
    if (state.itemPropertyFilterCategoryModels != null) {
      add(FilterCategorySelectEvent(itemPropertyFilterModel: state.itemPropertyFilterCategoryModels!.where((element) => propertyFilterKey != null ? element.propertyFilterKey == propertyFilterKey : element.propertyFilterKey == PropertyFilterKey.lookingFor).first));
    }
  }

  Future<void> _initializeFilterValues(FilterCategorySelectEvent event, Emitter<PropertyFilterState> emit) async {
    switch (event.itemPropertyFilterModel.propertyFilterKey) {
      case null:
        return;
      case PropertyFilterKey.lookingFor:
        var propertyFilterValues = EnquiryType.values.where((e) => (e != EnquiryType.none) && (e != EnquiryType.buy)).map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
        _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.lookingFor);
        break;
      case PropertyFilterKey.saleType:
        var propertyFilterValues = SaleType.values.where((e) => e != SaleType.none).map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
        _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.saleType, isMultiSelect: true);
        break;
      case PropertyFilterKey.propertyType:
        var propertyFilterValues = PropertyType.values.map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
        _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.propertyType, isMultiSelect: true);
        break;
      case PropertyFilterKey.propertySubType:
        final masterPropertyTypes = await _masterDataRepository.getCustomPropertyTypes(isPropertyListingEnabled: getIt<LeadratHomeBloc>().isPropertyListingEnabled);
        if (masterPropertyTypes == null) return;
        List<PropertyFilterValue> subTypes = [];
        for (var item in masterPropertyTypes) {
          item.childTypes?.forEach((element) => subTypes.add(
                PropertyFilterValue(displayName: element.displayName ?? "", value: element),
              ));
        }
        _emitInitializedFilterValues(event, emit, propertyFilterValues: subTypes, propertyFilterKey: PropertyFilterKey.propertySubType, isMultiSelect: true);
        break;

      case PropertyFilterKey.assignTo:
        _emitInitializedFilterValues(event, emit, propertyFilterKey: PropertyFilterKey.assignTo, isInitialized: false);
        String userId = await getIt<SharedPreferenceManager>().getString("user_id") ?? '';
        var users = getIt<PropertyEntityMapper>().getAllUsers();
        PropertyFilterValue? propertyFilterValue;
        List<PropertyFilterValue<dynamic>>? propertyFilterValues = [];
        users?.forEach((element) {
          if (element?.id == userId) {
            propertyFilterValue = PropertyFilterValue(value: element, displayName: 'You');
          } else {
            propertyFilterValues.add(PropertyFilterValue(value: element, displayName: '${element?.userName} ${element?.lastName}'));
          }
        });
        if (propertyFilterValue != null) {
          propertyFilterValues.insert(0, propertyFilterValue!);
        }

        _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.assignTo, isMultiSelect: true, hasSearch: true);

        break;
      case PropertyFilterKey.facing:
        var propertyFilterValues = Facing.values.where((e) => e != Facing.unknown).map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
        _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.facing);
        break;
      case PropertyFilterKey.furnishingStatus:
        var propertyFilterValues = FurnishStatus.values.where((e) => e != FurnishStatus.none).map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
        _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.furnishingStatus, isMultiSelect: true);
        break;
      case PropertyFilterKey.bHKType:
        var propertyFilterValues = BHKType.values.where((e) => e != BHKType.none).map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
        _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.bHKType, isMultiSelect: true);
        break;
      case PropertyFilterKey.bHK:
        List<PropertyFilterValue<NoOfBHK>> propertyFilterValues = [];

        if (isPropertyListingEnabled) {
          propertyFilterValues = NoOfBHK.values.where((bhk) => bhk.noOfBhk % 1 == 0).map((e) => PropertyFilterValue(value: e, displayName: e.description.replaceAll("BHK", "BR"))).toList();
        } else {
          propertyFilterValues = NoOfBHK.values.map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
        }
        _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.bHK, isMultiSelect: true);
        break;
      case PropertyFilterKey.floors:
        var propertyFilterValues = Floors.values.whereNot((element) => element == Floors.zero).map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
        _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.floors, isMultiSelect: true);
        break;
      case PropertyFilterKey.ownerNames:
        _emitInitializedFilterValues(event, emit, propertyFilterKey: PropertyFilterKey.ownerNames, isInitialized: false);
        final result = await _getOwnerNamesOrAddressesUseCase(GetOwnerNamesOrAddressesUseCaseParams.owner);
        result.fold(
          (failure) => _emitInitializedFilterValues(event, emit, propertyFilterValues: [], propertyFilterKey: PropertyFilterKey.ownerNames, isInitialized: false),
          (ownerNames) {
            if (ownerNames == null) return;
            var propertyFilterValues = ownerNames.map((ownerName) => PropertyFilterValue(value: ownerName, displayName: ownerName)).toList();
            _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.ownerNames, hasSearch: true, isMultiSelect: true);
          },
        );
        break;
      case PropertyFilterKey.projects:
        _emitInitializedFilterValues(event, emit, propertyFilterKey: PropertyFilterKey.projects, isInitialized: false);
        final result = await _getProjectNameWithIdUseCase(NoParams());
        result.fold(
          (failure) => _emitInitializedFilterValues(event, emit, propertyFilterValues: [], propertyFilterKey: PropertyFilterKey.projects, isInitialized: false),
          (projects) {
            if (projects == null) return;
            var propertyFilterValues = projects.map((project) => PropertyFilterValue(value: project, displayName: project.name)).toList();
            _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.projects, isMultiSelect: true, hasSearch: true);
          },
        );
        break;
      case PropertyFilterKey.propertySize:
        _emitInitializedFilterValues(event, emit, propertyFilterKey: PropertyFilterKey.propertySize);
        break;
      case PropertyFilterKey.dateRange:
        if (state.dateTypes == null) add(InitialiseDateTypesEvent());
        if (state.selectedDateType?.value == PropertyDateType.possessionDate) {
          var propertyFilterValues = PossessionType.values.map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
          _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.dateRange);
        } else {
          var propertyFilterValues = DateRange.values.map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
          _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.dateRange);
        }
        break;
      case PropertyFilterKey.locations:
        _emitInitializedFilterValues(event, emit, propertyFilterKey: PropertyFilterKey.locations, isInitialized: false);
        final result = await _getOwnerNamesOrAddressesUseCase(GetOwnerNamesOrAddressesUseCaseParams.address);
        result.fold(
          (failure) => _emitInitializedFilterValues(event, emit, propertyFilterValues: [], propertyFilterKey: PropertyFilterKey.locations, isInitialized: false),
          (addresses) {
            if (addresses == null) return;
            var propertyFilterValues = addresses.map((addresses) => PropertyFilterValue(value: addresses, displayName: addresses)).toList();
            _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.locations, isMultiSelect: true, hasSearch: true);
          },
        );
        break;
      case PropertyFilterKey.noOfBathrooms:
        var propertyFilterValues = Feature.values.map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
        _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.noOfBathrooms, isMultiSelect: true);
        break;
      case PropertyFilterKey.noOfLivingRooms:
        var propertyFilterValues = Feature.values.map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
        _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.noOfLivingRooms, isMultiSelect: true);
        break;
      case PropertyFilterKey.noOfBalconies:
        var propertyFilterValues = Feature.values.map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
        _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.noOfBalconies, isMultiSelect: true);
        break;
      case PropertyFilterKey.noOfBedrooms:
        var propertyFilterValues = Feature.values.map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
        _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.noOfBedrooms, isMultiSelect: true);
        break;
      case PropertyFilterKey.noOfKitchens:
        var propertyFilterValues = Feature.values.map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
        _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.noOfKitchens, isMultiSelect: true);
        break;
      case PropertyFilterKey.noOfUtilities:
        var propertyFilterValues = Feature.values.map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
        _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.noOfUtilities, isMultiSelect: true);
        break;
      case PropertyFilterKey.amenities:
        _emitInitializedFilterValues(event, emit, propertyFilterKey: PropertyFilterKey.amenities, isInitialized: false);
        var amenities = getIt<PropertyEntityMapper>().getAllCustomAmenities();
        var propertyFilterValues = amenities?.map((e) => PropertyFilterValue(value: e, displayName: e.amenityDisplayName)).toList();
        _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.amenities, isMultiSelect: true, hasSearch: true);
        break;
      case PropertyFilterKey.listingStatus:
        var propertyFilterValues = ListingStatus.values.map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
        _emitInitializedFilterValues(event, emit, propertyFilterKey: PropertyFilterKey.listingStatus, isInitialized: true, propertyFilterValues: propertyFilterValues);
        break;
      case PropertyFilterKey.offeringType:
        var propertyFilterValues = OfferingType.values.map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
        _emitInitializedFilterValues(event, emit, propertyFilterKey: PropertyFilterKey.offeringType, isInitialized: true, propertyFilterValues: propertyFilterValues);
        break;
      case PropertyFilterKey.completionStatus:
        var propertyFilterValues = CompletionStatus.values.whereNot((element) => element == CompletionStatus.none).map((e) => PropertyFilterValue(value: e, displayName: e.description)).toList();
        _emitInitializedFilterValues(event, emit, propertyFilterKey: PropertyFilterKey.completionStatus, isInitialized: true, propertyFilterValues: propertyFilterValues);
        break;
      case PropertyFilterKey.community:
        _emitInitializedFilterValues(event, emit, propertyFilterKey: PropertyFilterKey.community, isInitialized: false);
        final result = await _getCommunityUseCase(NoParams());
        result.fold(
          (failure) => _emitInitializedFilterValues(event, emit, propertyFilterValues: [], propertyFilterKey: PropertyFilterKey.community, isInitialized: false),
          (communities) {
            if (communities == null) return;
            var propertyFilterValues = communities.map((e) => PropertyFilterValue(value: e, displayName: e)).toList();
            _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.community, isMultiSelect: true, hasSearch: true);
          },
        );
        break;
      case PropertyFilterKey.subCommunity:
        _emitInitializedFilterValues(event, emit, propertyFilterKey: PropertyFilterKey.subCommunity, isInitialized: false);
        final result = await _getSubCommunityUseCase(NoParams());
        result.fold(
          (failure) => _emitInitializedFilterValues(event, emit, propertyFilterValues: [], propertyFilterKey: PropertyFilterKey.subCommunity, isInitialized: false),
          (subCommunities) {
            if (subCommunities == null) return;
            var propertyFilterValues = subCommunities.map((e) => PropertyFilterValue(value: e, displayName: e)).toList();
            _emitInitializedFilterValues(event, emit, propertyFilterValues: propertyFilterValues, propertyFilterKey: PropertyFilterKey.subCommunity, isMultiSelect: true, hasSearch: true);
          },
        );
        break;
      case PropertyFilterKey.saleableArea:
        _emitInitializedFilterValues(event, emit, propertyFilterKey: PropertyFilterKey.saleableArea);
      case PropertyFilterKey.builtUpArea:
        _emitInitializedFilterValues(event, emit, propertyFilterKey: PropertyFilterKey.builtUpArea);
      case PropertyFilterKey.carpetArea:
        _emitInitializedFilterValues(event, emit, propertyFilterKey: PropertyFilterKey.carpetArea);
      case PropertyFilterKey.minBudget:
        _emitInitializedFilterValues(event, emit, propertyFilterKey: PropertyFilterKey.minBudget);

      case PropertyFilterKey.prospectCount:
        _emitInitializedFilterValues(event, emit, propertyFilterKey: PropertyFilterKey.prospectCount, hasCount: true);
      case PropertyFilterKey.leadCount:
        _emitInitializedFilterValues(event, emit, propertyFilterKey: PropertyFilterKey.leadCount, hasCount: true);
    }
  }

  _emitInitializedFilterValues(FilterCategorySelectEvent event, Emitter<PropertyFilterState> emit, {List<PropertyFilterValue<dynamic>>? propertyFilterValues, required PropertyFilterKey propertyFilterKey, bool hasSearch = false, bool isMultiSelect = false, bool isInitialized = true, bool hasCount = false}) {
    List<ItemPropertyFilterCategoryModel>? itemPropertyFilterModels = state.itemPropertyFilterCategoryModels
        ?.map((e) => e.propertyFilterKey == event.itemPropertyFilterModel.propertyFilterKey
            ? e.copyWith(
                isInitialized: isInitialized,
                propertyFilterKey: propertyFilterKey,
                propertyFilterValues: propertyFilterValues,
                isSelected: true,
                hasSearch: hasSearch,
                textEditingController: hasSearch ? TextEditingController() : null,
                isMultiSelect: isMultiSelect,
                hasCount: hasCount,
                maxCountController: hasCount ? TextEditingController() : null,
                minCountController: hasCount ? TextEditingController() : null,
              )
            : e.copyWith(isSelected: false))
        .toList();
    emit(state.copyWith(itemPropertyFilterCategoryModels: itemPropertyFilterModels));
    if (itemPropertyFilterModels != null && isInitialized) {
      add(FilterCategorySelectEvent(itemPropertyFilterModel: itemPropertyFilterModels.where((element) => element.propertyFilterKey == event.itemPropertyFilterModel.propertyFilterKey).toList().first));
    }
  }

  Future<void> _onInitialiseCurrenciesEvent(InitialiseCurrenciesEvent event, Emitter<PropertyFilterState> emit) async {
    var currencies = await getIt<PropertiesRepository>().getCurrencies();
    globalSettings = await _globalSettingRepository.getGlobalSettings();
    List<SelectableItem<String>> allCurrencies = [];
    SelectableItem<String>? selectableItem;
    currencies?.forEach((item) => allCurrencies.add(SelectableItem<String>(title: item, value: item)));
    if (globalSettings != null) {
      var defaultSymbol = globalSettings?.countries?.firstOrNull?.defaultCurrency ?? "INR";
      selectableItem = allCurrencies.firstWhereOrNull((element) => element.value == defaultSymbol);
      selectedCurrency = selectableItem;
    }
    emit(state.copyWith(selectedCurrency: selectableItem, currencies: allCurrencies));
  }

  Future<void> _onInitialiseDateTypesEvent(InitialiseDateTypesEvent event, Emitter<PropertyFilterState> emit) async {
    List<SelectableItem<PropertyDateType>>? dateTypes = PropertyDateType.values.map((e) => SelectableItem<PropertyDateType>(title: e.description, value: e)).toList();
    emit(state.copyWith(dateTypes: dateTypes));
  }

  List? _getSelectedFilterValues(PropertyFilterKey propertyFilterKey) {
    return state.itemPropertyFilterCategoryModels?.where((element) => element.propertyFilterKey == propertyFilterKey).firstOrNull?.selectedFilterValues();
  }

  String? _getLeadOrProspectCount({required PropertyFilterKey propertyFilterKey, bool isMinCount = false}) {
    final selectedFilterCategoryModel = state.itemPropertyFilterCategoryModels?.where((element) => element.propertyFilterKey == propertyFilterKey).firstOrNull;

    return isMinCount ? selectedFilterCategoryModel?.minCountController?.text : selectedFilterCategoryModel?.maxCountController?.text;
  }

  PropertyFilterModel _getPropertyFilterModel() {
    final propertyFilter = PropertyFilterModel(
      enquiryType: _getSelectedFilterValues(PropertyFilterKey.lookingFor)?.cast<EnquiryType>().first,
      fromDate: utcToDateFormat(state.fromDate?.toUniversalTimeStartOfDay() ?? DateTime.now()),
      toDate: utcToDateFormat(state.toDate?.toUniversalTimeStartOfDay() ?? DateTime.now()),
      facing: _getSelectedFilterValues(PropertyFilterKey.facing)?.cast<Facing>().first,
      amenities: _getSelectedFilterValues(PropertyFilterKey.amenities)?.cast<CustomAmenityModel>(),
      furnishStatus: _getSelectedFilterValues(PropertyFilterKey.furnishingStatus)?.cast<FurnishStatus>(),
      currency: state.selectedCurrency?.value,
      noOfBHKs: _getSelectedFilterValues(PropertyFilterKey.bHK)?.cast<NoOfBHK>(),
      projects: _getSelectedFilterValues(PropertyFilterKey.projects)?.cast<ProjectEntity>(),
      assignTo: _getSelectedFilterValues(PropertyFilterKey.assignTo)?.cast<GetAllUsersModel>(),
      bhkTypes: _getSelectedFilterValues(PropertyFilterKey.bHKType)?.cast<BHKType>(),
      dateRange: state.selectedDateType?.value != PropertyDateType.possessionDate ? _getSelectedFilterValues(PropertyFilterKey.dateRange)?.cast<DateRange>().first : null,
      dateType: state.selectedDateType?.value,
      floors: _getSelectedFilterValues(PropertyFilterKey.floors)?.cast<Floors>(),
      locations: _getSelectedFilterValues(PropertyFilterKey.locations)?.cast<String>(),
      noOfBalconies: _getSelectedFilterValues(PropertyFilterKey.noOfBalconies)?.cast<Feature>(),
      noOfBathrooms: _getSelectedFilterValues(PropertyFilterKey.noOfBathrooms)?.cast<Feature>(),
      noOfBedrooms: _getSelectedFilterValues(PropertyFilterKey.noOfBedrooms)?.cast<Feature>(),
      noOfKitchens: _getSelectedFilterValues(PropertyFilterKey.noOfKitchens)?.cast<Feature>(),
      noOfLivingRooms: _getSelectedFilterValues(PropertyFilterKey.noOfLivingRooms)?.cast<Feature>(),
      noOfUtilities: _getSelectedFilterValues(PropertyFilterKey.noOfUtilities)?.cast<Feature>(),
      ownerName: _getSelectedFilterValues(PropertyFilterKey.ownerNames)?.cast<String>().first,
      propertySize: _getSelectedFilterValues(PropertyFilterKey.propertySize)?.cast<PropertySize>().first,
      propertyTypes: _getSelectedFilterValues(PropertyFilterKey.propertyType)?.cast<PropertyType>(),
      propertySubTypes: _getSelectedFilterValues(PropertyFilterKey.propertySubType)?.cast<MasterPropertyTypeModel>(),
      saleTypes: _getSelectedFilterValues(PropertyFilterKey.saleType)?.cast<SaleType>(),
      completionStatus: _getSelectedFilterValues(PropertyFilterKey.completionStatus)?.cast<CompletionStatus>().firstOrNull,
      offeringType: _getSelectedFilterValues(PropertyFilterKey.offeringType)?.cast<OfferingType>().firstOrNull,
      communities: _getSelectedFilterValues(PropertyFilterKey.community)?.cast<String>(),
      subCommunities: _getSelectedFilterValues(PropertyFilterKey.subCommunity)?.cast<String>(),
      builtUpAreaUnit: state.selectedBuiltUpAreaUnit?.value?.id,
      carpetAreaUnit: state.selectedCarpetAreaUnit?.value?.id,
      saleableAreaUnit: state.selectedSaleableAreaUnit?.value?.id,
      propertySizeUnit: state.selectedPropertyAreaUnit?.value?.id,
      minBuiltUpArea: state.builtUpArea?.min,
      maxBuiltUpArea: state.builtUpArea?.max,
      minCarpetArea: state.carpetArea?.min,
      maxCarpetArea: state.carpetArea?.max,
      minPropertySize: state.propertySize?.min,
      maxPropertySize: state.propertySize?.max,
      minSaleableArea: state.saleableArea?.min,
      maxSaleableArea: state.saleableArea?.max,
      saleableArea: state.managePropertySalableArea,
      carpetArea: state.managePropertyCarpetArea,
      builtUpArea: state.managePropertyBuiltUpArea,
      propertyArea: state.managePropertyArea,
      toMinBudget: state.minBudget?.max,
      fromMinBudget: state.minBudget?.min,
      toMaxBudget: state.maxBudget?.max,
      fromMaxBudget: state.maxBudget?.min,
      maxLeadCount: _getLeadOrProspectCount(propertyFilterKey: PropertyFilterKey.leadCount, isMinCount: false),
      minLeadCount: _getLeadOrProspectCount(propertyFilterKey: PropertyFilterKey.leadCount, isMinCount: true),
      maxProspectCount: _getLeadOrProspectCount(propertyFilterKey: PropertyFilterKey.prospectCount, isMinCount: false),
      minProspectCount: _getLeadOrProspectCount(propertyFilterKey: PropertyFilterKey.prospectCount, isMinCount: true),
      possessionTypeDateRange: state.selectedDateType?.value == PropertyDateType.possessionDate ? _getSelectedFilterValues(PropertyFilterKey.dateRange)?.cast<PossessionType>().first : null,
    );
    return propertyFilter;
  }

  void _makePropertySubTypeFields(Emitter<PropertyFilterState> emit) {
    if (state.itemPropertyFilterCategoryModels!.any((element) => element.propertyFilterKey == PropertyFilterKey.propertyType && element.isInitialized)) {
      var propertyFilterValues = state.itemPropertyFilterCategoryModels!.where((element) => element.propertyFilterKey == PropertyFilterKey.propertyType).first.propertyFilterValues;
      List<PropertyFilterValue>? subPropertyFilterValues = state.itemPropertyFilterCategoryModels!.where((element) => element.propertyFilterKey == PropertyFilterKey.propertySubType).first.propertyFilterValues;
      List<PropertyFilterValue> tempSubTypes = [];
      propertyFilterValues?.forEach((values) {
        if (values.isSelected ?? false) {
          subPropertyFilterValues?.forEach((subPropertyType) {
            if ((subPropertyType.value as MasterPropertyTypeModel).baseId == (values.value as PropertyType).baseId) {
              tempSubTypes.add(subPropertyType.copyWith(visible: true));
            }
          });
        }
      });
      List<PropertyFilterValue>? subTypes = subPropertyFilterValues?.map((subPropertyFilterValue) {
        if (tempSubTypes.any((tempSubProperty) => tempSubProperty.displayName == subPropertyFilterValue.displayName)) {
          return subPropertyFilterValue.copyWith(visible: true);
        } else {
          return subPropertyFilterValue.copyWith(visible: false, isSelected: false);
        }
      }).toList();
      if (tempSubTypes.isEmpty) {
        subTypes = subPropertyFilterValues?.map((e) => e.copyWith(visible: true)).toList();
      }
      final itemPropertyFilterModels = state.itemPropertyFilterCategoryModels?.map((itemPropertyFilterModel) => itemPropertyFilterModel.propertyFilterKey == PropertyFilterKey.propertySubType ? itemPropertyFilterModel.copyWith(isSelected: true, propertyFilterValues: subTypes) : itemPropertyFilterModel.copyWith(isSelected: false)).toList();
      emit(state.copyWith(itemPropertyFilterCategoryModels: itemPropertyFilterModels));
    }
  }

  FutureOr<void> _onSelectCarpetArea(SelectCarpetAreaEvent event, Emitter<PropertyFilterState> emit) {
    if (event.selectedCarpetArea?.isSelected ?? false) {
      emit(state.copyWith(selectedCarpetAreaUnit: event.selectedCarpetArea));
    } else {
      emit(state.copyWith(updateCarpetAreaUnit: true));
    }
  }

  FutureOr<void> _onSelectPropertyArea(SelectPropertyAreaEvent event, Emitter<PropertyFilterState> emit) {
    if (event.selectedPropertyArea?.isSelected ?? false) {
      emit(state.copyWith(selectedPropertyAreaUnit: event.selectedPropertyArea));
    } else {
      emit(state.copyWith(updatePropertyAreaUnit: true));
    }
  }

  FutureOr<void> _onSelectBuiltUpArea(SelectBuiltUpAreaEvent event, Emitter<PropertyFilterState> emit) {
    if (event.selectedBuiltUpArea?.isSelected ?? false) {
      emit(state.copyWith(selectedBuiltUpAreaUnit: event.selectedBuiltUpArea));
    } else {
      emit(state.copyWith(updateBuiltUpAreaUnit: true));
    }
  }

  FutureOr<void> _onSelectSaleableArea(SelectSaleableAreaEvent event, Emitter<PropertyFilterState> emit) {
    if (event.selectedSaleableArea?.isSelected ?? false) {
      emit(state.copyWith(selectedSaleableAreaUnit: event.selectedSaleableArea));
    } else {
      emit(state.copyWith(updateSaleableAreaUnit: true));
    }
  }

  FutureOr<void> _onPropertySizeChange(OnPropertySizeChangeEvent event, Emitter<PropertyFilterState> emit) {
    emit(state.copyWith(propertySize: event.rangeInput));
  }

  FutureOr<void> _onCarpetAreaChange(OnCarpetAreaChangeEvent event, Emitter<PropertyFilterState> emit) {
    emit(state.copyWith(carpetArea: event.rangeInput));
  }

  FutureOr<void> _onBuiltUpAreaChange(OnBuiltUpAreaChangeEvent event, Emitter<PropertyFilterState> emit) {
    emit(state.copyWith(builtUpArea: event.rangeInput));
  }

  FutureOr<void> _onSaleableAreaChange(OnSaleableAreaChangeEvent event, Emitter<PropertyFilterState> emit) {
    emit(state.copyWith(saleableArea: event.rangeInput));
  }

  FutureOr<void> _onAreaValueChanged(AreaValueChanged event, Emitter<PropertyFilterState> emit) {
    if (event.input != null) {
      switch (event.areaType) {
        case AreaType.sealable:
          emit(state.copyWith(managePropertySalableArea: event.input));
          break;
        case AreaType.carpet:
          emit(state.copyWith(managePropertyCarpetArea: event.input));
          break;
        case AreaType.builtUp:
          emit(state.copyWith(managePropertyBuiltUpArea: event.input));
          break;
        case AreaType.propertyArea:
          emit(state.copyWith(managePropertyArea: event.input));
          break;
      }
    }
  }

  FutureOr<void> _onAreaUnitChanged(AreaUnitChanged event, Emitter<PropertyFilterState> emit) {
    if (event.selectedAreaUnit != null) {
      switch (event.areaType) {
        case AreaType.sealable:
          emit(state.copyWith(selectedSaleableAreaUnit: event.selectedAreaUnit));
          break;
        case AreaType.carpet:
          emit(state.copyWith(selectedCarpetAreaUnit: event.selectedAreaUnit));
          break;
        case AreaType.builtUp:
          emit(state.copyWith(selectedBuiltUpAreaUnit: event.selectedAreaUnit));
          break;
        case AreaType.propertyArea:
          emit(state.copyWith(selectedPropertyAreaUnit: event.selectedAreaUnit));
          break;
      }
    }
  }

  (AreaType?, double?, SelectableItem<MasterAreaUnitsModel>?) getAreaDetails(ItemPropertyFilterCategoryModel? selectedCategoryItem) {
    AreaType? areaType;
    double? area;
    SelectableItem<MasterAreaUnitsModel>? areaUnit;
    if (selectedCategoryItem != null) {
      if (selectedCategoryItem.propertyFilterKey == PropertyFilterKey.saleableArea) {
        areaType = AreaType.sealable;
        area = state.managePropertySalableArea;
        areaUnit = state.selectedSaleableAreaUnit;
      } else if (selectedCategoryItem.propertyFilterKey == PropertyFilterKey.carpetArea) {
        areaType = AreaType.carpet;
        area = state.managePropertyCarpetArea;
        areaUnit = state.selectedCarpetAreaUnit;
      } else if (selectedCategoryItem.propertyFilterKey == PropertyFilterKey.builtUpArea) {
        areaType = AreaType.builtUp;
        area = state.managePropertyBuiltUpArea;
        areaUnit = state.selectedBuiltUpAreaUnit;
      } else {
        areaType = AreaType.propertyArea;
        area = state.managePropertyArea;
        areaUnit = state.selectedPropertyAreaUnit;
      }
    }
    return (areaType, area, areaUnit);
  }

  Future<void> _onPropertiesChangeCurrencyEvent(PropertiesChangeCurrencyEvent event, Emitter<PropertyFilterState> emit) async {
    emit(state.copyWith(selectedCurrency: event.selectedCurrency));
  }

  FutureOr<void> _onOnMinBudgetChangeEvent(OnMinBudgetChangeEvent event, Emitter<PropertyFilterState> emit) {
    emit(state.copyWith(minBudget: event.rangeInput));
  }

  FutureOr<void> _onOnMaxBudgetChangeEvent(OnMaxBudgetChangeEvent event, Emitter<PropertyFilterState> emit) {
    emit(state.copyWith(maxBudget: event.rangeInput));
  }

  DateTime? utcToDateFormat(DateTime? date) {
    if (date == null) return null;
    DateTime utcDate = date.toUniversalTimeStartOfDay();
    return utcDate;
  }
}
