// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_details_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserDetailsModelAdapter extends TypeAdapter<UserDetailsModel> {
  @override
  final int typeId = 105;

  @override
  UserDetailsModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserDetailsModel(
      userId: fields[0] as String?,
      userName: fields[1] as String?,
      firstName: fields[2] as String?,
      lastName: fields[3] as String?,
      isActive: fields[4] as bool?,
      emailConfirmed: fields[5] as bool?,
      imageUrl: fields[6] as String?,
      altPhoneNumber: fields[7] as String?,
      altEmail: fields[8] as String?,
      address: fields[9] as String?,
      email: fields[10] as String?,
      bloodGroup: fields[11] as BloodGroupType?,
      gender: fields[12] as GenderEnum?,
      permanentAddress: fields[13] as String?,
      phoneNumber: fields[14] as String?,
      empNo: fields[15] as String?,
      officeName: fields[16] as String?,
      officeAddress: fields[17] as String?,
      reportsTo: fields[18] as BaseUserModel?,
      department: fields[19] as DTOWithNameModel?,
      designation: fields[20] as DTOWithNameModel?,
      description: fields[21] as String?,
      profileCompletion: fields[22] as double?,
      documents: (fields[23] as Map?)?.map((dynamic k, dynamic v) =>
          MapEntry(k as String, (v as List?)?.cast<UserDocumentModel>())),
      leadCount: fields[24] as int?,
      rolePermission: (fields[25] as List?)?.cast<RolePermissionModel?>(),
      timeZoneInfo: fields[36] as UserTimeZoneInfoModel?,
      shouldShowTimeZone: fields[37] as bool?,
      callThrough: fields[38] as CallThroughTypeEnum?,
      whatsappThrough: fields[39] as WhatsappThroughTypeEnum?,
      organizationName: fields[40] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, UserDetailsModel obj) {
    writer
      ..writeByte(31)
      ..writeByte(0)
      ..write(obj.userId)
      ..writeByte(1)
      ..write(obj.userName)
      ..writeByte(2)
      ..write(obj.firstName)
      ..writeByte(3)
      ..write(obj.lastName)
      ..writeByte(4)
      ..write(obj.isActive)
      ..writeByte(5)
      ..write(obj.emailConfirmed)
      ..writeByte(6)
      ..write(obj.imageUrl)
      ..writeByte(7)
      ..write(obj.altPhoneNumber)
      ..writeByte(8)
      ..write(obj.altEmail)
      ..writeByte(9)
      ..write(obj.address)
      ..writeByte(10)
      ..write(obj.email)
      ..writeByte(11)
      ..write(obj.bloodGroup)
      ..writeByte(12)
      ..write(obj.gender)
      ..writeByte(13)
      ..write(obj.permanentAddress)
      ..writeByte(14)
      ..write(obj.phoneNumber)
      ..writeByte(15)
      ..write(obj.empNo)
      ..writeByte(16)
      ..write(obj.officeName)
      ..writeByte(17)
      ..write(obj.officeAddress)
      ..writeByte(18)
      ..write(obj.reportsTo)
      ..writeByte(19)
      ..write(obj.department)
      ..writeByte(20)
      ..write(obj.designation)
      ..writeByte(21)
      ..write(obj.description)
      ..writeByte(22)
      ..write(obj.profileCompletion)
      ..writeByte(23)
      ..write(obj.documents)
      ..writeByte(24)
      ..write(obj.leadCount)
      ..writeByte(25)
      ..write(obj.rolePermission)
      ..writeByte(36)
      ..write(obj.timeZoneInfo)
      ..writeByte(37)
      ..write(obj.shouldShowTimeZone)
      ..writeByte(38)
      ..write(obj.callThrough)
      ..writeByte(39)
      ..write(obj.whatsappThrough)
      ..writeByte(40)
      ..write(obj.organizationName);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserDetailsModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserDetailsModel _$UserDetailsModelFromJson(Map<String, dynamic> json) =>
    UserDetailsModel(
      userId: json['userId'] as String?,
      userName: json['userName'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      isActive: json['isActive'] as bool?,
      emailConfirmed: json['emailConfirmed'] as bool?,
      imageUrl: json['imageUrl'] as String?,
      altPhoneNumber: json['altPhoneNumber'] as String?,
      altEmail: json['altEmail'] as String?,
      address: json['address'] as String?,
      email: json['email'] as String?,
      bloodGroup:
          $enumDecodeNullable(_$BloodGroupTypeEnumMap, json['bloodGroup']),
      gender: $enumDecodeNullable(_$GenderEnumEnumMap, json['gender']),
      permanentAddress: json['permanentAddress'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      empNo: json['empNo'] as String?,
      officeName: json['officeName'] as String?,
      officeAddress: json['officeAddress'] as String?,
      reportsTo: json['reportsTo'] == null
          ? null
          : BaseUserModel.fromJson(json['reportsTo'] as Map<String, dynamic>),
      department: json['department'] == null
          ? null
          : DTOWithNameModel.fromJson(
              json['department'] as Map<String, dynamic>),
      designation: json['designation'] == null
          ? null
          : DTOWithNameModel.fromJson(
              json['designation'] as Map<String, dynamic>),
      description: json['description'] as String?,
      profileCompletion: (json['profileCompletion'] as num?)?.toDouble(),
      documents: (json['documents'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(
            k,
            (e as List<dynamic>?)
                ?.map((e) =>
                    UserDocumentModel.fromJson(e as Map<String, dynamic>))
                .toList()),
      ),
      leadCount: (json['leadCount'] as num?)?.toInt(),
      rolePermission: (json['rolePermission'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : RolePermissionModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      timeZoneInfo: json['timeZoneInfo'] == null
          ? null
          : UserTimeZoneInfoModel.fromJson(
              json['timeZoneInfo'] as Map<String, dynamic>),
      shouldShowTimeZone: json['shouldShowTimeZone'] as bool?,
      callThrough: $enumDecodeNullable(
          _$CallThroughTypeEnumEnumMap, json['callThrough']),
      whatsappThrough: $enumDecodeNullable(
          _$WhatsappThroughTypeEnumEnumMap, json['whatsappThrough']),
      organizationName: json['organizationName'] as String?,
    );

Map<String, dynamic> _$UserDetailsModelToJson(UserDetailsModel instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'userName': instance.userName,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'isActive': instance.isActive,
      'emailConfirmed': instance.emailConfirmed,
      'imageUrl': instance.imageUrl,
      'altPhoneNumber': instance.altPhoneNumber,
      'altEmail': instance.altEmail,
      'address': instance.address,
      'email': instance.email,
      'bloodGroup': _$BloodGroupTypeEnumMap[instance.bloodGroup],
      'gender': _$GenderEnumEnumMap[instance.gender],
      'permanentAddress': instance.permanentAddress,
      'phoneNumber': instance.phoneNumber,
      'empNo': instance.empNo,
      'officeName': instance.officeName,
      'officeAddress': instance.officeAddress,
      'reportsTo': instance.reportsTo?.toJson(),
      'department': instance.department?.toJson(),
      'designation': instance.designation?.toJson(),
      'description': instance.description,
      'profileCompletion': instance.profileCompletion,
      'documents': instance.documents
          ?.map((k, e) => MapEntry(k, e?.map((e) => e.toJson()).toList())),
      'leadCount': instance.leadCount,
      'rolePermission':
          instance.rolePermission?.map((e) => e?.toJson()).toList(),
      'timeZoneInfo': instance.timeZoneInfo?.toJson(),
      'shouldShowTimeZone': instance.shouldShowTimeZone,
      'callThrough': _$CallThroughTypeEnumEnumMap[instance.callThrough],
      'whatsappThrough':
          _$WhatsappThroughTypeEnumEnumMap[instance.whatsappThrough],
      'organizationName': instance.organizationName,
    };

const _$BloodGroupTypeEnumMap = {
  BloodGroupType.none: 0,
  BloodGroupType.aPositive: 1,
  BloodGroupType.aNegative: 2,
  BloodGroupType.bPositive: 3,
  BloodGroupType.bNegative: 4,
  BloodGroupType.oPositive: 5,
  BloodGroupType.oNegative: 6,
  BloodGroupType.abPositive: 7,
  BloodGroupType.abNegative: 8,
};

const _$GenderEnumEnumMap = {
  GenderEnum.notMentioned: 0,
  GenderEnum.male: 1,
  GenderEnum.female: 2,
  GenderEnum.other: 3,
};

const _$CallThroughTypeEnumEnumMap = {
  CallThroughTypeEnum.askEveryTime: 0,
  CallThroughTypeEnum.ivrOnly: 1,
  CallThroughTypeEnum.dialerOnly: 2,
};

const _$WhatsappThroughTypeEnumEnumMap = {
  WhatsappThroughTypeEnum.askEveryTime: 0,
  WhatsappThroughTypeEnum.templateShare: 1,
  WhatsappThroughTypeEnum.openConversation: 2,
};
