import 'package:collection/collection.dart';
import 'package:leadrat/core_main/common/data/master_data/data_source/local/masterdata_local_data_source.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/project_enums/possession_type.dart';
import 'package:leadrat/core_main/enums/property_enums/facing.dart';
import 'package:leadrat/core_main/enums/property_enums/furnish_status.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:leadrat/core_main/extensions/double_extension.dart';
import 'package:leadrat/core_main/extensions/integer_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/features/data_management/domain/entities/prospect_entity.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';
import 'package:leadrat/features/projects/domain/entities/get_project_entity.dart';
import 'package:leadrat/features/properties/domain/entities/get_property_entity.dart';

class TemplateUtils {
  static final bool _isPropertyListingEnabled = getIt<LeadratHomeBloc>().isPropertyListingEnabled;

  static String getLeadTemplateMessage(GetLeadEntity model, String message, UserDetailsModel? userDetails, String? domain) {
    String possessionDate = (model.possesionType?.value == null || model.possesionType?.value == PossessionType.customDate.value ? model.enquiry?.possessionDate?.customDateFormatter('dd-MM-yyyy') ?? '' : model.possesionType?.description ?? '');

    final List<Map<String, String>> placeholders = [
      generateKeyVariations('User Name', userDetails?.fullName ?? ""),
      generateKeyVariations('User Mobile', userDetails?.phoneNumber ?? ""),
      generateKeyVariations('User Email', userDetails?.email ?? ""),
      generateKeyVariations('Tenant Name', domain ?? ""),
      generateKeyVariations('Lead Name', model.name ?? ""),
      generateKeyVariations('Agencies', model.agencies?.map((e) => e.name ?? '').join(',') ?? ""),
      generateKeyVariations('BHK Type', model.enquiry?.bHKTypes?.where((e) => e != BHKType.none).map((bhk) => bhk.description).join(',') ?? ""),
      generateKeyVariations('Carpet Area', model.enquiry?.carpetArea != null ? '${model.enquiry?.carpetArea.toString()} ${getAreaUnitByUnitId(model.enquiry?.carpetAreaUnitId)}' : ""),
      generateKeyVariations('Channel Partner Name', model.channelPartnerName ?? ""),
      generateKeyVariations('Channel Partner Contact No', model.channelPartnerContactNo ?? ""),
      generateKeyVariations('Channel Partner Executive Name', model.channelPartnerExecutiveName ?? ""),
      generateKeyVariations('Closing Manager', model.closingManagerUser?.fullName ?? ""),
      generateKeyVariations('Company Name', model.companyName ?? ""),
      generateKeyVariations('Currency', model.enquiry?.currency ?? ""),
      generateKeyVariations('Custom Lead Status', model.status?.status ?? ""),
      generateKeyVariations('Designation', model.designation ?? ""),
      generateKeyVariations('Beds', model.enquiry?.beds?.nonNulls.map((e) => e.description).join(' ,') ?? ''),
      generateKeyVariations(
          'Enquired Location',
          model.enquiry?.addresses
                  ?.map(
                    (address) => "${address.subLocality != null ? '${address.subLocality!} ,' : ''}${address.locality != null ? '${address.locality!} ,' : ''}${address.city != null ? '${address.city!} ,' : ''}${address.state != null ? '${address.state!} ,' : ''} ",
                  )
                  .join(';') ??
              ""),
      generateKeyVariations('Enquiry Type', model.enquiry?.enquiryTypes?.map((e) => e.description).join(',') ?? ""),
      generateKeyVariations('Lead Alternate Contact No', model.alternateContactNo ?? ""),
      generateKeyVariations('Lead Contact No', model.contactNo ?? ""),
      generateKeyVariations('Lead Email', model.email ?? ""),
      generateKeyVariations('Lead Source', model.enquiry?.leadSource?.description ?? ""),
      generateKeyVariations('Lower Budget', model.enquiry?.lowerBudget?.convertCurrencyFormat() ?? ""),
      generateKeyVariations('No Of BHK', model.enquiry?.bHKs != null && model.enquiry!.bHKs!.isNotEmpty ? '${model.enquiry?.bHKs?.map((e) => e.toString().replaceAll('.0', '')).join(',')} bhk' : ""),
      generateKeyVariations('BR', model.enquiry?.bHKs != null && model.enquiry!.bHKs!.isNotEmpty ? '${model.enquiry?.bHKs?.map((e) => e.toString().replaceAll('.0', '')).join(',')} br' : ""),
      generateKeyVariations('Possession Date', possessionDate),
      generateKeyVariations('Price Range', '${model.enquiry?.lowerBudget?.convertCurrencyFormat() ?? ""} - ${model.enquiry?.upperBudget?.convertCurrencyFormat() ?? ""}'),
      generateKeyVariations('Project Microsite URL', model.projects?.map((project) => project.serialNo?.appendWithSerialNumber(appModule: AppModule.project)).join('\n') ?? ""),
      generateKeyVariations('Project Name', model.projects?.map((project) => project.name.toString()).join(',') ?? ""),
      generateKeyVariations('Property Microsite URL', model.properties?.map((property) => property.serialNo?.appendWithSerialNumber(appModule: AppModule.property, isListingEnabled: _isPropertyListingEnabled)).join('\n') ?? ""),
      generateKeyVariations('Property Name', model.properties?.map((property) => property.title.toString()).join(',') ?? ""),
      generateKeyVariations('Property Type', model.enquiry?.propertyType?.displayName ?? ""),
      generateKeyVariations('Referral Contact No', model.referralContactNo ?? ""),
      generateKeyVariations('Referral Name', model.referralName ?? ""),
      generateKeyVariations('Referral Email', model.referralEmail ?? ""),
      generateKeyVariations('Schedule Date', model.scheduledDate?.customDateFormatter('dd-MM-yyyy') ?? ""),
      generateKeyVariations('Date', model.scheduledDate?.customDateFormatter('dd-MM-yyyy') ?? ""),
      generateKeyVariations('Schedule Time', model.scheduledDate?.toTimeToFormattedString() ?? ""),
      generateKeyVariations('Sourcing Manager', model.sourcingManager?.fullName ?? ""),
      generateKeyVariations('Sub Source', model.enquiry?.subSource ?? ""),
      generateKeyVariations('Upper Budget', model.enquiry?.upperBudget?.convertCurrencyFormat() ?? ""),
      generateKeyVariations('Assign To', model.assignedUser?.fullName ?? ""),
      generateKeyVariations('Primary Owner', model.assignedUser?.fullName ?? ""),
      generateKeyVariations('Secondary Owner', model.secondaryUser?.fullName ?? ""),
      generateKeyVariations('Sale Type', model.enquiry?.saleType?.description ?? ""),
      generateKeyVariations('Campaigns', model.campaigns?.map((campaign) => campaign.name).join(',') ?? ""),
      generateKeyVariations('Property Area', model.enquiry?.propertyArea != null ? '${model.enquiry?.propertyArea.toString()} ${getAreaUnitByUnitId(model.enquiry?.propertyAreaUnitId)}' : ""),
      generateKeyVariations('Net Area', model.enquiry?.netArea != null ? '${model.enquiry?.netArea.toString()} ${getAreaUnitByUnitId(model.enquiry?.netAreaUnitId)}' : ""),
      generateKeyVariations('BuiltUp Area', model.enquiry?.builtUpArea != null ? '${model.enquiry?.builtUpArea.toString()} ${getAreaUnitByUnitId(model.enquiry?.builtUpAreaUnitId)}' : ""),
      generateKeyVariations('Saleable Area', model.enquiry?.saleableArea != null ? '${model.enquiry?.saleableArea.toString()} ${getAreaUnitByUnitId(model.enquiry?.saleableAreaUnitId)}' : ""),
      generateKeyVariations('Unit Name', model.enquiry?.unitName != null ? '${model.enquiry?.unitName}' : ""),
      generateKeyVariations('Unit Number or Name', model.enquiry?.unitName != null ? '${model.enquiry?.unitName}' : ""),
      generateKeyVariations('Nationality', model.nationality != null ? '${model.nationality}' : ""),
      generateKeyVariations('Cluster Name', model.enquiry?.clusterName != null ? '${model.enquiry?.clusterName}' : ""),
      generateKeyVariations('Property Sub Type', model.enquiry?.propertyTypes != null ? '${model.enquiry?.propertyTypes?.map((i) => i.childType?.displayName).nonNulls.join(', ')}' : ""),
      generateKeyVariations('Purpose', model.enquiry?.purpose?.description ?? ""),
    ];

    for (var element in placeholders) {
      for (var key in element.keys) {
        message = message.replaceAll(key, element.values.first);
      }
    }

    return message;
  }

  static String getDataTemplateMessage(ProspectEntity model, String message, UserDetailsModel? userDetails, String? domain) {
    String possessionDate = (model.enquiry?.possesionType == null || model.enquiry?.possesionType?.value == PossessionType.customDate.value ? model.possesionDate?.customDateFormatter('dd-MM-yyyy') ?? '' : model.enquiry?.possesionType?.description ?? '');

    final List<Map<String, String>> placeholders = [
      generateKeyVariations('User Name', userDetails?.fullName ?? ""),
      generateKeyVariations('User Mobile', userDetails?.phoneNumber ?? ""),
      generateKeyVariations('User Email', userDetails?.email ?? ""),
      generateKeyVariations('Tenant Name', domain ?? ""),
      generateKeyVariations('Lead Name', model.name ?? ""),
      generateKeyVariations('Agencies', _getListOfNamesInOneString(model.agencies?.map((e) => e!.name!).toList() ?? []) ?? ""),
      generateKeyVariations('BHK Type', _getListOfNamesInOneString(model.enquiry?.bhkTypes?.where((e) => e != BHKType.none).map((bhk) => bhk.description).toList() ?? []) ?? ""),
      generateKeyVariations('Carpet Area', model.enquiry?.carpetArea != null ? '${model.enquiry?.carpetArea.toString()} ${getAreaUnitByUnitId(model.enquiry?.carpetAreaUnitId?.id)}' : ""),
      generateKeyVariations('Channel Partner Contact No', model.executiveContactNo ?? ""),
      generateKeyVariations('Channel Partner Executive Name', model.executiveName ?? ""),
      generateKeyVariations('Closing Manager', model.closingManager?.fullName ?? ""),
      generateKeyVariations('Company Name', model.companyName ?? ""),
      generateKeyVariations('Currency', model.enquiry?.currency ?? ""),
      generateKeyVariations('Custom Lead Status', model.status?.status ?? ""),
      generateKeyVariations('Designation', model.designation ?? ""),
      generateKeyVariations('Beds', model.enquiry?.beds?.nonNulls.map((e) => e.description).join(' ,') ?? ''),
      generateKeyVariations(
          'Enquired Location',
          model.enquiry?.addresses
                  ?.map(
                    (address) => "${address?.subLocality != null ? '${address?.subLocality!} ,' : ''}${address?.locality != null ? '${address?.locality!} ,' : ''}${address?.city != null ? '${address?.city!} ,' : ''}${address?.state != null ? '${address?.state!} ,' : ''} ",
                  )
                  .join(';') ??
              ""),
      generateKeyVariations('Enquiry Type', _getListOfNamesInOneString(model.enquiry?.enquiryTypes?.map((e) => e.description).toList() ?? []) ?? ""),
      generateKeyVariations('Lead Alternate Contact No', model.alternateContactNo ?? ""),
      generateKeyVariations('Lead Contact No', model.contactNo ?? ""),
      generateKeyVariations('Lead Email', model.email ?? ""),
      generateKeyVariations('Lead Source', model.enquiry?.prospectSource?.displayName ?? ""),
      generateKeyVariations('Lower Budget', model.enquiry?.lowerBudget?.convertCurrencyFormat() ?? ""),
      generateKeyVariations('No Of BHK', _getListOfNamesInOneString(model.enquiry?.bhKs?.map((e) => e.toString()).toList() ?? []) != null ? '${_getListOfNamesInOneString(model.enquiry?.bhKs?.map((e) => e.toString()).toList() ?? [])} BHK' : ""),
      generateKeyVariations('Possession Date', possessionDate),
      generateKeyVariations('Price Range', '${model.enquiry!.lowerBudget?.convertCurrencyFormat() ?? ""} - ${model.enquiry!.upperBudget?.convertCurrencyFormat() ?? ""}'),
      generateKeyVariations('Project Microsite URL', model.projects != null ? model.projects!.map((project) => project?.serialNo?.appendWithSerialNumber(appModule: AppModule.project)).join('\n') : ""),
      generateKeyVariations('Project Name', model.projects != null ? model.projects!.map((project) => project?.name.toString()).join(',') : ""),
      generateKeyVariations('Property Microsite URL', model.properties != null ? model.properties!.map((property) => property?.serialNo?.appendWithSerialNumber(appModule: AppModule.property, isListingEnabled: _isPropertyListingEnabled)).join('\n') : ""),
      generateKeyVariations('Property Name', model.properties != null ? model.properties!.map((property) => property?.title.toString()).join(',') : ""),
      generateKeyVariations('Property Type', model.enquiry?.propertyType?.displayName ?? ""),
      generateKeyVariations('Referral Contact No', ""),
      generateKeyVariations('Referral Name', ""),
      generateKeyVariations('Referral Email', ""),
      generateKeyVariations('Schedule Date', model.scheduleDate != null ? model.scheduleDate!.customDateFormatter('dd-MM-yyyy') : ""),
      generateKeyVariations('Schedule Time', model.scheduleDate != null ? model.scheduleDate!.toTimeToFormattedString() : ""),
      generateKeyVariations('Sourcing Manager', model.sourcingManager?.fullName ?? ""),
      generateKeyVariations('Sub Source', model.enquiry?.subSource ?? ""),
      generateKeyVariations('Upper Budget', model.enquiry?.upperBudget?.convertCurrencyFormat() ?? ""),
      generateKeyVariations('Assign To', model.assignedUser?.fullName ?? ""),
      generateKeyVariations('Primary Owner', model.assignedUser?.fullName ?? ""),
      generateKeyVariations('Secondary Owner', ""),
      generateKeyVariations('Campaigns', model.campaigns?.map((campaign) => campaign.name).join(',') ?? ""),
      generateKeyVariations('Beds', model.enquiry?.beds?.map((beds) => beds.name).join(',') ?? ""),
      generateKeyVariations('Baths', model.enquiry?.baths?.map((baths) => baths).join(',') ?? ""),
      generateKeyVariations('BR', _getListOfNamesInOneString(model.enquiry?.bhKs?.map((e) => e.toString()).toList() ?? []) != null ? '${_getListOfNamesInOneString(model.enquiry?.bhKs?.map((e) => e.toString()).toList() ?? [])} BR' : ""),
      generateKeyVariations('Preferred Floor', model.enquiry?.floors?.map((floor) => floor).join(',') ?? ""),
      generateKeyVariations('Furnish Status', '${model.enquiry?.furnished ?? ''}'),
      generateKeyVariations('Offering Type', '${model.enquiry?.offerType ?? ''}'),
      generateKeyVariations('Channel Partner Contact No', model.executiveContactNo ?? ''),
      generateKeyVariations('Property Area', model.enquiry?.propertyArea != null ? '${model.enquiry?.propertyArea.toString()} ${getAreaUnitByUnitId(model.enquiry?.propertyAreaUnitId?.id)}' : ""),
      generateKeyVariations('Net Area', model.enquiry?.netArea != null ? '${model.enquiry?.netArea.toString()} ${getAreaUnitByUnitId(model.enquiry?.netAreaUnitId?.id)}' : ""),
      generateKeyVariations('BuiltUp Area', model.enquiry?.builtUpArea != null ? '${model.enquiry?.builtUpArea.toString()} ${getAreaUnitByUnitId(model.enquiry?.builtUpAreaUnitId?.id)}' : ""),
      generateKeyVariations('Saleable Area', model.enquiry?.saleableArea != null ? '${model.enquiry?.saleableArea.toString()} ${getAreaUnitByUnitId(model.enquiry?.saleableAreaUnitId?.id)}' : ""),
      generateKeyVariations('Unit Name', model.enquiry?.unitName != null ? '${model.enquiry?.unitName}' : ""),
      generateKeyVariations('Unit Number or Name', model.enquiry?.unitName != null ? '${model.enquiry?.unitName}' : ""),
      generateKeyVariations('Nationality', model.nationality != null ? '${model.nationality}' : ""),
      generateKeyVariations('Cluster Name', model.enquiry?.clusterName != null ? '${model.enquiry?.clusterName}' : ""),
      generateKeyVariations('Property Sub Type', model.enquiry?.propertyTypes != null ? '${model.enquiry?.propertyTypes?.map((i) => i.childType?.displayName).nonNulls.join(', ')}' : ""),
      generateKeyVariations('Purpose', model.enquiry?.purpose?.description ?? ""),
    ];

    for (var element in placeholders) {
      for (var key in element.keys) {
        message = message.replaceAll(key, element.values.first);
      }
    }

    return message;
  }

  static String getProjectsMessage(GetProjectEntity? model, String message, UserDetailsModel? userDetails, String? domain, String? leadName) {
    var masterAreaUnits = getIt<MasterDataLocalDataSource>().getAreaUnits();
    var areaUnit = masterAreaUnits?.firstWhereOrNull((element) => element.id == model?.areaUnitId)?.unit;
    final List<Map<String, String>> placeholders = [
      generateKeyVariations('User Name', userDetails?.fullName ?? ""),
      generateKeyVariations('User Mobile', userDetails?.phoneNumber ?? ""),
      generateKeyVariations('User Email', userDetails?.email ?? ""),
      generateKeyVariations('Tenant Name', domain ?? ""),
      generateKeyVariations('Builder Name', model?.builderDetail?.name ?? ""),
      generateKeyVariations('Builder Phone', model?.builderDetail?.contactNo ?? ""),
      generateKeyVariations('Builder Point of Contact', model?.builderDetail?.pointOfContact ?? ""),
      generateKeyVariations('Land Area', model?.area != null ? '${model?.area?.toString() ?? ''} ${getAreaUnitByUnitId(model?.areaUnitId) ?? ''}' : ''),
      generateKeyVariations('Address', model != null && model.address != null ? "${model.address!.subLocality != null ? '${model.address!.subLocality!} ,' : ''}${model.address!.locality != null ? '${model.address!.locality!} ,' : ''}${model.address!.city != null ? '${model.address!.city!} ,' : ''}${model.address!.state != null ? '${model.address!.state!} ,' : ''} " : ""),
      generateKeyVariations('Brokerage', model?.monetaryInfo?.brokerage?.formatBrokerage(currency: model.monetaryInfo?.brokerageCurrency) ?? ""),
      generateKeyVariations('Dimension', '${model?.area?.doubleToWord() ?? ""} ${areaUnit ?? ""}'),
      generateKeyVariations('Facing', model?.facings?.where((facing) => facing != Facing.unknown).map((facing) => facing.description).join(',') ?? ''),
      generateKeyVariations('Possession Date', model != null && model.possesionType != null && model.possesionType != PossessionType.none ? model.possesionType!.description : ""),
      generateKeyVariations('Project Sub-Type', model?.projectType?.childType?.displayName ?? ""),
      generateKeyVariations('Project Type', model?.projectType?.displayName ?? ""),
      generateKeyVariations('Project URL', model?.serialNo?.appendWithSerialNumber(appModule: AppModule.project) ?? ""),
      generateKeyVariations('Microsite Url', model?.serialNo?.appendWithSerialNumber(appModule: AppModule.project) ?? ""),
      generateKeyVariations('Microsite URL', model?.serialNo?.appendWithSerialNumber(appModule: AppModule.project) ?? ""),
      generateKeyVariations('Project Name', model?.name ?? ""),
      generateKeyVariations('Name', model?.name ?? ""),
      generateKeyVariations('Total Floors', model?.totalFloor?.doubleToWord() ?? ""),
      generateKeyVariations('Total Price', model?.maximumPrice?.toInt().convertCurrencyFormat() ?? ""),
      generateKeyVariations('Lead Name', leadName ?? ''),
      generateKeyVariations('Notes', model?.notes ?? ""),
    ];
    for (var element in placeholders) {
      for (var key in element.keys) {
        message = message.replaceAll(key, element.values.first);
      }
    }

    return message;
  }

  static String getPropertyMessage(GetPropertyEntity? model, String message, UserDetailsModel? userDetails, String? domain, String? leadName) {
    String possessionDate = (model?.possessionDate != null && model!.possessionDate!.isBefore(DateTime.now())) ? 'Ready to move' : (model?.possesionType?.value == PossessionType.customDate.value ? model?.possessionDate?.customDateFormatter('dd-MM-yyyy') ?? '' : model?.possesionType?.description ?? '');

    final List<Map<String, String>> placeholders = [
      generateKeyVariations('User Name', userDetails?.fullName ?? ""),
      generateKeyVariations('User Mobile', userDetails?.phoneNumber ?? ""),
      generateKeyVariations('User Email', userDetails?.email ?? ""),
      generateKeyVariations('Tenant Name', domain ?? ''),
      generateKeyVariations('About Property', model?.aboutProperty ?? ""),
      generateKeyVariations('Address', model != null && model.address != null ? "${model.address!.subLocality != null ? '${model.address!.subLocality!} ,' : ''}${model.address!.locality != null ? '${model.address!.locality!} ,' : ''}${model.address!.city != null ? '${model.address!.city!} ,' : ''}${model.address!.state != null ? '${model.address!.state!} ,' : ''} " : ""),
      generateKeyVariations('BHK Type', model?.bhkType != null && model?.bhkType != BHKType.none ? model?.bhkType?.description ?? "" : ''),
      generateKeyVariations('BR Type', model?.bhkType != null && model?.bhkType != BHKType.none ? model?.bhkType?.description ?? "" : ''),
      generateKeyVariations('Brokerage', model?.monetaryInfo?.brokerage?.formatBrokerage(currency: model.monetaryInfo?.brokerageCurrency) ?? ""),
      generateKeyVariations('Built-up Area', model?.dimension?.buildUpArea != null ? '${model?.dimension?.buildUpArea?.toString() ?? ''} ${getAreaUnitByUnitId(model?.dimension?.buildUpAreaId) ?? ''}' : ''),
      generateKeyVariations('Carpet Area', model?.dimension?.carpetArea != null ? '${model?.dimension?.carpetArea?.toString() ?? ''} ${getAreaUnitByUnitId(model?.dimension?.carpetAreaId) ?? ''}' : ''),
      generateKeyVariations('Dimension', model?.dimension?.area != null ? '${model?.dimension?.area?.toString() ?? ''} ${getAreaUnitByUnitId(model?.dimension?.areaUnitId) ?? ''}' : ''),
      generateKeyVariations('Enquired For', model?.enquiredFor?.description ?? ''),
      generateKeyVariations('Facing', model?.facing != null && model?.facing != Facing.unknown ? model?.facing!.description ?? "" : ''),
      generateKeyVariations('Floor Number', model?.attributes?.firstWhereOrNull((attribute) => attribute?.attributeName == 'floorNumber')?.defaultValue ?? ''),
      generateKeyVariations('Furnish Status', model?.furnishStatus != null && model?.furnishStatus != FurnishStatus.none ? model?.furnishStatus!.description ?? "" : ''),
      generateKeyVariations('Microsite Url', model?.serialNo?.appendWithSerialNumber(appModule: AppModule.property, isListingEnabled: _isPropertyListingEnabled) ?? ""),
      generateKeyVariations('Microsite URL', model?.serialNo?.appendWithSerialNumber(appModule: AppModule.property, isListingEnabled: _isPropertyListingEnabled) ?? ""),
      generateKeyVariations('No Of BHK', "${model?.noOfBHK?.toString().replaceAll('.0', '') ?? ''} ${(model?.noOfBHK?.toString().isNotNullOrEmpty() ?? false) ? "BHK" : ''}"),
      generateKeyVariations('No Of BR', "${model?.noOfBHK?.toString().replaceAll('.0', '') ?? ''} ${(model?.noOfBHK?.toString().isNotNullOrEmpty() ?? false) ? "BR" : ''}"),
      generateKeyVariations('No Of Balconies', model?.attributes?.firstWhereOrNull((attribute) => attribute?.attributeName == 'numberOfBalconies')?.defaultValue ?? ''),
      generateKeyVariations('No Of Bath Rooms', model?.attributes?.firstWhereOrNull((attribute) => attribute?.attributeName == 'numberOfBathrooms')?.defaultValue ?? ''),
      generateKeyVariations('No Of Bed Rooms', model?.attributes?.firstWhereOrNull((attribute) => attribute?.attributeName == 'numberOfBedrooms')?.defaultValue ?? ''),
      generateKeyVariations('No Of Kitchens', model?.attributes?.firstWhereOrNull((attribute) => attribute?.attributeName == 'numberOfKitchens')?.defaultValue ?? ''),
      generateKeyVariations('No of Living Rooms', model?.attributes?.firstWhereOrNull((attribute) => attribute?.attributeName == 'numberOfDrawingOrLivingRooms')?.defaultValue ?? ''),
      generateKeyVariations('No Of Utilities', model?.attributes?.firstWhereOrNull((attribute) => attribute?.attributeName == 'numberOfUtilities')?.defaultValue ?? ''),
      generateKeyVariations('Notes', model?.notes ?? ''),
      generateKeyVariations('Owner Email', model?.ownerDetails?.email ?? ''),
      generateKeyVariations('Owner Name', model?.ownerDetails?.name ?? ''),
      generateKeyVariations('Owner Phone', model?.ownerDetails?.phone ?? ''),
      generateKeyVariations('Possession Date', possessionDate),
      generateKeyVariations('Property Area', model?.dimension?.area != null ? '${model?.dimension?.area?.toString() ?? ''} ${getAreaUnitByUnitId(model?.dimension?.areaUnitId) ?? ''}' : ''),
      generateKeyVariations('Property Size', model?.dimension?.area != null ? '${model?.dimension?.area?.toString() ?? ''} ${getAreaUnitByUnitId(model?.dimension?.areaUnitId) ?? ''}' : ''),
      generateKeyVariations('BuildUp Area', model?.dimension?.area != null ? '${model?.dimension?.buildUpArea?.toString() ?? ''} ${getAreaUnitByUnitId(model?.dimension?.areaUnitId) ?? ''}' : ''),
      generateKeyVariations('Property Status', model?.status?.description ?? ''),
      generateKeyVariations('Property Sub-Type', model?.propertyType?.childType?.displayName ?? ''),
      generateKeyVariations('Property Type', model?.propertyType?.displayName ?? ''),
      generateKeyVariations('Property URL', model?.serialNo?.appendWithSerialNumber(appModule: AppModule.property, isListingEnabled: _isPropertyListingEnabled) ?? ""),
      generateKeyVariations('Sale Type', model?.saleType?.description ?? ''),
      generateKeyVariations('Saleable Area', model?.dimension?.saleableArea != null ? '${model?.dimension?.saleableArea?.toString() ?? ''} ${getAreaUnitByUnitId(model?.dimension?.saleableAreaId) ?? ''}' : ''),
      generateKeyVariations('BuiltUp Area', model?.dimension?.buildUpArea != null ? '${model?.dimension?.buildUpArea?.toString() ?? ''} ${getAreaUnitByUnitId(model?.dimension?.buildUpAreaId) ?? ''}' : ''),
      generateKeyVariations('Carpet Area', model?.dimension?.carpetArea != null ? '${model?.dimension?.carpetArea?.toString() ?? ''} ${getAreaUnitByUnitId(model?.dimension?.carpetAreaId) ?? ''}' : ''),
      generateKeyVariations('Title', model?.title ?? ''),
      generateKeyVariations('Total Floors', model?.attributes?.firstWhereOrNull((attribute) => attribute?.attributeName == 'numberOfFloors')?.defaultValue ?? ''),
      generateKeyVariations('Total Price', model?.monetaryInfo?.expectedPrice?.convertCurrencyFormat() ?? ''),
      generateKeyVariations('No of Parking', model?.attributes?.firstWhereOrNull((attribute) => attribute?.attributeName == 'numberOfParking')?.defaultValue ?? ''),
      generateKeyVariations('Net Area', model?.dimension?.netArea != null ? '${model?.dimension?.netArea.toString()} ${getAreaUnitByUnitId(model?.dimension?.netAreaUnitId)}' : ""),
      generateKeyVariations('Lead Name', leadName ?? ''),
      generateKeyVariations('Notes', model?.notes ?? ""),
    ];

    for (var element in placeholders) {
      for (var key in element.keys) {
        message = message.replaceAll(key, element.values.first);
      }
    }

    return message;
  }

  static String? _getListOfNamesInOneString(List<String?>? list) {
    if (list == null || list.isEmpty) return null;
    return list.join(',');
  }

  static String? getAreaUnitByUnitId(String? areaUnitId) {
    if (areaUnitId == null) return null;
    return getIt<MasterDataLocalDataSource>().getAreaUnits()?.firstWhereOrNull((element) => element.id == areaUnitId)?.unit;
  }

  static Map<String, String> generateKeyVariations(String key, String value) {
    Map<String, String> variations = {};
    variations['#$key#'] = value;
    String noSpacesKey = key.replaceAll(' ', '');
    variations['#$noSpacesKey#'] = value;
    String lowercaseKey = key.toLowerCase();
    String uppercaseKey = key.toUpperCase();
    String titleCaseKey = key.split(' ').map((word) => word.isNotEmpty ? word[0].toUpperCase() + word.substring(1).toLowerCase() : word).join(' ');
    String camelCaseKey = key.replaceAll(' ', '').replaceRange(0, 1, key[0].toLowerCase());

    variations['#$lowercaseKey#'] = value;
    variations['#$uppercaseKey#'] = value;
    variations['#$titleCaseKey#'] = value;
    variations['#$camelCaseKey#'] = value;

    List<String> words = key.split(' ');
    int totalCombinations = 1 << words.length; // 2^n combinations
    for (int i = 0; i < totalCombinations; i++) {
      List<String> combination = [];
      for (int j = 0; j < words.length; j++) {
        if ((i & (1 << j)) != 0) {
          combination.add(words[j].toUpperCase());
        } else {
          combination.add(words[j].toLowerCase());
        }
      }
      String mixedCaseKey = combination.join(' ');
      variations['#$mixedCaseKey#'] = value;
    }
    return variations;
  }

/*
   static String? _getValues(Map<int, String>? values) {
    try {
      if (values?.isEmpty ?? false) {
        return "default";
      }

      var repString = "";
      values?.values.toList().forEach(
        (element) {
          repString += element;
        },
      );
      return repString.trimLeft();
    } catch (ex) {
      return "default";
    }
  }

  String? _getDateFormat(DateTime? scheduledDate) {
    if (scheduledDate == null) return null;
    return DateFormat("d MMM, yyyy").format(scheduledDate.toUserTimeZone()!);
  }

  String? _getLocation(AddressEntity? address) {
    if (address == null) return null;
    return "${address.subLocality ?? ''}, ${address.locality ?? ''}, ${address.city ?? ''}, ${address.state ?? ''}, ${address.country ?? ''}";
  }*/
}

class TemplateProcessor {
  static String processTemplate(String template, Map<String, String> replacements) {
    final placeholderRegex = RegExp(r'#([^#]+)#');

    return template.replaceAllMapped(placeholderRegex, (match) {
      String placeholder = match.group(1)!.trim();
      String? bestMatch = _findBestMatch(placeholder, replacements);
      return bestMatch != null ? '${replacements[bestMatch]}' : placeholder;
    });
  }

  static String? _findBestMatch(String placeholder, Map<String, String> replacements) {
    String normalizedPlaceholder = _normalizePlaceholder(placeholder);
    for (var key in replacements.keys) {
      if (_normalizePlaceholder(key) == normalizedPlaceholder) {
        return key;
      }
    }

    for (var key in replacements.keys) {
      String normalizedKey = _normalizePlaceholder(key);
      if (normalizedPlaceholder.contains(normalizedKey) || normalizedKey.contains(normalizedPlaceholder)) {
        return key;
      }
    }

    for (var key in replacements.keys) {
      String normalizedKey = _normalizePlaceholder(key);

      if (_calculateSimilarity(normalizedPlaceholder, normalizedKey) > 0.7) {
        return key;
      }
    }

    return null;
  }

  static String _normalizePlaceholder(String input) {
    return input.toLowerCase().replaceAll(RegExp(r'[^a-z0-9]'), '');
  }

  static double _calculateSimilarity(String s1, String s2) {
    int m = s1.length;
    int n = s2.length;

    List<List<int>> dp = List.generate(m + 1, (_) => List.filled(n + 1, 0));

    for (int i = 1; i <= m; i++) {
      for (int j = 1; j <= n; j++) {
        if (s1[i - 1] == s2[j - 1]) {
          dp[i][j] = dp[i - 1][j - 1] + 1;
        } else {
          dp[i][j] = max(dp[i - 1][j], dp[i][j - 1]);
        }
      }
    }

    int lcsLength = dp[m][n];
    return (2.0 * lcsLength) / (m + n);
  }

  static int max(int a, int b) => a > b ? a : b;
}
