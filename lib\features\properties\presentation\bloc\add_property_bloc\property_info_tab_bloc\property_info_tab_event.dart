part of 'property_info_tab_bloc.dart';

@immutable
sealed class PropertyInfoTabEvent {}

final class InitPropertyInfoTabEvent extends PropertyInfoTabEvent {
  final AddPropertyModel addPropertyModel;

  InitPropertyInfoTabEvent(this.addPropertyModel);
}

final class SelectPossessionDateEvent extends PropertyInfoTabEvent {
  final DateTime selectedDate;

  SelectPossessionDateEvent(this.selectedDate);
}

final class UpdateOfficeAndCoWorkingSpaceSelected extends PropertyInfoTabEvent {}

final class SelectCurrencyEvent extends PropertyInfoTabEvent {
  final SelectableItem<String?>? currency;

  SelectCurrencyEvent(this.currency);
}

final class SelectCommonAreaCurrencyEvent extends PropertyInfoTabEvent {
  final SelectableItem<String?>? currency;

  SelectCommonAreaCurrencyEvent({this.currency});
}

final class ToggleIsNegotiableEvent extends PropertyInfoTabEvent {
  final bool? isNegotiable;

  ToggleIsNegotiableEvent(this.isNegotiable);
}

final class SelectProjectEvent extends PropertyInfoTabEvent {
  final SelectableItem<String>? project;

  SelectProjectEvent(this.project);
}

final class SelectBrokerageUnit extends PropertyInfoTabEvent {
  final SelectableItem<String>? brokerageUnit;

  SelectBrokerageUnit(this.brokerageUnit);
}

final class GetProjectsEvent extends PropertyInfoTabEvent {}

final class SelectLocationEvent extends PropertyInfoTabEvent {
  final AddressModel? location;

  SelectLocationEvent(this.location);
}

final class SelectSecurityDepositEvent extends PropertyInfoTabEvent {
  final SelectableItem<SecurityDepositType> selectableItem;

  SelectSecurityDepositEvent(this.selectableItem);
}

final class SelectNoticePeriodEvent extends PropertyInfoTabEvent {
  final SelectableItem<NoticePeriodType> selectableItem;

  SelectNoticePeriodEvent(this.selectableItem);
}

final class SelectLockInPeriodEvent extends PropertyInfoTabEvent {
  final SelectableItem<LockInPeriodType> selectableItem;

  SelectLockInPeriodEvent(this.selectableItem);
}

final class SelectCommonAreaUnitsEvent extends PropertyInfoTabEvent {
  final SelectableItem<MasterAreaUnitsModel> selectableItem;

  SelectCommonAreaUnitsEvent({required this.selectableItem});
}

final class NavigateBackToBasicInfoTabEvent extends PropertyInfoTabEvent {}

final class NavigateToAttributeTabEvent extends PropertyInfoTabEvent {}

final class ClearPropertyInfoTabStateEvent extends PropertyInfoTabEvent {}

final class ClearProjectEvent extends PropertyInfoTabEvent {
  final ClearOption? clearOption;

  ClearProjectEvent({
    this.clearOption,
  });
}

final class InitListingSourceAndAddresses extends PropertyInfoTabEvent {}

final class SelectListingSourceEvent extends PropertyInfoTabEvent {
  final SelectableItem<ViewAddressesModel> selectedListingSource;
  final int index;
  final bool updateListingLocations;

  SelectListingSourceEvent(this.selectedListingSource, {this.index = 0, this.updateListingLocations = true});
}

final class SelectListingSiteAddress extends PropertyInfoTabEvent {
  final SelectableItem<ViewListingSourceAddressModel> selectedListingSiteAddress;
  final int index;

  SelectListingSiteAddress(this.selectedListingSiteAddress, {this.index = 0});
}

final class ChangePermitValueEvent extends PropertyInfoTabEvent {
  final String selectedPermitValue;

  ChangePermitValueEvent(this.selectedPermitValue);
}

final class SelectPaymentFrequency extends PropertyInfoTabEvent {
  final SelectableItem<PaymentFrequency> selectPaymentFrequency;

  SelectPaymentFrequency(this.selectPaymentFrequency);
}

final class SelectNoOfChequeEvent extends PropertyInfoTabEvent {
  final SelectableItem<int> selectedNoOfCheque;

  SelectNoOfChequeEvent(this.selectedNoOfCheque);
}

final class AddNewListingSourceAndAddressEvent extends PropertyInfoTabEvent {}

final class RemoveNewListingSourceAndAddressEvent extends PropertyInfoTabEvent {
  final int index;

  RemoveNewListingSourceAndAddressEvent(this.index);
}

final class ChangeTaxationModeEvent extends PropertyInfoTabEvent {
  final TaxationMode taxationMode;

  ChangeTaxationModeEvent(this.taxationMode);
}

final class AddContactEvent extends PropertyInfoTabEvent {
  AddContactEvent();
}

final class RemoveContactEvent extends PropertyInfoTabEvent {
  final String uniqueId;

  RemoveContactEvent({required this.uniqueId});
}

final class OwnerContactChangedEvent extends PropertyInfoTabEvent {
  final String countryCode;
  final bool isAlternateContactNumberChanged;
  final String uniqueId;

  OwnerContactChangedEvent({required this.countryCode, this.isAlternateContactNumberChanged = false, required this.uniqueId});
}

final class CollapseOwnerContactEvent extends PropertyInfoTabEvent {
  final String uniqueId;

  CollapseOwnerContactEvent({required this.uniqueId});
}

class OnBuilderContactChangedEvent extends PropertyInfoTabEvent {
  final String countryCode;
  final String contactNumber;

  OnBuilderContactChangedEvent(this.countryCode, this.contactNumber);
}

final class SelectPossessionType extends PropertyInfoTabEvent {
  final SelectableItem<PossessionType?>? selectPossessionType;

  SelectPossessionType(this.selectPossessionType);
}

final class ToggleSecurityAmount extends PropertyInfoTabEvent {
  final bool? isSecurityAmountPercentageSelected;

  ToggleSecurityAmount(this.isSecurityAmountPercentageSelected);
}

final class InitAttributesEvent extends PropertyInfoTabEvent {}

final class ToggleFurnishStatusEvent extends PropertyInfoTabEvent {
  final SelectableItem<String> selectedFurnishStatus;

  ToggleFurnishStatusEvent(this.selectedFurnishStatus);
}

final class ToggleFacingEvent extends PropertyInfoTabEvent {
  final SelectableItem<String> selectedFacing;

  ToggleFacingEvent(this.selectedFacing);
}

final class AttachOrDeAttachCustomAttributeEvent extends PropertyInfoTabEvent {
  final CustomAttributesModel? attribute;
  final bool isAttach;

  AttachOrDeAttachCustomAttributeEvent({this.attribute, this.isAttach = false});
}

final class ToggleRatingsEvent extends PropertyInfoTabEvent {
  final int? rating;

  ToggleRatingsEvent(this.rating);
}

final class SelectNumberOfFloorsOccupiedEvent extends PropertyInfoTabEvent {
  final List<SelectableItem<int>> item;

  SelectNumberOfFloorsOccupiedEvent({
    required this.item,
  });
}

final class SelectDepositCurrencyEvent extends PropertyInfoTabEvent {
  final SelectableItem<String?>? currency;

  SelectDepositCurrencyEvent(this.currency);
}
