part of 'property_info_tab_bloc.dart';

@immutable
class PropertyInfoTabState {
  final PageState pageState;
  final String? errorMessage;
  final DateTime? possessionAvailablity;
  final int? totalPrice;
  final List<SelectableItem<String>>? currencies;
  final String? selectedPropertySubType;
  final EnquiryType? selectedEnquiredType;
  final String? currency;
  final String? depositCurrency;
  final bool? isNegotiable;
  final int? maintenanceCost;
  final List<SelectableItem<String>>? projects;
  final SelectableItem<String>? selectedProject;
  final double? brokerageAmount;
  final List<SelectableItem<String>>? brokerageUnits;
  final SelectableItem<String>? brokerageUnit;
  final String? notes;
  final AddressModel? location;
  final String? ownerName;
  final String? ownerNumber;
  final String? ownerEmail;
  final GlobalSettingModel? globalSettings;
  final AddPropertyModel? addPropertyModel;
  final List<SelectableItem<SecurityDepositType>>? selectableSecurityDeposit;
  final SelectableItem<SecurityDepositType>? selectedSecurityDeposit;
  final List<SelectableItem<NoticePeriodType>>? selectableNoticePeriodType;
  final SelectableItem<NoticePeriodType>? selectedNoticePeriodType;
  final List<SelectableItem<LockInPeriodType>>? selectableLockInPeriodType;
  final SelectableItem<LockInPeriodType>? selectedLockInPeriodType;
  final List<SelectableItem<MasterAreaUnitsModel>>? selectableCommonAreaUnits;
  final SelectableItem<MasterAreaUnitsModel>? selectedCommonAreaUnits;
  final String selectedPermitValue;
  final List<SelectableItem<PaymentFrequency>> selectablePaymentFrequency;
  final SelectableItem<PaymentFrequency>? selectedPaymentFrequency;
  final List<ItemListingSourceAddressModel> listingSiteAndLocations;
  final String? defaultCountryCode;
  final List<SelectableItem<int>>? numberOfCheques;
  final SelectableItem<int>? selectedNumberOfCheque;
  final int? totalListingSiteAndAddress;
  final TaxationMode taxationMode;
  final String? builderContactNumber;
  final List<SelectableItem<PossessionType?>>? possessionTypeSelectableItems;
  final SelectableItem<PossessionType?>? possessionTypeSelectedItem;
  final bool isPossessionDateCustomSelected;
  final bool isSecurityAmountPercentageSelected;
  final List<SelectableItem<CustomAttributesModel>> customBasicAttributes;
  final int? ratings;
  final List<SelectableItem<String>>? furnishStatuses;
  final SelectableItem<String>? selectedFurnishStatus;
  final List<SelectableItem<String>>? facings;
  final SelectableItem<String>? selectedFacing;
  final List<OwnerDetailsStorage> ownerDetailsStorages;
  final List<SelectableItem<int>>? selectableNoOfFloorsOccupied;
  final List<SelectableItem<int>>? selectedNoOfFloorsOccupied;
  final bool? isOfficeSpaceSelected;
  final bool? isCoWorkingOfficeSpaceSelected;

  const PropertyInfoTabState({
    this.pageState = PageState.initial,
    this.errorMessage,
    this.possessionAvailablity,
    this.totalPrice,
    this.currencies,
    this.selectedPropertySubType,
    this.selectedEnquiredType,
    this.currency,
    this.isNegotiable,
    this.maintenanceCost,
    this.projects,
    this.selectedProject,
    this.brokerageAmount,
    this.brokerageUnits,
    this.brokerageUnit,
    this.notes,
    this.location,
    this.ownerName,
    this.ownerNumber,
    this.ownerEmail,
    this.globalSettings,
    this.addPropertyModel,
    this.selectableSecurityDeposit,
    this.selectedSecurityDeposit,
    this.selectableNoticePeriodType,
    this.selectedNoticePeriodType,
    this.selectableLockInPeriodType,
    this.selectedLockInPeriodType,
    this.selectableCommonAreaUnits,
    this.selectedCommonAreaUnits,
    this.selectedPermitValue = "DLD",
    this.selectablePaymentFrequency = const [],
    this.selectedPaymentFrequency,
    this.listingSiteAndLocations = const [],
    this.defaultCountryCode,
    this.numberOfCheques,
    this.selectedNumberOfCheque,
    this.totalListingSiteAndAddress,
    this.builderContactNumber,
    this.taxationMode = TaxationMode.gstInclusive,
    this.possessionTypeSelectableItems,
    this.possessionTypeSelectedItem,
    this.isPossessionDateCustomSelected = false,
    this.isSecurityAmountPercentageSelected = false,
    this.depositCurrency,
    this.customBasicAttributes = const [],
    this.ratings,
    this.furnishStatuses,
    this.selectedFurnishStatus,
    this.facings,
    this.selectedFacing,
    this.ownerDetailsStorages = const [],
    this.selectableNoOfFloorsOccupied = const [],
    this.selectedNoOfFloorsOccupied = const [],
    this.isCoWorkingOfficeSpaceSelected,
    this.isOfficeSpaceSelected,
  });

  PropertyInfoTabState copyWith({
    PageState? pageState,
    String? errorMessage,
    DateTime? possessionAvailablity,
    int? totalPrice,
    List<SelectableItem<String>>? currencies,
    String? selectedPropertySubType,
    EnquiryType? selectedEnquiredType,
    String? currency,
    bool? isNegotiable,
    int? maintenanceCost,
    List<SelectableItem<String>>? projects,
    SelectableItem<String>? selectedProject,
    double? brokerageAmount,
    List<SelectableItem<String>>? brokerageUnits,
    SelectableItem<String>? brokerageUnit,
    String? notes,
    AddressModel? location,
    String? ownerName,
    String? ownerNumber,
    String? ownerEmail,
    GlobalSettingModel? globalSettings,
    AddPropertyModel? addPropertyModel,
    List<SelectableItem<SecurityDepositType>>? selectableSecurityDeposit,
    SelectableItem<SecurityDepositType>? selectedSecurityDeposit,
    List<SelectableItem<NoticePeriodType>>? selectableNoticePeriodType,
    SelectableItem<NoticePeriodType>? selectedNoticePeriodType,
    List<SelectableItem<LockInPeriodType>>? selectableLockInPeriodType,
    SelectableItem<LockInPeriodType>? selectedLockInPeriodType,
    List<SelectableItem<MasterAreaUnitsModel>>? selectableCommonAreaUnits,
    SelectableItem<MasterAreaUnitsModel>? selectedCommonAreaUnits,
    String? selectedPermitValue,
    List<SelectableItem<PaymentFrequency>>? selectablePaymentFrequency,
    SelectableItem<PaymentFrequency>? selectedPaymentFrequency,
    List<ItemListingSourceAddressModel>? listingSiteAndLocations,
    String? defaultCountryCode,
    List<SelectableItem<int>>? numberOfCheques,
    SelectableItem<int>? selectedNumberOfCheque,
    int? totalListingSiteAndAddress,
    TaxationMode? taxationMode,
    String? builderContactNumber,
    List<SelectableItem<CustomAttributesModel>>? customBasicAttributes,
    int? ratings,
    List<SelectableItem<String>>? furnishStatuses,
    SelectableItem<String>? selectedFurnishStatus,
    List<SelectableItem<String>>? facings,
    SelectableItem<String>? selectedFacing,
    List<OwnerDetailsStorage>? ownerDetailsStorages,
    List<SelectableItem<int>>? selectableNoOfFloorsOccupied,
    List<SelectableItem<int>>? selectedNoOfFloorsOccupied,
    bool? isOfficeSpaceSelected,
    bool? isCoWorkingOfficeSpaceSelected,
    List<SelectableItem<PossessionType?>>? possessionTypeSelectableItems,
    SelectableItem<PossessionType?>? possessionTypeSelectedItem,
    bool? isPossessionDateCustomSelected,
    bool? isSecurityAmountPercentageSelected,
    String? depositCurrency,
  }) {
    return PropertyInfoTabState(
      pageState: pageState ?? PageState.initial,
      errorMessage: errorMessage,
      possessionAvailablity: possessionAvailablity ?? this.possessionAvailablity,
      totalPrice: totalPrice ?? this.totalPrice,
      currencies: currencies ?? this.currencies,
      selectedPropertySubType: selectedPropertySubType ?? this.selectedPropertySubType,
      selectedEnquiredType: selectedEnquiredType ?? this.selectedEnquiredType,
      currency: currency ?? this.currency,
      isNegotiable: isNegotiable ?? this.isNegotiable,
      maintenanceCost: maintenanceCost ?? this.maintenanceCost,
      projects: projects ?? this.projects,
      selectedProject: selectedProject ?? this.selectedProject,
      brokerageAmount: brokerageAmount ?? this.brokerageAmount,
      brokerageUnits: brokerageUnits ?? this.brokerageUnits,
      brokerageUnit: brokerageUnit ?? this.brokerageUnit,
      notes: notes ?? this.notes,
      location: location ?? this.location,
      ownerName: ownerName ?? this.ownerName,
      ownerNumber: ownerNumber ?? this.ownerNumber,
      ownerEmail: ownerEmail ?? this.ownerEmail,
      globalSettings: globalSettings ?? this.globalSettings,
      addPropertyModel: addPropertyModel ?? this.addPropertyModel,
      selectableSecurityDeposit: selectableSecurityDeposit ?? this.selectableSecurityDeposit,
      selectedSecurityDeposit: selectedSecurityDeposit ?? this.selectedSecurityDeposit,
      selectableNoticePeriodType: selectableNoticePeriodType ?? this.selectableNoticePeriodType,
      selectedNoticePeriodType: selectedNoticePeriodType ?? this.selectedNoticePeriodType,
      selectableLockInPeriodType: selectableLockInPeriodType ?? this.selectableLockInPeriodType,
      selectedLockInPeriodType: selectedLockInPeriodType ?? this.selectedLockInPeriodType,
      selectableCommonAreaUnits: selectableCommonAreaUnits ?? this.selectableCommonAreaUnits,
      selectedCommonAreaUnits: selectedCommonAreaUnits ?? this.selectedCommonAreaUnits,
      selectedPermitValue: selectedPermitValue ?? this.selectedPermitValue,
      selectedPaymentFrequency: selectedPaymentFrequency ?? this.selectedPaymentFrequency,
      selectablePaymentFrequency: selectablePaymentFrequency ?? this.selectablePaymentFrequency,
      listingSiteAndLocations: listingSiteAndLocations ?? this.listingSiteAndLocations,
      defaultCountryCode: defaultCountryCode ?? this.defaultCountryCode,
      numberOfCheques: numberOfCheques ?? this.numberOfCheques,
      selectedNumberOfCheque: selectedNumberOfCheque ?? this.selectedNumberOfCheque,
      totalListingSiteAndAddress: totalListingSiteAndAddress ?? this.totalListingSiteAndAddress,
      taxationMode: taxationMode ?? this.taxationMode,
      builderContactNumber: builderContactNumber ?? this.builderContactNumber,
      possessionTypeSelectableItems: possessionTypeSelectableItems ?? this.possessionTypeSelectableItems,
      possessionTypeSelectedItem: possessionTypeSelectedItem ?? this.possessionTypeSelectedItem,
      isPossessionDateCustomSelected: isPossessionDateCustomSelected ?? this.isPossessionDateCustomSelected,
      isSecurityAmountPercentageSelected: isSecurityAmountPercentageSelected ?? this.isSecurityAmountPercentageSelected,
      depositCurrency: depositCurrency ?? this.depositCurrency,
      customBasicAttributes: customBasicAttributes ?? this.customBasicAttributes,
      ratings: ratings ?? this.ratings,
      furnishStatuses: furnishStatuses ?? this.furnishStatuses,
      selectedFurnishStatus: selectedFurnishStatus ?? this.selectedFurnishStatus,
      facings: facings ?? this.facings,
      selectedFacing: selectedFacing ?? this.selectedFacing,
      ownerDetailsStorages: ownerDetailsStorages ?? this.ownerDetailsStorages,
      selectableNoOfFloorsOccupied: selectableNoOfFloorsOccupied ?? this.selectableNoOfFloorsOccupied,
      selectedNoOfFloorsOccupied: selectedNoOfFloorsOccupied ?? this.selectedNoOfFloorsOccupied,
      isCoWorkingOfficeSpaceSelected: isCoWorkingOfficeSpaceSelected ?? this.isCoWorkingOfficeSpaceSelected,
      isOfficeSpaceSelected: isOfficeSpaceSelected ?? this.isOfficeSpaceSelected,
    );
  }

  PropertyInfoTabState clearState({
    PageState? pageState,
    String? errorMessage,
    DateTime? possessionAvailablity,
    int? totalPrice,
    List<SelectableItem<String>>? currencies,
    String? currency,
    bool? isNegotiable,
    int? maintenanceCost,
    List<SelectableItem<String>>? projects,
    SelectableItem<String>? selectedProject,
    double? brokerageAmount,
    List<SelectableItem<String>>? brokerageUnits,
    SelectableItem<String>? brokerageUnit,
    String? notes,
    AddressModel? location,
    String? selectedPropertySubType,
    EnquiryType? selectedEnquiredType,
    String? ownerName,
    String? ownerNumber,
    String? ownerEmail,
    GlobalSettingModel? globalSettings,
    AddPropertyModel? addPropertyModel,
    List<SelectableItem<SecurityDepositType>>? selectableSecurityDeposit,
    SelectableItem<SecurityDepositType>? selectedSecurityDeposit,
    List<SelectableItem<NoticePeriodType>>? selectableNoticePeriodType,
    SelectableItem<NoticePeriodType>? selectedNoticePeriodType,
    List<SelectableItem<LockInPeriodType>>? selectableLockInPeriodType,
    SelectableItem<LockInPeriodType>? selectedLockInPeriodType,
    List<SelectableItem<MasterAreaUnitsModel>>? selectableCommonAreaUnits,
    SelectableItem<MasterAreaUnitsModel>? selectedCommonAreaUnits,
    String? selectedPermitValue,
    List<SelectableItem<PaymentFrequency>> selectablePaymentFrequency = const [],
    SelectableItem<PaymentFrequency>? selectedPaymentFrequency,
    List<ItemListingSourceAddressModel> listingSiteAndLocations = const [],
    String? defaultCountryCode,
    List<SelectableItem<int>>? numberOfCheques,
    SelectableItem<int>? selectedNumberOfCheque,
    int? totalListingSiteAndAddress,
    TaxationMode taxationMode = TaxationMode.gstInclusive,
    String? builderContactNumber,
    List<SelectableItem<PossessionType?>>? possessionTypeSelectableItems,
    SelectableItem<PossessionType?>? possessionTypeSelectedItem,
    bool? isPossessionDateCustomSelected,
    String? depositCurrency,
  }) {
    return PropertyInfoTabState(
      pageState: pageState ?? PageState.initial,
      errorMessage: errorMessage,
      possessionAvailablity: possessionAvailablity,
      totalPrice: totalPrice,
      currencies: currencies,
      currency: currency,
      isNegotiable: isNegotiable,
      maintenanceCost: maintenanceCost,
      projects: projects,
      selectedProject: selectedProject,
      brokerageAmount: brokerageAmount,
      brokerageUnits: brokerageUnits,
      brokerageUnit: brokerageUnit,
      notes: notes,
      location: location,
      ownerName: ownerName,
      ownerNumber: ownerNumber,
      ownerEmail: ownerEmail,
      globalSettings: globalSettings,
      addPropertyModel: addPropertyModel,
      selectedEnquiredType: selectedEnquiredType,
      selectedPropertySubType: selectedPropertySubType,
      selectableCommonAreaUnits: selectableCommonAreaUnits,
      selectableLockInPeriodType: selectableLockInPeriodType,
      selectableNoticePeriodType: selectableNoticePeriodType,
      selectableSecurityDeposit: selectableSecurityDeposit,
      selectedCommonAreaUnits: selectedCommonAreaUnits,
      selectedLockInPeriodType: selectedLockInPeriodType,
      selectedNoticePeriodType: selectedNoticePeriodType,
      selectedSecurityDeposit: selectedSecurityDeposit,
      selectedPermitValue: selectedPermitValue ?? "DLD",
      selectablePaymentFrequency: selectablePaymentFrequency,
      selectedPaymentFrequency: selectedPaymentFrequency,
      listingSiteAndLocations: listingSiteAndLocations,
      defaultCountryCode: defaultCountryCode,
      numberOfCheques: numberOfCheques,
      selectedNumberOfCheque: selectedNumberOfCheque,
      totalListingSiteAndAddress: totalListingSiteAndAddress,
      taxationMode: taxationMode,
      builderContactNumber: builderContactNumber,
      isPossessionDateCustomSelected: false,
      possessionTypeSelectedItem: null,
      possessionTypeSelectableItems: const [],
      depositCurrency: depositCurrency,
      customBasicAttributes: [],
      ratings: null,
      furnishStatuses: null,
      selectedFurnishStatus: null,
      facings: null,
      selectedFacing: null,
      ownerDetailsStorages: const [],
      selectableNoOfFloorsOccupied: const [],
      selectedNoOfFloorsOccupied: const [],
      isCoWorkingOfficeSpaceSelected: null,
      isOfficeSpaceSelected: null,
    );
  }
}

enum ClearOption { securityDeposit, lockInPeriod, noticePeriod, project }
