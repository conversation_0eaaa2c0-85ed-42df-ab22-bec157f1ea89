import 'package:leadrat/core_main/common/entites/address_entity.dart';
import 'package:leadrat/core_main/common/entites/property_type_entity.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/common/lead_source_enum.dart';
import 'package:leadrat/core_main/enums/common/no_of_beds.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/purpose_enum.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/property_enums/sale_type.dart';

class EnquiryEntity {
  final EnquiryType? enquiredFor;
  final SaleType? saleType;
  final LeadSource? leadSource;
  final String? subSource;
  final int? lowerBudget;
  final int? upperBudget;
  final double? noOfBHK;
  final BHKType? bHKType;
  final double? area;
  final String? areaUnitId;
  final String? areaUnit;
  final bool? isPrimary;
  final AddressEntity? address;
  final String? propertyTypeId;
  final double? carpetArea;
  final double? saleableArea;
  final double? builtUpArea;
  final String? carpetAreaUnitId;
  final String? saleableAreaUnitId;
  final String? builtUpAreaUnitId;
  final double? conversionFactor;
  final double? saleableAreaConversionFactor;
  final double? builtUpAreaConversionFactor;
  final DateTime? possessionDate;
  final String? currency;
  final List<EnquiryType>? enquiryTypes;
  final List<BHKType>? bHKTypes;
  final List<double>? bHKs;
  final List<int>? baths;
  final List<AddressEntity>? addresses;
  final PropertyTypeEntity? propertyType;
  final List<PropertyTypeEntity>? propertyTypes;
  final List<Beds>? beds;
  final int? furnished;
  final int? offerType;
  final List<String>? floors;
  final String? propertyAreaUnitId;
  final double? propertyArea;
  final String? netAreaUnitId;
  final double? netArea;
  final double? propertyAreaConversionFactor;
  final double? netAreaConversionFactor;
  final String? unitName;
  final String? clusterName;
  final PurposeEnum? purpose;
  final PossessionType? possesionType;

  EnquiryEntity({
    this.enquiredFor,
    this.saleType,
    this.leadSource,
    this.subSource,
    this.lowerBudget,
    this.upperBudget,
    this.noOfBHK,
    this.bHKType,
    this.area,
    this.areaUnitId,
    this.areaUnit,
    this.isPrimary,
    this.address,
    this.propertyTypeId,
    this.carpetArea,
    this.saleableArea,
    this.builtUpArea,
    this.carpetAreaUnitId,
    this.saleableAreaUnitId,
    this.builtUpAreaUnitId,
    this.conversionFactor,
    this.saleableAreaConversionFactor,
    this.builtUpAreaConversionFactor,
    this.possessionDate,
    this.currency,
    this.enquiryTypes,
    this.bHKTypes,
    this.bHKs,
    this.baths,
    this.addresses,
    this.propertyType,
    this.propertyTypes,
    this.beds,
    this.furnished,
    this.offerType,
    this.floors,
    this.propertyAreaUnitId,
    this.propertyArea,
    this.netAreaUnitId,
    this.netArea,
    this.propertyAreaConversionFactor,
    this.netAreaConversionFactor,
    this.unitName,
    this.clusterName,
    this.purpose,
    this.possesionType,
  });
}
