import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/remote/leadrat_rest_service.dart';
import 'package:leadrat/core_main/remote/rest_response/paged_rest_response.dart';
import 'package:leadrat/core_main/remote/rest_response/rest_response_wrapper.dart';
import 'package:leadrat/core_main/resources/common/rest_resources.dart';
import 'package:leadrat/features/lead/data/data_source/remote/leads_remote_data_source.dart';
import 'package:leadrat/features/lead/data/models/add_lead_model.dart';
import 'package:leadrat/features/lead/data/models/appointment_project_model.dart';
import 'package:leadrat/features/lead/data/models/custom_filter_model.dart';
import 'package:leadrat/features/lead/data/models/delete_document_model.dart';
import 'package:leadrat/features/lead/data/models/get_all_leads_wrapper_model.dart';
import 'package:leadrat/features/lead/data/models/get_lead_model.dart';
import 'package:leadrat/features/lead/data/models/lead_call_log_model.dart';
import 'package:leadrat/features/lead/data/models/lead_category_model.dart';
import 'package:leadrat/features/lead/data/models/lead_document_model.dart';
import 'package:leadrat/features/lead/data/models/lead_filter_model.dart';
import 'package:leadrat/features/lead/data/models/lead_history_model.dart';
import 'package:leadrat/features/lead/data/models/lead_link_model.dart';
import 'package:leadrat/features/lead/data/models/projects_dto.dart';
import 'package:leadrat/features/lead/data/models/re_assign_lead_model.dart';
import 'package:leadrat/features/lead/data/models/update_appointments_model.dart';
import 'package:leadrat/features/lead/data/models/update_custom_status_model.dart';
import 'package:leadrat/features/lead/data/models/update_lead_status_model.dart';
import 'package:leadrat/features/projects/data/models/project_unit_info_model.dart';
import 'package:leadrat/features/properties/data/models/get_property_model.dart';

import '../../models/booked_lead_model.dart';
import '../../models/get_booked_lead_model.dart';

class LeadsRemoteDataSourceImpl extends LeadratRestService implements LeadsRemoteDataSource {
  @override
  Future<GetAllLeadsWrapperModel?> getAllInitialLeads(LeadFilterModel? leadFilterModel, {int pageNumber = 0, int pageSize = 10}) async {
    final filters = leadFilterModel != null ? Uri.encodeFull(leadFilterModel.toString()) : "";
    final restRequest = createGetRequest("${LeadRestResources.getInitialLeads(pageSize: pageSize, pageNumber: pageNumber)}$filters");
    final response = await executeRequestAsync<ResponseWrapper<GetAllLeadsWrapperModel?>>(
      restRequest,
      (json) => ResponseWrapper<GetAllLeadsWrapperModel?>.fromJson(json, (data) => fromJsonObject(data, GetAllLeadsWrapperModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<GetAllLeadsWrapperCustomModel?> getAllInitialCustomLeads(LeadFilterModel? leadFilterModel, {int pageNumber = 0, int pageSize = 10}) async {
    final filters = leadFilterModel != null ? leadFilterModel.toString() : "";
    final restRequest = createGetRequest("${LeadRestResources.getCustomLeads(pageNumber: pageNumber, pageSize: pageSize)}$filters");
    final response = await executeRequestAsync<ResponseWrapper<GetAllLeadsWrapperCustomModel?>>(
      restRequest,
      (json) => ResponseWrapper<GetAllLeadsWrapperCustomModel?>.fromJson(json, (data) => fromJsonObject(data, GetAllLeadsWrapperCustomModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<Map<String, Map<String, int>>?> getLeadsCommunication(List<String> leadIds) async {
    var leadIdPath = "";
    if (leadIds.isNotEmpty) {
      leadIdPath = leadIds.map((id) => "LeadIds=$id").join('&');
    }

    final restRequest = createGetRequest(LeadRestResources.leadCommunications(leadIdPath));
    final response = await executeRequestAsync<ResponseWrapper<Map<String, Map<String, int>>>>(
      restRequest,
      (json) => ResponseWrapper<Map<String, Map<String, int>>>.fromJson(
        json,
        (data) => (data as Map<String, dynamic>).map(
          (key, value) => MapEntry(key, (value as Map<String, dynamic>).map((k, v) => MapEntry(k, v as int))),
        ),
      ),
    );
    return response.data;
  }

  @override
  Future<GetLeadModel?> getLeadDetails(String leadId) async {
    final restRequest = createGetRequest(LeadRestResources.getLeadById(leadId));
    final response = await executeRequestAsync<ResponseWrapper<GetLeadModel?>>(
      restRequest,
      (json) => ResponseWrapper<GetLeadModel?>.fromJson(json, (data) => fromJsonObject(data, GetLeadModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<Map<String, Map<String, List<LeadHistoryModel>>>?> getLeadHistory(String leadId) async {
    final restRequest = createGetRequest(LeadRestResources.getLeadHistory(leadId));

    final response = await executeRequestAsync<LeadHistoryResponseModel>(
      restRequest,
      (json) => LeadHistoryResponseModel.fromJson(json),
    );

    return response.data;
  }

  @override
  Future<Iterable<LeadDocumentModel>?> getLeadDocuments(String leadId) async {
    final restRequest = createGetRequest(LeadRestResources.getDocuments(leadId));
    final response = await executeRequestAsync<ResponseWrapper<Iterable<LeadDocumentModel>>>(
      restRequest,
      (json) => ResponseWrapper<Iterable<LeadDocumentModel>>.fromJson(
        json,
        (data) => fromJsonList(data, LeadDocumentModel.fromJson),
      ),
    );
    return response.data;
  }

  @override
  Future<bool?> updateLeadFlag(UpdateLeadFlagModel updateLeadFlagModel) async {
    final restRequest = createPutRequest(LeadRestResources.updateLeadFlag(updateLeadFlagModel.id ?? ""), body: updateLeadFlagModel.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restRequest,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );
    return response.data;
  }

  @override
  Future<List<String>?> addLeadDocuments(AddLeadDocumentsModel addLeadDocumentsModel) async {
    final restRequest = createPostRequest(LeadRestResources.addDocument, body: addLeadDocumentsModel.toJson());
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );

    return response.data;
  }

  @override
  Future<Map<DateTime, List<LeadHistoryModel>>?> getLeadNoteHistory(String leadId) async {
    final restResponse = createGetRequest(LeadRestResources.getLeadNoteHistory(leadId));
    final response = await executeRequestAsync<ResponseWrapper<Map<DateTime, List<LeadHistoryModel>>>>(
      restResponse,
      (json) => ResponseWrapper<Map<DateTime, List<LeadHistoryModel>>>.fromJson(
        json,
        (data) => data.map<DateTime, List<LeadHistoryModel>>(
          (key, value) => MapEntry(
            DateTime.parse(key as String),
            (value as List<dynamic>).map((item) => LeadHistoryModel.fromJson(item as Map<String, dynamic>)).toList(),
          ),
        ),
      ),
    );
    return response.data;
  }

  @override
  Future<bool?> updateLeadNote(String leadId, String note) async {
    final restResponse = createPutRequest(LeadRestResources.updateNote(leadId), body: {"id": leadId, "notes": note});
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restResponse,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );
    return response.data;
  }

  @override
  Future<bool?> deleteLeadDocument(DeleteDocumentModel deleteDocumentModel) async {
    final restResponse = createDeleteRequest(LeadRestResources.deleteDocument, body: deleteDocumentModel.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restResponse,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );
    return response.data;
  }

  @override
  Future<bool?> updateLeadContactCount(LeadContactCountModel leadContactCountModel) async {
    final restResponse = createPutRequest(LeadRestResources.contactCount(leadContactCountModel.id ?? ""), body: leadContactCountModel.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restResponse,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );
    return response.data;
  }

  @override
  Future<bool?> updateLeadTemplate(LeadCommunicationModel leadCommunicationModel) async {
    final restResponse = createPostRequest(LeadRestResources.leadTemplate, body: leadCommunicationModel.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restResponse,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );
    return response.data;
  }

  @override
  Future<List<LeadSubSourceModel>?> getLeadSubSource() async {
    final restRequest = createGetRequest(SourceRestResource.leadSubSource);
    final response = await executeRequestAsync<ResponseWrapper<List<LeadSubSourceModel>?>>(
      restRequest,
      (json) => ResponseWrapper<List<LeadSubSourceModel>?>.fromJson(json, (data) => fromJsonList(data, LeadSubSourceModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<bool?> updateLeadStatus(UpdateLeadStatusModel? updateLeadStatusModel) async {
    final restRequest = createPutRequest(LeadRestResources.updateLeadStatus(updateLeadStatusModel?.id ?? ''), body: updateLeadStatusModel?.toJson() ?? '');
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(restRequest, (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool));
    return response.data;
  }

  @override
  Future<bool?> updateAppointments(UpdateAppointmentsModel? updateAppointmentsModel) async {
    final restRequest = createPutRequest(LeadRestResources.updateAppointments, body: updateAppointmentsModel?.toJson() ?? '');
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(restRequest, (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool));
    return response.data;
  }

  @override
  Future<bool?> reAssignLead(ReAssignLeadModel? reAssignModel) async {
    final restRequest = createPutRequest(LeadRestResources.reAssignLead, body: reAssignModel?.toJson() ?? '');
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(restRequest, (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool));
    return response.data;
  }

  @override
  Future<String?> addLeadAsync(AddLeadModel addLeadModel) async {
    final restRequest = createPostRequest(LeadRestResources.lead, body: addLeadModel.toJson());
    final response = await executeRequestAsync<ResponseWrapper<String?>>(restRequest, (json) => ResponseWrapper<String?>.fromJson(json, (data) => data as String));
    return response.data;
  }

  @override
  Future<String?> updateLead(UpdateLeadModel updateLeadModel) async {
    final restRequest = createPutRequest("${LeadRestResources.lead}/${updateLeadModel.id}", body: updateLeadModel.toJson());
    final response = await executeRequestAsync<ResponseWrapper<String?>>(restRequest, (json) => ResponseWrapper<String?>.fromJson(json, (data) => data as String));
    return response.data;
  }

  @override
  Future<LeadContactModel?> getLeadByContactNo(String countryCode, String contactNumber) async {
    final restRequest = createGetRequest(LeadRestResources.getLeadByContactNumber(contactNumber, countryCode.replaceAll('+', '%2B')));
    final response = await executeRequestAsync<ResponseWrapper<LeadContactModel?>>(
      restRequest,
      (json) => ResponseWrapper<LeadContactModel?>.fromJson(json, (data) => fromJsonObject(data, LeadContactModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<LeadCategoryModel?> searchLeads({String? keyword, int pageNumber = 1, int pageSize = 10}) async {
    if (keyword == null) return null;
    final restRequest = createGetRequest(LeadRestResources.searchLeads(searchQuery: keyword.trim(), pageNumber: pageNumber, pageSize: pageSize));
    final response = await executeRequestAsync<ResponseWrapper<LeadCategoryModel?>>(
      restRequest,
      (json) => ResponseWrapper<LeadCategoryModel?>.fromJson(json, (data) => fromJsonObject(data, LeadCategoryModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<PagedResponse<PropertyWithDegreeMatchedModel?, String>?> matchingProperties({String? leadId, String? searchText, int pageNumber = 1, int pageSize = 10, double? radiusInKms}) async {
    if (leadId == null) return null;
    var resource = LeadRestResources.matchingProperties(leadId, pageSize: pageSize, pageNumber: pageNumber);
    if (searchText.isNotNullOrEmpty()) {
      resource += "&search=$searchText";
    }
    if (radiusInKms != null && radiusInKms != 0) {
      resource += "&RadiusInKms=$radiusInKms";
    }
    final restRequest = createGetRequest(resource);
    final response = await executeRequestAsync<PagedResponse<PropertyWithDegreeMatchedModel?, String>>(
      restRequest,
      (json) => PagedResponse<PropertyWithDegreeMatchedModel?, String>.fromJson(json, (data) => fromJsonObject(data, PropertyWithDegreeMatchedModel.fromJson), (json) => json as String),
    );
    return response;
  }

  @override
  Future<PagedResponse<ProjectWithDegreeMatchedModel?, String>?> matchingProjects({String? leadId, String? searchText, int pageNumber = 1, int pageSize = 10, double? radiusInKms}) async {
    if (leadId == null) return null;
    var resource = LeadRestResources.getMatchingProjects(leadId: leadId, pageSize: pageSize, pageNumber: pageNumber);
    if (searchText.isNotNullOrEmpty()) {
      resource += "&search=$searchText";
    }
    if (radiusInKms != null && radiusInKms != 0) {
      resource += "&RadiusInKms=$radiusInKms";
    }
    final restRequest = createGetRequest(resource);
    final response = await executeRequestAsync<PagedResponse<ProjectWithDegreeMatchedModel?, String>>(
      restRequest,
      (json) => PagedResponse<ProjectWithDegreeMatchedModel?, String>.fromJson(json, (data) => fromJsonObject(data, ProjectWithDegreeMatchedModel.fromJson), (json) => json as String),
    );
    return response;
  }

  @override
  Future<List<AppointmentProjectModel>?> getAppointmentsByProjects(ProjectDto projectDto) async {
    if (projectDto.leadId == null) return null;
    var projectsName = '';
    if (projectDto.projects?.isNotEmpty ?? false) {
      projectDto.projects?.forEach((element) => projectsName += "&Projects=${Uri.encodeQueryComponent(element)}");
    }
    final restRequest = createGetRequest(LeadRestResources.getAppointmentsByProjects(projectDto.leadId!) + projectsName);
    final response = await executeRequestAsync<ResponseWrapper<List<AppointmentProjectModel>?>>(
      restRequest,
      (json) => ResponseWrapper<List<AppointmentProjectModel>?>.fromJson(json, (data) => fromJsonList(data, AppointmentProjectModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<bool?> addProjectsInLead(ProjectDto projectDto) async {
    if (projectDto.leadId == null) return null;
    final restRequest = createPostRequest(LeadRestResources.addProjectsInLead, body: projectDto.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restRequest,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getChannelPartnerNames() async {
    final restRequest = createGetRequest(ChannelPartner.channelPartnerNames);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<List<CustomFilterModel>?> getCustomStatusFilter() async {
    final restRequest = createGetRequest(CustomFilterRestResources.defaultFilterCardView);
    final response = await executeRequestAsync<ResponseWrapper<List<CustomFilterModel>?>>(
      restRequest,
      (json) => ResponseWrapper<List<CustomFilterModel>?>.fromJson(json, (data) => fromJsonList(data, CustomFilterModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<String?> bookLead(BookedLeadModel bookedLeadModel) async {
    try {
      final restRequest = createPutRequest(LeadRestResources.updateBookingForm(bookedLeadModel.leadId), body: bookedLeadModel.toJson());
      final response = await executeRequestAsync<ResponseWrapper<String?>>(restRequest, (json) => ResponseWrapper<String?>.fromJson(json, (data) => data as String));
      return response.data;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool?> checkLeadAssignedByLeadId(String leadId) async {
    try {
      final restRequest = createGetRequest(LeadRestResources.checkLeadAssignedByLeadId(leadId));
      final response = await executeRequestAsync<bool?>(restRequest, (json) => json as bool);
      return response;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<GetBookedLeadModel?> getBookedLead(String? leadId) async {
    final restRequest = createGetRequest(LeadRestResources.getBookingDetails(leadId ?? ''));
    final response = await executeRequestAsync<ResponseWrapper<GetBookedLeadModel?>>(
      restRequest,
      (json) => ResponseWrapper<GetBookedLeadModel?>.fromJson(json, (data) => fromJsonObject(data, GetBookedLeadModel.fromJson)),
    );

    return response.data;
  }

  @override
  Future<List<LeadHistoryModel>?> getLeadHistoryBasedOnTimeZone(String leadId) async {
    final restRequest = createGetRequest(LeadRestResources.getLeadHistoryBasedOnTimeZone(leadId));
    final response = await executeRequestAsync<ResponseWrapper<List<LeadHistoryModel>?>>(
      restRequest,
      (json) => ResponseWrapper<List<LeadHistoryModel>?>.fromJson(json, (data) => fromJsonList(data, LeadHistoryModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getCommunities() async {
    final restRequest = createGetRequest(LeadRestResources.communities);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getCountries() async {
    final restRequest = createGetRequest(LeadRestResources.countries);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getSubCommunities() async {
    final restRequest = createGetRequest(LeadRestResources.subCommunities);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getTowerNames() async {
    final restRequest = createGetRequest(LeadRestResources.towerName);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getCurrencies() async {
    final restRequest = createGetRequest(LeadRestResources.currency);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getCampaignNames() async {
    final restRequest = createGetRequest(LeadRestResources.campaignNames);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<bool?> updateClickedLink(LeadLinkModel leadLinkModel) async {
    final restRequest = createPutRequest(LeadRestResources.updateClickLink, body: leadLinkModel.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restRequest,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getLeadExcelData() async {
    final restRequest = createGetRequest(LeadRestResources.uploadTypeNames);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getAdditionalPropertyKeys() async {
    final restRequest = createGetRequest(LeadRestResources.additionalPropertykeys);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getAdditionalPropertyValues(String key) async {
    final restRequest = createGetRequest(LeadRestResources.additionalpropertyValues(key));
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getLeadNationality() async {
    final restRequest = createGetRequest(LeadRestResources.getLeadNationality);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getLeadClusterNames() async {
    final restRequest = createGetRequest(LeadRestResources.getLeadClusterNames);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getLeadUnitNames() async {
    final restRequest = createGetRequest(LeadRestResources.getLeadUnitNames);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<bool?> updateLeadCallLog(LeadCallLogModel leadCallLogModel) async {
    final restRequest = createPostRequest(LeadCallLogRestResources.leadCallLog, body: leadCallLogModel.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restRequest,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );
    return response.data;
  }

  @override
  Future<bool>? customFilterBulkUpdate(UpdateCustomStatusModel updateCustomStatusModel) async {
    final restRequest = createPutRequest(CustomFilterRestResources.customFilterBulkUpdate, body: updateCustomStatusModel.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restRequest,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );
    return response.data ?? false;
  }

  @override
  Future<List<String?>?> getAllLeadCities() async {
    final restRequest = createGetRequest(LeadRestResources.leadCities);
    final response = await executeRequestAsync<ResponseWrapper<List<String?>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String?>?>.fromJson(json, (data) => (data as List?)?.map((item) => item as String?).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String?>?> getAllLeadLocality() async {
    final restRequest = createGetRequest(LeadRestResources.leadLocality);
    final response = await executeRequestAsync<ResponseWrapper<List<String?>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String?>?>.fromJson(json, (data) => (data as List?)?.map((item) => item as String?).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String?>?> getAllLeadStates() async {
    final restRequest = createGetRequest(LeadRestResources.leadStates);
    final response = await executeRequestAsync<ResponseWrapper<List<String?>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String?>?>.fromJson(json, (data) => (data as List?)?.map((item) => item as String?).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String?>?> getAllLeadZones() async {
    final restRequest = createGetRequest(LeadRestResources.leadZones);
    final response = await executeRequestAsync<ResponseWrapper<List<String?>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String?>?>.fromJson(json, (data) => (data as List?)?.map((item) => item as String?).toList()),
    );
    return response.data;
  }
}
