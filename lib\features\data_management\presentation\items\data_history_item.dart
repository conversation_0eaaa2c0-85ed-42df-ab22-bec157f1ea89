import 'package:intl/intl.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/common/no_of_beds.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/features/data_management/domain/entities/data_history_entity.dart';

import '../../../properties/presentation/items/item_property_model.dart';

sealed class DataHistoryItem {}

class DataHistoryItemModel extends DataHistoryItem {
  String? notes;
  String? assignTo;
  String? status;
  DateTime timeStamp;
  String? modifiedBy;
  List<String?>? subitems = [];
  List<DataHistoryEntity> itemsHistory;
  final bool isCustomFormEnabled;

  DataHistoryItemModel({
    this.notes,
    this.assignTo,
    this.status,
    required this.timeStamp,
    this.modifiedBy,
    this.subitems,
    required this.itemsHistory,
    this.isCustomFormEnabled = false,
  }) {
    subitems = [];
    timeStamp = timeStamp.toLocal();
    modifiedBy = itemsHistory.firstOrNull?.modifiedBy;

    for (var item in itemsHistory) {
      switch (item.fieldName) {
        case 'Notes':
          notes = item.newValue;
          break;
        case 'Status':
          status = (item.oldValue?.isNotEmpty ?? false) ? "Status changed from <b><u>'${item.oldValue}'</u></b> to <b><u>'${item.newValue}'</u></b>" : "Status changed to <b><u>'${item.newValue}'</u></b>";
          assignTo = "assigned to <b>${item.newValue}</b>";
          break;
        default:
          if (item.oldValue == null && item.newValue == null) {
            status = item.fieldName;
          } else {
            // Adding bullet points
            var formattedString = _formatString(item);
            if (formattedString.trim().isNotEmpty) {
              subitems?.add(formattedString);
            }
          }
      }
    }
  }

  String _formatString(DataHistoryEntity dataHistoryItem) {
    try {
      DataHistoryEntity item = dataHistoryItem;
      String formattedString = "";
      String? newValue;
      String? oldValue;

      if (dataHistoryItem.fieldName == null) return "";

      if (dataHistoryItem.fieldName == "Data Source" || dataHistoryItem.fieldName == "Sub Source") {
        item = dataHistoryItem.copyWith(newValue: dataHistoryItem.newValue ?? "--");
      }

      if (dataHistoryItem.fieldName!.toLowerCase().contains("Data got added")) {
        return "Data got added";
      }

      if ((item.fieldName?.toLowerCase().contains("date") ?? false) || (item.fieldName?.toLowerCase().contains("created") ?? false)) {
        final userTimeZoneInfo = getIt<UsersDataRepository>().getLoggedInUser()?.timeZoneInfo;
        if (userTimeZoneInfo?.baseUTcOffset != null && userTimeZoneInfo?.timeZoneDisplay != null && userTimeZoneInfo?.timeZoneId != null && userTimeZoneInfo?.timeZoneName != null) {
          DateTime? oldDate = _tryParseDate(item.oldValue);
          oldValue = oldDate != null ? _formatDate(oldDate.toUserTimeZone() ?? oldDate.toLocal(), "dd/MM/yyyy hh:mm a") : '';

          DateTime? newDate = _tryParseDate(item.newValue);
          newValue = newDate != null ? _formatDate(newDate.toUserTimeZone() ?? newDate.toLocal(), "dd/MM/yyyy hh:mm a") : '';
        } else {
          final formats = ["yyyy-MM-ddTHH:mm:ss.fffffffZ", "yyyy-MM-ddTHH:mm:ssZ", "MM/dd/yyyy HH:mm:ss", "dd/MM/yyyy", "MM-dd-yyyy HH:mm:ss", "dd-MM-yyyy HH:mm:ss"];

          DateTime? oldDate = _tryParseExact(item.oldValue, formats);
          oldValue = oldDate != null ? _formatDate(oldDate, "dd/MM/yyyy hh:mm a") : '';

          DateTime? newDate = _tryParseExact(item.newValue, formats);
          newValue = newDate != null ? _formatDate(newDate, "dd/MM/yyyy hh:mm a") : '';
        }
      } else {
        oldValue = item.oldValue;
        newValue = item.newValue;
      }

      if (item.fieldName!.toLowerCase().contains("assigned to user")) {
        if (oldValue.isNotNullOrEmpty()) {
          formattedString = "• Data assigned updated <b>'$oldValue'</b> to <b>'$newValue'</b>";
        } else {
          formattedString = "• Data assigned added <b>'$newValue'</b>";
        }
      } else if (item.fieldName!.toLowerCase().contains('beds')) {
        String tempOldValue = getBeds(oldValue ?? '');
        String tempNewValue = getBeds(newValue ?? '');
        formattedString = oldValue.isNotNullOrEmpty()
            ? "• ${item.fieldName} updated <b>'$tempOldValue'</b> to <b>'${tempNewValue.isNullOrEmpty() ? 'has been removed' : tempNewValue}'</b>"
            : "• ${item.fieldName} added - <b>${tempNewValue.isNullOrEmpty() ? 'has been removed' : tempNewValue}</b>";

      } else if (!isCustomFormEnabled && item.fieldName!.toLowerCase().contains('bhks')) {
        String tempOldValue = getBhks(oldValue ?? '');
        String tempNewValue = getBhks(newValue ?? '');
        formattedString = oldValue.isNotNullOrEmpty()
            ? "• ${item.fieldName} updated <b>'$tempOldValue'</b> to <b>'${tempNewValue.isNullOrEmpty() ? 'has been removed' : tempNewValue}'</b>"
            : "• ${item.fieldName} added - <b>${tempNewValue.isNullOrEmpty() ? 'has been removed' : tempNewValue}</b>";
      }
      else if (item.fieldName!.toLowerCase().contains("contact records")) {
        formattedString = "• <b>$newValue</b> had been initiated";
      } else if (item.fieldName!.toLowerCase() == "data communication") {
        formattedString = "• ${item.fieldName} added <b>'${item.newValue}'</b>";
      } else if (item.fieldName == "Company Name") {
        formattedString = oldValue.isNotNullOrEmpty() ? "• ${item.fieldName} updated <b>'$oldValue'</b> to <b>'$newValue'</b>" : "• ${item.fieldName} added - <b>$newValue</b>";
      } else if (item.fieldName == "Agency Name") {
        formattedString = oldValue.isNotNullOrEmpty() ? "• ${item.fieldName} updated <b>'$oldValue'</b> to <b>'$newValue'</b>" : "• ${item.fieldName} added <b>$newValue</b>";
      } else if (item.fieldName!.toLowerCase() == "leadcalllog") {
        formattedString = "•$newValue";
      } else if (!isCustomFormEnabled && item.fieldName == "BHKType") {
        if (newValue != "None") {
          formattedString = oldValue.isNotNullOrEmpty() ? "• ${item.fieldName} updated <b>'$oldValue'</b> to <b>'$newValue'</b>" : "• ${item.fieldName} added - <b>$newValue</b>";
        }
      } else if (oldValue.isNullOrEmpty() || oldValue == ' ' || ['None', 'Unknown'].contains(oldValue)) {
        if (item.fieldName?.toLowerCase() == 'is converted to lead' && item.newValue?.toLowerCase() == 'true') {
          formattedString = "• ${item.fieldName} added <b>'It is Converted To Lead'</b>";
        } else if (item.fieldName?.toLowerCase() == 'is converted to lead' && item.newValue?.toLowerCase() == 'false') {
          formattedString = "• ${item.fieldName} added <b>'It is not Converted To Lead'</b>";
        } else if (item.fieldName?.toLowerCase() == 'is qualified' && item.newValue?.toLowerCase() == 'true') {
          formattedString = "• ${item.fieldName} added <b>'It is qualified'</b>";
        } else if (item.fieldName?.toLowerCase() == 'is qualified' && item.newValue?.toLowerCase() == 'false') {
          formattedString = "• ${item.fieldName} added <b>'It is not qualified'</b>";
        } else {
          if (!item.fieldName!.toLowerCase().contains('bhks')) {
            formattedString = "• ${item.fieldName} added <b>'$newValue'</b>";
          }
        }
      } else if (item.fieldName?.toLowerCase() == "lower budget") {
        formattedString = oldValue.isNotNullOrEmpty() ? "• ${item.fieldName} updated <b>'Rs.$oldValue /-'</b> to <b>'Rs.$newValue /-'</b>" : "• ${item.fieldName} added <b>Rs.$newValue /-</b>";
      } else if (item.fieldName?.toLowerCase() == "upper budget") {
        formattedString = oldValue.isNotNullOrEmpty() ? "• ${item.fieldName} updated <b>'$oldValue'</b> to <b>'$newValue'</b>" : "• ${item.fieldName} added <b>$newValue</b>";
      } else if (!["scheduled date", "picked date"].contains(item.fieldName?.toLowerCase()) && newValue.isNullOrEmpty()) {
        formattedString = oldValue.isNotNullOrEmpty() ? "• ${item.fieldName} <b>'$oldValue'</b> has been removed" : '';
      } else if (item.fieldName?.toLowerCase().contains("is qualified") ?? false) {
        formattedString = "• It is qualified";
      } else if (item.fieldName?.toLowerCase().contains("is converted") ?? false) {
        formattedString = "• It is converted";
      } else {
        if (!(item.fieldName?.toLowerCase().contains("assigned from user") ?? false)) {
          formattedString = "• ${item.fieldName} updated <b>'$oldValue'</b> to <b>'$newValue'</b>";
        }
      }
      return formattedString;
    } catch (exception, stackTrace) {
      exception.logException(stackTrace);
      return '';
    }
  }

  DateTime? _tryParseDate(String? value) {
    try {
      return value != null ? DateTime.parse(value) : null;
    } catch (_) {
      return null;
    }
  }

  DateTime? _tryParseExact(String? value, List<String> formats) {
    for (var format in formats) {
      try {
        return value != null ? DateFormat(format).parse(value, true).toUtc() : null;
      } catch (_) {}
    }
    return null;
  }

  String _formatDate(DateTime date, String format) {
    return DateFormat(format).format(date.toUserTimeZone()!);
  }

  String getBhks(String oldValue) {
    List<String>? bhksList = oldValue.split(',');
    String formattedBhks = '';
    if (bhksList.isNotEmpty) {
      for (var value in bhksList) {
        try {
          if (value.isNotEmpty) {
            double convertedValue = double.parse(value);
            formattedBhks = '$formattedBhks${convertedValue == 0.5 ? '1 RK, ' : "${convertNumber(convertedValue)} ${isCustomFormEnabled ? 'BR' : 'BHK'} ,"}';
          }
        } catch (ex) {
          ex.logException();
        }
      }
    }
    if (formattedBhks.length > 2) formattedBhks = formattedBhks.substring(0, formattedBhks.length - 1);
    return formattedBhks;
  }

  String getBeds(String value) {
    try {
      var beds = value.split(',');
      var noOfBeds = beds.map((e) => int.tryParse(e)).toList().nonNulls;
      String formattedBeds = '';
      if (beds.isNotEmpty) {
        formattedBeds = noOfBeds.map((e) => getEnumFromBeds(Beds.values, e)).map((element) => element?.description).nonNulls.join(", ");
      }
      return formattedBeds;
    } catch (_) {
      return '';
    }
  }
}

class DataHistoryItemHeaderModel extends DataHistoryItem {
  final DateTime? timeStamp;

  DataHistoryItemHeaderModel(this.timeStamp);
}
