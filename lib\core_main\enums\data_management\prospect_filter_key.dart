enum ProspectFilterKey {
  source("Source"),
  subSource("Sub-Source"),
  budget("Budget"),
  status("Status"),
  prospectVisibility("Showing Data"),
  assignedTo("Assigned To"),
  assignedFrom("Assigned From"),
  projects("Projects"),
  properties("Properties"),
  enquiredFor("Enquired For"),
  dateRange("Date Range"),
  propertyType("Property Type"),
  propertySubType("Property Sub-Type"),
  noOfBHK("BHK"),
  bHKTypes("BHK Type"),
  locations("Locations"),
  agencyName("Agency Names"),
  companyName("Company Names"),
  carpetArea("Carpet Area"),
  createdBy("Created By"),
  qualifiedBy("Qualified By"),
  convertedBy("Converted By"),
  withHistory("With History"),
  community("Community"),
  subCommunity("Sub Community"),
  towerName("Tower Name"),
  country("Country"),
  pinCode("Pin Code"),
  beds("Beds"),
  baths("Baths"),
  floors("Floors"),
  offerTypes("Offer Types"),
  furnished("Furnished"),
  buildUpArea("buildUp Area"),
  salableArea("Salable Area"),
  netArea("Net Area"),
  propertyArea("Property Area"),
  unitNumberOrName("Unit Number/Name"),
  clusterName("Cluster Name"),
  nationality("Nationality"),
  campaigns("Campaigns"),
  minBudget("Min Budget"),
  maxBudget("Max Budget"),
  excelSheet("Excel Sheet"),
  purpose("Purpose");

  final String description;

  const ProspectFilterKey(this.description);
}
