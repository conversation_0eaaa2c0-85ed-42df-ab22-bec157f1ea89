// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lead_filter_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LeadFilterModel _$LeadFilterModelFromJson(Map<String, dynamic> json) =>
    LeadFilterModel(
      sources: (json['Source'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      subSources: (json['SubSources'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      filterTypes: (json['FilterTypes'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$LeadCategoryTypeEnumMap, e))
          .toList(),
      filterType:
          $enumDecodeNullable(_$LeadCategoryTypeEnumMap, json['FilterType']),
      defaultCategoryTypes: (json['DefaultCategoryTypes'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$LeadCategoryTypeEnumMap, e))
          .toList(),
      assignedToIds: (json['assignTo'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      secondaryUsers: (json['SecondaryUsers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      leadVisibility:
          $enumDecodeNullable(_$LeadVisibilityEnumMap, json['LeadVisibility']),
      enquiredFor: (json['enquiredFor'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$EnquiryTypeEnumMap, e))
          .toList(),
      projects: (json['Projects'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      properties: (json['Properties'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      dateType: $enumDecodeNullable(_$DateTypeEnumMap, json['dateType']),
      dateRange: $enumDecodeNullable(_$DateRangeEnumMap, json['DateRange']),
      fromDate: json['fromDate'] as String?,
      toDate: json['toDate'] as String?,
      meetingOrVisitStatuses: (json['MeetingOrVisitStatuses'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$LeadMeetingStatusEnumMap, e))
          .toList(),
      noOfBHKs: (json['NoOfBHKs'] as List<dynamic>?)
          ?.map((e) => (e as num).toDouble())
          .toList(),
      furnished: (json['Furnished'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      baths: (json['Baths'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      floors:
          (json['Floors'] as List<dynamic>?)?.map((e) => e as String).toList(),
      beds: (json['Beds'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      offerTypes: (json['OfferTypes'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      brs: (json['Brs'] as List<dynamic>?)
          ?.map((e) => (e as num).toDouble())
          .toList(),
      bhkTypes: (json['BhkTypes'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$BHKTypeEnumMap, e))
          .toList(),
      propertyTypes: (json['PropertyType'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      propertySubTypes: (json['PropertySubType'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      locations: (json['Locations'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      communities: (json['Communities'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      subCommunities: (json['SubCommunities'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      towerNames: (json['TowerNames'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      countries: (json['Countries'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      pincode: json['Pincode'] as String?,
      agencyNames: (json['AgencyNames'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      isWithTeam: json['IsWithTeam'] as bool?,
      userType: $enumDecodeNullable(_$UserTypeEnumMap, json['UserType']),
      bookedByIds: (json['bookedByIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      dataConverted: json['DataConverted'] as String?,
      qualifiedByIds: (json['QualifiedByIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      appointmentDoneByUserIds:
          (json['AppointmentDoneByUserIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
      toDateForMeetingOrVisit: json['ToDateForMeetingOrVisit'] as String?,
      fromDateForMeetingOrVisit: json['FromDateForMeetingOrVisit'] as String?,
      assignFromIds: (json['AssignedFromIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      customFlags: (json['CustomFlags'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      currency: json['Currency'] as String?,
      customFilterIds: json['CustomFilterIds'],
      isWithHistory: json['IsWithHistory'] as bool?,
      doneBy:
          (json['DoneBy'] as List<dynamic>?)?.map((e) => e as String).toList(),
      secondaryFromIds: (json['SecondaryFromIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      generalManagerIds: (json['GeneralManagerIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      designations: (json['designationsId'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      statusIds: (json['StatusIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      subStatuses: (json['SubStatusIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      referralName: json['ReferralName'] as String?,
      referralEmail: json['ReferralEmail'] as String?,
      referralContactNo: json['ReferralContactNo'] as String?,
      referralNumber: json['ReferralNumber'] as String?,
      createdByIds: (json['createdByIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      lastModifiedByIds: (json['lastModifiedByIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      archivedByIds: (json['archivedByIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      restoredByIds: (json['restoredByIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      sourcingManagers: (json['sourcingManagers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      closingManagers: (json['closingManagers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      builtUpAreaUnitId: json['BuiltUpAreaUnitId'] as String?,
      carpetAreaUnitId: json['CarpetAreaUnitId'] as String?,
      saleableAreaUnitId: json['SaleableAreaUnitId'] as String?,
      netAreaUnitId: json['NetAreaUnitId'] as String?,
      propertyAreaUnitId: json['PropertyAreaUnitId'] as String?,
      saleableArea: (json['SaleableArea'] as num?)?.toDouble(),
      carpetArea: (json['CarpetArea'] as num?)?.toDouble(),
      builtUpArea: (json['BuiltUpArea'] as num?)?.toDouble(),
      netArea: (json['NetArea'] as num?)?.toDouble(),
      propertyArea: (json['PropertyArea'] as num?)?.toDouble(),
      unitNames: (json['UnitNames'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      clusterNames: (json['ClusterNames'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      nationality: (json['Nationality'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      campaignNames: (json['CampaignNames'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      isUntouched: json['IsUntouched'] as String?,
      channelPartnerNames: (json['ChannelPartnerNames'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      excelSheets: (json['ExcelSheets'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      profession: (json['Profession'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$ProfessionEnumMap, e))
          .toList(),
      facebookProperties: (json['FacebookProperties'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      facebookPropertyValues: (json['FacebookPropertyValues'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      purposes: (json['Purposes'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$PurposeEnumEnumMap, e))
          .toList(),
      possessionTypeDateRange: $enumDecodeNullable(
          _$PossessionTypeEnumMap, json['PossessionTypeDateRange']),
      defaultFilterTypes: (json['DefaultFilterTypes'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$LeadCategoryTypeEnumMap, e))
          .toList(),
      maxBuiltUpArea: (json['MaxBuiltUpArea'] as num?)?.toDouble(),
      minBuiltUpArea: (json['MinBuiltUpArea'] as num?)?.toDouble(),
      maxSaleAbleArea: (json['MaxSaleableArea'] as num?)?.toDouble(),
      minSaleAbleArea: (json['MinSaleableArea'] as num?)?.toDouble(),
      maxCarpetArea: (json['MaxCarpetArea'] as num?)?.toDouble(),
      minCarpetArea: (json['MinCarpetArea'] as num?)?.toDouble(),
      maxPropertyArea: (json['MaxPropertyArea'] as num?)?.toDouble(),
      minPropertyArea: (json['MinPropertyArea'] as num?)?.toDouble(),
      fromMinBudget: (json['FromMinBudget'] as num?)?.toDouble(),
      toMinBudget: (json['ToMinBudget'] as num?)?.toDouble(),
      toMaxBudget: (json['ToMaxBudget'] as num?)?.toDouble(),
      fromMaxBudget: (json['FromMaxBudget'] as num?)?.toDouble(),
      maxNetArea: (json['MaxNetArea'] as num?)?.toDouble(),
      minNetArea: (json['MinNetArea'] as num?)?.toDouble(),
      originalOwnerIds: (json['OriginalOwnerIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$LeadFilterModelToJson(LeadFilterModel instance) =>
    <String, dynamic>{
      'Source': instance.sources,
      'SubSources': instance.subSources,
      'FilterTypes': instance.filterTypes
          ?.map((e) => _$LeadCategoryTypeEnumMap[e]!)
          .toList(),
      'FilterType': _$LeadCategoryTypeEnumMap[instance.filterType],
      'DefaultCategoryTypes': instance.defaultCategoryTypes
          .map((e) => _$LeadCategoryTypeEnumMap[e]!)
          .toList(),
      'assignTo': instance.assignedToIds,
      'SecondaryUsers': instance.secondaryUsers,
      'LeadVisibility': _$LeadVisibilityEnumMap[instance.leadVisibility],
      'enquiredFor':
          instance.enquiredFor?.map((e) => _$EnquiryTypeEnumMap[e]!).toList(),
      'Projects': instance.projects,
      'Properties': instance.properties,
      'dateType': _$DateTypeEnumMap[instance.dateType],
      'PossessionTypeDateRange':
          _$PossessionTypeEnumMap[instance.possessionTypeDateRange],
      'DateRange': _$DateRangeEnumMap[instance.dateRange],
      'fromDate': instance.fromDate,
      'toDate': instance.toDate,
      'MeetingOrVisitStatuses': instance.meetingOrVisitStatuses
          ?.map((e) => _$LeadMeetingStatusEnumMap[e]!)
          .toList(),
      'NoOfBHKs': instance.noOfBHKs,
      'Furnished': instance.furnished,
      'Beds': instance.beds,
      'Baths': instance.baths,
      'Floors': instance.floors,
      'OfferTypes': instance.offerTypes,
      'Brs': instance.brs,
      'BhkTypes': instance.bhkTypes?.map((e) => _$BHKTypeEnumMap[e]!).toList(),
      'PropertyType': instance.propertyTypes,
      'PropertySubType': instance.propertySubTypes,
      'Locations': instance.locations,
      'Communities': instance.communities,
      'SubCommunities': instance.subCommunities,
      'TowerNames': instance.towerNames,
      'Countries': instance.countries,
      'Pincode': instance.pincode,
      'AgencyNames': instance.agencyNames,
      'IsWithTeam': instance.isWithTeam,
      'UserType': _$UserTypeEnumMap[instance.userType],
      'bookedByIds': instance.bookedByIds,
      'DataConverted': instance.dataConverted,
      'QualifiedByIds': instance.qualifiedByIds,
      'AppointmentDoneByUserIds': instance.appointmentDoneByUserIds,
      'ToDateForMeetingOrVisit': instance.toDateForMeetingOrVisit,
      'FromDateForMeetingOrVisit': instance.fromDateForMeetingOrVisit,
      'AssignedFromIds': instance.assignFromIds,
      'CustomFlags': instance.customFlags,
      'Currency': instance.currency,
      'IsWithHistory': instance.isWithHistory,
      'DoneBy': instance.doneBy,
      'SecondaryFromIds': instance.secondaryFromIds,
      'GeneralManagerIds': instance.generalManagerIds,
      'StatusIds': instance.statusIds,
      'SubStatusIds': instance.subStatuses,
      'CustomFilterIds': instance.customFilterIds,
      'CampaignNames': instance.campaignNames,
      'ReferralName': instance.referralName,
      'ReferralEmail': instance.referralEmail,
      'ReferralContactNo': instance.referralContactNo,
      'ReferralNumber': instance.referralNumber,
      'createdByIds': instance.createdByIds,
      'lastModifiedByIds': instance.lastModifiedByIds,
      'archivedByIds': instance.archivedByIds,
      'restoredByIds': instance.restoredByIds,
      'sourcingManagers': instance.sourcingManagers,
      'closingManagers': instance.closingManagers,
      'BuiltUpAreaUnitId': instance.builtUpAreaUnitId,
      'CarpetAreaUnitId': instance.carpetAreaUnitId,
      'SaleableAreaUnitId': instance.saleableAreaUnitId,
      'NetAreaUnitId': instance.netAreaUnitId,
      'PropertyAreaUnitId': instance.propertyAreaUnitId,
      'SaleableArea': instance.saleableArea,
      'CarpetArea': instance.carpetArea,
      'BuiltUpArea': instance.builtUpArea,
      'NetArea': instance.netArea,
      'PropertyArea': instance.propertyArea,
      'IsUntouched': instance.isUntouched,
      'ChannelPartnerNames': instance.channelPartnerNames,
      'ExcelSheets': instance.excelSheets,
      'Profession':
          instance.profession?.map((e) => _$ProfessionEnumMap[e]!).toList(),
      'FacebookProperties': instance.facebookProperties,
      'FacebookPropertyValues': instance.facebookPropertyValues,
      'designationsId': instance.designations,
      'UnitNames': instance.unitNames,
      'ClusterNames': instance.clusterNames,
      'Nationality': instance.nationality,
      'Purposes':
          instance.purposes?.map((e) => _$PurposeEnumEnumMap[e]!).toList(),
      'DefaultFilterTypes': instance.defaultFilterTypes
          ?.map((e) => _$LeadCategoryTypeEnumMap[e]!)
          .toList(),
      'MaxBuiltUpArea': instance.maxBuiltUpArea,
      'MinBuiltUpArea': instance.minBuiltUpArea,
      'MaxSaleableArea': instance.maxSaleAbleArea,
      'MinSaleableArea': instance.minSaleAbleArea,
      'MaxCarpetArea': instance.maxCarpetArea,
      'MinCarpetArea': instance.minCarpetArea,
      'MaxPropertyArea': instance.maxPropertyArea,
      'MinPropertyArea': instance.minPropertyArea,
      'FromMinBudget': instance.fromMinBudget,
      'ToMinBudget': instance.toMinBudget,
      'FromMaxBudget': instance.fromMaxBudget,
      'ToMaxBudget': instance.toMaxBudget,
      'MinNetArea': instance.minNetArea,
      'MaxNetArea': instance.maxNetArea,
      'OriginalOwnerIds': instance.originalOwnerIds,
    };

const _$LeadCategoryTypeEnumMap = {
  LeadCategoryType.newLead: 0,
  LeadCategoryType.pending: 1,
  LeadCategoryType.scheduledMeeting: 2,
  LeadCategoryType.siteVisitScheduled: 3,
  LeadCategoryType.notInterested: 4,
  LeadCategoryType.callBack: 5,
  LeadCategoryType.unassignedLeads: 6,
  LeadCategoryType.booked: 7,
  LeadCategoryType.dropped: 8,
  LeadCategoryType.hotLeads: 9,
  LeadCategoryType.escalated: 10,
  LeadCategoryType.aboutToConvert: 11,
  LeadCategoryType.scheduleToday: 12,
  LeadCategoryType.overdue: 13,
  LeadCategoryType.warmLeads: 15,
  LeadCategoryType.coldLeads: 16,
  LeadCategoryType.highlightedLeads: 17,
  LeadCategoryType.allLeads: 14,
  LeadCategoryType.scheduledTomorrow: 18,
  LeadCategoryType.upcomingSchedules: 19,
  LeadCategoryType.active: 20,
  LeadCategoryType.allWithNID: 21,
  LeadCategoryType.bookingCancel: 22,
  LeadCategoryType.untouched: 23,
  LeadCategoryType.expressionOfInterest: 24,
  LeadCategoryType.siteVisitDone: 25,
  LeadCategoryType.meetingDone: 26,
  LeadCategoryType.invoiced: 27,
};

const _$LeadVisibilityEnumMap = {
  LeadVisibility.selfWithReportee: 0,
  LeadVisibility.self: 1,
  LeadVisibility.reportee: 2,
  LeadVisibility.unassignLead: 3,
  LeadVisibility.duplicate: 5,
};

const _$EnquiryTypeEnumMap = {
  EnquiryType.none: 0,
  EnquiryType.buy: 1,
  EnquiryType.sale: 2,
  EnquiryType.rent: 3,
};

const _$DateTypeEnumMap = {
  DateType.none: 0,
  DateType.receivedDate: 1,
  DateType.scheduledDate: 2,
  DateType.modifiedDate: 3,
  DateType.assignedDate: 5,
  DateType.bookedDate: 6,
  DateType.assignedFromDate: 7,
  DateType.possessionDate: 8,
  DateType.siteVisitOrMeetingDoneByDate: 10,
};

const _$DateRangeEnumMap = {
  DateRange.today: 0,
  DateRange.yesterday: 1,
  DateRange.lastSevenDays: 2,
  DateRange.lastTwentyEightDays: 3,
  DateRange.customDate: 4,
};

const _$LeadMeetingStatusEnumMap = {
  LeadMeetingStatus.none: 0,
  LeadMeetingStatus.meetingDone: 1,
  LeadMeetingStatus.meetingNotDone: 2,
  LeadMeetingStatus.sitVisitDone: 3,
  LeadMeetingStatus.siteVisitNotDone: 4,
};

const _$BHKTypeEnumMap = {
  BHKType.none: 0,
  BHKType.simplex: 1,
  BHKType.duplex: 2,
  BHKType.pentHouse: 3,
  BHKType.others: 4,
};

const _$UserTypeEnumMap = {
  UserType.none: 0,
  UserType.primary: 1,
  UserType.secondary: 2,
  UserType.both: 3,
};

const _$ProfessionEnumMap = {
  Profession.none: 0,
  Profession.salaried: 1,
  Profession.business: 2,
  Profession.selfEmployed: 3,
  Profession.doctor: 4,
  Profession.retired: 5,
  Profession.housewife: 6,
  Profession.student: 7,
  Profession.unemployed: 8,
  Profession.others: 9,
};

const _$PurposeEnumEnumMap = {
  PurposeEnum.none: 0,
  PurposeEnum.investment: 1,
  PurposeEnum.selfUse: 2,
};

const _$PossessionTypeEnumMap = {
  PossessionType.none: 0,
  PossessionType.underConstruction: 1,
  PossessionType.sixMonths: 2,
  PossessionType.oneYear: 3,
  PossessionType.twoYear: 4,
  PossessionType.customDate: 5,
};
