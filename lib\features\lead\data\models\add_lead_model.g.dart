// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_lead_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AddLeadModel _$AddLeadModelFromJson(Map<String, dynamic> json) => AddLeadModel(
      id: json['id'] as String?,
      leadStatusId: json['leadStatusId'] as String?,
      enquiry: json['enquiry'] == null
          ? null
          : CreateLeadEnquiryModel.fromJson(
              json['enquiry'] as Map<String, dynamic>),
      name: json['name'] as String?,
      contactNo: json['contactNo'] as String?,
      alternateContactNo: json['alternateContactNo'] as String?,
      email: json['email'] as String?,
      notes: json['notes'] as String?,
      scheduledDate: json['scheduledDate'] == null
          ? null
          : DateTime.parse(json['scheduledDate'] as String),
      revertDate: json['revertDate'] == null
          ? null
          : DateTime.parse(json['revertDate'] as String),
      chosenProject: json['chosenProject'] as String?,
      chosenProperty: json['chosenProperty'] as String?,
      bookedUnderName: json['bookedUnderName'] as String?,
      leadNumber: json['leadNumber'] as String?,
      shareCount: (json['shareCount'] as num?)?.toInt(),
      soldPrice: json['soldPrice'] as String?,
      rating: json['rating'] as String?,
      leadTags: json['leadTags'] == null
          ? null
          : LeadTagModel.fromJson(json['leadTags'] as Map<String, dynamic>),
      projects: (json['projects'] as List<dynamic>?)
          ?.map((e) => TempProjectModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      properties: (json['properties'] as List<dynamic>?)
          ?.map((e) => TempPropertyModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      projectsList: (json['projectsList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      propertiesList: (json['propertiesList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      assignTo: json['assignTo'] as String?,
      assignedFrom: json['assignedFrom'] as String?,
      referralName: json['referralName'] as String?,
      referralContactNo: json['referralContactNo'] as String?,
      agencyName: json['agencyName'] as String?,
      agencies: (json['agencies'] as List<dynamic>?)
          ?.map((e) => DTOWithNameModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      campaigns: (json['campaigns'] as List<dynamic>?)
          ?.map((e) => DTOWithNameModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      campaignName: json['campaignName'] as String?,
      companyName: json['companyName'] as String?,
      unmatchedBudget: (json['unmatchedBudget'] as num?)?.toInt(),
      purchasedFrom: json['purchasedFrom'] as String?,
      closingManager: json['closingManager'] as String?,
      sourcingManager: json['sourcingManager'] as String?,
      address: json['address'] == null
          ? null
          : AddressModel.fromJson(json['address'] as Map<String, dynamic>),
      profession: $enumDecodeNullable(_$ProfessionEnumMap, json['profession']),
      channelPartnerName: json['channelPartnerName'] as String?,
      channelPartnerList: (json['channelPartnerList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      channelPartnerExecutiveName:
          json['channelPartnerExecutiveName'] as String?,
      channelPartnerContactNo: json['channelPartnerContactNo'] as String?,
      channelPartners: (json['channelPartners'] as List<dynamic>?)
          ?.map((e) => ChannelPartnerModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      existingProjects: (json['existingProjects'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      designation: json['designation'] as String?,
      secondaryUserId: json['secondaryUserId'] as String?,
      statusId: json['statusId'] as String?,
      bookedDate: json['bookedDate'] == null
          ? null
          : DateTime.parse(json['bookedDate'] as String),
      unitTypeId: json['unitTypeId'] as String?,
      agreementValue: (json['agreementValue'] as num?)?.toDouble(),
      serialNumber: json['serialNumber'] as String?,
      duplicateLeadVersion: json['duplicateLeadVersion'] as String?,
      confidentialNotes: json['confidentialNotes'] as String?,
      additionalProperties:
          (json['additionalProperties'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String?),
      ),
      nationality: json['nationality'] as String?,
      referralEmail: json['referralEmail'] as String?,
      possesionType:
          $enumDecodeNullable(_$PossessionTypeEnumMap, json['possesionType']),
    );

Map<String, dynamic> _$AddLeadModelToJson(AddLeadModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.leadStatusId case final value?) 'leadStatusId': value,
      if (instance.enquiry?.toJson() case final value?) 'enquiry': value,
      if (instance.name case final value?) 'name': value,
      if (instance.contactNo case final value?) 'contactNo': value,
      if (instance.alternateContactNo case final value?)
        'alternateContactNo': value,
      if (instance.email case final value?) 'email': value,
      if (instance.notes case final value?) 'notes': value,
      if (instance.scheduledDate?.toIso8601String() case final value?)
        'scheduledDate': value,
      if (instance.revertDate?.toIso8601String() case final value?)
        'revertDate': value,
      if (instance.chosenProject case final value?) 'chosenProject': value,
      if (instance.chosenProperty case final value?) 'chosenProperty': value,
      if (instance.bookedUnderName case final value?) 'bookedUnderName': value,
      if (instance.leadNumber case final value?) 'leadNumber': value,
      if (instance.shareCount case final value?) 'shareCount': value,
      if (instance.soldPrice case final value?) 'soldPrice': value,
      if (instance.rating case final value?) 'rating': value,
      if (instance.leadTags?.toJson() case final value?) 'leadTags': value,
      if (instance.projects?.map((e) => e.toJson()).toList() case final value?)
        'projects': value,
      if (instance.properties?.map((e) => e.toJson()).toList()
          case final value?)
        'properties': value,
      if (instance.projectsList case final value?) 'projectsList': value,
      if (instance.propertiesList case final value?) 'propertiesList': value,
      if (instance.assignTo case final value?) 'assignTo': value,
      if (instance.assignedFrom case final value?) 'assignedFrom': value,
      if (instance.referralName case final value?) 'referralName': value,
      if (instance.referralContactNo case final value?)
        'referralContactNo': value,
      if (instance.agencyName case final value?) 'agencyName': value,
      if (instance.agencies?.map((e) => e.toJson()).toList() case final value?)
        'agencies': value,
      if (instance.campaigns?.map((e) => e.toJson()).toList() case final value?)
        'campaigns': value,
      if (instance.campaignName case final value?) 'campaignName': value,
      if (instance.companyName case final value?) 'companyName': value,
      if (instance.unmatchedBudget case final value?) 'unmatchedBudget': value,
      if (instance.purchasedFrom case final value?) 'purchasedFrom': value,
      if (instance.closingManager case final value?) 'closingManager': value,
      if (instance.sourcingManager case final value?) 'sourcingManager': value,
      if (instance.address?.toJson() case final value?) 'address': value,
      if (_$ProfessionEnumMap[instance.profession] case final value?)
        'profession': value,
      if (instance.channelPartnerName case final value?)
        'channelPartnerName': value,
      if (instance.channelPartnerList case final value?)
        'channelPartnerList': value,
      if (instance.channelPartnerExecutiveName case final value?)
        'channelPartnerExecutiveName': value,
      if (instance.channelPartnerContactNo case final value?)
        'channelPartnerContactNo': value,
      if (instance.channelPartners?.map((e) => e.toJson()).toList()
          case final value?)
        'channelPartners': value,
      if (instance.existingProjects case final value?)
        'existingProjects': value,
      if (instance.designation case final value?) 'designation': value,
      if (instance.secondaryUserId case final value?) 'secondaryUserId': value,
      if (instance.statusId case final value?) 'statusId': value,
      if (instance.bookedDate?.toIso8601String() case final value?)
        'bookedDate': value,
      if (instance.unitTypeId case final value?) 'unitTypeId': value,
      if (instance.agreementValue case final value?) 'agreementValue': value,
      if (instance.serialNumber case final value?) 'serialNumber': value,
      if (instance.duplicateLeadVersion case final value?)
        'duplicateLeadVersion': value,
      if (instance.confidentialNotes case final value?)
        'confidentialNotes': value,
      if (instance.additionalProperties case final value?)
        'additionalProperties': value,
      if (instance.nationality case final value?) 'nationality': value,
      if (instance.referralEmail case final value?) 'referralEmail': value,
      if (_$PossessionTypeEnumMap[instance.possesionType] case final value?)
        'possesionType': value,
    };

const _$ProfessionEnumMap = {
  Profession.none: 0,
  Profession.salaried: 1,
  Profession.business: 2,
  Profession.selfEmployed: 3,
  Profession.doctor: 4,
  Profession.retired: 5,
  Profession.housewife: 6,
  Profession.student: 7,
  Profession.unemployed: 8,
  Profession.others: 9,
};

const _$PossessionTypeEnumMap = {
  PossessionType.none: 0,
  PossessionType.underConstruction: 1,
  PossessionType.sixMonths: 2,
  PossessionType.oneYear: 3,
  PossessionType.twoYear: 4,
  PossessionType.customDate: 5,
};

UpdateLeadModel _$UpdateLeadModelFromJson(Map<String, dynamic> json) =>
    UpdateLeadModel(
      id: json['id'] as String?,
      leadStatusId: json['leadStatusId'] as String?,
      enquiry: json['enquiry'] == null
          ? null
          : CreateLeadEnquiryModel.fromJson(
              json['enquiry'] as Map<String, dynamic>),
      name: json['name'] as String?,
      contactNo: json['contactNo'] as String?,
      alternateContactNo: json['alternateContactNo'] as String?,
      email: json['email'] as String?,
      notes: json['notes'] as String?,
      scheduledDate: json['scheduledDate'] == null
          ? null
          : DateTime.parse(json['scheduledDate'] as String),
      revertDate: json['revertDate'] == null
          ? null
          : DateTime.parse(json['revertDate'] as String),
      chosenProject: json['chosenProject'] as String?,
      chosenProperty: json['chosenProperty'] as String?,
      bookedUnderName: json['bookedUnderName'] as String?,
      leadNumber: json['leadNumber'] as String?,
      shareCount: (json['shareCount'] as num?)?.toInt(),
      soldPrice: json['soldPrice'] as String?,
      rating: json['rating'] as String?,
      leadTags: json['leadTags'] == null
          ? null
          : LeadTagModel.fromJson(json['leadTags'] as Map<String, dynamic>),
      projects: (json['projects'] as List<dynamic>?)
          ?.map((e) => TempProjectModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      properties: (json['properties'] as List<dynamic>?)
          ?.map((e) => TempPropertyModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      projectsList: (json['projectsList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      propertiesList: (json['propertiesList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      assignTo: json['assignTo'] as String?,
      assignedFrom: json['assignedFrom'] as String?,
      referralName: json['referralName'] as String?,
      referralContactNo: json['referralContactNo'] as String?,
      agencyName: json['agencyName'] as String?,
      agencies: (json['agencies'] as List<dynamic>?)
          ?.map((e) => DTOWithNameModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      campaigns: (json['campaigns'] as List<dynamic>?)
          ?.map((e) => DTOWithNameModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      campaignName: json['campaignName'] as String?,
      companyName: json['companyName'] as String?,
      unmatchedBudget: (json['unmatchedBudget'] as num?)?.toInt(),
      purchasedFrom: json['purchasedFrom'] as String?,
      closingManager: json['closingManager'] as String?,
      sourcingManager: json['sourcingManager'] as String?,
      address: json['address'] == null
          ? null
          : AddressModel.fromJson(json['address'] as Map<String, dynamic>),
      profession: $enumDecodeNullable(_$ProfessionEnumMap, json['profession']),
      channelPartnerName: json['channelPartnerName'] as String?,
      channelPartnerList: (json['channelPartnerList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      channelPartnerExecutiveName:
          json['channelPartnerExecutiveName'] as String?,
      channelPartnerContactNo: json['channelPartnerContactNo'] as String?,
      channelPartners: (json['channelPartners'] as List<dynamic>?)
          ?.map((e) => ChannelPartnerModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      existingProjects: (json['existingProjects'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      designation: json['designation'] as String?,
      secondaryUserId: json['secondaryUserId'] as String?,
      statusId: json['statusId'] as String?,
      bookedDate: json['bookedDate'] == null
          ? null
          : DateTime.parse(json['bookedDate'] as String),
      unitTypeId: json['unitTypeId'] as String?,
      agreementValue: (json['agreementValue'] as num?)?.toDouble(),
      serialNumber: json['serialNumber'] as String?,
      duplicateLeadVersion: json['duplicateLeadVersion'] as String?,
      confidentialNotes: json['confidentialNotes'] as String?,
      additionalProperties:
          (json['additionalProperties'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String?),
      ),
      nationality: json['nationality'] as String?,
      possesionType:
          $enumDecodeNullable(_$PossessionTypeEnumMap, json['possesionType']),
      referralEmail: json['referralEmail'] as String?,
    );

Map<String, dynamic> _$UpdateLeadModelToJson(UpdateLeadModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.leadStatusId case final value?) 'leadStatusId': value,
      if (instance.enquiry?.toJson() case final value?) 'enquiry': value,
      if (instance.name case final value?) 'name': value,
      if (instance.contactNo case final value?) 'contactNo': value,
      if (instance.alternateContactNo case final value?)
        'alternateContactNo': value,
      if (instance.email case final value?) 'email': value,
      if (instance.notes case final value?) 'notes': value,
      if (instance.scheduledDate?.toIso8601String() case final value?)
        'scheduledDate': value,
      if (instance.revertDate?.toIso8601String() case final value?)
        'revertDate': value,
      if (instance.chosenProject case final value?) 'chosenProject': value,
      if (instance.chosenProperty case final value?) 'chosenProperty': value,
      if (instance.bookedUnderName case final value?) 'bookedUnderName': value,
      if (instance.leadNumber case final value?) 'leadNumber': value,
      if (instance.shareCount case final value?) 'shareCount': value,
      if (instance.soldPrice case final value?) 'soldPrice': value,
      if (instance.rating case final value?) 'rating': value,
      if (instance.leadTags?.toJson() case final value?) 'leadTags': value,
      if (instance.projects?.map((e) => e.toJson()).toList() case final value?)
        'projects': value,
      if (instance.properties?.map((e) => e.toJson()).toList()
          case final value?)
        'properties': value,
      if (instance.projectsList case final value?) 'projectsList': value,
      if (instance.propertiesList case final value?) 'propertiesList': value,
      if (instance.assignTo case final value?) 'assignTo': value,
      if (instance.assignedFrom case final value?) 'assignedFrom': value,
      if (instance.referralName case final value?) 'referralName': value,
      if (instance.referralContactNo case final value?)
        'referralContactNo': value,
      if (instance.agencyName case final value?) 'agencyName': value,
      if (instance.agencies?.map((e) => e.toJson()).toList() case final value?)
        'agencies': value,
      if (instance.campaigns?.map((e) => e.toJson()).toList() case final value?)
        'campaigns': value,
      if (instance.campaignName case final value?) 'campaignName': value,
      if (instance.companyName case final value?) 'companyName': value,
      if (instance.unmatchedBudget case final value?) 'unmatchedBudget': value,
      if (instance.purchasedFrom case final value?) 'purchasedFrom': value,
      if (instance.closingManager case final value?) 'closingManager': value,
      if (instance.sourcingManager case final value?) 'sourcingManager': value,
      if (instance.address?.toJson() case final value?) 'address': value,
      if (_$ProfessionEnumMap[instance.profession] case final value?)
        'profession': value,
      if (instance.channelPartnerName case final value?)
        'channelPartnerName': value,
      if (instance.channelPartnerList case final value?)
        'channelPartnerList': value,
      if (instance.channelPartnerExecutiveName case final value?)
        'channelPartnerExecutiveName': value,
      if (instance.channelPartnerContactNo case final value?)
        'channelPartnerContactNo': value,
      if (instance.channelPartners?.map((e) => e.toJson()).toList()
          case final value?)
        'channelPartners': value,
      if (instance.existingProjects case final value?)
        'existingProjects': value,
      if (instance.designation case final value?) 'designation': value,
      if (instance.secondaryUserId case final value?) 'secondaryUserId': value,
      if (instance.statusId case final value?) 'statusId': value,
      if (instance.bookedDate?.toIso8601String() case final value?)
        'bookedDate': value,
      if (instance.unitTypeId case final value?) 'unitTypeId': value,
      if (instance.agreementValue case final value?) 'agreementValue': value,
      if (instance.serialNumber case final value?) 'serialNumber': value,
      if (instance.duplicateLeadVersion case final value?)
        'duplicateLeadVersion': value,
      if (instance.confidentialNotes case final value?)
        'confidentialNotes': value,
      if (instance.additionalProperties case final value?)
        'additionalProperties': value,
      if (instance.nationality case final value?) 'nationality': value,
      if (instance.referralEmail case final value?) 'referralEmail': value,
      if (_$PossessionTypeEnumMap[instance.possesionType] case final value?)
        'possesionType': value,
    };
