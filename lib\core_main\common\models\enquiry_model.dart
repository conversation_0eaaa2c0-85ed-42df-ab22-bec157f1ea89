import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/entites/enquiry_entity.dart';
import 'package:leadrat/core_main/common/models/property_type_model.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/common/lead_source_enum.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/purpose_enum.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/property_enums/sale_type.dart';

import '../../enums/common/no_of_beds.dart';
import 'address_model.dart';

part 'enquiry_model.g.dart';

@JsonSerializable()
class EnquiryModel {
  final String? id;
  final DateTime? createdOn;
  final String? createdBy;
  final DateTime? lastModifiedOn;
  final String? lastModifiedBy;
  final EnquiryType? enquiredFor;
  final SaleType? saleType;
  final LeadSource? leadSource;
  final String? subSource;
  final int? lowerBudget;
  final int? upperBudget;
  final double? noOfBHK;
  final int? bHKType;
  final double? area;
  final String? areaUnitId;
  final String? areaUnit;
  final bool? isPrimary;
  final AddressModel? address;
  final String? propertyTypeId;
  final double? carpetArea;
  final String? carpetAreaUnitId;
  final double? conversionFactor;
  final double? builtUpArea;
  final String? builtUpAreaUnitId;
  final double? builtUpAreaConversionFactor;
  final double? saleableArea;
  final String? saleableAreaUnitId;
  final double? saleableAreaConversionFactor;
  final DateTime? possessionDate;
  final String? currency;
  final List<EnquiryType>? enquiryTypes;
  @JsonKey(name: 'bhkTypes')
  final List<int>? bHKTypes;
  @JsonKey(name: 'bhKs')
  final List<double>? bHKs;
  final List<AddressModel>? addresses;
  final PropertyTypeModel? propertyType;
  final List<PropertyTypeModel>? propertyTypes;
  @JsonKey(name: 'beds')
  final List<Beds>? beds;
  final int? furnished;
  final List<int>? baths;
  final int? offerType;
  final List<String>? floors;
  final String? propertyAreaUnitId;
  final double? propertyArea;
  final String? netAreaUnitId;
  final double? netArea;
  final double? propertyAreaConversionFactor;
  final double? netAreaConversionFactor;
  final String? unitName;
  final String? clusterName;
  @JsonKey(defaultValue: PurposeEnum.none)
  final PurposeEnum? purpose;
  final PossessionType? possesionType;

  EnquiryModel({
    this.id,
    this.createdOn,
    this.createdBy,
    this.lastModifiedOn,
    this.lastModifiedBy,
    this.enquiredFor,
    this.saleType,
    this.leadSource,
    this.subSource,
    this.lowerBudget,
    this.upperBudget,
    this.noOfBHK,
    this.bHKType,
    this.area,
    this.areaUnitId,
    this.areaUnit,
    this.isPrimary,
    this.address,
    this.propertyTypeId,
    this.carpetArea,
    this.carpetAreaUnitId,
    this.conversionFactor,
    this.builtUpArea,
    this.builtUpAreaUnitId,
    this.builtUpAreaConversionFactor,
    this.saleableArea,
    this.saleableAreaUnitId,
    this.saleableAreaConversionFactor,
    this.possessionDate,
    this.currency,
    this.enquiryTypes,
    this.bHKTypes,
    this.bHKs,
    this.addresses,
    this.propertyType,
    this.propertyTypes,
    this.beds,
    this.furnished,
    this.baths,
    this.offerType,
    this.floors,
    this.propertyAreaUnitId,
    this.propertyArea,
    this.netAreaUnitId,
    this.netArea,
    this.propertyAreaConversionFactor,
    this.netAreaConversionFactor,
    this.unitName,
    this.clusterName,
    this.purpose,
    this.possesionType,
  });

  factory EnquiryModel.fromJson(Map<String, dynamic> json) => _$EnquiryModelFromJson(json);

  Map<String, dynamic> toJson() => _$EnquiryModelToJson(this);

  EnquiryEntity toEntity() {
    return EnquiryEntity(
        enquiredFor: enquiredFor,
        saleType: saleType,
        leadSource: leadSource,
        subSource: subSource,
        lowerBudget: lowerBudget,
        upperBudget: upperBudget,
        noOfBHK: noOfBHK,
        bHKType: BHKType.getBhkType(bHKType),
        area: area,
        areaUnitId: areaUnitId,
        areaUnit: areaUnit,
        isPrimary: isPrimary,
        address: address?.toEntity(),
        propertyTypeId: propertyTypeId,
        carpetArea: carpetArea,
        carpetAreaUnitId: carpetAreaUnitId,
        conversionFactor: conversionFactor,
        builtUpArea: builtUpArea,
        builtUpAreaUnitId: builtUpAreaUnitId,
        builtUpAreaConversionFactor: builtUpAreaConversionFactor,
        saleableArea: saleableArea,
        saleableAreaUnitId: saleableAreaUnitId,
        saleableAreaConversionFactor: saleableAreaConversionFactor,
        possessionDate: possessionDate,
        currency: currency,
        enquiryTypes: enquiryTypes,
        bHKTypes: bHKTypes?.map((element) => BHKType.getBhkType(element)).toList(),
        bHKs: bHKs,
        addresses: addresses?.map((e) => e.toEntity()).toList(),
        propertyType: propertyType?.toEntity(),
        beds: beds,
        furnished: furnished,
        baths: baths,
        offerType: offerType,
        floors: floors,
        propertyTypes: propertyTypes?.map((e) => e.toEntity()).toList(),
        netArea: netArea,
        netAreaConversionFactor: netAreaConversionFactor,
        netAreaUnitId: netAreaUnitId,
        propertyArea: propertyArea,
        propertyAreaConversionFactor: propertyAreaConversionFactor,
        propertyAreaUnitId: propertyAreaUnitId,
        unitName: unitName,
        clusterName: clusterName,
        purpose: purpose,
        possesionType: possesionType);
  }
}
