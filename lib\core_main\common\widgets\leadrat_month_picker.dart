import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

class LeadratMonthPicker extends LeadratStatefulWidget {
  final DateTime? selectedDate;
  final ValueChanged<DateTime>? onMonthSelected;
  final String? placeholder;

  const LeadratMonthPicker({
    Key? key,
    this.selectedDate,
    this.onMonthSelected,
    this.placeholder = "Select month",
  }) : super(key: key);

  @override
  State<LeadratMonthPicker> createState() => _LeadratMonthPickerState();
}

class _LeadratMonthPickerState extends LeadratState<LeadratMonthPicker> {
  late String _displayText;

  @override
  void initState() {
    super.initState();
    _updateDisplayText();
  }

  @override
  void didUpdateWidget(covariant LeadratMonthPicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedDate != widget.selectedDate) {
      _updateDisplayText();
    }
  }

  void _updateDisplayText() {
    _displayText = widget.selectedDate != null ? DateFormat('MMMM yyyy').format(widget.selectedDate!) : widget.placeholder ?? "Select month";
  }

  Future<void> _selectMonth(BuildContext context) async {
    final selected = widget.selectedDate ?? DateTime.now();

    DateTime? pickedDate = await showDialog<DateTime>(
      context: context,
      builder: (context) {
        int selectedYear = selected.year;
        int selectedMonth = selected.month;

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Select Month and Year'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    DropdownButton<int>(
                      value: selectedYear,
                      isExpanded: true,
                      items: List.generate(100, (index) {
                        int year = DateTime.now().year - 50 + index;
                        return DropdownMenuItem(
                          value: year,
                          child: Text(year.toString()),
                        );
                      }),
                      onChanged: (year) {
                        if (year != null) {
                          setState(() {
                            selectedYear = year;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 10),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: List.generate(12, (index) {
                        final month = index + 1;
                        return ChoiceChip(
                          label: Text(DateFormat.MMM().format(DateTime(0, month))),
                          selected: month == selectedMonth,
                          onSelected: (_) {
                            setState(() {
                              selectedMonth = month;
                            });
                          },
                        );
                      }),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    // This returns the last day of the selected month
                    Navigator.of(context).pop(
                      DateTime(selectedYear, selectedMonth + 1, 0),
                    );
                  },
                  child: const Text('OK'),
                ),
              ],
            );
          },
        );
      },
    );

    if (pickedDate != null && mounted) {
      setState(() {
        _displayText = DateFormat('MMMM yyyy').format(pickedDate);
      });
      widget.onMonthSelected?.call(pickedDate);
    }
  }

  @override
  Widget buildContent(BuildContext context) {
    return TextFormField(
      controller: TextEditingController(text: _displayText),
      style: LexendTextStyles.lexend12Regular.copyWith(
        color: ColorPalette.primaryDarkColor,
        overflow: TextOverflow.ellipsis,
      ),
      decoration: InputDecoration(
        suffixIcon: const Icon(Icons.calendar_today),
        hintStyle: LexendTextStyles.lexend10Regular.copyWith(
          color: ColorPalette.primary,
        ),
      ),
      readOnly: true,
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
        _selectMonth(context);
      },
    );
  }
}
