part of 'property_info_bloc.dart';

@immutable
final class PropertyInfoState {
  final PropertyInfoPageState? pageState;
  final String statusMessage;
  final ItemPropertyInfoModel? itemPropertyInfoModel;
  final int selectedPropertyImageIndex;
  final bool isAmenitiesSelected;
  final double matchingLeadsCount;
  final double associateLeadsCount;
  final List<SelectableItem<String?>>? allUsers;
  final List<SelectableItem<String?>>? listingOnBehalfAllUsers;
  final bool? isHighlighted;
  final List<SelectableItem<String>>? assignUsers;
  final List<SelectableItem<String>>? listingOnBehalf;

  const PropertyInfoState({
    this.pageState,
    this.statusMessage = '',
    this.itemPropertyInfoModel,
    this.isAmenitiesSelected = true,
    this.selectedPropertyImageIndex = 0,
    this.matchingLeadsCount = 0,
    this.associateLeadsCount = 0,
    this.allUsers,
    this.listingOnBehalfAllUsers,
    this.assignUsers,
    this.listingOnBehalf,
    this.isHighlighted = false,
  });

  PropertyInfoState copyWith({
    PropertyInfoPageState? pageState,
    ItemPropertyInfoModel? itemPropertyInfoModel,
    int? propertyImageIndex,
    String? statusMessage,
    double? matchingLeadsCount,
    double? associateLeadsCount,
    List<SelectableItem<String?>>? allUsers,
    List<SelectableItem<String?>>? listingOnBehalfAllUsers,
    bool? isAmenitiesSelected,
    List<SelectableItem<String>>? assignUsers,
    bool? isHighlighted,
    List<SelectableItem<String>>? listingOnBehalf,
  }) {
    return PropertyInfoState(
      itemPropertyInfoModel: itemPropertyInfoModel ?? this.itemPropertyInfoModel,
      statusMessage: statusMessage ?? this.statusMessage,
      pageState: pageState ?? PropertyInfoPageState.normal,
      selectedPropertyImageIndex: propertyImageIndex ?? selectedPropertyImageIndex,
      matchingLeadsCount: matchingLeadsCount ?? this.matchingLeadsCount,
      associateLeadsCount: associateLeadsCount ?? this.associateLeadsCount,
      allUsers: allUsers ?? this.allUsers,
      listingOnBehalfAllUsers: listingOnBehalfAllUsers ?? this.listingOnBehalfAllUsers,
      isAmenitiesSelected: isAmenitiesSelected ?? this.isAmenitiesSelected,
      isHighlighted: isHighlighted ?? this.isHighlighted,
      assignUsers: assignUsers ?? this.assignUsers,
      listingOnBehalf: listingOnBehalf ?? this.listingOnBehalf,
    );
  }

  PropertyInfoState reset() {
    return const PropertyInfoState(
      itemPropertyInfoModel: null,
      statusMessage: '',
      pageState: null,
      selectedPropertyImageIndex: 0,
      matchingLeadsCount: 0,
      isAmenitiesSelected: true,
      associateLeadsCount: 0,
      isHighlighted: false,
      allUsers: null,
      listingOnBehalfAllUsers: null,
    );
  }
}

enum PropertyInfoPageState {
  normal,
  initial,
  loading,
  failure,
  success,
  error,
  reAssignSuccessful,
  deleteSuccessful,
}
