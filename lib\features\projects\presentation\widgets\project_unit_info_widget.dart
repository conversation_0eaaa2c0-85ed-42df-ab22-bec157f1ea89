import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_stateless_widget.dart';
import 'package:leadrat/core_main/common/widgets/text_underline.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/project_enums/unit_type_status.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/features/projects/presentation/blocs/project_info_bloc/project_info_bloc.dart';
import 'package:leadrat/features/projects/presentation/items/item_project_Info_model.dart';
import 'package:leadrat/features/properties/presentation/widgets/properties_images_carousel_view.dart';

class ProjectUnitInfoWidget extends LeadratStatelessWidget {
  ProjectUnitInfoWidget({this.projectItem, super.key}) {
    controller = PageController();
  }

  final ProjectInfoState? projectItem;
  late final PageController controller;
  final propertyInfoBloc = getIt<ProjectInfoBloc>();

  @override
  Widget buildContent(BuildContext context) {
    var units = projectItem?.itemProjectModel?.projectByIdEntity?.unitTypes?.where((element) => element.unitTypeStatus == UnitTypeStatus.active).toList() ?? [];
    return units.isEmpty
        ? const SizedBox()
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 4),
              const UnderlineTextWidget(title: 'Unit Info'),
              Container(
                margin: const EdgeInsets.only(bottom: 8),
                height: 50,
                child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: units.length,
                    itemBuilder: (context, index) {
                      return Container(
                        height: 35,
                        margin: const EdgeInsets.all(8),
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        decoration: const BoxDecoration(color: ColorPalette.darkBlack, borderRadius: BorderRadius.all(Radius.circular(3))),
                        child: Center(child: Text('${units[index].name}', style: LexendTextStyles.lexend11Regular.copyWith(color: ColorPalette.tertiaryTextColor))),
                      );
                    }),
              ),
              if (units.isNotEmpty)
                if (units.isNotEmpty)
                  Stack(children: [
                    Padding(
                      padding: EdgeInsets.only(top: context.height(1.8)),
                      child: const Divider(color: ColorPalette.lightBackground, thickness: 1.6),
                    ),
                    SizedBox(
                      height: context.height(7),
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: units.length,
                        itemBuilder: (context, index) {
                          int selectedIndex = projectItem?.selectedUnitIndex ?? 0;
                          return GestureDetector(
                            onTap: () => propertyInfoBloc.add(UnitIdToggleEvent(index: index)),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 8),
                              child: Column(
                                children: [
                                  Text('${units[index].name}', maxLines: 1, style: LexendTextStyles.lexend11Regular.copyWith(color: projectItem?.selectedUnitIndex == index ? ColorPalette.white : ColorPalette.lightBackground)),
                                  SizedBox(
                                    width: context.width(18),
                                    child: Divider(color: selectedIndex == index ? ColorPalette.white : ColorPalette.lightBackground),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ]),
              if (units.isNotEmpty)
                SizedBox(
                  height: 280,
                  child: PageView.builder(
                    controller: controller,
                    scrollDirection: Axis.horizontal,
                    itemCount: units.length,
                    onPageChanged: (value) => propertyInfoBloc.add(UnitIdToggleEvent(index: value)),
                    itemBuilder: (context, index) {
                      List<String>? images = units[projectItem != null && projectItem!.selectedUnitIndex != null ? projectItem!.selectedUnitIndex! : index].images?.where((element) => element.imageFilePath != null && element.imageFilePath!.isNotEmpty).map((e) => e.imageFilePath!).toList();
                      return Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 16, bottom: 8),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [Text('BuildUp Area : ${getFormattedAreaSize(units[index].buildUpArea, units[index].buildUpAreaId)} ', style: LexendTextStyles.lexend11Regular.copyWith(color: ColorPalette.primary)), Text(units[index].price != null ? units[index].price!.toDouble().budgetToWord(units[index].currency) : '--', style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.white))],
                            ),
                          ),
                          PropertiesCarouselViewWidget(images: images ?? [], carouselSliderController: propertyInfoBloc.carouselSliderController, propertyImageIndex: projectItem?.selectedUnitGalleryImageIndex ?? 0, function: (index, reason) => propertyInfoBloc.add(ToggleUnitGalleryImageEvent(index: index))),
                        ],
                      );
                    },
                  ),
                )
            ],
          );
  }
}
