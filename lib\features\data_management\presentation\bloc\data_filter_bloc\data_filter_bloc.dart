import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/usecase/use_case.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/repository/global_setting_repository.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_type_model.dart';
import 'package:leadrat/core_main/common/data/master_data/repository/masterdata_repository.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/models/budget_range_model.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/area_type_enum.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/common/budget_enum.dart';
import 'package:leadrat/core_main/enums/common/date_range.dart';
import 'package:leadrat/core_main/enums/common/no_of_bhk.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/property_type_enum.dart';
import 'package:leadrat/core_main/enums/common/purpose_enum.dart';
import 'package:leadrat/core_main/enums/data_management/prospect_date_type.dart';
import 'package:leadrat/core_main/enums/data_management/prospect_filter_key.dart';
import 'package:leadrat/core_main/enums/data_management/prospect_visibility.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:leadrat/core_main/extensions/enum_extension.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/features/data_management/data/models/data_filter_model.dart';
import 'package:leadrat/features/data_management/domain/entities/prospect_sub_source_entity.dart';
import 'package:leadrat/features/data_management/domain/repository/prospect_repository.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_excel_data_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_location_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_source_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_status_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_sub_source_usecase.dart';
import 'package:leadrat/features/data_management/presentation/items/data_filter_item.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/features/lead/domain/usecase/get_project_name_with_id_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_property_name_with_id_use_case.dart';
import 'package:leadrat/features/lead/presentation/widgets/range_input_widget.dart';

import '../../../../../core_main/enums/common/no_of_beds.dart';
import '../../../../../core_main/enums/common/no_of_br.dart';
import '../../../../../core_main/enums/property_enums/furnish_status.dart';
import '../../../../../core_main/enums/property_enums/offering_type.dart';
import '../../../../lead/domain/repository/leads_repository.dart';

part 'data_filter_event.dart';

part 'data_filter_state.dart';

class DataFilterBloc extends Bloc<DataFilterEvent, DataFilterState> {
  List<DataFilterCategoryItem> dataFilterCategories = [];
  late DataFilterModel _selectedDataFilterModel;
  DataFilterItem? selectedFilterItem;
  final MasterDataRepository _masterDataRepository;
  final UsersDataRepository _usersDataRepository;
  final LeadsRepository _leadsRepository;
  final GlobalSettingRepository _globalSettingRepository;
  final GetPropertyNameWithIdUseCase _getPropertyNameWithIdUseCase;
  final GetProjectNameWithIdUseCase _getProjectNameWithIdUseCase;
  final GetProspectStatusUseCase _getProspectStatusUseCase;
  final GetProspectSubSourceUseCase _getProspectSubSourceUseCase;
  final GetProspectLocationUseCase _getProspectLocationUseCase;
  final GetProspectSourceUseCase _getProspectSourceUseCase;
  final GetProspectExcelDataUseCase _getProspectExcelDataUseCase;
  List<MasterPropertyTypeModel> _allPropertyTypes = [];
  List<ProspectSubSourceEntity?> _allProspectSource = [];
  UserDetailsModel? _currentUser;
  GlobalSettingModel? _globalSettingModel;

  DataFilterBloc(
    this._masterDataRepository,
    this._usersDataRepository,
    this._leadsRepository,
    this._getPropertyNameWithIdUseCase,
    this._getProjectNameWithIdUseCase,
    this._getProspectStatusUseCase,
    this._getProspectSubSourceUseCase,
    this._getProspectLocationUseCase,
    this._getProspectSourceUseCase,
    this._globalSettingRepository,
    this._getProspectExcelDataUseCase,
  ) : super(const DataFilterState()) {
    on<InitDataFilterEvent>(_onInitDataFilter);
    on<FilterCategorySelectEvent>(_onFilterCategorySelect);
    on<SelectFilterEvent>(_onSelectFilter);
    on<SearchFiltersItemsEvent>(_onSearchFiltersItems);
    on<ResetFilterEvent>(_onResetFilter);
    on<SelectFromDateEvent>(_onSelectFromDate);
    on<SelectToDateEvent>(_onSelectToDate);
    on<CustomBudgetChangeEvent>(_onCustomBudgetChange);
    on<SelectDateTypeEvent>(_onSelectDateType);
    on<ChangeCurrencyEvent>(_onChangeCurrency);
    on<ApplyDataFilterEvent>(_onApplyDataFilter);
    on<SelectAreaUnitEvent>(_onSelectAreaUnitEvent);

    on<OnMinBudgetChangeEvent>(_onOnMinBudgetChangeEvent);
    on<OnMaxBudgetChangeEvent>(_onOnMaxBudgetChangeEvent);

    on<AreaSizeInputChangeEvent>(_onAreaSizeInputChangeEvent);
    on<AreaUnitChangedEvent>(_onAreaUnitChanged);
  }

  FutureOr<void> _onInitDataFilter(InitDataFilterEvent event, Emitter<DataFilterState> emit) async {
    _globalSettingModel = event.globalSettingModel;
    _selectedDataFilterModel = event.dataFilterModel ?? DataFilterModel(prospectVisiblity: ProspectVisibility.selfWithReportee);
    dataFilterCategories = _setUpDataFilters();
    _currentUser = _usersDataRepository.getLoggedInUser();
    _updateState(
      emit,
      dataFilterCategories: dataFilterCategories,
      dataFilterModel: _selectedDataFilterModel,
      pageState: PageState.initial,
      selectedToDate: null,
      selectedFromDate: null,
      customBudgetRange: state.customBudget,
      selectedCurrency: null,
      allCurrencies: [],
      selectedDateType: null,
    );
    add(FilterCategorySelectEvent(selectedCategoryIndex: state.selectedCategoryIndex, dataFilterCategoryItem: dataFilterCategories[state.selectedCategoryIndex]));
    await _initSelectedFilters(emit);
  }

  List<DataFilterCategoryItem> _setUpDataFilters() {
    return [
      DataFilterCategoryItem(filterKey: ProspectFilterKey.prospectVisibility, filters: [], hasMultiSelect: false),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.status, filters: [], hasSelectAll: true),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.source, filters: [], hasSelectAll: true),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.subSource, filters: [], hasSearch: true, searchHintText: 'search sub source', searchController: TextEditingController()),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.dateRange, filters: [], hasMultiSelect: false, hasCustomHeaderView: true),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.enquiredFor, filters: []),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.propertyType, filters: []),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.propertySubType, filters: []),
      if (!(_globalSettingModel?.isCustomLeadFormEnabled ?? true)) DataFilterCategoryItem(filterKey: ProspectFilterKey.bHKTypes, filters: []),
      if (!(_globalSettingModel?.isCustomLeadFormEnabled ?? true)) DataFilterCategoryItem(filterKey: ProspectFilterKey.noOfBHK, filters: []),
      DataFilterCategoryItem(
        filterKey: ProspectFilterKey.minBudget,
        hasSelectAll: false,
        hasMultiSelect: false,
        filters: [],
      ),
      DataFilterCategoryItem(
        filterKey: ProspectFilterKey.maxBudget,
        hasSelectAll: false,
        hasMultiSelect: false,
        filters: [],
      ),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.agencyName, filters: [], hasSearch: true, searchHintText: 'search agency', searchController: TextEditingController()),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.campaigns, filters: [], hasSearch: true, searchHintText: 'search campaigns', searchController: TextEditingController()),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.assignedTo, filters: [], hasSearch: true, searchHintText: 'search user', searchController: TextEditingController()),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.assignedFrom, filters: [], hasSearch: true, searchHintText: 'search user', searchController: TextEditingController()),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.createdBy, filters: [], hasSearch: true, searchHintText: 'search user', searchController: TextEditingController()),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.qualifiedBy, filters: [], hasSearch: true, searchHintText: 'search user', searchController: TextEditingController()),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.convertedBy, filters: [], hasSearch: true, searchHintText: 'search user', searchController: TextEditingController()),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.properties, filters: [], hasSearch: true, searchHintText: 'search property', searchController: TextEditingController()),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.projects, filters: [], hasSearch: true, searchHintText: 'search project', searchController: TextEditingController()),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.locations, filters: [], hasSearch: true, searchHintText: 'search location', searchController: TextEditingController()),
      if (_globalSettingModel?.isCustomLeadFormEnabled ?? false) DataFilterCategoryItem(filterKey: ProspectFilterKey.beds, filters: []),
      if (_globalSettingModel?.isCustomLeadFormEnabled ?? false) DataFilterCategoryItem(filterKey: ProspectFilterKey.baths, filters: []),
      if (_globalSettingModel?.isCustomLeadFormEnabled ?? false) DataFilterCategoryItem(filterKey: ProspectFilterKey.floors, filters: []),
      if (_globalSettingModel?.isCustomLeadFormEnabled ?? false) DataFilterCategoryItem(filterKey: ProspectFilterKey.offerTypes, filters: []),
      if (_globalSettingModel?.isCustomLeadFormEnabled ?? false) DataFilterCategoryItem(filterKey: ProspectFilterKey.furnished, filters: []),
      if (_globalSettingModel?.isCustomLeadFormEnabled ?? false) DataFilterCategoryItem(filterKey: ProspectFilterKey.community, filters: [], hasSearch: true, searchHintText: "search community", searchController: TextEditingController()),
      if (_globalSettingModel?.isCustomLeadFormEnabled ?? false) DataFilterCategoryItem(filterKey: ProspectFilterKey.subCommunity, filters: [], hasSearch: true, searchHintText: "search sub community", searchController: TextEditingController()),
      if (_globalSettingModel?.isCustomLeadFormEnabled ?? false) DataFilterCategoryItem(filterKey: ProspectFilterKey.towerName, filters: [], hasSearch: true, searchHintText: "search tower name", searchController: TextEditingController()),
      if (_globalSettingModel?.isCustomLeadFormEnabled ?? false) DataFilterCategoryItem(filterKey: ProspectFilterKey.country, filters: [], hasSearch: true, searchHintText: "search country", searchController: TextEditingController()),
      if (_globalSettingModel?.isCustomLeadFormEnabled ?? false) DataFilterCategoryItem(filterKey: ProspectFilterKey.pinCode, filters: [], hasSearch: false, searchHintText: "enter pin code", searchController: TextEditingController(), pinCodeController: TextEditingController()),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.carpetArea, hasSelectAll: false, hasMultiSelect: false, filters: [], isAreaRelated: true),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.buildUpArea, hasSelectAll: false, hasMultiSelect: false, filters: [], isAreaRelated: true),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.salableArea, hasSelectAll: false, hasMultiSelect: false, filters: [], isAreaRelated: true),
      if (_globalSettingModel?.isCustomLeadFormEnabled ?? false) DataFilterCategoryItem(filterKey: ProspectFilterKey.netArea, hasSelectAll: false, hasMultiSelect: false, filters: [], isAreaRelated: true),
      if (_globalSettingModel?.isCustomLeadFormEnabled ?? false) DataFilterCategoryItem(filterKey: ProspectFilterKey.propertyArea, hasSelectAll: false, hasMultiSelect: false, filters: [], isAreaRelated: true),
      if (_globalSettingModel?.isCustomLeadFormEnabled ?? false) DataFilterCategoryItem(filterKey: ProspectFilterKey.unitNumberOrName, filters: [], hasSearch: true, hasMultiSelect: true),
      if (_globalSettingModel?.isCustomLeadFormEnabled ?? false) DataFilterCategoryItem(filterKey: ProspectFilterKey.clusterName, filters: [], hasSearch: true, hasMultiSelect: true),
      if (_globalSettingModel?.isCustomLeadFormEnabled ?? false) DataFilterCategoryItem(filterKey: ProspectFilterKey.nationality, filters: [], hasMultiSelect: true, hasSearch: true),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.purpose, filters: [], hasMultiSelect: true, hasSearch: false),
      DataFilterCategoryItem(filterKey: ProspectFilterKey.excelSheet, filters: [], hasMultiSelect: false, hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
    ];
  }

  void _updateState(
    Emitter<DataFilterState> emit, {
    List<DataFilterCategoryItem>? dataFilterCategories,
    DataFilterModel? dataFilterModel,
    PageState? pageState,
    int? selectedCategoryIndex,
    DateTime? selectedFromDate,
    DateTime? selectedToDate,
    BudgetRangeModel? customBudgetRange,
    List<SelectableItem<String>>? allCurrencies,
    SelectableItem<String>? selectedCurrency,
    List<SelectableItem<ProspectDateType>>? dateTypes,
    SelectableItem<ProspectDateType>? selectedDateType,
  }) async {
    emit(state.copyWith(
      dataFilterCategories: dataFilterCategories ?? state.dataFilterCategories,
      dataFilterModel: dataFilterModel ?? state.dataFilterModel,
      pageState: pageState ?? PageState.initial,
      selectedCategoryIndex: selectedCategoryIndex ?? state.selectedCategoryIndex,
      searchFilteredCategories: _updateSearchCategory(selectedCategoryIndex, dataFilterCategories),
      selectedFromDate: selectedFromDate,
      selectedToDate: selectedToDate,
      customBudget: state.customBudget,
      currencies: allCurrencies,
      selectedCurrency: selectedCurrency,
      errorMessage: "",
      dateTypes: dateTypes,
      selectedDateType: selectedDateType,
    ));
  }

  List<DataFilterCategoryItem>? _updateSearchCategory(int? selectedCategoryIndex, List<DataFilterCategoryItem>? dataFilterCategories) {
    final isSearching = state.searchFilteredCategories.isNotEmpty ? state.searchFilteredCategories[selectedCategoryIndex ?? state.selectedCategoryIndex].searchController?.text.isNotNullOrEmpty() : false;

    if (selectedFilterItem != null && (isSearching ?? false)) {
      final selectedSearchFilteredCategories = state.searchFilteredCategories[selectedCategoryIndex ?? state.selectedCategoryIndex];
      List<DataFilterItem>? updatedSearchedFilterList = selectedSearchFilteredCategories.filters?.map((e) {
        if (e.displayName == selectedFilterItem!.displayName) {
          return selectedFilterItem!.copyWith(isSelected: !selectedFilterItem!.isSelected);
        }
        return selectedSearchFilteredCategories.hasMultiSelect ? e : e.copyWith(isSelected: false);
      }).toList();
      var updatedCategoryList = state.searchFilteredCategories.map((e) => e.filterKey == selectedSearchFilteredCategories.filterKey ? selectedSearchFilteredCategories.copyWith(filters: updatedSearchedFilterList) : e).toList();
      return updatedCategoryList;
    }
    return dataFilterCategories;
  }

  Future<void> _initSelectedFilters(Emitter<DataFilterState> emit) async {
    if (_selectedDataFilterModel.prospectVisiblity != null) await _initializeFilter(ProspectFilterKey.prospectVisibility, _initShowingData, emit);
    if (_selectedDataFilterModel.statusIds?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.status, _initProspectStatus, emit);
    if (_selectedDataFilterModel.sources?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.source, _initProspectSource, emit);
    if (_selectedDataFilterModel.subSources?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.subSource, _initProspectSubSource, emit);
    if (_selectedDataFilterModel.enquiryTypes?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.enquiredFor, _initEnquiredForFilters, emit);

    if (_selectedDataFilterModel.fromMinBudget != null || _selectedDataFilterModel.toMinBudget != null) await _initializeFilter(ProspectFilterKey.minBudget, _initMinBudgetFilters, emit);

    if (_selectedDataFilterModel.fromMaxBudget != null || _selectedDataFilterModel.toMaxBudget != null) await _initializeFilter(ProspectFilterKey.maxBudget, _initMaxBudgetFilters, emit);

    if (_selectedDataFilterModel.agencyNames?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.agencyName, _initAgencyNames, emit);
    if (_selectedDataFilterModel.campaignNames?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.campaigns, _initCampaignNames, emit);
    if (_selectedDataFilterModel.userIds?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.assignedTo, _initAssignedToUsers, emit);
    if (_selectedDataFilterModel.assignedFromIds?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.assignedFrom, _initAssignedFromUsers, emit);
    if (_selectedDataFilterModel.createdByIds?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.createdBy, _initCreatedByUsers, emit);
    if (_selectedDataFilterModel.qualifiedByIds?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.qualifiedBy, _initQualifiedByUsers, emit);
    if (_selectedDataFilterModel.convertedByIds?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.convertedBy, _initConvertedByUsers, emit);
    if (_selectedDataFilterModel.dateRange != null) await _initializeFilter(ProspectFilterKey.dateRange, _initDateRanges, emit);
    if (_selectedDataFilterModel.noOfBHKs?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.noOfBHK, _initNoOfBHKs, emit);
    if (_selectedDataFilterModel.bhkTypes?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.bHKTypes, _initBHKTypes, emit);
    if (_selectedDataFilterModel.locations?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.locations, _initProspectLocations, emit);
    if (_selectedDataFilterModel.propertyTypes?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.propertyType, _initPropertyTypes, emit);
    if (_selectedDataFilterModel.propertySubTypes?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.propertySubType, _initPropertySubTypes, emit);
    if (_selectedDataFilterModel.properties?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.properties, _initProperties, emit);
    if (_selectedDataFilterModel.projects?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.projects, _initProjects, emit);
    if (_selectedDataFilterModel.furnished?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.furnished, _initFurnishingStatus, emit);
    if (_selectedDataFilterModel.beds?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.beds, _initNoOfBeds, emit);
    if (_selectedDataFilterModel.baths?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.baths, _initNoOfBaths, emit);
    if (_selectedDataFilterModel.floors?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.floors, _initNoOfFloors, emit);
    if (_selectedDataFilterModel.offerTypes?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.offerTypes, _initOfferingTypes, emit);

    if (_selectedDataFilterModel.communities?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.community, _initCommunities, emit);
    if (_selectedDataFilterModel.subCommunities?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.subCommunity, _initSubCommunities, emit);
    if (_selectedDataFilterModel.towerNames?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.towerName, _initTowerNames, emit);
    if (_selectedDataFilterModel.countries?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.country, _initCountries, emit);
    if (_selectedDataFilterModel.pincode?.isNotEmpty ?? false) dataFilterCategories.firstWhereOrNull((category) => category.filterKey == ProspectFilterKey.pinCode)?.pinCodeController?.text = _selectedDataFilterModel.pincode ?? '';
    if (_selectedDataFilterModel.minCarpetArea != null || _selectedDataFilterModel.minCarpetArea != null) await _initializeFilter(ProspectFilterKey.carpetArea, _initCarpetAreaUnits, emit);
    if (_selectedDataFilterModel.minBuiltUpArea != null || _selectedDataFilterModel.maxBuiltUpArea != null) await _initializeFilter(ProspectFilterKey.buildUpArea, _initBuildUpAreaUnits, emit);
    if (_selectedDataFilterModel.minSaleAbleArea != null || _selectedDataFilterModel.maxSaleableArea != null) await _initializeFilter(ProspectFilterKey.salableArea, _initSealableAreaUnits, emit);
    if (_selectedDataFilterModel.minNetArea != null || _selectedDataFilterModel.maxNetArea != null) await _initializeFilter(ProspectFilterKey.netArea, _initNetAreaUnits, emit);
    if (_selectedDataFilterModel.maxPropertyArea != null || _selectedDataFilterModel.minPropertyArea != null) await _initializeFilter(ProspectFilterKey.propertyArea, _initPropertyAreaUnits, emit);
    if (_selectedDataFilterModel.clusterNames?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.clusterName, _initClusterName, emit);
    if (_selectedDataFilterModel.unitNames?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.unitNumberOrName, _initUnitNameNumber, emit);
    if (_selectedDataFilterModel.nationality?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.nationality, _initNationality, emit);
    if (_selectedDataFilterModel.purposes?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.purpose, _initPurposes, emit);
    if (_selectedDataFilterModel.excelSheets?.isNotEmpty ?? false) await _initializeFilter(ProspectFilterKey.excelSheet, _initExcelSheets, emit);
  }

  Future<void> _initializeFilter(ProspectFilterKey filterKey, Function? initFunction, Emitter<DataFilterState> emit) async {
    try {
      if (initFunction != null) {
        await initFunction(emit);
      } else {
        switch (filterKey) {
          case ProspectFilterKey.prospectVisibility:
            _initShowingData(emit);
            break;
          case ProspectFilterKey.status:
            await _initProspectStatus(emit);
            break;
          case ProspectFilterKey.source:
            await _initProspectSource(emit);
            break;
          case ProspectFilterKey.subSource:
            await _initProspectSubSource(emit);
            break;
          case ProspectFilterKey.dateRange:
            _initDateRanges(emit);
            break;
          case ProspectFilterKey.enquiredFor:
            _initEnquiredForFilters(emit);
            break;
          case ProspectFilterKey.propertyType:
            _initPropertyTypes(emit);
            break;
          case ProspectFilterKey.propertySubType:
            await _initPropertySubTypes(emit);
            break;
          case ProspectFilterKey.bHKTypes:
            _initBHKTypes(emit);
            break;
          case ProspectFilterKey.noOfBHK:
            _initNoOfBHKs(emit);
            break;
          case ProspectFilterKey.minBudget:
            await _initMinBudgetFilters(emit);
            break;
          case ProspectFilterKey.maxBudget:
            await _initMaxBudgetFilters(emit);
          case ProspectFilterKey.agencyName:
            await _initAgencyNames(emit);
            break;
          case ProspectFilterKey.campaigns:
            await _initCampaignNames(emit);
            break;
          case ProspectFilterKey.assignedTo:
            await _initAssignedToUsers(emit);
            break;
          case ProspectFilterKey.assignedFrom:
            await _initAssignedFromUsers(emit);
            break;
          case ProspectFilterKey.createdBy:
            await _initCreatedByUsers(emit);
            break;
          case ProspectFilterKey.qualifiedBy:
            await _initQualifiedByUsers(emit);
            break;
          case ProspectFilterKey.convertedBy:
            await _initConvertedByUsers(emit);
            break;
          case ProspectFilterKey.properties:
            await _initProperties(emit);
            break;
          case ProspectFilterKey.projects:
            await _initProjects(emit);
            break;
          case ProspectFilterKey.locations:
            await _initProspectLocations(emit);
            break;
          case ProspectFilterKey.furnished:
            _initFurnishingStatus(emit);
            break;
          case ProspectFilterKey.beds:
            _initNoOfBeds(emit);
            break;
          case ProspectFilterKey.baths:
            _initNoOfBaths(emit);
            break;
          case ProspectFilterKey.floors:
            _initNoOfFloors(emit);
            break;
          case ProspectFilterKey.offerTypes:
            _initOfferingTypes(emit);
            break;

          case ProspectFilterKey.community:
            await _initCommunities(emit);
            break;
          case ProspectFilterKey.subCommunity:
            await _initSubCommunities(emit);
            break;
          case ProspectFilterKey.towerName:
            await _initTowerNames(emit);
            break;
          case ProspectFilterKey.country:
            await _initCountries(emit);
            break;
          case ProspectFilterKey.carpetArea:
            await _initCarpetAreaUnits(emit);
            break;
          case ProspectFilterKey.buildUpArea:
            await _initBuildUpAreaUnits(emit);
            break;
          case ProspectFilterKey.salableArea:
            await _initSealableAreaUnits(emit);
            break;
          case ProspectFilterKey.propertyArea:
            await _initPropertyAreaUnits(emit);
            break;
          case ProspectFilterKey.netArea:
            await _initNetAreaUnits(emit);
            break;
          case ProspectFilterKey.nationality:
            await _initNationality(emit);
          case ProspectFilterKey.clusterName:
            await _initClusterName(emit);
          case ProspectFilterKey.unitNumberOrName:
            await _initUnitNameNumber(emit);
          case ProspectFilterKey.purpose:
            _initPurposes(emit);
            break;
          case ProspectFilterKey.excelSheet:
            await _initExcelSheets(emit);
            break;
          default:
            break;
        }
      }
    } catch (ex) {
      "Error while initializing $filterKey filter: ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initNationality(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedNationality = _selectedDataFilterModel.nationality;
      final initialCountry = await getIt<ProspectRepository>().getProspectNationality();
      var countries = initialCountry
          ?.map(
            (e) => DataFilterItem(displayName: e, isSelected: initialSelectedNationality?.contains(e) ?? false, filterKey: ProspectFilterKey.nationality),
          )
          .toList();

      _updateFilterCategory(ProspectFilterKey.nationality, countries ?? [], emit);
    } catch (ex) {
      "Error while initializing carpet area units ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initClusterName(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedClusterName = _selectedDataFilterModel.clusterNames;
      final initialCountry = await getIt<ProspectRepository>().getProspectClusterNames();
      var clusterName = initialCountry
          ?.map(
            (e) => DataFilterItem(displayName: e, isSelected: initialSelectedClusterName?.contains(e) ?? false, filterKey: ProspectFilterKey.nationality),
          )
          .toList();

      _updateFilterCategory(ProspectFilterKey.clusterName, clusterName ?? [], emit);
    } catch (ex) {
      "Error while initializing cluster name ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initUnitNameNumber(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedUnitName = _selectedDataFilterModel.unitNames;
      final initialUnitName = await getIt<ProspectRepository>().getProspectUnitNames();
      var unitNameNumber = initialUnitName
          ?.map(
            (e) => DataFilterItem(displayName: e, isSelected: initialSelectedUnitName?.contains(e) ?? false, filterKey: ProspectFilterKey.nationality),
          )
          .toList();

      _updateFilterCategory(ProspectFilterKey.unitNumberOrName, unitNameNumber ?? [], emit);
    } catch (ex) {
      "Error while initializing unit name/number ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initCarpetAreaUnits(Emitter<DataFilterState> emit) async {
    try {
      if (_selectedDataFilterModel.maxCarpetArea == null && _selectedDataFilterModel.minCarpetArea == null) emit(state.copyWith(updateSelectCarpetArea: false));
      final initialSelectCarpetAreaUnit = _selectedDataFilterModel.carpetAreaUnitId;
      final initialSelectCarpetArea = _selectedDataFilterModel.minCarpetArea ?? _selectedDataFilterModel.maxCarpetArea;
      List<SelectableItem<MasterAreaUnitsModel>>? areaUnits = await _masterDataRepository.getAreaUnits().then((value) {
        return value?.map((type) => SelectableItem<MasterAreaUnitsModel>(title: type?.unit ?? '', value: type)).whereNotNull().toList();
      });
      final selectedCarpetAreaUnit = areaUnits?.firstWhereOrNull((element) => element.value?.id == initialSelectCarpetAreaUnit);
      emit(state.copyWith(
        selectableAreaUnits: areaUnits,
        selectedCarpetAreaUnit: initialSelectCarpetAreaUnit == null ? state.selectedCarpetAreaUnit : selectedCarpetAreaUnit,
        updateSelectCarpetAreaUnit: initialSelectCarpetAreaUnit != null ? true : false,
        updateSelectCarpetArea: initialSelectCarpetArea != null ? true : false,
      ));
    } catch (ex) {
      "Error while initializing carpet area units ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initBuildUpAreaUnits(Emitter<DataFilterState> emit) async {
    try {
      if (_selectedDataFilterModel.maxBuiltUpArea == null && _selectedDataFilterModel.minBuiltUpArea == null) emit(state.copyWith(updateSelectBuiltArea: false));
      final initialSelectBuiltUpAreaUnit = _selectedDataFilterModel.builtUpAreaUnitId;
      final initialSelectBuiltUpArea = _selectedDataFilterModel.minBuiltUpArea ?? _selectedDataFilterModel.maxBuiltUpArea;

      List<SelectableItem<MasterAreaUnitsModel>>? areas = await _masterDataRepository.getAreaUnits().then((value) {
        return value?.map((type) => SelectableItem<MasterAreaUnitsModel>(title: type?.unit ?? '', value: type)).whereNotNull().toList();
      });
      final selectedBuiltUpAreaUnit = areas?.firstWhereOrNull((element) => element.value?.id == initialSelectBuiltUpAreaUnit);
      emit(state.copyWith(
        selectableAreaUnits: areas,
        selectedBuildUpAreaUnit: initialSelectBuiltUpAreaUnit == null ? state.selectedBuildUpAreaUnit : selectedBuiltUpAreaUnit,
        updateSelectBuildUpAreaUnit: initialSelectBuiltUpAreaUnit != null ? true : false,
        updateSelectBuiltArea: initialSelectBuiltUpArea != null ? true : false,
      ));
    } catch (ex) {
      "Error while initializing build up area units ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initSealableAreaUnits(Emitter<DataFilterState> emit) async {
    try {
      if (_selectedDataFilterModel.maxSaleableArea == null && _selectedDataFilterModel.minSaleAbleArea == null) emit(state.copyWith(updateSelectSaleableArea: false));
      final initialSelectSaleableAreaUnit = _selectedDataFilterModel.saleableAreaUnitId;
      final initialSelectSaleableArea = _selectedDataFilterModel.minSaleAbleArea ?? _selectedDataFilterModel.maxSaleableArea;
      List<SelectableItem<MasterAreaUnitsModel>>? areaUnits = await _masterDataRepository.getAreaUnits().then((value) {
        return value?.map((type) => SelectableItem<MasterAreaUnitsModel>(title: type?.unit ?? '', value: type)).whereNotNull().toList();
      });
      final selectedSaleableAreaUnit = areaUnits?.firstWhereOrNull((element) => element.value?.id == initialSelectSaleableAreaUnit);
      emit(state.copyWith(
        selectableAreaUnits: areaUnits,
        selectedSalableAreaUnit: selectedSaleableAreaUnit,
        updateSelectSalableAreaUnit: initialSelectSaleableAreaUnit != null ? true : false,
        updateSelectSaleableArea: initialSelectSaleableArea != null ? true : false,
      ));
    } catch (ex) {
      "Error while initializing sealable area units ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initNetAreaUnits(Emitter<DataFilterState> emit) async {
    try {
      if (_selectedDataFilterModel.maxNetArea == null && _selectedDataFilterModel.minNetArea == null) emit(state.copyWith(updateSelectNetArea: false));
      final initialSelectedNetAreaUnit = _selectedDataFilterModel.netAreaUnitId;
      final initialSelectedPropertyArea = _selectedDataFilterModel.maxNetArea ?? _selectedDataFilterModel.minNetArea;
      List<SelectableItem<MasterAreaUnitsModel>>? areaUnits = await _masterDataRepository.getAreaUnits().then((value) {
        return value?.map((type) => SelectableItem<MasterAreaUnitsModel>(title: type?.unit ?? '', value: type)).whereNotNull().toList();
      });
      final selectedNetAreaUnit = areaUnits?.firstWhereOrNull((element) => element.value?.id == initialSelectedNetAreaUnit);
      emit(state.copyWith(
        selectableAreaUnits: areaUnits,
        selectedNetAreaUnit: selectedNetAreaUnit,
        updateSelectNetArea: initialSelectedPropertyArea != null ? true : false,
        updateSelectNetAreaUnit: initialSelectedNetAreaUnit != null ? true : false,
      ));
    } catch (ex) {
      "Error while initializing net area units ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initPropertyAreaUnits(Emitter<DataFilterState> emit) async {
    try {
      if (_selectedDataFilterModel.maxPropertyArea == null && _selectedDataFilterModel.minPropertyArea == null) emit(state.copyWith(updateSelectPropertyArea: false));
      final initialSelectedPropertyAreaUnit = _selectedDataFilterModel.propertyAreaUnitId;
      final initialSelectedPropertyArea = _selectedDataFilterModel.minPropertyArea ?? _selectedDataFilterModel.maxPropertyArea;
      List<SelectableItem<MasterAreaUnitsModel>>? areaUnits = await _masterDataRepository.getAreaUnits().then((value) {
        return value?.map((type) => SelectableItem<MasterAreaUnitsModel>(title: type?.unit ?? '', value: type)).whereNotNull().toList();
      });
      final selectedPropertyAreaUnit = areaUnits?.firstWhereOrNull((element) => element.value?.id == initialSelectedPropertyAreaUnit);
      emit(state.copyWith(
        selectableAreaUnits: areaUnits,
        selectedPropertyAreaUnit: selectedPropertyAreaUnit,
        updateSelectPropertyAreaUnit: initialSelectedPropertyAreaUnit != null ? true : false,
        updateSelectPropertyArea: initialSelectedPropertyArea != null ? true : false,
      ));
    } catch (ex) {
      "Error while initializing sealable area units ${ex.toString()}".printInConsole();
    }
  }

  FutureOr<void> _onSelectAreaUnitEvent(SelectAreaUnitEvent event, Emitter<DataFilterState> emit) {
    switch (event.areaType) {
      case AreaType.carpetArea:
        if (event.selectedArea?.isSelected ?? false) {
          emit(state.copyWith(selectedCarpetAreaUnit: event.selectedArea));
        } else {
          emit(state.copyWith(updateSelectCarpetAreaUnit: false));
        }
      case AreaType.buildUpArea:
        if (event.selectedArea?.isSelected ?? false) {
          emit(state.copyWith(selectedBuildUpAreaUnit: event.selectedArea));
        } else {
          emit(state.copyWith(updateSelectBuildUpAreaUnit: false));
        }
      case AreaType.salableArea:
        if (event.selectedArea?.isSelected ?? false) {
          emit(state.copyWith(selectedSalableAreaUnit: event.selectedArea));
        } else {
          emit(state.copyWith(updateSelectSalableAreaUnit: false));
        }
      case AreaType.propertyArea:
        if (event.selectedArea?.isSelected ?? false) {
          emit(state.copyWith(selectedPropertyAreaUnit: event.selectedArea));
        } else {
          emit(state.copyWith(updateSelectPropertyAreaUnit: false));
        }
      case AreaType.netArea:
        if (event.selectedArea?.isSelected ?? false) {
          emit(state.copyWith(selectedNetAreaUnit: event.selectedArea));
        } else {
          emit(state.copyWith(updateSelectNetAreaUnit: false));
        }
    }
  }

  void _updateFilterCategory(ProspectFilterKey filterKey, List<DataFilterItem> filters, Emitter<DataFilterState> emit) {
    var updatedCategories = state.dataFilterCategories.map((category) => category.filterKey == filterKey ? category.copyWith(filters: filters, isInitialized: true) : category).toList();
    _updateState(emit, dataFilterCategories: updatedCategories);
  }

  void _initShowingData(Emitter<DataFilterState> emit) {
    final initialSelectedDataVisibility = _selectedDataFilterModel.prospectVisiblity;
    var prospectVisibilities = ProspectVisibility.values
        .where((e) => _usersDataRepository.checkHasPermission(AppModule.prospect, CommandType.viewUnassignedData) || e != ProspectVisibility.unassignData)
        .map((e) => DataFilterItem(
              displayName: e.description,
              filterKey: ProspectFilterKey.prospectVisibility,
              isSelected: initialSelectedDataVisibility == e,
              value: e,
            ))
        .toList();
    prospectVisibilities.removeWhere((element) => element.displayName == ProspectVisibility.deletedData.description);
    _updateFilterCategory(ProspectFilterKey.prospectVisibility, prospectVisibilities, emit);
  }

  void _initFurnishingStatus(Emitter<DataFilterState> emit) {
    try {
      final initialSelectedFurnishingStatus = _selectedDataFilterModel.furnished;
      final furnishingStatuses = FurnishStatus.values.where((e) => e != FurnishStatus.none).map((e) => DataFilterItem<int>(displayName: e.description, value: e.value, filterKey: ProspectFilterKey.furnished, isSelected: initialSelectedFurnishingStatus?.contains(e.value) ?? false)).toList();
      _updateFilterCategory(ProspectFilterKey.furnished, furnishingStatuses, emit);
    } catch (ex) {
      "Error while initializing Furnishing Status ${ex.toString()}".printInConsole();
    }
  }

  void _initNoOfBeds(Emitter<DataFilterState> emit) {
    try {
      final initialSelectedNoOfBeds = _selectedDataFilterModel.beds;
      final beds = Beds.values.map((e) => DataFilterItem<int>(displayName: e.description, value: e.value, filterKey: ProspectFilterKey.beds, isSelected: initialSelectedNoOfBeds?.contains(e.value) ?? false)).toList();
      _updateFilterCategory(ProspectFilterKey.beds, beds, emit);
    } catch (ex) {
      "Error while initializing Beds ${ex.toString()}".printInConsole();
    }
  }

  void _initNoOfBaths(Emitter<DataFilterState> emit) {
    try {
      final initialSelectedNoOfBaths = _selectedDataFilterModel.baths;
      final baths = NoOfBr.values.map((e) => DataFilterItem<int>(displayName: e.description, value: e.noOfBr.toInt(), filterKey: ProspectFilterKey.baths, isSelected: initialSelectedNoOfBaths?.contains(e.noOfBr.toInt()) ?? false)).toList();
      _updateFilterCategory(ProspectFilterKey.baths, baths, emit);
    } catch (ex) {
      "Error while initializing Baths ${ex.toString()}".printInConsole();
    }
  }

  void _initNoOfFloors(Emitter<DataFilterState> emit) {
    try {
      final initialSelectedNoOfFloors = _selectedDataFilterModel.floors;
      List<SelectableItem<String>> floors = [
        SelectableItem(title: 'upper basement'),
        SelectableItem(title: 'lower basement'),
        SelectableItem(title: 'ground floor'),
      ];

      for (int i = 0; i <= 200; i++) {
        floors.add(SelectableItem(title: '$i'));
      }
      final allFloors = floors.map((e) => DataFilterItem<String>(displayName: e.title, value: e.title, filterKey: ProspectFilterKey.floors, isSelected: initialSelectedNoOfFloors?.contains(e.title) ?? false)).toList();
      _updateFilterCategory(ProspectFilterKey.floors, allFloors, emit);
    } catch (ex) {
      "Error while initializing Floors ${ex.toString()}".printInConsole();
    }
  }

  void _initOfferingTypes(Emitter<DataFilterState> emit) {
    try {
      final initialSelectedOfferingType = _selectedDataFilterModel.offerTypes;
      final offerTypes = OfferingType.values.where((e) => e != OfferingType.none).map((e) => DataFilterItem<int>(displayName: e.description, value: e.value, filterKey: ProspectFilterKey.offerTypes, isSelected: initialSelectedOfferingType?.contains(e.value) ?? false)).toList();
      _updateFilterCategory(ProspectFilterKey.offerTypes, offerTypes, emit);
    } catch (ex) {
      "Error while initializing Offering Types ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initCommunities(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedCommunities = _selectedDataFilterModel.communities;
      final communities = await _leadsRepository.getCommunities();
      final leadCommunities = communities?.map((community) => DataFilterItem(displayName: community, filterKey: ProspectFilterKey.community, isSelected: initialSelectedCommunities?.contains(community) ?? false)).toList();
      if (leadCommunities?.isNotEmpty ?? false) {
        _updateFilterCategory(ProspectFilterKey.community, leadCommunities!, emit);
      }
    } catch (ex) {
      "Error while initializing Communities ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initSubCommunities(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedSubCommunities = _selectedDataFilterModel.subCommunities;
      final subCommunities = await _leadsRepository.getSubCommunities();
      final leadSubCommunities = subCommunities?.map((subCommunity) => DataFilterItem(displayName: subCommunity, filterKey: ProspectFilterKey.subCommunity, isSelected: initialSelectedSubCommunities?.contains(subCommunity) ?? false)).toList();
      if (leadSubCommunities?.isNotEmpty ?? false) {
        _updateFilterCategory(ProspectFilterKey.subCommunity, leadSubCommunities!, emit);
      }
    } catch (ex) {
      "Error while initializing sub communites ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initTowerNames(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedTowerNames = _selectedDataFilterModel.towerNames;
      final towerNames = await _leadsRepository.getTowerNames();
      final leadTowerNames = towerNames?.map((towerName) => DataFilterItem(displayName: towerName, filterKey: ProspectFilterKey.towerName, isSelected: initialSelectedTowerNames?.contains(towerName) ?? false)).toList();
      if (leadTowerNames?.isNotEmpty ?? false) {
        _updateFilterCategory(ProspectFilterKey.towerName, leadTowerNames!, emit);
      }
    } catch (ex) {
      "Error while initializing tower names ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initCountries(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedCountries = _selectedDataFilterModel.countries;
      final countries = await _leadsRepository.getCountries();
      final leadCountries = countries?.map((country) => DataFilterItem(displayName: country, filterKey: ProspectFilterKey.country, isSelected: initialSelectedCountries?.contains(country) ?? false)).toList();
      if (leadCountries?.isNotEmpty ?? false) {
        _updateFilterCategory(ProspectFilterKey.country, leadCountries!, emit);
      }
    } catch (ex) {
      "Error while initializing countries ${ex.toString()}".printInConsole();
    }
  }

  void _initEnquiredForFilters(Emitter<DataFilterState> emit) {
    try {
      final initialSelectedEnquiredFor = _selectedDataFilterModel.enquiryTypes;
      final enquiryTypeFilters = EnquiryType.values.map((e) => DataFilterItem(displayName: e.description, filterKey: ProspectFilterKey.enquiredFor, isSelected: initialSelectedEnquiredFor?.contains(e) ?? false)).toList();
      enquiryTypeFilters.removeWhere((element) => element.displayName == EnquiryType.none.description);
      _updateFilterCategory(ProspectFilterKey.enquiredFor, enquiryTypeFilters, emit);
    } catch (ex) {
      "Error while initializing Enquired types ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initializeCurrency(Emitter<DataFilterState> emit, String? selectedCurrency) async {
    try {
      if (state.currencies.isEmpty) {
        var globalSettingModel = await getIt<GlobalSettingRepository>().getGlobalSettings();
        var currencies = await getIt<ProspectRepository>().getCurrencies();
        List<SelectableItem<String>> allCurrencies = [];
        SelectableItem<String>? selectableItem;
        currencies?.forEach((item) => allCurrencies.add(SelectableItem<String>(title: item, value: item)));
        if (globalSettingModel != null) {
          var defaultSymbol = globalSettingModel.countries?.firstOrNull?.defaultCurrency ?? "INR";
          selectableItem = allCurrencies.firstWhereOrNull((element) => element.value == defaultSymbol);
        }
        _updateState(emit, allCurrencies: allCurrencies, selectedCurrency: selectableItem);
      }
      if (selectedCurrency != null && state.currencies.isNotEmpty) {
        final selectedItem = state.currencies.firstWhereOrNull((element) => element.value == selectedCurrency);
        _updateState(emit, selectedCurrency: selectedItem);
      }
    } catch (ex, stackTrace) {
      ex.logException(stackTrace);
      "Error while initializing currency".printInConsole();
    }
  }

  void _initDateRanges(Emitter<DataFilterState> emit) {
    try {
      final initialSelectedDateType = _selectedDataFilterModel.dateType;
      final dateTypes = ProspectDateType.values
          .map((type) {
            if (type != ProspectDateType.all) {
              return SelectableItem<ProspectDateType>(title: type.description, value: type);
            }
          })
          .nonNulls
          .toList();
      final selectedDateType = dateTypes.firstWhereOrNull((element) => element.value == initialSelectedDateType);
      emit(state.copyWith(dateTypes: dateTypes, updateSelectedDateType: selectedDateType != null ? true : false, resetFilter: _selectedDataFilterModel.fromDate != null && _selectedDataFilterModel.toDate != null ? false : true));
      final initialSelectedDateRange = _selectedDataFilterModel.dateRange ?? _selectedDataFilterModel.possessionTypeDateRange;
      if (state.selectedDateType?.value != ProspectDateType.possessionDate) {
        var dateRangeFilters = DateRange.values
            .map((e) => DataFilterItem(
                  displayName: e.description,
                  filterKey: ProspectFilterKey.dateRange,
                  isSelected: initialSelectedDateRange == getEnumFromDescription(DateRange.values, e.description),
                ))
            .toList();
        dateRangeFilters = dateRangeFilters.map((item) => item.displayName == DateRange.customDate.description ? item.copyWith(hasCustomView: true) : item).toList();
        _updateFilterCategory(ProspectFilterKey.dateRange, dateRangeFilters, emit);
      } else {
        List<DataFilterItem> dateRangeFilters = PossessionType.values
            .where((e) => e.value != 0) // or use e.value if you have a custom getter
            .map((e) => DataFilterItem<PossessionType>(
                  displayName: e.description,
                  value: e,
                  filterKey: ProspectFilterKey.dateRange,
                  isSelected: initialSelectedDateRange == getEnumFromDescription(PossessionType.values, e.description),
                ))
            .toList();
        dateRangeFilters = dateRangeFilters.map((item) => item.displayName == PossessionType.customDate.description ? item.copyWith(hasCustomView: true, hasMonthFilter: true) : item).toList();
        _updateFilterCategory(ProspectFilterKey.dateRange, dateRangeFilters, emit);
      }
    } catch (ex) {
      "Error while initializing  date range ${ex.toString()}".printInConsole();
    }
  }

  void updateDateRange(Emitter<DataFilterState> emit) {
    if (state.selectedDateType?.value != ProspectDateType.possessionDate) {
      var dateRangeFilters = DateRange.values
          .map((e) => DataFilterItem(
                displayName: e.description,
                filterKey: ProspectFilterKey.dateRange,
              ))
          .toList();
      dateRangeFilters = dateRangeFilters.map((item) => item.displayName == DateRange.customDate.description ? item.copyWith(hasCustomView: true) : item).toList();
      _updateFilterCategory(ProspectFilterKey.dateRange, dateRangeFilters, emit);
    } else {
      List<DataFilterItem> dateRangeFilters = PossessionType.values
          .where((e) => e.value != 0) // or use e.value if you have a custom getter
          .map((e) => DataFilterItem<PossessionType>(
                displayName: e.description,
                value: e,
                filterKey: ProspectFilterKey.dateRange,
              ))
          .toList();
      dateRangeFilters = dateRangeFilters.map((item) => item.displayName == PossessionType.customDate.description ? item.copyWith(hasCustomView: true, hasMonthFilter: true) : item).toList();
      _updateFilterCategory(ProspectFilterKey.dateRange, dateRangeFilters, emit);
    }
  }

  void _initNoOfBHKs(Emitter<DataFilterState> emit) {
    try {
      final initialSelectedNoOfBHKs = _selectedDataFilterModel.noOfBHKs;
      final dateRangeFilters = NoOfBHK.values
          .map((e) => DataFilterItem<double>(
                displayName: e.description,
                value: e.noOfBhk,
                filterKey: ProspectFilterKey.noOfBHK,
                isSelected: initialSelectedNoOfBHKs?.contains(e.noOfBhk) ?? false,
              ))
          .toList();
      _updateFilterCategory(ProspectFilterKey.noOfBHK, dateRangeFilters, emit);
    } catch (ex) {
      "Error while initializing no of bhk ${ex.toString()}".printInConsole();
    }
  }

  void _initBHKTypes(Emitter<DataFilterState> emit) {
    try {
      final initialSelectedBHKTypes = _selectedDataFilterModel.bhkTypes;
      final bhkTypes = BHKType.values
          .map((e) => DataFilterItem(
                displayName: e.description,
                filterKey: ProspectFilterKey.bHKTypes,
                isSelected: initialSelectedBHKTypes?.contains(e) ?? false,
              ))
          .toList();
      bhkTypes.removeWhere((element) => element.displayName == BHKType.none.description);
      _updateFilterCategory(ProspectFilterKey.bHKTypes, bhkTypes, emit);
    } catch (ex) {
      "Error while initializing bhkTypes ${ex.toString()}".printInConsole();
    }
  }

  void _initPropertyTypes(Emitter<DataFilterState> emit) {
    try {
      final initialSelectedPropertyTypes = _selectedDataFilterModel.propertyTypes;
      final propertyTypes = PropertyType.values
          .map((e) => DataFilterItem(
                displayName: e.description,
                description: e.baseId,
                filterKey: ProspectFilterKey.propertyType,
                isSelected: initialSelectedPropertyTypes?.contains(e.baseId) ?? false,
              ))
          .toList();
      _updateFilterCategory(ProspectFilterKey.propertyType, propertyTypes, emit);
    } catch (ex) {
      "Error while initializing propertyTypes ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initPropertySubTypes(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedPropertySubTypes = _selectedDataFilterModel.propertySubTypes;
      final masterPropertyTypes = await _masterDataRepository.getCustomPropertyTypes(isPropertyListingEnabled: getIt<LeadratHomeBloc>().isPropertyListingEnabled);
      if (masterPropertyTypes == null) return;
      _allPropertyTypes = masterPropertyTypes;
      List<DataFilterItem> subTypes = [];
      for (var item in masterPropertyTypes) {
        item.childTypes?.forEach((element) => subTypes.add(
              DataFilterItem(
                displayName: element.displayName ?? "",
                description: element.id,
                filterKey: ProspectFilterKey.propertySubType,
                isSelected: initialSelectedPropertySubTypes?.contains(element.id) ?? false,
              ),
            ));
      }
      subTypes.sort((a, b) => a.displayName.compareTo(b.displayName));
      _updateFilterCategory(ProspectFilterKey.propertySubType, subTypes, emit);
    } catch (ex) {
      "Error while initializing lead locations ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _updatePropertySubTypes(Emitter<DataFilterState> emit, List<DataFilterItem>? selectedPropertyTypes) async {
    if (_allPropertyTypes.isEmpty) await _initPropertySubTypes(emit);
    if (selectedPropertyTypes != null) {
      if (selectedPropertyTypes.isNotEmpty) {
        List<DataFilterItem> subTypes = [];
        for (var item in selectedPropertyTypes) {
          if (item.isSelected) {
            final selectedPropertySubTypes = _allPropertyTypes.firstWhereOrNull((e) => e.displayName == item.displayName);
            selectedPropertySubTypes?.childTypes?.forEach((element) => subTypes.add(DataFilterItem(
                  displayName: element.displayName ?? "",
                  description: element.id,
                  filterKey: ProspectFilterKey.propertySubType,
                )));
          }
        }
        _updateFilterCategory(ProspectFilterKey.propertySubType, subTypes, emit);
      } else {
        _initPropertySubTypes(emit);
      }
    }
  }

  Future<void> _initAssignedToUsers(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedAssignedToUsersId = _selectedDataFilterModel.userIds;
      bool hasPermission = (getIt<UsersDataRepository>().checkHasPermission(AppModule.prospect, CommandType.viewAllProspects) && getIt<UsersDataRepository>().checkHasPermission(AppModule.users, CommandType.viewForFilter));
      final getAllUsers = hasPermission ? await _usersDataRepository.getAllUsers() : await _usersDataRepository.getAllReportees();
      final assignToUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => DataFilterItem(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                filterKey: ProspectFilterKey.assignedTo,
                isSelected: initialSelectedAssignedToUsersId?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      assignToUsers?.insert(0, DataFilterItem(displayName: "You", description: _currentUser?.userId ?? "", filterKey: ProspectFilterKey.assignedTo, isSelected: initialSelectedAssignedToUsersId?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => assignToUsers?.add(DataFilterItem(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: ProspectFilterKey.assignedTo, isSelected: initialSelectedAssignedToUsersId?.contains(e?.id ?? "") ?? false)));
      if (assignToUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(ProspectFilterKey.assignedTo, assignToUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing assigned to users ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initAssignedFromUsers(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedAssignedFromUsersId = _selectedDataFilterModel.assignedFromIds;
      bool hasPermission = (getIt<UsersDataRepository>().checkHasPermission(AppModule.prospect, CommandType.viewAllProspects) && getIt<UsersDataRepository>().checkHasPermission(AppModule.users, CommandType.viewForFilter));
      final getAllUsers = hasPermission ? await _usersDataRepository.getAllUsers() : await _usersDataRepository.getAllReportees();
      final assignFromUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => DataFilterItem(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                filterKey: ProspectFilterKey.assignedFrom,
                isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      assignFromUsers?.insert(0, DataFilterItem(displayName: "You", description: _currentUser?.userId ?? "", filterKey: ProspectFilterKey.assignedFrom, isSelected: initialSelectedAssignedFromUsersId?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => assignFromUsers?.add(DataFilterItem(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: ProspectFilterKey.assignedFrom, isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false)));
      if (assignFromUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(ProspectFilterKey.assignedFrom, assignFromUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing assigned from users ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initCreatedByUsers(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedCreatedByUsersId = _selectedDataFilterModel.createdByIds;
      final getAllUsers = await _usersDataRepository.getAllReportees();
      final createdByUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => DataFilterItem(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                filterKey: ProspectFilterKey.createdBy,
                isSelected: initialSelectedCreatedByUsersId?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      createdByUsers?.insert(0, DataFilterItem(displayName: "You", description: _currentUser?.userId ?? "", filterKey: ProspectFilterKey.createdBy, isSelected: initialSelectedCreatedByUsersId?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => createdByUsers?.add(DataFilterItem(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: ProspectFilterKey.createdBy, isSelected: initialSelectedCreatedByUsersId?.contains(e?.id ?? "") ?? false)));
      if (createdByUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(ProspectFilterKey.createdBy, createdByUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing created by users ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initQualifiedByUsers(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedQualifiedByUsersId = _selectedDataFilterModel.qualifiedByIds;
      final getAllUsers = await _usersDataRepository.getAllReportees();
      final qualifiedByUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => DataFilterItem(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                filterKey: ProspectFilterKey.qualifiedBy,
                isSelected: initialSelectedQualifiedByUsersId?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      qualifiedByUsers?.insert(0, DataFilterItem(displayName: "You", description: _currentUser?.userId ?? "", filterKey: ProspectFilterKey.qualifiedBy, isSelected: initialSelectedQualifiedByUsersId?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => qualifiedByUsers?.add(DataFilterItem(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: ProspectFilterKey.qualifiedBy, isSelected: initialSelectedQualifiedByUsersId?.contains(e?.id ?? "") ?? false)));
      if (qualifiedByUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(ProspectFilterKey.qualifiedBy, qualifiedByUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing qualified by users ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initConvertedByUsers(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedConvertedByUsersId = _selectedDataFilterModel.convertedByIds;
      final getAllUsers = await _usersDataRepository.getAllReportees();
      final convertedByUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => DataFilterItem(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                filterKey: ProspectFilterKey.convertedBy,
                isSelected: initialSelectedConvertedByUsersId?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      convertedByUsers?.insert(0, DataFilterItem(displayName: "You", description: _currentUser?.userId ?? "", filterKey: ProspectFilterKey.convertedBy, isSelected: initialSelectedConvertedByUsersId?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => convertedByUsers?.add(DataFilterItem(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: ProspectFilterKey.convertedBy, isSelected: initialSelectedConvertedByUsersId?.contains(e?.id ?? "") ?? false)));
      if (convertedByUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(ProspectFilterKey.convertedBy, convertedByUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing converted by users ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initProperties(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedProperties = _selectedDataFilterModel.properties;
      final propertiesNameWithIdResponse = await _getPropertyNameWithIdUseCase(NoParams());
      propertiesNameWithIdResponse.fold(
        (failure) => null,
        (propertiesNameWithId) {
          final dataProperties = propertiesNameWithId
              ?.map((item) => DataFilterItem(
                    displayName: item.title ?? "",
                    description: item.id,
                    filterKey: ProspectFilterKey.properties,
                    isSelected: initialSelectedProperties?.contains(item.title) ?? false,
                  ))
              .toList();
          if (dataProperties?.isNotEmpty ?? false) {
            _updateFilterCategory(ProspectFilterKey.properties, dataProperties!, emit);
          }
        },
      );
    } catch (ex) {
      "Error while initializing data properties ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initProjects(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedProjects = _selectedDataFilterModel.projects;
      final projectsNameWithIdResponse = await _getProjectNameWithIdUseCase(NoParams());
      projectsNameWithIdResponse.fold(
        (failure) => null,
        (propertiesNameWithId) {
          final dataProjects = propertiesNameWithId
              ?.map((item) => DataFilterItem(
                    displayName: item.name ?? "",
                    description: item.id,
                    filterKey: ProspectFilterKey.projects,
                    isSelected: initialSelectedProjects?.contains(item.name) ?? false,
                  ))
              .toList();
          if (dataProjects?.isNotEmpty ?? false) {
            _updateFilterCategory(ProspectFilterKey.projects, dataProjects!, emit);
          }
        },
      );
    } catch (ex) {
      "Error while initializing data projects ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initMinBudgetFilters(Emitter<DataFilterState> emit) async {
    try {
      await _initializeCurrency(emit, _selectedDataFilterModel.currency);

      if (_selectedDataFilterModel.toMinBudget == null && _selectedDataFilterModel.fromMinBudget == null) emit(state.copyWith(updateSelectMinBudget: false));

      final initialSelectMinBudget = _selectedDataFilterModel.fromMinBudget ?? _selectedDataFilterModel.toMinBudget;

      emit(state.copyWith(
        updateSelectMinBudget: initialSelectMinBudget != null ? true : false,
      ));
    } catch (ex) {
      "Error while initializing carpet area units ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initMaxBudgetFilters(Emitter<DataFilterState> emit) async {
    try {
      await _initializeCurrency(emit, _selectedDataFilterModel.currency);

      if (_selectedDataFilterModel.toMaxBudget == null && _selectedDataFilterModel.fromMaxBudget == null) emit(state.copyWith(updateSelectMaxBudget: false));

      final initialSelectMaxBudget = _selectedDataFilterModel.fromMaxBudget ?? _selectedDataFilterModel.toMaxBudget;

      emit(state.copyWith(
        updateSelectMaxBudget: initialSelectMaxBudget != null ? true : false,
      ));
    } catch (ex) {
      "Error while initializing carpet area units ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initAgencyNames(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedAgencyNames = _selectedDataFilterModel.agencyNames;
      final getAgencyNames = await _masterDataRepository.getAgencyNames();
      final agencyNames = getAgencyNames
          ?.map((e) => DataFilterItem(
                displayName: e,
                filterKey: ProspectFilterKey.agencyName,
                isSelected: initialSelectedAgencyNames?.contains(e) ?? false,
              ))
          .toList();
      if (agencyNames?.isNotEmpty ?? false) {
        _updateFilterCategory(ProspectFilterKey.agencyName, agencyNames!, emit);
      }
    } catch (ex) {
      "Error while initializing agency names ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initCampaignNames(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedCampaignNames = _selectedDataFilterModel.campaignNames;
      final getCampaignNames = await _leadsRepository.getCampaignNames();
      final campaignNames = getCampaignNames
          ?.map((e) => DataFilterItem(
                displayName: e,
                filterKey: ProspectFilterKey.campaigns,
                isSelected: initialSelectedCampaignNames?.contains(e) ?? false,
              ))
          .toList();
      if (campaignNames?.isNotEmpty ?? false) {
        _updateFilterCategory(ProspectFilterKey.campaigns, campaignNames!, emit);
      }
    } catch (ex) {
      "Error while initializing Campaign names ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initProspectStatus(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedProspectStatus = _selectedDataFilterModel.statusIds?.map((e) => e);
      final getProspectStatus = await _getProspectStatusUseCase(NoParams());
      getProspectStatus.fold(
        (failure) => null,
        (success) {
          final prospectStatus = success
              ?.map((item) => DataFilterItem(
                    displayName: item?.displayName ?? "",
                    description: item?.id,
                    filterKey: ProspectFilterKey.status,
                    isSelected: initialSelectedProspectStatus?.contains(item?.id) ?? false,
                    value: item,
                  ))
              .toList();
          if (prospectStatus?.isNotEmpty ?? false) {
            _updateFilterCategory(ProspectFilterKey.status, prospectStatus!, emit);
          }
        },
      );
    } catch (ex) {
      "Error while initializing prospect status ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initProspectSource(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedProspectSource = _selectedDataFilterModel.sources?.map((e) => e).toList();
      final getProspectSources = await _getProspectSubSourceUseCase(NoParams());
      getProspectSources.fold(
        (failure) => null,
        (prospectSource) {
          final prospectSources = prospectSource
              ?.where((element) => element?.source?.isEnabled ?? false)
              .map((item) => DataFilterItem(
                    displayName: item?.source?.displayName ?? "",
                    description: item?.source?.id,
                    filterKey: ProspectFilterKey.source,
                    isSelected: initialSelectedProspectSource?.contains(item?.source?.id) ?? false,
                    value: item,
                  ))
              .toList();
          if (prospectSources?.isNotEmpty ?? false) {
            _updateFilterCategory(ProspectFilterKey.source, prospectSources!, emit);
          }
        },
      );
    } catch (ex) {
      "Error while initializing prospect sources ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initProspectSubSource(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedSubSource = _selectedDataFilterModel.subSources;
      final prospectSubSource = await _getProspectSubSourceUseCase(NoParams());

      prospectSubSource.fold((l) => null, (prospectSource) {
        if (prospectSource != null) {
          _allProspectSource = prospectSource.where((element) => element?.source?.isEnabled ?? false).toList();

          final Set<String> seenSubSources = <String>{};
          final Set<String> selectedSubSources = initialSelectedSubSource?.toSet() ?? <String>{};
          final List<DataFilterItem> subTypes = [];

          for (final item in _allProspectSource) {
            final subSourceList = item?.subSource;
            if (subSourceList == null) continue;

            final sourceDisplayName = item?.source?.displayName;

            for (final subSourceName in subSourceList) {
              if (!seenSubSources.contains(subSourceName)) {
                seenSubSources.add(subSourceName);

                subTypes.add(DataFilterItem(
                  displayName: subSourceName,
                  description: sourceDisplayName,
                  filterKey: ProspectFilterKey.subSource,
                  isSelected: selectedSubSources.contains(subSourceName),
                ));
              }
            }
          }
          subTypes.sort((a, b) => a.displayName.compareTo(b.displayName));
          _updateFilterCategory(ProspectFilterKey.subSource, subTypes, emit);
        }
      });
    } catch (ex) {
      "Error while initializing prospect sub source ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _updateProspectSubSource(Emitter<DataFilterState> emit, List<DataFilterItem>? selectedLeadSource) async {
    if (_allProspectSource.isEmpty) await _initProspectSubSource(emit);
    if (selectedLeadSource != null && selectedLeadSource.isNotEmpty) {
      List<DataFilterItem> subSource = [];
      for (var item in selectedLeadSource) {
        if (item.isSelected) {
          final selectedSource = _allProspectSource.firstWhereOrNull((e) => e?.source?.displayName == item.displayName);
          selectedSource?.subSource?.forEach((name) => subSource.add(DataFilterItem(displayName: name, filterKey: ProspectFilterKey.propertySubType)));
        }
      }
      _updateFilterCategory(ProspectFilterKey.subSource, subSource, emit);
    } else {
      await _initProspectSubSource(emit);
    }
  }

  Future<void> _initProspectLocations(Emitter<DataFilterState> emit) async {
    try {
      final initialSelectedDataAddresses = _selectedDataFilterModel.locations;
      final getProspectAddresses = await _getProspectLocationUseCase(NoParams());
      getProspectAddresses.fold(
        (failure) => null,
        (addresses) {
          final prospectAddresses = addresses
              ?.whereNot((element) => element.isNullOrEmpty())
              .map((address) => DataFilterItem(
                    displayName: address,
                    filterKey: ProspectFilterKey.locations,
                    isSelected: initialSelectedDataAddresses?.contains(address) ?? false,
                  ))
              .toList();
          if (prospectAddresses?.isNotEmpty ?? false) {
            _updateFilterCategory(ProspectFilterKey.locations, prospectAddresses!, emit);
          }
        },
      );
    } catch (ex) {
      "Error while initializing lead locations ${ex.toString()}".printInConsole();
    }
  }

  FutureOr<void> _onFilterCategorySelect(FilterCategorySelectEvent event, Emitter<DataFilterState> emit) async {
    var selectedItem = event.dataFilterCategoryItem;
    var updatedLeadCategories = state.dataFilterCategories
        .map((category) => category.filterKey == selectedItem.filterKey
            ? selectedItem.copyWith(isSelected: true, isInitialized: (selectedItem.filterKey == ProspectFilterKey.pinCode) ? true : selectedItem.filters?.isNotEmpty ?? false, searchController: TextEditingController())
            : category.copyWith(
                isSelected: false,
              ))
        .toList();
    _updateState(emit, dataFilterCategories: updatedLeadCategories, selectedCategoryIndex: event.selectedCategoryIndex);

    if ((selectedItem.filters?.isEmpty ?? true)) {
      await _initializeFilter(selectedItem.filterKey, null, emit);
    }
  }

  FutureOr<void> _onSelectFilter(SelectFilterEvent event, Emitter<DataFilterState> emit) async {
    var selectedFilterCategory = state.dataFilterCategories.firstWhereOrNull((element) => element.filterKey == event.selectedFilterCategory.filterKey);
    List<DataFilterItem>? updatedFilterList;

    //check for date range filter
    if (selectedFilterCategory?.filterKey == ProspectFilterKey.dateRange && state.selectedDateType?.value == null) {
      emit(state.copyWith(pageState: PageState.failure, errorMessage: "Please select the date type"));
      return null;
    }

    if (selectedFilterCategory == null) return;
    selectedFilterItem = event.dataFilterItem;
    if (event.isSelectAll) {
      if (selectedFilterCategory.searchController?.text.isNullOrEmpty() ?? true) {
        updatedFilterList = selectedFilterCategory.filters?.map((e) => e.copyWith(isSelected: !(selectedFilterCategory.isAllSelected))).toList();
      } else {
        final searchedFilters = state.searchFilteredCategories.firstWhereOrNull((element) => element.filterKey == event.selectedFilterCategory.filterKey);
        updatedFilterList = selectedFilterCategory.filters?.map((item) {
          if (searchedFilters?.filters?.any((element) => element.displayName == item.displayName && element.description == item.description) ?? false) {
            return item.copyWith(isSelected: !(selectedFilterCategory.isAllSelected));
          }
          return item;
        }).toList();
      }
    } else if (event.dataFilterItem != null) {
      var selectedFilterItem = event.dataFilterItem!;
      updatedFilterList = selectedFilterCategory.filters?.map((item) {
        if (item.displayName == selectedFilterItem.displayName && item.description == selectedFilterItem.description) {
          return selectedFilterItem.copyWith(isSelected: !selectedFilterItem.isSelected);
        }
        return selectedFilterCategory.hasMultiSelect ? item : item.copyWith(isSelected: false);
      }).toList();
    }

    if (selectedFilterCategory.filterKey == ProspectFilterKey.propertyType) {
      final selectedPropertyTypes = updatedFilterList?.where((element) => element.isSelected).toList();
      await _updatePropertySubTypes(emit, selectedPropertyTypes);
    } else if (selectedFilterItem?.filterKey == ProspectFilterKey.source) {
      final selectedLeadSource = updatedFilterList?.where((element) => element.isSelected).toList();
      await _updateProspectSubSource(emit, selectedLeadSource);
    }

    if (updatedFilterList != null) {
      var updatedCategoryList = state.dataFilterCategories.map((e) => e.filterKey == selectedFilterCategory.filterKey ? selectedFilterCategory.copyWith(filters: updatedFilterList) : e).toList();
      _updateState(emit, dataFilterCategories: updatedCategoryList);
    }
    if (selectedFilterCategory.searchController?.text.isNotNullOrEmpty() ?? false) {
      add(SearchFiltersItemsEvent(searchText: selectedFilterCategory.searchController?.text, filterKey: event.selectedFilterCategory.filterKey));
    }
  }

  FutureOr<void> _onSearchFiltersItems(SearchFiltersItemsEvent event, Emitter<DataFilterState> emit) async {
    if (event.searchText == null) return;
    final selectedFilterCategoryFilters = state.dataFilterCategories.firstWhereOrNull((category) => category.filterKey == event.filterKey)?.filters;
    final filteredList = selectedFilterCategoryFilters?.where((element) => element.displayName.toLowerCase().contains(event.searchText!.toLowerCase())).toList();
    final filteredCategories = state.dataFilterCategories.map((category) {
      if (category.filterKey == event.filterKey) return category.copyWith(filters: filteredList);
      return category;
    }).toList();
    emit(state.copyWith(searchFilteredCategories: filteredCategories));
  }

  FutureOr<void> _onResetFilter(ResetFilterEvent event, Emitter<DataFilterState> emit) {
    emit(state.copyWith(
      resetFilter: true,
      selectedCategoryIndex: 0,
      updateSelectBuildUpAreaUnit: false,
      updateSelectCarpetAreaUnit: false,
      updateSelectNetAreaUnit: false,
      updateSelectPropertyAreaUnit: false,
      updateSelectSalableAreaUnit: false,
      updateSelectedDateType: false,
      updateSelectCarpetArea: false,
      updateSelectSaleableArea: false,
      updateSelectBuiltArea: false,
      updateSelectPropertyArea: false,
      updateSelectMaxBudget: false,
      updateSelectMinBudget: false,
    ));
    final updatedDataFilterCategories = state.dataFilterCategories.map((category) {
      return category.copyWith(
        pinCodeController: category.pinCodeController != null ? TextEditingController() : null,
        searchController: category.searchController != null ? TextEditingController() : null,
        filters: category.filters?.map((filter) => filter.copyWith(isSelected: false)).toList(),
      );
    }).toList();
    _updateState(
      emit,
      dataFilterCategories: updatedDataFilterCategories,
      dataFilterModel: DataFilterModel(prospectVisiblity: ProspectVisibility.selfWithReportee),
      pageState: PageState.initial,
      selectedToDate: null,
      selectedFromDate: null,
      customBudgetRange: state.customBudget,
      selectedCurrency: null,
      allCurrencies: [],
      selectedDateType: null,
    );
    _selectedDataFilterModel = DataFilterModel();
    add(FilterCategorySelectEvent(selectedCategoryIndex: state.selectedCategoryIndex, dataFilterCategoryItem: dataFilterCategories[state.selectedCategoryIndex]));
  }

  FutureOr<void> _onSelectFromDate(SelectFromDateEvent event, Emitter<DataFilterState> emit) {
    emit(state.copyWith(selectedFromDate: event.selectedFromDate));
  }

  FutureOr<void> _onSelectToDate(SelectToDateEvent event, Emitter<DataFilterState> emit) {
    emit(state.copyWith(selectedToDate: event.selectedToDate));
  }

  FutureOr<void> _onCustomBudgetChange(CustomBudgetChangeEvent event, Emitter<DataFilterState> emit) {
    emit(state.copyWith(customBudget: state.customBudget.copyWith(minBudget: event.startValue, maxBudget: event.endValue)));
  }

  FutureOr<void> _onSelectDateType(SelectDateTypeEvent event, Emitter<DataFilterState> emit) {
    emit(state.copyWith(selectedDateType: event.selectedDateType, errorMessage: ''));
    updateDateRange(emit);
  }

  FutureOr<void> _onChangeCurrency(ChangeCurrencyEvent event, Emitter<DataFilterState> emit) {
    _updateState(emit, selectedCurrency: event.selectedCurrency);
  }

  void _initPurposes(Emitter<DataFilterState> emit) {
    try {
      final initialSelectedPurposes = _selectedDataFilterModel.purposes;
      final purposes = PurposeEnum.values.where((e) => e != PurposeEnum.none).map((e) => DataFilterItem<PurposeEnum>(displayName: e.description, value: e, filterKey: ProspectFilterKey.purpose, isSelected: initialSelectedPurposes?.contains(e) ?? false)).toList();
      _updateFilterCategory(ProspectFilterKey.purpose, purposes, emit);
    } catch (ex) {
      "Error while initializing Purposes  ${ex.toString()}".printInConsole();
    }
  }

  FutureOr<void> _onApplyDataFilter(ApplyDataFilterEvent event, Emitter<DataFilterState> emit) {
    var dataFilterModel = DataFilterModel();
    String? fromDate, toDate;
    var selectedFilterCategories = state.dataFilterCategories.where((category) => category.filters?.any((filter) => filter.isSelected) ?? false).toList();
    final selectedDateRange = DataFilterItem.getSelectedEnums(selectedFilterCategories, ProspectFilterKey.dateRange, DateRange.values)?.firstOrNull;
    final selectedPossessionDateRange = DataFilterItem.getSelectedEnums<PossessionType>(selectedFilterCategories, ProspectFilterKey.dateRange, PossessionType.values)?.firstOrNull;
    if (selectedDateRange != null && selectedDateRange == DateRange.customDate) {
      fromDate = utcToDateFormat(state.selectedFromDate ?? DateTime.now());
      toDate = utcToDateFormat(state.selectedToDate ?? DateTime.now());
    } else if (selectedDateRange != null) {
      var selectedDate = _getDateTimeFromRange(selectedDateRange);
      fromDate = selectedDate.$2;
      toDate = selectedDate.$1;
    }
    BudgetRangeModel? selectedBudget;
    if (getIt<LeadratHomeBloc>().globalSettings?.isCustomLeadFormEnabled ?? false) {
      selectedBudget = DataFilterItem.getSelectedEnums(selectedFilterCategories, ProspectFilterKey.budget, InternationalBudgetEnum.values)?.firstOrNull?.budget;
    } else {
      selectedBudget = DataFilterItem.getSelectedEnums(selectedFilterCategories, ProspectFilterKey.budget, BudgetEnum.values)?.firstOrNull?.budget;
    }
    if (selectedPossessionDateRange != null && selectedPossessionDateRange == PossessionType.customDate) {
      fromDate = utcToDateFormat(state.selectedFromDate?.toUniversalTimeStartOfDay() ?? DateTime.now().toUserTimeZone());
      toDate = utcToDateFormat(state.selectedToDate?.toUniversalTimeStartOfDay() ?? DateTime.now().toUserTimeZone());
    }
    final isCustomBudgetRange = selectedBudget?.isCustomBudget ?? false;
    final budgetFilter = isCustomBudgetRange ? state.customBudget : selectedBudget;
    final prospectVisibility = selectedFilterCategories.firstWhereOrNull((element) => element.filterKey == ProspectFilterKey.prospectVisibility)?.filters?.firstWhereOrNull((element) => element.isSelected);

    dataFilterModel = dataFilterModel.copyWith(
      prospectVisiblity: prospectVisibility?.value,
      sources: DataFilterItem.getSelectedDescription(selectedFilterCategories, ProspectFilterKey.source),
      subSources: DataFilterItem.getSelectedDisplayNames(selectedFilterCategories, ProspectFilterKey.subSource),
      statusIds: DataFilterItem.getSelectedDescription(selectedFilterCategories, ProspectFilterKey.status),
      userIds: DataFilterItem.getSelectedDescription(selectedFilterCategories, ProspectFilterKey.assignedTo),
      assignedFromIds: DataFilterItem.getSelectedDescription(selectedFilterCategories, ProspectFilterKey.assignedFrom),
      enquiryTypes: DataFilterItem.getSelectedEnums<EnquiryType>(selectedFilterCategories, ProspectFilterKey.enquiredFor, EnquiryType.values),
      projects: DataFilterItem.getSelectedDisplayNames(selectedFilterCategories, ProspectFilterKey.projects),
      properties: DataFilterItem.getSelectedDisplayNames(selectedFilterCategories, ProspectFilterKey.properties),
      dateType: state.selectedDateType?.value,
      dateRange: selectedDateRange != null ? getEnumFromDescription(DateRange.values, selectedDateRange.description) : null,
      possessionTypeDateRange: DataFilterItem.getSelectedEnums<PossessionType>(selectedFilterCategories, ProspectFilterKey.dateRange, PossessionType.values)?.firstOrNull,
      fromDate: fromDate,
      toDate: toDate,
      noOfBHKs: DataFilterItem.getSelectedValue<double>(selectedFilterCategories, ProspectFilterKey.noOfBHK),
      bhkTypes: DataFilterItem.getSelectedEnums(selectedFilterCategories, ProspectFilterKey.bHKTypes, BHKType.values),
      propertyTypes: DataFilterItem.getSelectedDescription(selectedFilterCategories, ProspectFilterKey.propertyType),
      propertySubTypes: DataFilterItem.getSelectedDescription(selectedFilterCategories, ProspectFilterKey.propertySubType),
      locations: DataFilterItem.getSelectedDisplayNames(selectedFilterCategories, ProspectFilterKey.locations),
      agencyNames: DataFilterItem.getSelectedDisplayNames(selectedFilterCategories, ProspectFilterKey.agencyName),
      createdByIds: DataFilterItem.getSelectedDescription(selectedFilterCategories, ProspectFilterKey.createdBy),
      qualifiedByIds: DataFilterItem.getSelectedDescription(selectedFilterCategories, ProspectFilterKey.qualifiedBy),
      convertedByIds: DataFilterItem.getSelectedDescription(selectedFilterCategories, ProspectFilterKey.convertedBy),
      campaignNames: DataFilterItem.getSelectedDisplayNames(selectedFilterCategories, ProspectFilterKey.campaigns),
      currency: (state.minBudget != null || state.maxBudget != null) ? state.selectedCurrency?.value : null,
      furnished: DataFilterItem.getSelectedValue<int>(selectedFilterCategories, ProspectFilterKey.furnished),
      beds: DataFilterItem.getSelectedValue<int>(selectedFilterCategories, ProspectFilterKey.beds),
      baths: DataFilterItem.getSelectedValue<int>(selectedFilterCategories, ProspectFilterKey.baths),
      floors: DataFilterItem.getSelectedValue<String>(selectedFilterCategories, ProspectFilterKey.floors),
      offerTypes: DataFilterItem.getSelectedValue<int>(selectedFilterCategories, ProspectFilterKey.offerTypes),
      communities: DataFilterItem.getSelectedDisplayNames(selectedFilterCategories, ProspectFilterKey.community),
      subCommunities: DataFilterItem.getSelectedDisplayNames(selectedFilterCategories, ProspectFilterKey.subCommunity),
      towerNames: DataFilterItem.getSelectedDisplayNames(selectedFilterCategories, ProspectFilterKey.towerName),
      countries: DataFilterItem.getSelectedDisplayNames(selectedFilterCategories, ProspectFilterKey.country),
      pincode: state.dataFilterCategories.firstWhereOrNull((category) => category.filterKey == ProspectFilterKey.pinCode)?.pinCodeController?.text ?? '',
      builtUpAreaUnitId: state.selectedBuildUpAreaUnit?.value?.id,
      builtUpArea: (state.dataFilterCategories.firstWhereOrNull((category) => category.filterKey == ProspectFilterKey.buildUpArea)?.pinCodeController?.text ?? '').wordToDouble(),
      carpetArea: (state.dataFilterCategories.firstWhereOrNull((category) => category.filterKey == ProspectFilterKey.carpetArea)?.pinCodeController?.text ?? '').wordToDouble(),
      saleableArea: (state.dataFilterCategories.firstWhereOrNull((category) => category.filterKey == ProspectFilterKey.salableArea)?.pinCodeController?.text ?? '').wordToDouble(),
      netArea: (state.dataFilterCategories.firstWhereOrNull((category) => category.filterKey == ProspectFilterKey.netArea)?.pinCodeController?.text ?? '').wordToDouble(),
      propertyArea: (state.dataFilterCategories.firstWhereOrNull((category) => category.filterKey == ProspectFilterKey.propertyArea)?.pinCodeController?.text ?? '').wordToDouble(),
      carpetAreaUnitId: state.selectedCarpetAreaUnit?.value?.id,
      saleableAreaUnitId: state.selectedSalableAreaUnit?.value?.id,
      netAreaUnitId: state.selectedNetAreaUnit?.value?.id,
      propertyAreaUnitId: state.selectedPropertyAreaUnit?.value?.id,
      unitNames: DataFilterItem.getSelectedDisplayNames(selectedFilterCategories, ProspectFilterKey.unitNumberOrName),
      clusterNames: DataFilterItem.getSelectedDisplayNames(selectedFilterCategories, ProspectFilterKey.clusterName),
      nationality: DataFilterItem.getSelectedDisplayNames(selectedFilterCategories, ProspectFilterKey.nationality),
      purposes: DataFilterItem.getSelectedEnums<PurposeEnum>(selectedFilterCategories, ProspectFilterKey.purpose, PurposeEnum.values),
      minBuiltUpArea: state.builtUpArea?.min,
      maxBuiltUpArea: state.builtUpArea?.max,
      minSaleAbleArea: state.saleableArea?.min,
      maxSaleableArea: state.saleableArea?.max,
      minCarpetArea: state.carpetArea?.min,
      maxCarpetArea: state.carpetArea?.max,
      maxPropertyArea: state.propertyArea?.max,
      minPropertyArea: state.propertyArea?.min,
      toMinBudget: state.minBudget?.max,
      fromMinBudget: state.minBudget?.min,
      toMaxBudget: state.maxBudget?.max,
      fromMaxBudget: state.maxBudget?.min,
      maxNetArea: state.netArea?.max,
      minNetArea: state.netArea?.min,
      excelSheets: DataFilterItem.getSelectedValue(selectedFilterCategories, ProspectFilterKey.excelSheet),
    );
    _updateState(emit, pageState: PageState.success, dataFilterModel: dataFilterModel);
  }

  String? utcToDateFormat(DateTime? date) {
    if (date == null) return null;
    DateTime utcDate = date.toUniversalTimeStartOfDay();
    return utcDate.toString();
  }

  (String?, String?) _getDateTimeFromRange(DateRange selectedDateRange) {
    switch (selectedDateRange) {
      case DateRange.today:
        return (utcToDateFormat(DateTime.now()), utcToDateFormat(DateTime.now()));
      case DateRange.yesterday:
        return (utcToDateFormat(DateTime.now().subtract(const Duration(days: 1))), utcToDateFormat(DateTime.now().subtract(const Duration(days: 1))));
      case DateRange.lastSevenDays:
        return (utcToDateFormat(DateTime.now()), utcToDateFormat(DateTime.now().subtract(const Duration(days: 7))));
      case DateRange.lastTwentyEightDays:
        return (utcToDateFormat(DateTime.now()), utcToDateFormat(DateTime.now().subtract(const Duration(days: 28))));
      case DateRange.customDate:
        return (null, null);
    }
  }

  FutureOr<void> _onOnMinBudgetChangeEvent(OnMinBudgetChangeEvent event, Emitter<DataFilterState> emit) {
    emit(state.copyWith(minBudget: event.rangeInput));
  }

  FutureOr<void> _onOnMaxBudgetChangeEvent(OnMaxBudgetChangeEvent event, Emitter<DataFilterState> emit) {
    emit(state.copyWith(maxBudget: event.rangeInput));
  }

  (SelectableItem<MasterAreaUnitsModel>?, AreaType) getAreaUnitDetails(DataFilterCategoryItem? selectedCategoryItem) {
    SelectableItem<MasterAreaUnitsModel>? selectedAreaUnit;
    AreaType areaType = AreaType.carpetArea;
    if (selectedCategoryItem?.filterKey == ProspectFilterKey.carpetArea) {
      selectedAreaUnit = state.selectedCarpetAreaUnit;
      areaType = AreaType.carpetArea;
      areaType = AreaType.carpetArea;
    } else if (selectedCategoryItem?.filterKey == ProspectFilterKey.buildUpArea) {
      selectedAreaUnit = state.selectedBuildUpAreaUnit;
      areaType = AreaType.buildUpArea;
    } else if (selectedCategoryItem?.filterKey == ProspectFilterKey.salableArea) {
      selectedAreaUnit = state.selectedSalableAreaUnit;
      areaType = AreaType.salableArea;
    } else if (selectedCategoryItem?.filterKey == ProspectFilterKey.netArea) {
      selectedAreaUnit = state.selectedNetAreaUnit;
      areaType = AreaType.netArea;
    } else if (selectedCategoryItem?.filterKey == ProspectFilterKey.propertyArea) {
      selectedAreaUnit = state.selectedPropertyAreaUnit;
      areaType = AreaType.propertyArea;
    }
    return (selectedAreaUnit, areaType);
  }

  FutureOr<void> _onAreaSizeInputChangeEvent(AreaSizeInputChangeEvent event, Emitter<DataFilterState> emit) {
    if (event.filterKey == ProspectFilterKey.carpetArea) {
      emit(state.copyWith(carpetArea: event.rangeInput));
    }
    if (event.filterKey == ProspectFilterKey.netArea) {
      emit(state.copyWith(netArea: event.rangeInput));
    }
    if (event.filterKey == ProspectFilterKey.propertyArea) {
      emit(state.copyWith(propertyArea: event.rangeInput));
    }
    if (event.filterKey == ProspectFilterKey.buildUpArea) {
      emit(state.copyWith(builtUpArea: event.rangeInput));
    }
    if (event.filterKey == ProspectFilterKey.salableArea) {
      emit(state.copyWith(saleableArea: event.rangeInput));
    }
  }

  FutureOr<void> _onAreaUnitChanged(AreaUnitChangedEvent event, Emitter<DataFilterState> emit) {
    if (event.filterKey == ProspectFilterKey.carpetArea) {
      if (event.selectedAreaUnit?.isSelected ?? false) {
        emit(state.copyWith(selectedCarpetAreaUnit: event.selectedAreaUnit));
      } else {
        emit(state.copyWith(updateSelectCarpetAreaUnit: false));
      }
    }
    if (event.filterKey == ProspectFilterKey.netArea) {
      if (event.selectedAreaUnit?.isSelected ?? false) {
        emit(state.copyWith(selectedNetAreaUnit: event.selectedAreaUnit));
      } else {
        emit(state.copyWith(updateSelectNetAreaUnit: false));
      }
    }
    if (event.filterKey == ProspectFilterKey.propertyArea) {
      if (event.selectedAreaUnit?.isSelected ?? false) {
        emit(state.copyWith(selectedPropertyAreaUnit: event.selectedAreaUnit));
      } else {
        emit(state.copyWith(updateSelectPropertyArea: false));
      }
    }
    if (event.filterKey == ProspectFilterKey.buildUpArea) {
      if (event.selectedAreaUnit?.isSelected ?? false) {
        emit(state.copyWith(selectedBuildUpAreaUnit: event.selectedAreaUnit));
      } else {
        emit(state.copyWith(updateSelectBuildUpAreaUnit: false));
      }
    }
    if (event.filterKey == ProspectFilterKey.salableArea) {
      if (event.selectedAreaUnit?.isSelected ?? false) {
        emit(state.copyWith(selectedSalableAreaUnit: event.selectedAreaUnit));
      } else {
        emit(state.copyWith(updateSelectSalableAreaUnit: false));
      }
    }
  }

  Future<void> _initExcelSheets(Emitter<DataFilterState> emit) async {
    try {
      List<String>? initSelectedExcelSheets = _selectedDataFilterModel.excelSheets;
      final excelSheetsResponse = await _getProspectExcelDataUseCase(NoParams());
      excelSheetsResponse.fold(
        (failure) => null,
        (success) {
          if (success != null && success.isNotEmpty) {
            final selectedAgencyNames = initSelectedExcelSheets?.nonNulls.map((e) => e).toList();
            final channelPartners = success
                .map((name) => DataFilterItem<String>(
                      displayName: name,
                      description: name,
                      filterKey: ProspectFilterKey.excelSheet,
                      value: name,
                      isSelected: selectedAgencyNames?.contains(name) ?? false,
                    ))
                .toList();
            _updateFilterCategory(ProspectFilterKey.excelSheet, channelPartners, emit);
          }
        },
      );
    } catch (ex) {
      "Error while initializing excel sheets ${ex.toString()}".printInConsole();
    }
  }
}
