import 'dart:async';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_environment.dart';
import 'package:leadrat/core_main/enums/app_enum/white_labeled_enum.dart';
import 'package:leadrat/core_main/managers/shared_preference_manager/token_manager.dart';
import 'package:leadrat/core_main/resources/common/rest_resources.dart';
import 'package:leadrat/core_main/resources/theme/app_theme.dart';
import 'package:leadrat/core_main/services/secret_manager_service/secret_manager_service.dart';
import 'package:leadrat/features/auth/data/data_source/local/auth_local_data_source.dart';
import 'package:leadrat/features/home/<USER>/pages/splash_screen.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'firebase_options.dart';

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  RestResources.setAppEnvironment(AppEnvironment.prd);

  await dotenv.load(fileName: "assets/config/.env");

  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  await initDependencies();
  await getIt<TokenManager>().loadTokens();
  await getIt<SecretsManagerService>().getBaseUrl();
  unawaited(getIt<AppAnalysisRepository>().getAppFeatures());

  if (kReleaseMode == true) {
    await SentryFlutter.init(
          (options) {
        options.dsn = 'https://<EMAIL>/4508958845108224';
        options.tracesSampleRate = 1.0;
        options.profilesSampleRate = 1.0;
        options.experimental.replay.sessionSampleRate = 1.0;
        options.experimental.replay.onErrorSampleRate = 1.0;
      },
      appRunner: () => runApp(
        const SentryWidget(child: MyApp()),
      ),
    );
  } else {
    runApp(const MyApp());
  }
}

class MyApp extends StatelessWidget {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final whiteLabeledAppName = getIt<AuthLocalDataSource>().getTenantDetails()?.id == WhiteLabeledEnum.hjProperties.tenantId
        ? WhiteLabeledEnum.hjProperties.tenantId
        : getIt<AuthLocalDataSource>().getTenantDetails()?.id == WhiteLabeledEnum.prowinn.tenantId
        ? WhiteLabeledEnum.prowinn.tenantId
        : 'leadrat';
    return MultiBlocProvider(
      providers: registerBlocProviders(context),
      child: MaterialApp(
        title: whiteLabeledAppName,
        navigatorKey: navigatorKey,
        debugShowCheckedModeBanner: false,
        theme: AppTheme.darkThemeMode,
        home: const SplashScreen(),
      ),
    );
  }
}
