import 'package:leadrat/core_main/common/entites/enquiry_entity.dart';
import 'package:leadrat/core_main/common/models/base_user_model.dart';
import 'package:leadrat/core_main/common/models/dto_with_name_model.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/purpose_enum.dart';
import 'package:leadrat/core_main/enums/leads/profession.dart';
import 'package:leadrat/features/lead/data/models/lead_call_log_model.dart';
import 'package:leadrat/features/lead/domain/entities/lead_document_entity.dart';
import 'package:leadrat/features/lead/domain/entities/lead_status_custom_entity.dart';
import 'package:leadrat/features/lead/domain/entities/lead_tag_entity.dart';

import '../../../../core_main/common/models/address_model.dart';
import '../../data/models/get_lead_model.dart';

class GetLeadEntity {
  final String? id;
  final DateTime? lastModifiedOn;
  final DateTime? createdOn;
  final String? name;
  final String? contactNo;
  final String? alternateContactNo;
  final String? landline;
  final String? email;
  final String? notes;
  final DateTime? scheduledDate;
  final DateTime? possessionDate;
  final DateTime? bookedDate;
  final DateTime? revertDate;
  final String? chosenProject;
  final String? chosenProperty;
  final String? bookedUnderName;
  final String? leadNumber;
  final int? shareCount;
  final String? soldPrice;
  final String? rating;
  final String? referralContactNo;
  final String? referralName;
  final LeadTagEntity? leadTags;
  final Map<String, int>? contactRecords;
  final List<String?>? channelPartnerList;
  final List<ChannelPartnerModel>? channelPartners;
  final String? companyName;
  final String? agencyName;
  final Profession? profession;
  final String? channelPartnerName;
  final String? channelPartnerExecutiveName;
  final String? channelPartnerContactNo;
  final String? bookUnderName;
  final String? designation;
  final String? serialNumber;
  final List<LeadDocumentEntity>? documents;
  final LeadStatusCustomEntity? status;
  final BaseUserModel? lastModifiedBy;
  final BaseUserModel? createdByUser;
  final BaseUserModel? createdBy;
  final BaseUserModel? assignedUser;
  final BaseUserModel? secondaryUser;
  final BaseUserModel? assignedFromUser;
  final BaseUserModel? closingManagerUser;
  final List<CustomFlagModel>? customFlags;
  final EnquiryEntity? enquiry;
  final String? carpetAreaUnit;
  final String? builtUpAreaUnit;
  final String? referralEmail;
  final String? saleableAreaUnit;
  final String? propertyAreaUnit;
  final String? netAreaUnit;
  final List<DTOWithNameModel>? agencies;
  final List<DTOWithNameModel>? campaigns;
  final String? campaignName;
  final List<TempProjectModel>? projects;
  final List<TempPropertyModel>? properties;
  final BaseUserModel? sourcingManager;
  final Map<int, Map<int, Map<String, String>?>?>? callRecordingUrls;
  final AddressModel? address;
  final List<LinkModel>? links;
  final String? duplicateLeadVersion;
  final String? confidentialNotes;
  final Map<String, String?>? additionalProperties;
  final String? nationality;
  final List<LeadCallLogModel>? leadCallLogs;
  final PurposeEnum? purpose;
  final PossessionType? possesionType;

  GetLeadEntity({
    this.id,
    this.name,
    this.contactNo,
    this.alternateContactNo,
    this.landline,
    this.possessionDate,
    this.email,
    this.referralEmail,
    this.notes,
    this.scheduledDate,
    this.bookedDate,
    this.revertDate,
    this.chosenProject,
    this.chosenProperty,
    this.bookedUnderName,
    this.leadNumber,
    this.shareCount,
    this.soldPrice,
    this.rating,
    this.referralContactNo,
    this.referralName,
    this.leadTags,
    this.contactRecords,
    this.channelPartnerList,
    this.channelPartners,
    this.companyName,
    this.agencyName,
    this.profession,
    this.channelPartnerName,
    this.channelPartnerExecutiveName,
    this.channelPartnerContactNo,
    this.bookUnderName,
    this.designation,
    this.serialNumber,
    this.documents,
    this.status,
    this.lastModifiedBy,
    this.createdByUser,
    this.createdBy,
    this.assignedUser,
    this.assignedFromUser,
    this.closingManagerUser,
    this.createdOn,
    this.lastModifiedOn,
    this.secondaryUser,
    this.customFlags,
    this.enquiry,
    this.carpetAreaUnit,
    this.builtUpAreaUnit,
    this.saleableAreaUnit,
    this.propertyAreaUnit,
    this.netAreaUnit,
    this.agencies,
    this.campaigns,
    this.campaignName,
    this.projects,
    this.properties,
    this.sourcingManager,
    this.callRecordingUrls,
    this.address,
    this.links,
    this.duplicateLeadVersion,
    this.confidentialNotes,
    this.additionalProperties,
    this.nationality,
    this.leadCallLogs,
    this.purpose,
    this.possesionType,
  });

  GetLeadEntity copyWith({
    String? id,
    DateTime? lastModifiedOn,
    DateTime? createdOn,
    String? name,
    String? contactNo,
    String? alternateContactNo,
    String? landline,
    String? email,
    String? notes,
    DateTime? scheduledDate,
    DateTime? bookedDate,
    DateTime? revertDate,
    String? chosenProject,
    String? chosenProperty,
    String? bookedUnderName,
    String? leadNumber,
    int? shareCount,
    String? soldPrice,
    String? rating,
    String? referralContactNo,
    String? referralName,
    LeadTagEntity? leadTags,
    Map<String, int>? contactRecords,
    List<String?>? channelPartnerList,
    List<ChannelPartnerModel>? channelPartners,
    String? companyName,
    String? agencyName,
    Profession? profession,
    String? channelPartnerName,
    String? channelPartnerExecutiveName,
    String? channelPartnerContactNo,
    String? bookUnderName,
    String? designation,
    String? serialNumber,
    List<LeadDocumentEntity>? documents,
    LeadStatusCustomEntity? status,
    BaseUserModel? lastModifiedBy,
    BaseUserModel? createdByUser,
    BaseUserModel? createdBy,
    BaseUserModel? assignedUser,
    BaseUserModel? secondaryUser,
    BaseUserModel? assignedFromUser,
    BaseUserModel? closingManagerUser,
    List<CustomFlagModel>? customFlags,
    EnquiryEntity? enquiry,
    String? carpetAreaUnit,
    String? builtUpAreaUnit,
    String? saleableAreaUnit,
    List<DTOWithNameModel>? agencies,
    List<DTOWithNameModel>? campaigns,
    String? campaignName,
    List<TempProjectModel>? projects,
    List<TempPropertyModel>? properties,
    BaseUserModel? sourcingManager,
    Map<int, Map<int, Map<String, String>?>?>? callRecordingUrls,
    AddressModel? address,
    List<LinkModel>? links,
    String? duplicateLeadVersion,
    String? confidentialNotes,
    Map<String, String?>? additionalProperties,
    String? nationality,
    PurposeEnum? purpose,
    List<LeadCallLogModel>? leadCallLogs,
    PossessionType? possesionType,
  }) {
    return GetLeadEntity(
      id: id ?? this.id,
      lastModifiedOn: lastModifiedOn ?? this.lastModifiedOn,
      createdOn: createdOn ?? this.createdOn,
      name: name ?? this.name,
      contactNo: contactNo ?? this.contactNo,
      alternateContactNo: alternateContactNo ?? this.alternateContactNo,
      landline: landline ?? this.landline,
      email: email ?? this.email,
      notes: notes ?? this.notes,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      bookedDate: bookedDate ?? this.bookedDate,
      revertDate: revertDate ?? this.revertDate,
      chosenProject: chosenProject ?? this.chosenProject,
      chosenProperty: chosenProperty ?? this.chosenProperty,
      bookedUnderName: bookedUnderName ?? this.bookedUnderName,
      leadNumber: leadNumber ?? this.leadNumber,
      shareCount: shareCount ?? this.shareCount,
      soldPrice: soldPrice ?? this.soldPrice,
      rating: rating ?? this.rating,
      referralContactNo: referralContactNo ?? this.referralContactNo,
      referralName: referralName ?? this.referralName,
      leadTags: leadTags ?? this.leadTags,
      contactRecords: contactRecords ?? this.contactRecords,
      channelPartnerList: channelPartnerList ?? this.channelPartnerList,
      channelPartners: channelPartners ?? this.channelPartners,
      companyName: companyName ?? this.companyName,
      agencyName: agencyName ?? this.agencyName,
      profession: profession ?? this.profession,
      channelPartnerName: channelPartnerName ?? this.channelPartnerName,
      channelPartnerExecutiveName: channelPartnerExecutiveName ?? this.channelPartnerExecutiveName,
      channelPartnerContactNo: channelPartnerContactNo ?? this.channelPartnerContactNo,
      bookUnderName: bookUnderName ?? this.bookUnderName,
      designation: designation ?? this.designation,
      serialNumber: serialNumber ?? this.serialNumber,
      documents: documents ?? this.documents,
      status: status ?? this.status,
      lastModifiedBy: lastModifiedBy ?? this.lastModifiedBy,
      createdByUser: createdByUser ?? this.createdByUser,
      createdBy: createdBy ?? this.createdBy,
      assignedUser: assignedUser ?? this.assignedUser,
      secondaryUser: secondaryUser ?? this.secondaryUser,
      assignedFromUser: assignedFromUser ?? this.assignedFromUser,
      closingManagerUser: closingManagerUser ?? this.closingManagerUser,
      customFlags: customFlags ?? this.customFlags,
      enquiry: enquiry ?? this.enquiry,
      carpetAreaUnit: carpetAreaUnit ?? this.carpetAreaUnit,
      builtUpAreaUnit: builtUpAreaUnit ?? this.builtUpAreaUnit,
      saleableAreaUnit: saleableAreaUnit ?? this.saleableAreaUnit,
      agencies: agencies ?? this.agencies,
      campaigns: campaigns ?? this.campaigns,
      campaignName: campaignName ?? this.campaignName,
      projects: projects ?? this.projects,
      properties: properties ?? this.properties,
      sourcingManager: sourcingManager ?? this.sourcingManager,
      callRecordingUrls: callRecordingUrls ?? this.callRecordingUrls,
      address: address ?? this.address,
      links: links ?? this.links,
      duplicateLeadVersion: duplicateLeadVersion ?? this.duplicateLeadVersion,
      confidentialNotes: confidentialNotes ?? this.confidentialNotes,
      additionalProperties: additionalProperties ?? this.additionalProperties,
      nationality: nationality ?? this.nationality,
      leadCallLogs: leadCallLogs ?? this.leadCallLogs,
      purpose: purpose ?? this.purpose,
      possesionType: possesionType ?? this.possesionType,
    );
  }
}

class LeadWithDegreeMatchedEntity {
  final int? totalNoOfFields;
  final int? noOfFieldsMatched;
  final String? percentageOfFieldsMatched;
  final GetLeadEntity? leadEntity;

  LeadWithDegreeMatchedEntity({this.totalNoOfFields, this.noOfFieldsMatched, this.percentageOfFieldsMatched, this.leadEntity});
}
