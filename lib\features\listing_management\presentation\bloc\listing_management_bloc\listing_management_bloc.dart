import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/base/usecase/use_case.dart';
import 'package:leadrat/core_main/common/bloc/search_bloc/search_bloc.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/repository/global_setting_repository.dart';
import 'package:leadrat/core_main/common/data/user/models/get_all_users_model.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/widgets/confirm_action_bottom_modal.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/date_range.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/property_enums/listing_management_filter_key.dart';
import 'package:leadrat/core_main/enums/property_enums/property_status.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/utilities/dialog_manager.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/listing_management/data/models/listing_management_filter_model.dart';
import 'package:leadrat/features/listing_management/domain/usecase/clone_property_use_case.dart';
import 'package:leadrat/features/listing_management/domain/usecase/get_all_listing_management_properties_use_case.dart';
import 'package:leadrat/features/listing_management/domain/usecase/get_listing_management_property_count_use_case.dart';
import 'package:leadrat/features/listing_management/presentation/item/item_listing_management_model.dart';
import 'package:leadrat/features/listing_management/presentation/widgets/list_delist_properties_bottom_model.dart';
import 'package:leadrat/features/properties/data/models/list_property_model.dart';
import 'package:leadrat/features/properties/data/models/re_assign_property_model.dart';
import 'package:leadrat/features/properties/domain/usecase/archive_property_usecase.dart';
import 'package:leadrat/features/properties/domain/usecase/delete_property_use_case.dart';
import 'package:leadrat/features/properties/domain/usecase/get_all_custom_listing_source_use_case.dart';
import 'package:leadrat/features/properties/domain/usecase/property_list_delist_use_case.dart';
import 'package:leadrat/features/properties/domain/usecase/re_assign_property_use_case.dart';
import 'package:leadrat/features/properties/presentation/items/item_property_listing_model.dart';
import 'package:leadrat/main.dart';

part 'listing_management_event.dart';
part 'listing_management_state.dart';

class ListingManagementBloc extends Bloc<ListingManagementEvent, ListingManagementState> {
  final UsersDataRepository _userDataRepository;
  final ReAssignPropertyUseCase _reAssignPropertyUseCase;
  final DeletePropertyUseCase _deletePropertyUseCase;
  final ArchivePropertyUseCase _archivePropertyUseCase;
  final GetListingManagementPropertyCountUseCase _getPropertyListingCountUseCase;
  final GetAllListingManagementPropertyUseCase _getAllPropertyListingUseCase;
  final GetAllCustomListingSourceUseCase _getAllCustomListingSourceUseCase;
  final ListPropertiesUseCase _listPropertiesUseCase;
  final DeListPropertiesUseCase _deListPropertiesUseCase;
  final ClonePropertyUseCase _clonePropertyUseCase;
  GlobalSettingModel? globalSettings;
  int pageNumber = 1;
  ListingManagementFilterModel listingFilterModel = ListingManagementFilterModel();
  List<GetAllUsersModel?>? allUsers = [];
  List<ItemSimpleModel<ListingManagementFilterKey>> selectedFilters = [];

  ListingManagementBloc(
    this._userDataRepository,
    this._reAssignPropertyUseCase,
    this._deletePropertyUseCase,
    this._archivePropertyUseCase,
    this._getPropertyListingCountUseCase,
    this._getAllPropertyListingUseCase,
    this._getAllCustomListingSourceUseCase,
    this._listPropertiesUseCase,
    this._deListPropertiesUseCase,
    this._clonePropertyUseCase,
  ) : super(const ListingManagementState()) {
    on<ListingManagementInitialEvent>(_onListingManagementInitial);
    on<GetAllListingManagementProperties>(_onGetAllListingManagementProperties);
    on<InitAllUsersEvent>(_onInitAllUsers);
    on<GetPropertyListingCountEvent>(_onGetPropertyListingCount);
    on<LoadMorePropertiesEvent>(_onLoadMoreProperties);
    on<TogglePropertyVisibilityEvent>(_onTogglePropertyVisibility);
    on<RefreshPropertiesEvent>(_onRefreshProperties);
    on<SelectPropertiesEvent>(_onSelectProperties);
    on<DeSelectAllPropertiesEvent>(_onDeSelectAllProperties);
    on<ArchivePropertyEvent>(_onArchiveProperty);
    on<ToggleListDelistPropertyEvent>(_onToggleListDelistProperty);
    on<RemovePropertyListingItemEvent>(_onRemovePropertyListingItem);
    on<InitPropertyListingSites>(_onInitPropertyListingSites);
    on<SelectedListingSourcesEvent>(_onSelectedListingSources);
    on<ListOrDeListPropertiesEvent>(_onListOrDeListProperties);
    on<PublishOrUnpublishProperty>(_onPublishOrUnpublishProperty);
    on<ReAssignPropertyEvent>(_onReAssignProperties);
    on<RemoveFilterEvent>(_onRemoveFilter);
    on<ClonePropertyEvent>(_onCloneProperty);
  }

  FutureOr<void> _onListingManagementInitial(ListingManagementInitialEvent event, Emitter<ListingManagementState> emit) async {
    emit(state.reset());
    pageNumber = 1;
    globalSettings = globalSettings ?? await getIt<GlobalSettingRepository>().getGlobalSettings();
    listingFilterModel = event.listingFilter ?? ListingManagementFilterModel();
    selectedFilters = _initSelectedFilters();
    emit(state.copyWith(pageState: PageState.loading, selectedFilters: selectedFilters));
    add(GetAllListingManagementProperties());
    add(InitPropertyListingSites());
    add(InitAllUsersEvent());
  }

  FutureOr<void> _onGetAllListingManagementProperties(GetAllListingManagementProperties event, Emitter<ListingManagementState> emit) async {
    if (event.updatePropertyCount) add(GetPropertyListingCountEvent(listingFilter: listingFilterModel));
    final propertiesResult = await _getAllPropertyListingUseCase(GetAllListingManagementPropertyUseCaseParams(pageNumber: pageNumber, listingFilterModel: listingFilterModel));
    propertiesResult.fold(
      (failure) => emit(state.copyWith(pageState: PageState.failure, errorMessage: failure.message, isFetchingProperties: false)),
      (properties) {
        final itemPropertyModels = properties?.items.map((propertyItem) => ItemListingManagementModel(property: propertyItem)).toList();
        emit(state.copyWith(pageState: PageState.success, properties: pageNumber == 1 ? itemPropertyModels : [...?state.properties, ...?itemPropertyModels], totalPropertiesCount: properties?.totalCount ?? 0, isFetchingProperties: false));
      },
    );
  }

  FutureOr<void> _onGetPropertyListingCount(GetPropertyListingCountEvent event, Emitter<ListingManagementState> emit) async {
    final selectedPropertyVisibility = event.listingFilter?.propertyVisibility;
    final tempPropertyFilterModel = event.listingFilter?.copyWith(propertyVisibility: PropertyVisibility.all);
    final propertyListingCounts = await _getPropertyListingCountUseCase(tempPropertyFilterModel);
    propertyListingCounts.fold(
      (failure) => null,
      (success) {
        if (success != null) {
          final propertyCounts = success.entries
              .whereNot((element) => element.key == PropertyVisibility.archived)
              .map((entry) => ItemSimpleModel<PropertyVisibility>(
                    title: entry.key.description,
                    value: entry.key,
                    description: entry.value.toString(),
                    isSelected: entry.key == (selectedPropertyVisibility ?? PropertyVisibility.all),
                  ))
              .toList();
          emit(state.copyWith(propertyCounts: propertyCounts));
        }
      },
    );
  }

  FutureOr<void> _onLoadMoreProperties(LoadMorePropertiesEvent event, Emitter<ListingManagementState> emit) async {
    final selectedPropertyVisibility = state.propertyCounts.firstWhereOrNull((element) => element.isSelected);
    final totalPropertyCount = int.tryParse(selectedPropertyVisibility?.description ?? '0') ?? 0;
    final totalInitiatedPropertyCount = state.properties?.length ?? 0;
    if (totalInitiatedPropertyCount >= totalPropertyCount) return;
    if (state.isFetchingProperties) return;
    emit(state.copyWith(isFetchingProperties: true));
    pageNumber = pageNumber + 1;
    add(GetAllListingManagementProperties(updatePropertyCount: false));
  }

  FutureOr<void> _onTogglePropertyVisibility(TogglePropertyVisibilityEvent event, Emitter<ListingManagementState> emit) async {
    final updatedPropertyCountItems = state.propertyCounts.map((e) => e.copyWith(isSelected: e.value == event.propertyVisibility)).toList();
    add(DeSelectAllPropertiesEvent());
    emit(state.copyWith(
      pageState: PageState.loading,
      propertyCounts: updatedPropertyCountItems,
    ));
    pageNumber = 1;
    listingFilterModel = listingFilterModel.copyWith(propertyVisibility: event.propertyVisibility);
    add(GetAllListingManagementProperties(updatePropertyCount: false));
  }

  FutureOr<void> _onRefreshProperties(RefreshPropertiesEvent event, Emitter<ListingManagementState> emit) {
    add(DeSelectAllPropertiesEvent());
    emit(state.copyWith(pageState: PageState.loading));
    pageNumber = 1;
    add(GetAllListingManagementProperties(updatePropertyCount: false));
  }

  FutureOr<void> _onSelectProperties(SelectPropertiesEvent event, Emitter<ListingManagementState> emit) {
    final updatedProperties = state.properties?.map((property) => property.property?.id == event.property.property?.id ? property.copyWith(isSelected: !property.isSelected) : property).toList();
    final selectedProperties = updatedProperties?.where((property) => property.isSelected).toList() ?? [];
    emit(state.copyWith(properties: updatedProperties, selectedProperties: selectedProperties));
  }

  FutureOr<void> _onDeSelectAllProperties(DeSelectAllPropertiesEvent event, Emitter<ListingManagementState> emit) {
    final updatedProperties = state.properties?.map((property) => property.copyWith(isSelected: false)).toList();
    emit(state.copyWith(properties: updatedProperties, selectedProperties: []));
  }

  FutureOr<void> _onArchiveProperty(ArchivePropertyEvent event, Emitter<ListingManagementState> emit) async {
    final archivePropertyResult = await _archivePropertyUseCase([event.property.id]);
    archivePropertyResult.fold(
      (failure) {
        _hideProgressDialog();
        LeadratCustomSnackbar.show(message: "Unable to delete the property", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      },
      (success) {
        LeadratCustomSnackbar.show(message: "Property Deleted Successfully", navigatorKey: MyApp.navigatorKey, type: SnackbarType.success);
        _hideProgressDialog();
        var updatedPropertyList = state.properties ?? [];
        updatedPropertyList.removeWhere((element) => element.id == event.property.id);
        emit(state.copyWith(properties: updatedPropertyList));
        final searchBloc = getIt<SearchBloc>();
        if (searchBloc.state.propertyListings?.isNotEmpty ?? false) {
          final updatedPropertyListings = List<ItemListingManagementModel>.from(searchBloc.state.propertyListings ?? [])..removeWhere((element) => element.id == event.property.id);
          searchBloc.add(UpdatePropertyListingsEvent(updatedPropertyListings));
        }
      },
    );
  }

  FutureOr<void> _onCloneProperty(ClonePropertyEvent event, Emitter<ListingManagementState> emit) async {
    if (!_userDataRepository.checkHasPermission(AppModule.listingIntegration, CommandType.cloneProperty)) {
      LeadratCustomSnackbar.show(message: "You don't have permission to clone the property", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      DialogManager().hideTransparentProgressDialog();
      return;
    }
    final clonePropertyResult = await _clonePropertyUseCase(event.propertyId);
    clonePropertyResult.fold(
      (failure) {
        LeadratCustomSnackbar.show(message: failure.message, navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
        DialogManager().hideTransparentProgressDialog();
      },
      (success) {
        if (success.isNotNullOrEmpty()) {
          LeadratCustomSnackbar.show(message: "Property cloned successfully", navigatorKey: MyApp.navigatorKey, type: SnackbarType.success);
          add(ListingManagementInitialEvent(listingFilter: listingFilterModel));
          DialogManager().hideTransparentProgressDialog();
        }
      },
    );
  }

  FutureOr<void> _onInitAllUsers(InitAllUsersEvent event, Emitter<ListingManagementState> emit) async {
    List<SelectableItem<String?>>? users = [];
    allUsers = await _userDataRepository.getAssignUser();
    UserDetailsModel? loggedInUser = _userDataRepository.getLoggedInUser();

    allUsers?.forEach((user) {
      if (user?.isActive ?? false) {
        if (loggedInUser?.userId != user?.id) {
          users.add(SelectableItem(title: '${user?.firstName ?? ''} ${user?.lastName ?? ''}', value: user?.id));
        } else {
          users.insert(0, SelectableItem(title: 'You', value: user?.id));
        }
      }
    });
    emit(state.copyWith(allUsers: users));
  }

  FutureOr<void> _onToggleListDelistProperty(ToggleListDelistPropertyEvent event, Emitter<ListingManagementState> emit) {
    var selectedProperties = [...state.selectedProperties];
    if (event.property?.isNotEmpty ?? false) selectedProperties.addAll(event.property!);
    final listingItems = selectedProperties
        .map((propertyItem) => ItemPropertyListingModel(
              title: propertyItem.property?.title ?? '',
              propertyEntity: propertyItem.property,
              dldPermitNumber: propertyItem.property?.dldPermitNumber,
              assignedUser: allUsers?.firstWhereOrNull((element) => element?.id == propertyItem.property?.assignedTo?.firstOrNull),
              listedSources: propertyItem.property?.listingSources?.map((e) => ItemSimpleModel<String>(title: e.displayName ?? '', value: e.id)).toList() ?? [],
            ))
        .toList();
    emit(state.copyWith(selectedPropertiesToListDelist: listingItems, selectedListingSources: []));
    listDelistPropertiesBottomModal(isListing: event.isListing);
  }

  FutureOr<void> _onRemovePropertyListingItem(RemovePropertyListingItemEvent event, Emitter<ListingManagementState> emit) {
    final updatedPropertyListing = state.selectedPropertiesToListDelist.whereNot((e) => e.propertyEntity?.id == event.item.propertyEntity?.id).toList();
    emit(state.copyWith(selectedPropertiesToListDelist: updatedPropertyListing));
  }

  FutureOr<void> _onInitPropertyListingSites(InitPropertyListingSites event, Emitter<ListingManagementState> emit) async {
    if (state.listingSources.isNotEmpty) return;
    final listingSitesResult = await _getAllCustomListingSourceUseCase(NoParams());
    listingSitesResult.fold(
      (failure) => null,
      (listingSites) {
        if (listingSites != null) {
          final selectableListingSites = listingSites.map((e) => SelectableItem<String>(title: e.displayName ?? '', value: e.id)).toList();
          emit(state.copyWith(listingSources: selectableListingSites));
        }
      },
    );
  }

  FutureOr<void> _onSelectedListingSources(SelectedListingSourcesEvent event, Emitter<ListingManagementState> emit) {
    emit(state.copyWith(selectedListingSources: event.selectedListingSources));
  }

  FutureOr<void> _onListOrDeListProperties(ListOrDeListPropertiesEvent event, Emitter<ListingManagementState> emit) async {
    final selectedPropertyIds = state.selectedPropertiesToListDelist
        .whereNot((element) => element.isPropertySold || !element.canListOrDelist)
        .map(
          (e) => e.propertyEntity?.id,
        )
        .whereNotNull()
        .toList();
    if (selectedPropertyIds.isEmpty) {
      LeadratCustomSnackbar.show(message: "This properties cannot be listed without a permit/broker number/ address. Please provide both to proceed.", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
      _hideProgressDialog();
      return;
    }
    if (state.selectedPropertiesToListDelist.any((element) => !element.canListOrDelist)) {
      final totalSelectedPropertyCount = state.selectedPropertiesToListDelist.length;
      final invalidPropertyCount = totalSelectedPropertyCount - selectedPropertyIds.length;
      _hideProgressDialog();
      confirmActionBottomModal(
        imageVector: ImageResources.imageConvertToLead,
        title: 'Incomplete Data',
        subTitle: '$invalidPropertyCount/$totalSelectedPropertyCount properties do not have a permit/broker number/address number and cannot be ${event.isPropertyListing ? 'published' : 'unpublished'}. “Do you want to ${event.isPropertyListing ? 'publish' : 'unpublish'} the remaining ${selectedPropertyIds.length} properties”.',
        successButtonText: event.isPropertyListing ? "Publish" : "Delist",
        hideCancelButton: true,
        closePopupOnTap: true,
        onSuccess: () async {
          add(PublishOrUnpublishProperty(selectedPropertyIds: selectedPropertyIds, publishProperty: event.isPropertyListing));
        },
      );
    } else {
      add(PublishOrUnpublishProperty(selectedPropertyIds: selectedPropertyIds, publishProperty: event.isPropertyListing));
    }
  }

  FutureOr<void> _onPublishOrUnpublishProperty(PublishOrUnpublishProperty event, Emitter<ListingManagementState> emit) async {
    final listingSourceIds = state.selectedListingSources.map((e) => e.value).whereNotNull().toList();
    if (listingSourceIds.isEmpty) {
      LeadratCustomSnackbar.show(message: "Please select the listing source", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
      _hideProgressDialog();
      return;
    }
    final listPropertyModel = ListPropertyModel(ids: event.selectedPropertyIds, listingSourceIds: listingSourceIds);
    if (event.publishProperty) {
      emit(state.copyWith(propertyListedResult: PageState.loading));
      final result = await _listPropertiesUseCase(listPropertyModel);
      result.fold((failure) {
        LeadratCustomSnackbar.show(message: "Unable to list the selected properties", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
        _hideProgressDialog();
      }, (success) {
        if (success ?? false) {
          pageNumber = 1;
          emit(state.copyWith(pageState: PageState.loading, propertyListedResult: PageState.success));
          add(DeSelectAllPropertiesEvent());
          add(GetAllListingManagementProperties());
        } else {
          LeadratCustomSnackbar.show(message: "Failed to list the properties", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
          _hideProgressDialog();
        }
      });
    } else {
      emit(state.copyWith(propertyListedResult: PageState.loading));
      final result = await _deListPropertiesUseCase(listPropertyModel);
      result.fold((failure) {
        LeadratCustomSnackbar.show(message: "Unable to delist the selected properties", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
        _hideProgressDialog();
      }, (success) {
        if (success ?? false) {
          pageNumber = 1;
          emit(state.copyWith(pageState: PageState.loading, propertyListedResult: PageState.success));
          add(DeSelectAllPropertiesEvent());
          add(GetAllListingManagementProperties());
          LeadratCustomSnackbar.show(message: "Property de-listed successfully", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
          _hideProgressDialog();
        } else {
          LeadratCustomSnackbar.show(message: "Failed to de-list the properties", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
          _hideProgressDialog();
        }
      });
    }
  }

  FutureOr<void> _onReAssignProperties(ReAssignPropertyEvent event, Emitter<ListingManagementState> emit) async {
    var reAssignModel = ReAssignPropertyModel(userIds: event.userIds ?? [], propertiesId: event.propertyIds ?? []);

    var response = await _reAssignPropertyUseCase.call(reAssignModel);
    response.fold((failure) {
      LeadratCustomSnackbar.show(message: failure.message, navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
    }, (result) {
      if (result) {
        LeadratCustomSnackbar.show(message: "properties assigned successfully", navigatorKey: MyApp.navigatorKey, type: SnackbarType.success);
        add(DeSelectAllPropertiesEvent());
        _hideProgressDialog();
      } else {
        LeadratCustomSnackbar.show(message: "failed to assigned properties", navigatorKey: MyApp.navigatorKey, type: SnackbarType.success);
        _hideProgressDialog();
      }
    });
  }

  List<ItemSimpleModel<ListingManagementFilterKey>> _initSelectedFilters() {
    final selectedLeadFilterModel = listingFilterModel;
    List<ItemSimpleModel<ListingManagementFilterKey>> selectedFilters = [];
    if (selectedLeadFilterModel.enquiryType != null) {
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.lookingFor.description, description: '', value: ListingManagementFilterKey.lookingFor));
    }
    if (selectedLeadFilterModel.propertyTypes?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.propertyTypes?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.propertyType.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.propertyType));
    }
    if (selectedLeadFilterModel.propertySubTypes?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.propertySubTypes?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.propertySubType.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.propertySubType));
    }
    if (selectedLeadFilterModel.listingLevel != null) {
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.listingLevel.description, description: '', value: ListingManagementFilterKey.listingLevel));
    }
    if (selectedLeadFilterModel.offeringType != null) {
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.offeringType.description, description: '', value: ListingManagementFilterKey.offeringType));
    }
    if (selectedLeadFilterModel.completionStatus != null) {
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.completionStatus.description, description: '', value: ListingManagementFilterKey.completionStatus));
    }
    if (selectedLeadFilterModel.communities?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.communities?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.community.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.community));
    }
    if (selectedLeadFilterModel.subCommunities?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.subCommunities?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.subCommunity.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.subCommunity));
    }
    if (selectedLeadFilterModel.facing != null) {
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.facing.description, description: '', value: ListingManagementFilterKey.facing));
    }
    if (selectedLeadFilterModel.furnishStatus?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.furnishStatus?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.furnishingStatus.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.furnishingStatus));
    }
    if (selectedLeadFilterModel.floors?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.floors?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.floors.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.floors));
    }

    if (selectedLeadFilterModel.noOfBrs?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.noOfBrs?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.noOfBr.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.noOfBr));
    }
    if (selectedLeadFilterModel.noOfKitchens?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.noOfKitchens?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.noOfKitchens.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.noOfKitchens));
    }
    if (selectedLeadFilterModel.noOfUtilities?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.noOfUtilities?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.noOfUtilities.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.noOfUtilities));
    }
    if (selectedLeadFilterModel.noOfBedrooms?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.noOfBedrooms?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.noOfBedrooms.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.noOfBedrooms));
    }
    if (selectedLeadFilterModel.noOfLivingRooms?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.noOfLivingRooms?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.noOfLivingRooms.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.noOfLivingRooms));
    }
    if (selectedLeadFilterModel.noOfBathrooms?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.noOfBathrooms?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.noOfBathrooms.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.noOfBathrooms));
    }
    if (selectedLeadFilterModel.noOfBalconies?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.noOfBalconies?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.noOfBalconies.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.noOfBalconies));
    }
    if (selectedLeadFilterModel.locations?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.locations?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.locations.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.locations));
    }
    if (selectedLeadFilterModel.ownerName?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.ownerName?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.ownerNames.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.ownerNames));
    }
    if (selectedLeadFilterModel.assignTo?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.assignTo?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.listingBy.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.listingBy));
    }
    if (selectedLeadFilterModel.listingOnBehalf?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.listingOnBehalf?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.listingOnBehalf.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.listingOnBehalf));
    }
    if (selectedLeadFilterModel.amenities?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.amenities?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.amenities.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.amenities));
    }
    if (selectedLeadFilterModel.projects?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.projects?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.projects.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ListingManagementFilterKey.projects));
    }
    if (selectedLeadFilterModel.propertySizeUnit != null || selectedLeadFilterModel.minPropertySize != null || selectedLeadFilterModel.maxPropertySize != null) {
      var description = "${selectedLeadFilterModel.minPropertySize != null ? "Min: ${selectedLeadFilterModel.minPropertySize}" : ""}"
          "${selectedLeadFilterModel.maxPropertySize != null ? "${selectedLeadFilterModel.minPropertySize != null ? " - " : ""}Max - ${selectedLeadFilterModel.maxPropertySize} ${selectedLeadFilterModel.propertySizeUnit?.unit ?? ''}" : ""}";

      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.propertySize.description, description: description, value: ListingManagementFilterKey.propertySize));
    }
    if (selectedLeadFilterModel.carpetAreaUnit != null || selectedLeadFilterModel.minCarpetArea != null || selectedLeadFilterModel.maxCarpetArea != null) {
      var description = "${selectedLeadFilterModel.minCarpetArea != null ? "Min: ${selectedLeadFilterModel.minCarpetArea}" : ""}"
          "${selectedLeadFilterModel.maxCarpetArea != null ? "${selectedLeadFilterModel.minCarpetArea != null ? " = " : ""}Max - ${selectedLeadFilterModel.maxCarpetArea} ${selectedLeadFilterModel.carpetAreaUnit?.unit ?? ''}" : ""}";

      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.carpetArea.description, description: description, value: ListingManagementFilterKey.carpetArea));
    }
    if (selectedLeadFilterModel.netAreaUnit != null || selectedLeadFilterModel.minNetArea != null || selectedLeadFilterModel.maxNetArea != null) {
      var description = "${selectedLeadFilterModel.minNetArea != null ? "Min: ${selectedLeadFilterModel.minNetArea}" : ""}"
          "${selectedLeadFilterModel.maxNetArea != null ? "${selectedLeadFilterModel.minNetArea != null ? " = " : ""}Max - ${selectedLeadFilterModel.maxNetArea} ${selectedLeadFilterModel.netAreaUnit?.unit ?? ''}" : ""}";

      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.netArea.description, description: description, value: ListingManagementFilterKey.netArea));
    }
    if (selectedLeadFilterModel.maxLeadCount.isNotNullOrEmpty() || selectedLeadFilterModel.minLeadCount.isNotNullOrEmpty()) {
      String? count;
      if (selectedLeadFilterModel.minLeadCount.isNotNullOrEmpty()) {
        count = selectedLeadFilterModel.minLeadCount;
      }
      if (selectedLeadFilterModel.maxLeadCount.isNotNullOrEmpty()) {
        count = '${count != null ? '($count - ' : ''}${selectedLeadFilterModel.maxLeadCount} ${count != null ? ')' : ''}';
      }
      if (count != null) {
        selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.leadCount.description, description: count, value: ListingManagementFilterKey.leadCount));
      }
    }
    if (selectedLeadFilterModel.maxProspectCount.isNotNullOrEmpty() || selectedLeadFilterModel.minProspectCount.isNotNullOrEmpty()) {
      String? count;
      if (selectedLeadFilterModel.minProspectCount.isNotNullOrEmpty()) {
        count = selectedLeadFilterModel.minProspectCount;
      }
      if (selectedLeadFilterModel.maxProspectCount.isNotNullOrEmpty()) {
        count = '${count != null ? '($count - ' : ''}${selectedLeadFilterModel.maxProspectCount} ${count != null ? ')' : ''}';
      }
      if (count != null) {
        selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.prospectCount.description, description: count, value: ListingManagementFilterKey.prospectCount));
      }
    }

    if (selectedLeadFilterModel.saleableAreaUnit != null || selectedLeadFilterModel.minSaleableArea != null || selectedLeadFilterModel.maxSaleableArea != null) {
      var description = "${selectedLeadFilterModel.minSaleableArea != null ? "Min: ${selectedLeadFilterModel.minSaleableArea}" : ""}"
          "${selectedLeadFilterModel.maxSaleableArea != null ? "${selectedLeadFilterModel.minSaleableArea != null ? " - " : ""}Max - ${selectedLeadFilterModel.maxSaleableArea} ${selectedLeadFilterModel.saleableAreaUnit?.unit ?? ''}" : ""}";

      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.saleableArea.description, description: description, value: ListingManagementFilterKey.saleableArea));
    }
    if (selectedLeadFilterModel.builtUpAreaUnit != null || selectedLeadFilterModel.minBuiltUpArea != null || selectedLeadFilterModel.minBuiltUpArea != null) {
      var description = "${selectedLeadFilterModel.minBuiltUpArea != null ? "Min: ${selectedLeadFilterModel.minBuiltUpArea}" : ""}"
          "${selectedLeadFilterModel.maxBuiltUpArea != null ? "${selectedLeadFilterModel.minBuiltUpArea != null ? " - " : ""}Max - ${selectedLeadFilterModel.maxBuiltUpArea} ${selectedLeadFilterModel.builtUpAreaUnit?.unit ?? ''}" : ""}";

      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: ListingManagementFilterKey.builtUpArea.description, description: description, value: ListingManagementFilterKey.builtUpArea));
    }

    if ((selectedLeadFilterModel.fromMinBudget != null && selectedLeadFilterModel.fromMinBudget != 0) || (selectedLeadFilterModel.toMinBudget != null && selectedLeadFilterModel.toMinBudget != 0)) {
      String description = '';
      if (selectedLeadFilterModel.toMinBudget != null && selectedLeadFilterModel.fromMinBudget != null) {
        description = 'max:${selectedLeadFilterModel.toMinBudget}-min${selectedLeadFilterModel.fromMinBudget}  ${selectedLeadFilterModel.currency ?? ''}';
      } else if (selectedLeadFilterModel.fromMinBudget != null) {
        description = 'min:${selectedLeadFilterModel.fromMinBudget}${selectedLeadFilterModel.currency ?? ''}';
      } else if (selectedLeadFilterModel.toMinBudget != null) {
        description = 'max:${selectedLeadFilterModel.toMinBudget}${selectedLeadFilterModel.currency ?? ''}';
      }

      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: description, value: ListingManagementFilterKey.minBudget));
    }
    if ((selectedLeadFilterModel.fromMaxBudget != null && selectedLeadFilterModel.fromMaxBudget != 0) || (selectedLeadFilterModel.toMaxBudget != null && selectedLeadFilterModel.toMaxBudget != 0)) {
      String description = '';
      if (selectedLeadFilterModel.toMaxBudget != null && selectedLeadFilterModel.fromMaxBudget != null) {
        description = 'min:${selectedLeadFilterModel.toMaxBudget}-max${selectedLeadFilterModel.fromMaxBudget}';
      } else if (selectedLeadFilterModel.fromMaxBudget != null) {
        description = 'min:${selectedLeadFilterModel.fromMaxBudget}';
      } else if (selectedLeadFilterModel.toMaxBudget != null) {
        description = 'max:${selectedLeadFilterModel.toMaxBudget}';
      }

      // selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: description, value: ListingManagementFilterKey.maxBudget));
    }
    if (selectedLeadFilterModel.dateRange != null) {
      final isCustomDateRange = (selectedLeadFilterModel.dateRange?.description == DateRange.customDate.description);
      final dateFormat = DateFormat('dd-MM-yyyy');
      var dateRange = isCustomDateRange ? "from: ${dateFormat.format(DateTime.parse(selectedLeadFilterModel.fromDate!).toUserTimeZone()!)} - to: ${dateFormat.format(DateTime.parse(selectedLeadFilterModel.toDate!).toUserTimeZone()!)}" : selectedLeadFilterModel.dateRange?.description ?? "";
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: " $dateRange", value: ListingManagementFilterKey.dateRange));
    }
    if (selectedLeadFilterModel.possessionTypeDateRange != null) {
      final isCustomDateRange = (selectedLeadFilterModel.possessionTypeDateRange?.description == PossessionType.customDate.description);
      final dateFormat = DateFormat('MMM-yyyy');
      var dateRange = isCustomDateRange ? "${dateFormat.format(DateTime.parse(selectedLeadFilterModel.fromDate!).toLocal())} - ${dateFormat.format(DateTime.parse(selectedLeadFilterModel.toDate!).toLocal())}" : selectedLeadFilterModel.possessionTypeDateRange?.description ?? "";
      selectedFilters.add(ItemSimpleModel<ListingManagementFilterKey>(title: dateRange, value: ListingManagementFilterKey.dateRange));
    }

    return selectedFilters;
  }

  FutureOr<void> _onRemoveFilter(RemoveFilterEvent event, Emitter<ListingManagementState> emit) {
    if (event.leadFilterKey == null) return null;
    switch (event.leadFilterKey!) {
      case ListingManagementFilterKey.lookingFor:
        listingFilterModel = listingFilterModel.copyWith(enquiryType: EnquiryType.none);
        break;
      case ListingManagementFilterKey.propertyType:
        listingFilterModel = listingFilterModel.copyWith(propertyTypes: []);
        break;
      case ListingManagementFilterKey.propertySubType:
        listingFilterModel = listingFilterModel.copyWith(propertySubTypes: []);
        break;
      case ListingManagementFilterKey.listingLevel:
        listingFilterModel.listingLevel = null;
        listingFilterModel = listingFilterModel;
        break;
      case ListingManagementFilterKey.offeringType:
        listingFilterModel.offeringType = null;
        listingFilterModel = listingFilterModel;
        break;
      case ListingManagementFilterKey.completionStatus:
        listingFilterModel.completionStatus = null;
        listingFilterModel = listingFilterModel;
        break;
      case ListingManagementFilterKey.community:
        listingFilterModel = listingFilterModel.copyWith(communities: []);
        break;
      case ListingManagementFilterKey.subCommunity:
        listingFilterModel = listingFilterModel.copyWith(subCommunities: []);
        break;
      case ListingManagementFilterKey.listingOnBehalf:
        listingFilterModel = listingFilterModel.copyWith(listingOnBehalf: []);
        break;
      case ListingManagementFilterKey.facing:
        listingFilterModel.facing = null;
        listingFilterModel = listingFilterModel;
        break;
      case ListingManagementFilterKey.furnishingStatus:
        listingFilterModel = listingFilterModel.copyWith(furnishStatus: []);
        break;

      case ListingManagementFilterKey.noOfBr:
        listingFilterModel = listingFilterModel.copyWith(noOfBrs: []);
        break;
      case ListingManagementFilterKey.floors:
        listingFilterModel = listingFilterModel.copyWith(floors: []);
        break;
      case ListingManagementFilterKey.ownerNames:
        listingFilterModel = listingFilterModel.copyWith(ownerName: []);
        break;
      case ListingManagementFilterKey.projects:
        listingFilterModel = listingFilterModel.copyWith(projects: []);
        break;
      case ListingManagementFilterKey.dateRange:
        listingFilterModel = listingFilterModel.copyWith(fromDate: '', toDate: '', dateRange: null, updateDateRange: false, updateDateType: true);
        break;
      case ListingManagementFilterKey.propertySize:
        listingFilterModel = listingFilterModel.copyWith(resetPropertySize: true);
        break;
      case ListingManagementFilterKey.saleableArea:
        listingFilterModel = listingFilterModel.copyWith(resetSaleableArea: true);
        break;
      case ListingManagementFilterKey.builtUpArea:
        listingFilterModel = listingFilterModel.copyWith(resetBuiltUpArea: true);
        break;
      case ListingManagementFilterKey.carpetArea:
        listingFilterModel = listingFilterModel.copyWith(resetCarpetArea: true);
        break;
      case ListingManagementFilterKey.locations:
        listingFilterModel = listingFilterModel.copyWith(locations: []);
        break;
      case ListingManagementFilterKey.noOfBathrooms:
        listingFilterModel = listingFilterModel.copyWith(noOfBathrooms: []);
        break;
      case ListingManagementFilterKey.noOfLivingRooms:
        listingFilterModel = listingFilterModel.copyWith(noOfLivingRooms: []);
        break;
      case ListingManagementFilterKey.noOfBalconies:
        listingFilterModel = listingFilterModel.copyWith(noOfBalconies: []);
        break;
      case ListingManagementFilterKey.noOfBedrooms:
        listingFilterModel = listingFilterModel.copyWith(noOfBedrooms: []);
        break;
      case ListingManagementFilterKey.noOfKitchens:
        listingFilterModel = listingFilterModel.copyWith(noOfKitchens: []);
        break;
      case ListingManagementFilterKey.noOfUtilities:
        listingFilterModel = listingFilterModel.copyWith(noOfUtilities: []);
        break;
      case ListingManagementFilterKey.amenities:
        listingFilterModel = listingFilterModel.copyWith(amenities: []);
        break;
      case ListingManagementFilterKey.netArea:
        listingFilterModel = listingFilterModel.copyWith(resetNetArea: true);
        break;
      case ListingManagementFilterKey.minBudget:
        listingFilterModel = listingFilterModel.copyWith(updateMinBudget: false, updateCurrency: false);

      // case ListingManagementFilterKey.maxBudget:
      //   listingFilterModel = listingFilterModel.copyWith(updateMaxBudget: false, updateCurrency: false);
      case ListingManagementFilterKey.prospectCount:
        listingFilterModel = listingFilterModel.copyWith(resetProspectCount: true);
        break;
      case ListingManagementFilterKey.leadCount:
        listingFilterModel = listingFilterModel.copyWith(resetLeadCount: true);
        break;

      case ListingManagementFilterKey.listingBy:
        listingFilterModel = listingFilterModel.copyWith(assignTo: []);
        break;
    }
    selectedFilters.removeWhere((element) => element.value == event.leadFilterKey);
    add(ListingManagementInitialEvent(listingFilter: listingFilterModel));
  }

  void _hideProgressDialog() => DialogManager().hideTransparentProgressDialog();
}
