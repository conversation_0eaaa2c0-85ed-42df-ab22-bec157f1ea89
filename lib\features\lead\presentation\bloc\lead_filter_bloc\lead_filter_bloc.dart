import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/base/usecase/use_case.dart';
import 'package:leadrat/core_main/common/data/flags/repository/flag_repository.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_type_model.dart';
import 'package:leadrat/core_main/common/data/master_data/repository/masterdata_repository.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/entites/dto_with_name_entity.dart';
import 'package:leadrat/core_main/common/models/budget_range_model.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/common/date_range.dart';
import 'package:leadrat/core_main/enums/common/date_type.dart';
import 'package:leadrat/core_main/enums/common/no_of_beds.dart';
import 'package:leadrat/core_main/enums/common/no_of_bhk.dart';
import 'package:leadrat/core_main/enums/common/no_of_br.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/property_type_enum.dart';
import 'package:leadrat/core_main/enums/common/purpose_enum.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/leads/lead_category_type.dart';
import 'package:leadrat/core_main/enums/leads/lead_filter_keys.dart';
import 'package:leadrat/core_main/enums/leads/lead_meeting_status.dart';
import 'package:leadrat/core_main/enums/leads/lead_visibility.dart';
import 'package:leadrat/core_main/enums/leads/profession.dart';
import 'package:leadrat/core_main/enums/property_enums/furnish_status.dart';
import 'package:leadrat/core_main/enums/property_enums/offering_type.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:leadrat/core_main/extensions/enum_extension.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/features/lead/data/models/custom_filter_model.dart';
import 'package:leadrat/features/lead/data/models/lead_card_view_model.dart';
import 'package:leadrat/features/lead/data/models/lead_filter_model.dart';
import 'package:leadrat/features/lead/data/models/update_custom_status_model.dart';
import 'package:leadrat/features/lead/domain/entities/lead_sub_source_entity.dart';
import 'package:leadrat/features/lead/domain/repository/leads_card_view_repository.dart';
import 'package:leadrat/features/lead/domain/repository/leads_repository.dart';
import 'package:leadrat/features/lead/domain/usecase/get_Lead_excel_Data_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_additional_property_keys_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_additional_property_values_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_channel_partner_names_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_lead_sub_source_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_project_name_with_id_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_property_name_with_id_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/update_custom_filter_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/update_leads_categories_orders_use_case.dart';
import 'package:leadrat/features/lead/presentation/item/item_lead_filter_model.dart';
import 'package:leadrat/features/lead/presentation/widgets/range_input_widget.dart';

import '../../../../../core_main/enums/common/area_type_enum.dart';

part 'lead_filter_event.dart';

part 'lead_filter_state.dart';

class LeadFilterBloc extends Bloc<LeadFilterEvent, LeadFilterState> {
  List<ItemLeadFilterCategoryModel> leadFilterCategories = [];
  late LeadFilterModel _selectedLeadFilterModel;
  late GlobalSettingModel? globalSettingModel;
  final UsersDataRepository _usersDataRepository;
  final LeadsRepository _leadsRepository;
  final FlagRepository _flagRepository;
  final MasterDataRepository _masterDataRepository;
  final GetPropertyNameWithIdUseCase _getPropertyNameWithIdUseCase;
  final GetProjectNameWithIdUseCase _getProjectNameWithIdUseCase;
  final GetLeadSubSourceUseCase _getLeadSubSourceUseCase;
  final GetChannelPartnerNamesUseCase _getChannelPartnerNamesUseCase;
  final GetAdditionalPropertyValuesUseCase _getAdditionalPropertyValuesUseCase;
  final GetAdditionalPropertyKeysUseCase _getAdditionalPropertyKeysUseCase;
  final GetLeadExcelDataUseCase _getUploadTypeNamesUseCase;
  final UpdateLeadsCategoriesOrdersUseCase _updateLeadsCategoriesOrdersUseCase;
  final UpdateCustomFilterUseCase _updateCustomFilterUseCase;
  UserDetailsModel? _currentUser;
  bool isSearching = false;
  List<MasterPropertyTypeModel> _allPropertyTypes = [];
  List<LeadSubSourceEntity> _allLeadSource = [];
  ItemLeadFilterModel? selectedFilterItem;
  List<LeadCardViewModel> leadCategoriesOrders = [];
  List<LeadCardViewModel>? defaultTypes = [];
  List<CustomFilterModel> leadCustomCategoriesOrders = [];
  List<CustomFilterModel>? customDefaultLeadStatus = [];
  List<String?>? _leadCities = [];
  List<String?>? _leadZones = [];
  List<String?>? _leadStates = [];
  List<String?>? _leadLocalities = [];

  final List<LeadCategoryType> unusedLeadCategoryTypes = [
    LeadCategoryType.hotLeads,
    LeadCategoryType.warmLeads,
    LeadCategoryType.coldLeads,
    LeadCategoryType.highlightedLeads,
    LeadCategoryType.escalated,
    LeadCategoryType.aboutToConvert,
    LeadCategoryType.unassignedLeads,
    LeadCategoryType.allWithNID,
  ];

  LeadFilterBloc(
    this._usersDataRepository,
    this._flagRepository,
    this._masterDataRepository,
    this._getPropertyNameWithIdUseCase,
    this._getProjectNameWithIdUseCase,
    this._getLeadSubSourceUseCase,
    this._leadsRepository,
    this._getChannelPartnerNamesUseCase,
    this._getAdditionalPropertyValuesUseCase,
    this._getAdditionalPropertyKeysUseCase,
    this._getUploadTypeNamesUseCase,
    this._updateLeadsCategoriesOrdersUseCase,
    this._updateCustomFilterUseCase,
  ) : super(const LeadFilterState()) {
    on<InitLeadFilterEvent>(_onInitialLeadFilter);
    on<FilterCategorySelectEvent>(_onFilterCategorySelect);
    on<SelectFilterEvent>(_onSelectFilter);
    on<ApplyLeadFilterEvent>(_onApplyLeadFilter);
    on<SearchFiltersItemsEvent>(_onSearchFiltersItems);
    on<ResetFilterEvent>(_onResetFilter);
    on<SelectFromDateEvent>(_onSelectFromDate);
    on<SelectToDateEvent>(_onSelectToDate);
    on<CustomBudgetChangeEvent>(_onCustomBudgetChange);
    on<ToggleWithHistoryEvent>(_onToggleWithHistory);
    on<ToggleWithTeamEvent>(_onToggleWithTeam);
    on<ChangeCurrencyEvent>(_onChangeCurrency);
    on<SelectDateTypeEvent>(_onSelectDateType);
    on<SelectAreaUnitEvent>(_onSelectAreaUnitEvent);
    on<UpdateCategoryOrdersEvent>(_onUpdateCategoryOrders);
    on<OnMinBudgetChangeEvent>(_onOnMinBudgetChangeEvent);
    on<OnMaxBudgetChangeEvent>(_onOnMaxBudgetChangeEvent);
    on<AreaUnitChangedEvent>(_onAreaUnitChanged);
    on<AreaSizeInputChangeEvent>(_onAreaSizeInputChangeEvent);
  }

  FutureOr<void> _onInitialLeadFilter(InitLeadFilterEvent event, Emitter<LeadFilterState> emit) async {
    globalSettingModel = event.globalSettingModel;
    _selectedLeadFilterModel = event.leadFilterModel ?? LeadFilterModel();
    _currentUser = _usersDataRepository.getLoggedInUser();
    leadFilterCategories = _setUpLeadFilters();
    _updateState(
      emit,
      leadFilterCategories: leadFilterCategories,
      leadFilterModel: _selectedLeadFilterModel,
      pageState: PageState.initial,
      selectedToDate: null,
      selectedFromDate: null,
      customBudgetRange: state.customBudget,
      isWithTeam: false,
      isWithHistory: false,
      selectedCurrency: null,
      allCurrencies: [],
      selectedDateType: null,
      categoryOrders: leadCategoriesOrders,
      leadCustomCategoriesOrders: leadCustomCategoriesOrders,
    );
    add(FilterCategorySelectEvent(selectedCategoryIndex: state.selectedCategoryIndex, itemLeadFilterCategoryModel: leadFilterCategories[state.selectedCategoryIndex]));
    await _initSelectedFilters(emit);
  }

  FutureOr<void> _onFilterCategorySelect(FilterCategorySelectEvent event, Emitter<LeadFilterState> emit) async {
    var selectedItem = event.itemLeadFilterCategoryModel;
    var updatedLeadCategories = state.leadFilterCategories
        .map((category) => category.filterKey == selectedItem.filterKey
            ? selectedItem.copyWith(isSelected: true, isInitialized: (selectedItem.pinCodeController != null) ? true : selectedItem.filters?.isNotEmpty ?? false, searchController: TextEditingController())
            : category.copyWith(
                isSelected: false,
                searchController: TextEditingController(),
              ))
        .toList();
    _updateState(emit, leadFilterCategories: updatedLeadCategories, selectedCategoryIndex: event.selectedCategoryIndex);

    if ((selectedItem.filters?.isEmpty ?? true)) {
      await _initializeFilter(selectedItem.filterKey, null, emit);
    }
    if (selectedItem.filterKey == LeadFilterKey.facebookPropertyValues) {
      await _initFacebookPropertyValues(
        emit,
        key: state.leadFilterCategories.where((element) => element.filterKey == LeadFilterKey.facebookProperties).first.filters?.where((selectedFilterItem) => selectedFilterItem.isSelected).firstOrNull?.value,
      );
    }
  }

  FutureOr<void> _onSelectFilter(SelectFilterEvent event, Emitter<LeadFilterState> emit) async {
    var selectedFilterCategory = state.leadFilterCategories.firstWhereOrNull((element) => element.filterKey == event.selectedFilterCategory.filterKey);
    List<ItemLeadFilterModel>? updatedFilterList;

    //check for date range filter
    if (selectedFilterCategory?.filterKey == LeadFilterKey.dateRange && state.selectedDateType?.value == null) {
      emit(state.copyWith(pageState: PageState.failure, errorMessage: "Please select the date type"));
      return null;
    }

    if (selectedFilterCategory == null) return;
    selectedFilterItem = event.itemLeadFilterModel;
    if (event.isSelectAll) {
      if (selectedFilterCategory.searchController?.text.isNullOrEmpty() ?? true) {
        updatedFilterList = selectedFilterCategory.filters?.map((e) => e.copyWith(isSelected: !(selectedFilterCategory.isAllSelected))).toList();
      } else {
        final searchedFilters = state.searchFilteredCategories.firstWhereOrNull((element) => element.filterKey == event.selectedFilterCategory.filterKey);
        updatedFilterList = selectedFilterCategory.filters?.map((item) {
          if (searchedFilters?.filters?.any((element) => element.displayName == item.displayName && element.description == item.description) ?? false) {
            return item.copyWith(isSelected: !(selectedFilterCategory.isAllSelected));
          }
          return item;
        }).toList();
      }
    } else if (event.itemLeadFilterModel != null) {
      var selectedFilterItem = event.itemLeadFilterModel!;
      updatedFilterList = selectedFilterCategory.filters?.map((item) {
        if (item.displayName == selectedFilterItem.displayName && item.description == selectedFilterItem.description) {
          return selectedFilterItem.copyWith(isSelected: !selectedFilterItem.isSelected);
        }
        return selectedFilterCategory.hasMultiSelect ? item : item.copyWith(isSelected: false);
      }).toList();
    }

    if (updatedFilterList != null) {
      var updatedCategoryList = state.leadFilterCategories.map((e) => e.filterKey == selectedFilterCategory.filterKey ? selectedFilterCategory.copyWith(filters: updatedFilterList) : e).toList();
      _updateState(emit, leadFilterCategories: updatedCategoryList);
    }

    if (selectedFilterCategory.filterKey == LeadFilterKey.propertyType) {
      final selectedPropertyTypes = updatedFilterList?.where((element) => element.isSelected).toList();
      await _updatePropertySubTypes(emit, selectedPropertyTypes);
    } else if (selectedFilterItem?.filterKey == LeadFilterKey.source) {
      final selectedLeadSource = updatedFilterList?.where((element) => element.isSelected).toList();
      await _updateLeasSubSource(emit, selectedLeadSource);
    } else if (selectedFilterItem?.filterKey == LeadFilterKey.status) {
      final selectedStatus = updatedFilterList?.where((element) => element.isSelected).toList();
      await _updateSubStatus(emit, selectedStatus);
    }
    if (selectedFilterCategory.searchController?.text.isNotNullOrEmpty() ?? false) {
      add(SearchFiltersItemsEvent(searchText: selectedFilterCategory.searchController?.text, filterKey: event.selectedFilterCategory.filterKey));
    }
  }

  FutureOr<void> _onSearchFiltersItems(SearchFiltersItemsEvent event, Emitter<LeadFilterState> emit) async {
    if (event.searchText == null) return;
    final selectedFilterCategoryFilters = state.leadFilterCategories.firstWhereOrNull((category) => category.filterKey == event.filterKey)?.filters;
    final filteredList = selectedFilterCategoryFilters?.where((element) => element.displayName.toLowerCase().contains(event.searchText!.toLowerCase())).toList();
    final filteredCategories = state.leadFilterCategories.map((category) {
      if (category.filterKey == event.filterKey) return category.copyWith(filters: filteredList);
      return category;
    }).toList();
    emit(state.copyWith(searchFilteredCategories: filteredCategories));
  }

  FutureOr<void> _onSelectFromDate(SelectFromDateEvent event, Emitter<LeadFilterState> emit) {
    emit(state.copyWith(selectedFromDate: event.selectedFromDate));
  }

  FutureOr<void> _onSelectToDate(SelectToDateEvent event, Emitter<LeadFilterState> emit) {
    emit(state.copyWith(selectedToDate: event.selectedToDate));
  }

  FutureOr<void> _onApplyLeadFilter(ApplyLeadFilterEvent event, Emitter<LeadFilterState> emit) async {
    var leadFilterModel = LeadFilterModel();
    var selectedFilterCategories = state.leadFilterCategories.where((category) => category.filters?.any((filter) => filter.isSelected) ?? false).toList();
    final selectedDateRange = ItemLeadFilterModel.getSelectedEnums(selectedFilterCategories, LeadFilterKey.dateRange, DateRange.values)?.firstOrNull;
    final selectedPossessionDateRange = ItemLeadFilterModel.getSelectedEnums<PossessionType>(selectedFilterCategories, LeadFilterKey.dateRange, PossessionType.values)?.firstOrNull;

    String? fromDate, toDate;
    if (selectedDateRange != null && selectedDateRange == DateRange.customDate) {
      fromDate = state.selectedFromDate?.getBasedOnTimeZone().toString() ?? DateTime.now().getBasedOnTimeZone().toString();
      toDate = state.selectedToDate?.getBasedOnTimeZone().toString() ?? DateTime.now().getBasedOnTimeZone().toString();
    } else if (selectedDateRange != null) {
      var selectedDate = _getDateTimeFromRange(selectedDateRange);
      fromDate = selectedDate.$2;
      toDate = selectedDate.$1;
    }
    if (selectedPossessionDateRange != null && selectedPossessionDateRange == PossessionType.customDate) {
      fromDate = utcToDateFormat(state.selectedFromDate?.toUniversalTimeStartOfDay() ?? DateTime.now().toUserTimeZone());
      toDate = utcToDateFormat(state.selectedToDate?.toUniversalTimeStartOfDay() ?? DateTime.now().toUserTimeZone());
    }

    final mergedList = await getLeadCategoriesOrders();
    final customStatusIds = await getCustomLeadCategoriesOrders();

    leadFilterModel = leadFilterModel.copyWith(
      leadVisibility: ItemLeadFilterModel.getSelectedEnums<LeadVisibility>(selectedFilterCategories, LeadFilterKey.leadVisibility, LeadVisibility.values)?.firstOrNull,
      filterTypes: !(globalSettingModel?.isCustomStatusEnabled ?? false) ? mergedList : null,
      statusIds: ItemLeadFilterModel.getSelectedDescription(selectedFilterCategories, LeadFilterKey.status),
      customFlags: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.customFlags),
      sources: ItemLeadFilterModel.getSelectedValue<int>(selectedFilterCategories, LeadFilterKey.source),
      meetingOrVisitStatuses: ItemLeadFilterModel.getSelectedEnums<LeadMeetingStatus>(selectedFilterCategories, LeadFilterKey.meetingStatus, LeadMeetingStatus.values),
      enquiredFor: ItemLeadFilterModel.getSelectedEnums<EnquiryType>(selectedFilterCategories, LeadFilterKey.enquiredFor, EnquiryType.values),
      agencyNames: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.agencyName),
      noOfBHKs: ItemLeadFilterModel.getSelectedValue<double>(selectedFilterCategories, LeadFilterKey.noOfBHK),
      furnished: ItemLeadFilterModel.getSelectedValue<int>(selectedFilterCategories, LeadFilterKey.furnished),
      beds: ItemLeadFilterModel.getSelectedValue<int>(selectedFilterCategories, LeadFilterKey.beds),
      baths: ItemLeadFilterModel.getSelectedValue<int>(selectedFilterCategories, LeadFilterKey.baths),
      floors: ItemLeadFilterModel.getSelectedValue<String>(selectedFilterCategories, LeadFilterKey.floors),
      campaignNames: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.campaigns),
      offerTypes: ItemLeadFilterModel.getSelectedValue<int>(selectedFilterCategories, LeadFilterKey.offerTypes),
      bhkTypes: ItemLeadFilterModel.getSelectedEnums(selectedFilterCategories, LeadFilterKey.bHKTypes, BHKType.values),
      propertyTypes: ItemLeadFilterModel.getSelectedDescription(selectedFilterCategories, LeadFilterKey.propertyType),
      locations: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.locations),
      communities: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.community),
      subCommunities: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.subCommunity),
      towerNames: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.towerName),
      countries: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.country),
      pincode: state.leadFilterCategories.firstWhereOrNull((category) => category.filterKey == LeadFilterKey.pinCode)?.pinCodeController?.text ?? '',
      referralName: state.leadFilterCategories.firstWhereOrNull((category) => category.filterKey == LeadFilterKey.referralName)?.pinCodeController?.text ?? '',
      referralEmail: state.leadFilterCategories.firstWhereOrNull((category) => category.filterKey == LeadFilterKey.referralEmail)?.pinCodeController?.text ?? '',
      referralNumber: state.leadFilterCategories.firstWhereOrNull((category) => category.filterKey == LeadFilterKey.referralPhoneNo)?.pinCodeController?.text ?? '',
      properties: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.properties),
      projects: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.projects),
      currency: state.selectedCurrency?.value,
      fromDate: fromDate,
      toDate: toDate,
      possessionTypeDateRange: ItemLeadFilterModel.getSelectedEnums<PossessionType>(selectedFilterCategories, LeadFilterKey.dateRange, PossessionType.values)?.firstOrNull,
      dateRange: ItemLeadFilterModel.getSelectedEnums<DateRange>(selectedFilterCategories, LeadFilterKey.dateRange, DateRange.values)?.firstOrNull,
      subSources: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.subSource),
      propertySubTypes: ItemLeadFilterModel.getSelectedDescription(selectedFilterCategories, LeadFilterKey.propertySubType),
      assignFromIds: ItemLeadFilterModel.getSelectedDescription(selectedFilterCategories, LeadFilterKey.assignedFrom),
      assignedToIds: ItemLeadFilterModel.getSelectedDescription(selectedFilterCategories, LeadFilterKey.assignedTo),
      secondaryFromIds: ItemLeadFilterModel.getSelectedDescription(selectedFilterCategories, LeadFilterKey.secondaryOwnerFrom),
      secondaryUsers: ItemLeadFilterModel.getSelectedDescription(selectedFilterCategories, LeadFilterKey.secondaryOwner),
      doneBy: ItemLeadFilterModel.getSelectedDescription(selectedFilterCategories, LeadFilterKey.doneBy),
      appointmentDoneByUserIds: ItemLeadFilterModel.getSelectedDescription(selectedFilterCategories, LeadFilterKey.siteVisitOrMeetingDoneBy),
      bookedByIds: ItemLeadFilterModel.getSelectedDescription(selectedFilterCategories, LeadFilterKey.bookedBy),
      dataConverted: ItemLeadFilterModel.getSelectedValue(selectedFilterCategories, LeadFilterKey.dataConverted)?.firstOrNull,
      qualifiedByIds: ItemLeadFilterModel.getSelectedDescription(selectedFilterCategories, LeadFilterKey.qualifiedBy),
      createdByIds: ItemLeadFilterModel.getSelectedDescription(selectedFilterCategories, LeadFilterKey.createdBy),
      lastModifiedByIds: ItemLeadFilterModel.getSelectedDescription(selectedFilterCategories, LeadFilterKey.lastModifiedBy),
      archivedByIds: ItemLeadFilterModel.getSelectedDescription(selectedFilterCategories, LeadFilterKey.deletedBy),
      restoredByIds: ItemLeadFilterModel.getSelectedDescription(selectedFilterCategories, LeadFilterKey.restoredBy),
      originalOwnerIds: ItemLeadFilterModel.getSelectedDescription(selectedFilterCategories, LeadFilterKey.originalOwner),
      customFilterIds: (globalSettingModel?.isCustomStatusEnabled ?? false) ? customStatusIds : null,
      isWithTeam: state.isWithTeam,
      isWithHistory: state.isWithHistory,
      dateType: state.selectedDateType?.value,
      builtUpAreaUnitId: state.selectedBuildUpAreaUnit?.value?.id,
      builtUpArea: (state.leadFilterCategories.firstWhereOrNull((category) => category.filterKey == LeadFilterKey.buildUpArea)?.pinCodeController?.text ?? '').wordToDouble(),
      carpetArea: (state.leadFilterCategories.firstWhereOrNull((category) => category.filterKey == LeadFilterKey.carpetArea)?.pinCodeController?.text ?? '').wordToDouble(),
      saleableArea: (state.leadFilterCategories.firstWhereOrNull((category) => category.filterKey == LeadFilterKey.salableArea)?.pinCodeController?.text ?? '').wordToDouble(),
      carpetAreaUnitId: state.selectedCarpetAreaUnit?.value?.id,
      saleableAreaUnitId: state.selectedSalableAreaUnit?.value?.id,
      subStatuses: ItemLeadFilterModel.getSelectedDescription(selectedFilterCategories, LeadFilterKey.subStatus),
      netArea: (state.leadFilterCategories.firstWhereOrNull((category) => category.filterKey == LeadFilterKey.netArea)?.pinCodeController?.text ?? '').wordToDouble(),
      propertyArea: (state.leadFilterCategories.firstWhereOrNull((category) => category.filterKey == LeadFilterKey.propertyArea)?.pinCodeController?.text ?? '').wordToDouble(),
      netAreaUnitId: state.selectedNetAreaUnit?.value?.id,
      propertyAreaUnitId: state.selectedPropertyAreaUnit?.value?.id,
      unitNames: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.unitNumberOrName),
      clusterNames: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.clusterName),
      nationality: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.nationality),
      channelPartnerNames: ItemLeadFilterModel.getSelectedValue(selectedFilterCategories, LeadFilterKey.channelPartnerName),
      designations: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.designation),
      excelSheets: ItemLeadFilterModel.getSelectedValue(selectedFilterCategories, LeadFilterKey.excelSheet),
      profession: ItemLeadFilterModel.getSelectedValue(selectedFilterCategories, LeadFilterKey.profession),
      facebookProperties: ItemLeadFilterModel.getSelectedValue(selectedFilterCategories, LeadFilterKey.facebookProperties),
      facebookPropertyValues: ItemLeadFilterModel.getSelectedValue(selectedFilterCategories, LeadFilterKey.facebookPropertyValues),
      isUntouched: ItemLeadFilterModel.getSelectedValue(selectedFilterCategories, LeadFilterKey.unTouched)?.firstOrNull,
      purposes: ItemLeadFilterModel.getSelectedEnums<PurposeEnum>(selectedFilterCategories, LeadFilterKey.purpose, PurposeEnum.values),
      minBuiltUpArea: state.builtUpArea?.min,
      maxBuiltUpArea: state.builtUpArea?.max,
      minSaleAbleArea: state.saleableArea?.min,
      maxSaleAbleArea: state.saleableArea?.max,
      minCarpetArea: state.carpetArea?.min,
      maxCarpetArea: state.carpetArea?.max,
      minPropertyArea: state.propertyArea?.min,
      maxPropertyArea: state.propertyArea?.max,
      toMinBudget: state.minBudget?.max,
      fromMinBudget: state.minBudget?.min,
      toMaxBudget: state.maxBudget?.max,
      fromMaxBudget: state.maxBudget?.min,
      maxNetArea: state.netArea?.max,
      minNetArea: state.netArea?.min,
      cities: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.city),
      states: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.state),
      zones: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.zone),
      localities: ItemLeadFilterModel.getSelectedDisplayNames(selectedFilterCategories, LeadFilterKey.locality),
      latitude: double.tryParse(
        state.leadFilterCategories.firstWhereOrNull((category) => category.filterKey == LeadFilterKey.latitude)?.pinCodeController?.text ?? '',
      ),
      longitude: double.tryParse(
        state.leadFilterCategories.firstWhereOrNull((category) => category.filterKey == LeadFilterKey.longitude)?.pinCodeController?.text ?? '',
      ),
      radiusInKm: double.tryParse(
        state.leadFilterCategories.firstWhereOrNull((category) => category.filterKey == LeadFilterKey.radius)?.pinCodeController?.text ?? '',
      ),
      isShowUniqueLead: ItemLeadFilterModel.getSelectedValue(selectedFilterCategories, LeadFilterKey.showUniqueLead)?.firstOrNull,
      isShowParentLead: ItemLeadFilterModel.getSelectedValue(selectedFilterCategories, LeadFilterKey.showParentLead)?.firstOrNull,
      showChildCount: int.tryParse(
        state.leadFilterCategories.firstWhereOrNull((category) => category.filterKey == LeadFilterKey.showChildCount)?.pinCodeController?.text ?? '',
      ),
    );

    _updateState(emit, pageState: PageState.success, leadFilterModel: leadFilterModel);
  }

  FutureOr<void> _onResetFilter(ResetFilterEvent event, Emitter<LeadFilterState> emit) {
    emit(state.copyWith(
      updateSelectedDateType: false,
      updateSelectedToDate: false,
      updateSelectedFromDate: false,
      updateCustomBudget: false,
      selectedCategoryIndex: 0,
      updateSelectBuildUpAreaUnit: false,
      updateSelectCarpetAreaUnit: false,
      updateSelectNetAreaUnit: false,
      updateSelectPropertyAreaUnit: false,
      updateSelectSalableAreaUnit: false,
      updateSelectBuiltArea: false,
      updateSelectCarpetArea: false,
      updateSelectSaleableArea: false,
      updateSelectPropertyArea: false,
      updateSelectMaxBudget: false,
      updateSelectMinBudget: false,
      updateSelectNetArea: false,
    ));
    final updatedLeadFilterCategories = state.leadFilterCategories.map((category) {
      return category.copyWith(filters: category.filters?.map((filter) => filter.copyWith(isSelected: false)).toList());
    }).toList();
    final isCustomStatusEnabled = globalSettingModel?.isCustomStatusEnabled ?? false;

    final defaultCategoriesSelectedFilters = updatedLeadFilterCategories.map((e) {
      if (e.filterKey == LeadFilterKey.category) {
        final updatedCategoryFilters = e.filters?.map((category) {
          bool isSelected;

          if (isCustomStatusEnabled) {
            // When Custom Status is Enabled
            isSelected = leadCustomCategoriesOrders.where((i) => i.isDefault ?? false).map((item) => item.id).whereType<String>().toList().contains(category.description); // Assuming `category.id` is available
          } else {
            // Old Logic
            isSelected = leadCategoriesOrders.where((i) => i.isDefault ?? false).map((element) => element.categiryType).toList().contains(getEnumFromDescription(LeadCategoryType.values, category.displayName));
          }

          return category.copyWith(isSelected: isSelected);
        }).toList();

        return e.copyWith(filters: updatedCategoryFilters);
      }

      return e.copyWith(
        pinCodeController: e.pinCodeController != null ? TextEditingController() : null,
        searchController: e.searchController != null ? TextEditingController() : null,
      );
    }).toList();

    final defaultCategoryTypes = leadCategoriesOrders.where((i) => i.isDefault ?? false).map((item) => item.categiryType).whereType<LeadCategoryType>().toList();
    final List<String> customFilterIds = leadCustomCategoriesOrders.where((i) => (i.isDefault ?? false)).map((item) => item.id).whereType<String>().toList();

    _updateState(
      emit,
      leadFilterCategories: defaultCategoriesSelectedFilters,
      leadFilterModel: LeadFilterModel(leadVisibility: LeadVisibility.selfWithReportee, filterTypes: defaultCategoryTypes, defaultFilterTypes: defaultCategoryTypes, customFilterIds: customFilterIds),
      pageState: PageState.initial,
      selectedToDate: null,
      selectedFromDate: null,
      customBudgetRange: state.customBudget,
      isWithTeam: false,
      isWithHistory: false,
      selectedCurrency: null,
      allCurrencies: [],
      selectedDateType: null,
    );
    _selectedLeadFilterModel = LeadFilterModel();
    add(FilterCategorySelectEvent(selectedCategoryIndex: state.selectedCategoryIndex, itemLeadFilterCategoryModel: leadFilterCategories[state.selectedCategoryIndex]));
  }

  FutureOr<void> _onCustomBudgetChange(CustomBudgetChangeEvent event, Emitter<LeadFilterState> emit) {
    emit(state.copyWith(customBudget: state.customBudget.copyWith(minBudget: event.startValue, maxBudget: event.endValue)));
  }

  Future<void> _initSelectedFilters(Emitter<LeadFilterState> emit) async {
    if (_selectedLeadFilterModel.leadVisibility != null) await _initializeFilter(LeadFilterKey.leadVisibility, _initShowingLeads, emit);
    if (_selectedLeadFilterModel.filterTypes?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.category, _initLeadCategories, emit);
    if (_selectedLeadFilterModel.statusIds?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.status, _initLeadStatus, emit);
    if ((_selectedLeadFilterModel.subStatuses?.isNotEmpty ?? false) && (globalSettingModel?.isCustomStatusEnabled ?? false)) await _initializeFilter(LeadFilterKey.subStatus, _initSubStatuses, emit);
    if (_selectedLeadFilterModel.customFlags?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.customFlags, _initLeadFlags, emit);
    if (_selectedLeadFilterModel.sources?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.source, _initLeadSource, emit);
    if (_selectedLeadFilterModel.subSources?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.subSource, _initLeadSubSource, emit);
    if (_selectedLeadFilterModel.meetingOrVisitStatuses?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.meetingStatus, _initMeetingOrSiteVisitStatus, emit);
    if (_selectedLeadFilterModel.enquiredFor?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.enquiredFor, _initEnquiredForFilters, emit);
    // if (_selectedLeadFilterModel.budgetFilters?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.budget, _initBudgetFilters, emit);
    if (_selectedLeadFilterModel.fromMinBudget != null || _selectedLeadFilterModel.toMinBudget != null) await _initializeFilter(LeadFilterKey.minBudget, _initMinBudgetFilters, emit);
    if (_selectedLeadFilterModel.fromMaxBudget != null || _selectedLeadFilterModel.toMaxBudget != null) await _initializeFilter(LeadFilterKey.maxBudget, _initMaxBudgetFilters, emit);
    if (_selectedLeadFilterModel.agencyNames?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.agencyName, _initAgencyNames, emit);
    if (_selectedLeadFilterModel.assignedToIds?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.assignedTo, _initAssignedToUsers, emit);
    if (_selectedLeadFilterModel.assignFromIds?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.assignedFrom, _initAssignedFromUsers, emit);
    if (_selectedLeadFilterModel.secondaryUsers?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.secondaryOwner, _initAssignedSecondaryOwner, emit);
    if (_selectedLeadFilterModel.secondaryFromIds?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.secondaryOwnerFrom, _initAssignedSecondaryOwnerFrom, emit);
    if (_selectedLeadFilterModel.doneBy?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.doneBy, _initAssignmentDoneBy, emit);
    if (_selectedLeadFilterModel.dateRange != null) await _initializeFilter(LeadFilterKey.dateRange, _initDateRanges, emit);
    if (_selectedLeadFilterModel.noOfBHKs?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.noOfBHK, _initNoOfBHKs, emit);
    if (_selectedLeadFilterModel.furnished?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.furnished, _initFurnishingStatus, emit);
    if (_selectedLeadFilterModel.beds?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.beds, _initNoOfBeds, emit);
    if (_selectedLeadFilterModel.baths?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.baths, _initNoOfBaths, emit);
    if (_selectedLeadFilterModel.floors?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.floors, _initNoOfFloors, emit);
    if (_selectedLeadFilterModel.offerTypes?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.offerTypes, _initOfferingTypes, emit);

    if (_selectedLeadFilterModel.bhkTypes?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.bHKTypes, _initBHKTypes, emit);
    if (_selectedLeadFilterModel.locations?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.locations, _initLeadLocations, emit);
    if (_selectedLeadFilterModel.communities?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.community, _initCommunities, emit);
    if (_selectedLeadFilterModel.subCommunities?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.subCommunity, _initSubCommunities, emit);
    if (_selectedLeadFilterModel.towerNames?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.towerName, _initTowerNames, emit);
    if (_selectedLeadFilterModel.countries?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.country, _initCountries, emit);
    if (_selectedLeadFilterModel.pincode?.isNotEmpty ?? false) leadFilterCategories.firstWhereOrNull((category) => category.filterKey == LeadFilterKey.pinCode)?.pinCodeController?.text = _selectedLeadFilterModel.pincode ?? '';
    if (_selectedLeadFilterModel.referralName?.isNotEmpty ?? false) leadFilterCategories.firstWhereOrNull((category) => category.filterKey == LeadFilterKey.referralName)?.pinCodeController?.text = _selectedLeadFilterModel.referralName ?? '';
    if (_selectedLeadFilterModel.referralEmail?.isNotEmpty ?? false) leadFilterCategories.firstWhereOrNull((category) => category.filterKey == LeadFilterKey.referralEmail)?.pinCodeController?.text = _selectedLeadFilterModel.referralEmail ?? '';
    if (_selectedLeadFilterModel.referralNumber?.isNotEmpty ?? false) leadFilterCategories.firstWhereOrNull((category) => category.filterKey == LeadFilterKey.referralPhoneNo)?.pinCodeController?.text = _selectedLeadFilterModel.referralNumber ?? '';
    if (_selectedLeadFilterModel.propertyTypes?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.propertyType, _initPropertyTypes, emit);
    if (_selectedLeadFilterModel.properties?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.properties, _initProperties, emit);
    if (_selectedLeadFilterModel.propertySubTypes?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.propertySubType, _initPropertySubTypes, emit);
    if (_selectedLeadFilterModel.projects?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.projects, _initProjects, emit);
    if (_selectedLeadFilterModel.appointmentDoneByUserIds?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.siteVisitOrMeetingDoneBy, _initMeetingDoneByUsers, emit);
    if (_selectedLeadFilterModel.bookedByIds?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.bookedBy, _initBookedByUsers, emit);
    if (_selectedLeadFilterModel.dataConverted?.isNotNullOrEmpty() ?? false) await _initializeFilter(LeadFilterKey.dataConverted, _initConvertedFromData, emit);
    if (_selectedLeadFilterModel.qualifiedByIds?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.qualifiedBy, _initQualifiedByUsers, emit);
    if (_selectedLeadFilterModel.isWithTeam ?? false) _updateState(emit, isWithTeam: _selectedLeadFilterModel.isWithTeam);
    if (_selectedLeadFilterModel.isWithHistory ?? false) _updateState(emit, isWithHistory: _selectedLeadFilterModel.isWithHistory);
    if (_selectedLeadFilterModel.createdByIds?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.createdBy, _initCreatedByUser, emit);
    if (_selectedLeadFilterModel.lastModifiedByIds?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.lastModifiedBy, _initLastModifiedByUser, emit);
    if (_selectedLeadFilterModel.archivedByIds?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.deletedBy, _initDeletedByUser, emit);
    if (_selectedLeadFilterModel.restoredByIds?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.restoredBy, _initRestoredByUser, emit);
    if (_selectedLeadFilterModel.restoredByIds?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.originalOwner, _initOriginalOwners, emit);
    if (_selectedLeadFilterModel.minCarpetArea != null || _selectedLeadFilterModel.maxCarpetArea != null) await _initializeFilter(LeadFilterKey.carpetArea, _initCarpetAreaUnits, emit);
    if (_selectedLeadFilterModel.minBuiltUpArea != null || _selectedLeadFilterModel.maxBuiltUpArea != null) await _initializeFilter(LeadFilterKey.buildUpArea, _initBuildUpAreaUnits, emit);
    if (_selectedLeadFilterModel.minSaleAbleArea != null || _selectedLeadFilterModel.maxSaleAbleArea != null) await _initializeFilter(LeadFilterKey.salableArea, _initSealableAreaUnits, emit);
    if (_selectedLeadFilterModel.channelPartnerNames?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.channelPartnerName, _initChannelPartnerNames, emit);
    if (_selectedLeadFilterModel.excelSheets?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.excelSheet, _initExcelSheets, emit);
    if (_selectedLeadFilterModel.profession?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.profession, _initProfessions, emit);
    if (_selectedLeadFilterModel.facebookProperties?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.facebookProperties, _initFacebookProperties, emit);
    if (_selectedLeadFilterModel.designations?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.designation, _initDesignation, emit);
    if (_selectedLeadFilterModel.minNetArea != null || _selectedLeadFilterModel.maxNetArea != null) await _initializeFilter(LeadFilterKey.netArea, _initNetAreaUnits, emit);
    if (_selectedLeadFilterModel.minPropertyArea != null || _selectedLeadFilterModel.maxPropertyArea != null) await _initializeFilter(LeadFilterKey.propertyArea, _initPropertyAreaUnits, emit);
    if (_selectedLeadFilterModel.clusterNames?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.clusterName, _initClusterName, emit);
    if (_selectedLeadFilterModel.unitNames?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.unitNumberOrName, _initUnitName, emit);
    if (_selectedLeadFilterModel.nationality?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.nationality, _initNationality, emit);
    if (_selectedLeadFilterModel.campaignNames?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.campaigns, _initCampaignNames, emit);
    if (_selectedLeadFilterModel.purposes?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.purpose, _initPurposes, emit);
    if (_selectedLeadFilterModel.cities?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.city, _initLeadCities, emit);
    if (_selectedLeadFilterModel.zones?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.zone, _initLeadZones, emit);
    if (_selectedLeadFilterModel.states?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.state, _initLeadStates, emit);
    if (_selectedLeadFilterModel.localities?.isNotEmpty ?? false) await _initializeFilter(LeadFilterKey.locality, _initLeadLocality, emit);
  }

  List<ItemLeadFilterCategoryModel> _setUpLeadFilters() {
    return [
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.leadVisibility, filters: [], hasMultiSelect: false),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.category, filters: [], hasMultiSelect: false, hasSelectAll: false, isReorderAbleList: true),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.status, filters: [], hasSelectAll: true),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.subStatus, filters: [], hasSelectAll: true, searchController: TextEditingController(), searchHintText: "search sub status", hasSearch: true),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.customFlags, filters: [], hasSearch: true, searchHintText: "search tags", searchController: TextEditingController()),
      if (_usersDataRepository.checkHasPermission(AppModule.lead, CommandType.viewLeadSource)) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.source, filters: [], hasSelectAll: true, hasSearch: true, searchHintText: "search source", searchController: TextEditingController()),
      if (_usersDataRepository.checkHasPermission(AppModule.lead, CommandType.viewLeadSource)) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.subSource, filters: [], hasSelectAll: true, hasSearch: true, searchHintText: "search sub source", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.meetingStatus, filters: []),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.minBudget, hasSelectAll: false, hasMultiSelect: false, filters: []),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.maxBudget, hasSelectAll: false, hasMultiSelect: false, filters: []),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.enquiredFor, filters: []),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.agencyName, filters: [], hasSearch: true, searchHintText: "search agency", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.assignedTo, isCustomCategoryName: true, categoryName: (globalSettingModel?.isDualOwnershipEnabled ?? false) ? "Primary Owner" : LeadFilterKey.assignedTo.description, hasSearch: true, searchHintText: "search users", filters: [], searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.assignedFrom, isCustomCategoryName: true, categoryName: (globalSettingModel?.isDualOwnershipEnabled ?? false) ? "Primary Assigned From" : LeadFilterKey.assignedFrom.description, filters: [], hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      if (globalSettingModel?.isDualOwnershipEnabled ?? false) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.secondaryOwner, filters: [], hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      if (globalSettingModel?.isDualOwnershipEnabled ?? false) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.secondaryOwnerFrom, filters: [], hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.doneBy, filters: [], hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.dateRange, filters: [], hasMultiSelect: false, hasCustomHeaderView: true),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.properties, filters: [], hasSearch: true, searchHintText: "search properties", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.projects, filters: [], hasSearch: true, searchHintText: "search projects", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.propertyType, filters: []),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.propertySubType, filters: [], searchHintText: "search property-subtype", searchController: TextEditingController()),
      if (!(globalSettingModel?.isCustomLeadFormEnabled ?? false)) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.noOfBHK, filters: []),
      if (globalSettingModel?.isCustomLeadFormEnabled ?? false) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.beds, filters: []),
      if (globalSettingModel?.isCustomLeadFormEnabled ?? false) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.baths, filters: []),
      if (globalSettingModel?.isCustomLeadFormEnabled ?? false) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.floors, filters: []),
      if (globalSettingModel?.isCustomLeadFormEnabled ?? false) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.offerTypes, filters: []),
      if (globalSettingModel?.isCustomLeadFormEnabled ?? false) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.furnished, filters: []),
      if (!(globalSettingModel?.isCustomLeadFormEnabled ?? false)) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.bHKTypes, filters: []),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.locations, filters: [], hasSearch: true, searchHintText: "search locations", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.city, filters: [], hasSearch: true, searchHintText: "search city", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.zone, filters: [], hasSearch: true, searchHintText: "search zones", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.state, filters: [], hasSearch: true, searchHintText: "search states", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.locality, filters: [], hasSearch: true, searchHintText: "search locality", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.latitude, filters: [], hasSearch: false, searchHintText: "latitude", hasSelectAll: false, hasMultiSelect: false, searchController: TextEditingController(), pinCodeController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.longitude, filters: [], hasSearch: false, searchHintText: "longitude", hasSelectAll: false, hasMultiSelect: false, searchController: TextEditingController(), pinCodeController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.radius, filters: [], hasSearch: false, searchHintText: "radius", hasSelectAll: false, hasMultiSelect: false, searchController: TextEditingController(), pinCodeController: TextEditingController()),
      if (globalSettingModel?.isCustomLeadFormEnabled ?? false) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.community, filters: [], hasSearch: true, searchHintText: "search community", searchController: TextEditingController()),
      if (globalSettingModel?.isCustomLeadFormEnabled ?? false) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.subCommunity, filters: [], hasSearch: true, searchHintText: "search sub community", searchController: TextEditingController()),
      if (globalSettingModel?.isCustomLeadFormEnabled ?? false) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.towerName, filters: [], hasSearch: true, searchHintText: "search tower name", searchController: TextEditingController()),
      if (globalSettingModel?.isCustomLeadFormEnabled ?? false) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.country, filters: [], hasSearch: true, searchHintText: "search country", searchController: TextEditingController()),
      if (globalSettingModel?.isCustomLeadFormEnabled ?? false) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.pinCode, hasSelectAll: false, hasMultiSelect: false, filters: [], hasSearch: false, searchHintText: "enter pin code", searchController: TextEditingController(), pinCodeController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.bookedBy, filters: [], hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.dataConverted, hasMultiSelect: false, filters: [], hasSearch: false),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.qualifiedBy, filters: [], hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.siteVisitOrMeetingDoneBy, filters: [], hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.referralName, filters: [], hasSearch: false, searchHintText: "referral name", hasSelectAll: false, hasMultiSelect: false, searchController: TextEditingController(), pinCodeController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.referralEmail, filters: [], hasSearch: false, searchHintText: "referral email", hasSelectAll: false, hasMultiSelect: false, searchController: TextEditingController(), pinCodeController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.referralPhoneNo, filters: [], hasSearch: false, searchHintText: "referral phone no", hasSelectAll: false, hasMultiSelect: false, searchController: TextEditingController(), pinCodeController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.createdBy, filters: [], hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.lastModifiedBy, filters: [], hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.deletedBy, filters: [], hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.restoredBy, filters: [], hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.originalOwner, filters: [], hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.carpetArea, hasSelectAll: false, hasMultiSelect: false, filters: [], isAreaRelated: true),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.buildUpArea, hasSelectAll: false, hasMultiSelect: false, filters: [], isAreaRelated: true),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.salableArea, hasSelectAll: false, hasMultiSelect: false, filters: [], isAreaRelated: true),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.unTouched, hasMultiSelect: false, filters: [], hasSearch: false),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.channelPartnerName, filters: [], hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.excelSheet, filters: [], hasMultiSelect: false, hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.designation, filters: [], hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.profession, filters: [], hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.facebookProperties, filters: [], hasMultiSelect: false, hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.facebookPropertyValues, filters: [], hasMultiSelect: false, hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      if (globalSettingModel?.isCustomLeadFormEnabled ?? false) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.netArea, hasSelectAll: false, hasMultiSelect: false, filters: [], isAreaRelated: true),
      if (globalSettingModel?.isCustomLeadFormEnabled ?? false) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.propertyArea, hasSelectAll: false, hasMultiSelect: false, filters: [], isAreaRelated: true),
      if (globalSettingModel?.isCustomLeadFormEnabled ?? false) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.unitNumberOrName, filters: [], hasSearch: true, hasMultiSelect: true),
      if (globalSettingModel?.isCustomLeadFormEnabled ?? false) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.clusterName, filters: [], hasSearch: true, hasMultiSelect: true),
      if (globalSettingModel?.isCustomLeadFormEnabled ?? false) ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.nationality, filters: [], hasMultiSelect: true, hasSearch: true),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.campaigns, filters: [], hasMultiSelect: true, hasSearch: true, searchController: TextEditingController(), searchHintText: "Enter "),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.purpose, filters: [], hasMultiSelect: true, hasSearch: false),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.showUniqueLead, hasMultiSelect: false, filters: [], hasSearch: false),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.showChildCount, filters: [], hasSearch: false, searchHintText: "show child count", hasSelectAll: false, hasMultiSelect: false, searchController: TextEditingController(), pinCodeController: TextEditingController()),
      ItemLeadFilterCategoryModel(filterKey: LeadFilterKey.showParentLead, hasMultiSelect: false, filters: [], hasSearch: false),
    ];
  }

  Future<List<LeadCategoryType>> getLeadCategoriesOrders() async {
    final mergedList = <LeadCardViewModel>[];
    var filterTypes = <LeadCategoryType>[];
    final isCustomStatusDisabled = !(globalSettingModel?.isCustomStatusEnabled ?? false);
    final hasOrders = leadCategoriesOrders.isNotEmpty;
    final hasStateCategories = state.categories?.isNotEmpty ?? false;
    if (isCustomStatusDisabled) {
      if (hasOrders && hasStateCategories) {
        final stateCategories = state.categories ?? [];

        // Create map for fast lookup
        final stateCategoryMap = {for (var item in stateCategories) item.id: item};

        // Get selected category IDs
        final selectedIds = stateCategories.where((item) => item.isDefault ?? false).map((e) => e.id).toList();

        // Map selected items while preserving order
        final selectedItems = selectedIds.map((id) {
          final orderItem = leadCategoriesOrders.firstWhere((cat) => cat.id == id);
          final selectedItem = stateCategoryMap[id];
          return LeadCardViewModel(
            id: orderItem.id,
            categiryType: orderItem.categiryType,
            isDefault: selectedItem?.isDefault ?? false,
            title: selectedItem?.title ?? orderItem.title,
            orderRank: 0,
          );
        }).toList();

        // Get unselected items
        final unselectedItems = leadCategoriesOrders.where((cat) => !selectedIds.contains(cat.id)).map((cat) => cat.copyWith(isDefault: false)).toList();

        // Assign orderRank to all items
        for (int i = 0; i < selectedItems.length; i++) {
          mergedList.add(selectedItems[i].copyWith(orderRank: i));
        }
        for (int i = 0; i < unselectedItems.length; i++) {
          mergedList.add(unselectedItems[i].copyWith(orderRank: selectedItems.length + i));
        }

        // Persist and notify
        if (mergedList.isNotEmpty) {
          await getIt<LeadsCardViewRepository>().restoreMobileCardCategoriesOrders(mergedList);
        }

        _updateLeadsCategoriesOrdersUseCase(mergedList);
        filterTypes = mergedList.where((item) => item.isDefault ?? false).map((i) => i.categiryType!).toList();
      } else {
        filterTypes = leadCategoriesOrders.where((item) => item.isDefault ?? false).map((i) => i.categiryType!).toList();
      }
    }
    return filterTypes;
  }

  Future<List<String>> getCustomLeadCategoriesOrders() async {
    final mergedList = <CustomFilterModel>[];
    var customStatusIds = <String>[];
    final isCustomStatusDisabled = globalSettingModel?.isCustomStatusEnabled ?? false;
    final hasOrders = leadCustomCategoriesOrders.isNotEmpty;
    final hasStateCategories = customDefaultLeadStatus?.isNotEmpty ?? false;
    if (isCustomStatusDisabled) {
      if (hasOrders && hasStateCategories) {
        final stateCategories = customDefaultLeadStatus ?? [];

        // Create map for fast lookup
        final stateCategoryMap = {for (var item in stateCategories) item.id: item};

        // Get selected category IDs
        final selectedIds = stateCategories.where((item) => item.isDefault ?? false).map((e) => e.id).toList();

        // Map selected items while preserving order
        final selectedItems = selectedIds.map((id) {
          final orderItem = leadCustomCategoriesOrders.firstWhere((cat) => cat.id == id);
          final selectedItem = stateCategoryMap[id];
          return CustomFilterModel(
            id: orderItem.id,
            name: orderItem.name,
            isDefault: selectedItem?.isDefault ?? false,
            orderRank: 0,
          );
        }).toList();

        // Get unselected items
        final unselectedItems = leadCustomCategoriesOrders.where((cat) => !selectedIds.contains(cat.id)).map((cat) => cat.copyWith(isDefault: false)).toList();

        // Assign orderRank to all items
        for (int i = 0; i < selectedItems.length; i++) {
          mergedList.add(selectedItems[i].copyWith(orderRank: i));
        }
        for (int i = 0; i < unselectedItems.length; i++) {
          mergedList.add(unselectedItems[i].copyWith(orderRank: selectedItems.length + i));
        }

        // Persist and notify
        if (mergedList.isNotEmpty) {
          await getIt<LeadsRepository>().saveMobileCardCategoriesOrders(mergedList);
        }

        final updateCustomStatusModel = UpdateCustomStatusModel(
          bulkUpdateCustomFilters: mergedList
              .map((action) => BulkUpdateCustomFilterModel(
                    id: action.id,
                    orderRank: action.orderRank,
                    isDefault: action.isDefault,
                  ))
              .toList(),
        );

        await _updateCustomFilterUseCase(updateCustomStatusModel);
        customStatusIds = mergedList.where((i) => i.isDefault ?? false).map((j) => j.id!).toList();
      } else {
        customStatusIds = leadCustomCategoriesOrders.where((i) => i.isDefault ?? false).map((j) => j.id!).toList();
      }
    }
    return customStatusIds;
  }

  Future<void> _initializeFilter(LeadFilterKey filterKey, Function? initFunction, Emitter<LeadFilterState> emit) async {
    try {
      if (initFunction != null) {
        await initFunction(emit);
      } else {
        switch (filterKey) {
          case LeadFilterKey.leadVisibility:
            _initShowingLeads(emit);
            break;
          case LeadFilterKey.category:
            await _initLeadCategories(emit);
            break;
          case LeadFilterKey.status:
            await _initLeadStatus(emit);
            break;
          case LeadFilterKey.customFlags:
            await _initLeadFlags(emit);
            break;
          case LeadFilterKey.source:
            await _initLeadSource(emit);
            break;
          case LeadFilterKey.subSource:
            await _initLeadSubSource(emit);
            break;
          case LeadFilterKey.meetingStatus:
            _initMeetingOrSiteVisitStatus(emit);
            break;
          case LeadFilterKey.minBudget:
            await _initMinBudgetFilters(emit);
            break;
          case LeadFilterKey.maxBudget:
            await _initMaxBudgetFilters(emit);
          case LeadFilterKey.enquiredFor:
            _initEnquiredForFilters(emit);
            break;
          case LeadFilterKey.agencyName:
            await _initAgencyNames(emit);
            break;
          case LeadFilterKey.assignedTo:
            await _initAssignedToUsers(emit);
            break;
          case LeadFilterKey.campaigns:
            await _initCampaignNames(emit);
            break;
          case LeadFilterKey.assignedFrom:
            await _initAssignedFromUsers(emit);
            break;
          case LeadFilterKey.secondaryOwner:
            await _initAssignedSecondaryOwner(emit);
            break;
          case LeadFilterKey.secondaryOwnerFrom:
            await _initAssignedSecondaryOwnerFrom(emit);
            break;
          case LeadFilterKey.doneBy:
            await _initAssignmentDoneBy(emit);
            break;
          case LeadFilterKey.dateRange:
            _initDateRanges(emit);
            break;
          case LeadFilterKey.noOfBHK:
            _initNoOfBHKs(emit);
            break;
          case LeadFilterKey.furnished:
            _initFurnishingStatus(emit);
            break;
          case LeadFilterKey.beds:
            _initNoOfBeds(emit);
            break;
          case LeadFilterKey.baths:
            _initNoOfBaths(emit);
            break;
          case LeadFilterKey.floors:
            _initNoOfFloors(emit);
            break;
          case LeadFilterKey.offerTypes:
            _initOfferingTypes(emit);
            break;

          case LeadFilterKey.bHKTypes:
            _initBHKTypes(emit);
            break;
          case LeadFilterKey.propertyType:
            _initPropertyTypes(emit);
            break;
          case LeadFilterKey.locations:
            await _initLeadLocations(emit);
            break;
          case LeadFilterKey.community:
            await _initCommunities(emit);
            break;
          case LeadFilterKey.subCommunity:
            await _initSubCommunities(emit);
            break;
          case LeadFilterKey.towerName:
            await _initTowerNames(emit);
            break;
          case LeadFilterKey.country:
            await _initCountries(emit);
            break;
          case LeadFilterKey.properties:
            await _initProperties(emit);
            break;
          case LeadFilterKey.propertySubType:
            await _initPropertySubTypes(emit);
            break;
          case LeadFilterKey.projects:
            await _initProjects(emit);
            break;
          case LeadFilterKey.siteVisitOrMeetingDoneBy:
            await _initMeetingDoneByUsers(emit);
            break;
          case LeadFilterKey.bookedBy:
            await _initBookedByUsers(emit);
            break;
          case LeadFilterKey.dataConverted:
            await _initConvertedFromData(emit);
            break;
          case LeadFilterKey.qualifiedBy:
            await _initQualifiedByUsers(emit);
            break;
          case LeadFilterKey.subStatus:
            await _initSubStatuses(emit);
            break;
          case LeadFilterKey.referralName:
            await _initReferralName(emit);
            break;
          case LeadFilterKey.referralEmail:
            await _initReferralEmail(emit);
            break;
          case LeadFilterKey.referralPhoneNo:
            await _initReferralPhoneNo(emit);
            break;
          case LeadFilterKey.createdBy:
            await _initCreatedByUser(emit);
            break;
          case LeadFilterKey.lastModifiedBy:
            await _initLastModifiedByUser(emit);
            break;
          case LeadFilterKey.deletedBy:
            await _initDeletedByUser(emit);
            break;
          case LeadFilterKey.restoredBy:
            await _initRestoredByUser(emit);
            break;
          case LeadFilterKey.originalOwner:
            await _initOriginalOwners(emit);
            break;
          case LeadFilterKey.carpetArea:
            await _initCarpetAreaUnits(emit);
            break;
          case LeadFilterKey.buildUpArea:
            await _initBuildUpAreaUnits(emit);
            break;
          case LeadFilterKey.salableArea:
            await _initSealableAreaUnits(emit);
            break;
          case LeadFilterKey.unTouched:
            _initIsUntouched(emit);
            break;
          case LeadFilterKey.channelPartnerName:
            await _initChannelPartnerNames(emit);
            break;
          case LeadFilterKey.excelSheet:
            await _initExcelSheets(emit);
            break;
          case LeadFilterKey.designation:
            await _initDesignation(emit);
            break;
          case LeadFilterKey.profession:
            await _initProfessions(emit);
            break;
          case LeadFilterKey.facebookProperties:
            await _initFacebookProperties(emit);
            break;
          case LeadFilterKey.propertyArea:
            await _initPropertyAreaUnits(emit);
            break;
          case LeadFilterKey.netArea:
            await _initNetAreaUnits(emit);
            break;
          case LeadFilterKey.nationality:
            await _initNationality(emit);
            break;
          case LeadFilterKey.clusterName:
            await _initClusterName(emit);
            break;
          case LeadFilterKey.unitNumberOrName:
            await _initUnitName(emit);
            break;
          case LeadFilterKey.purpose:
            _initPurposes(emit);
            break;
          case LeadFilterKey.city:
            _initLeadCities(emit);
            break;
          case LeadFilterKey.zone:
            _initLeadZones(emit);
            break;
          case LeadFilterKey.state:
            _initLeadStates(emit);
            break;
          case LeadFilterKey.locality:
            _initLeadLocality(emit);
            break;
          case LeadFilterKey.longitude:
            _initLongitude(emit);
            break;
          case LeadFilterKey.latitude:
            _initLatitude(emit);
            break;
          case LeadFilterKey.radius:
            _initRadiusInKm(emit);
            break;
          case LeadFilterKey.showParentLead:
            _initIsShowParentLead(emit);
            break;
          case LeadFilterKey.showChildCount:
            _initShowChildCount(emit);
            break;
          case LeadFilterKey.showUniqueLead:
            _initIsShowUniqueLead(emit);
            break;
          default:
            break;
        }
      }
    } catch (ex) {
      "Error while initializing $filterKey filter: ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initClusterName(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedClusterNames = _selectedLeadFilterModel.clusterNames;
      final initialClusterName = await _leadsRepository.getLeadClusterNames();
      var clusterNames = initialClusterName?.map((e) => ItemLeadFilterModel(displayName: e, filterKey: LeadFilterKey.clusterName, isSelected: initialSelectedClusterNames?.contains(e) ?? false)).toList();

      _updateFilterCategory(LeadFilterKey.clusterName, clusterNames ?? [], emit);
    } catch (ex) {
      "Error while initializing cluster name ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initUnitName(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedUnitName = _selectedLeadFilterModel.unitNames;
      final initialUnitName = await _leadsRepository.getLeadUnitNames();
      var unitNames = initialUnitName?.map((e) => ItemLeadFilterModel(displayName: e, filterKey: LeadFilterKey.unitNumberOrName, isSelected: initialSelectedUnitName?.contains(e) ?? false)).toList();

      _updateFilterCategory(LeadFilterKey.unitNumberOrName, unitNames ?? [], emit);
    } catch (ex) {
      "Error while initializing unit name ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initCampaignNames(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedCampaignNames = _selectedLeadFilterModel.campaignNames;
      final getCampaignNames = await _leadsRepository.getCampaignNames();
      final campaignNames = getCampaignNames?.map((e) => ItemLeadFilterModel(displayName: e, filterKey: LeadFilterKey.campaigns, isSelected: initialSelectedCampaignNames?.contains(e) ?? false)).toList();
      if (campaignNames?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.campaigns, campaignNames!, emit);
      }
    } catch (ex) {
      "Error while initializing Campaign names ${ex.toString()}".printInConsole();
    }
  }

  void _initShowingLeads(Emitter<LeadFilterState> emit) {
    final initialSelectedLeadVisibility = state.leadFilterModel?.leadVisibility ?? _selectedLeadFilterModel.leadVisibility;
    var leadVisibilities = LeadVisibility.values
        .where((e) => _usersDataRepository.checkHasPermission(AppModule.lead, CommandType.viewUnassignedLead) || e != LeadVisibility.unassignLead)
        .map((e) => ItemLeadFilterModel(
              displayName: e.description,
              filterKey: LeadFilterKey.leadVisibility,
              isSelected: initialSelectedLeadVisibility == e,
            ))
        .toList();
    _updateFilterCategory(LeadFilterKey.leadVisibility, leadVisibilities, emit);
  }

  Future<void> _initLeadCategories(Emitter<LeadFilterState> emit) async {
    if (globalSettingModel?.isCustomStatusEnabled ?? false) {
      var customStatus = await getIt<LeadsRepository>().getCustomStatusFilter();
      customStatus.fold((l) => null, (customFilter) {
        leadCustomCategoriesOrders = customFilter ?? [];
        customDefaultLeadStatus = customFilter;
        if (leadCustomCategoriesOrders.isNotEmpty) {
          final selectedCustomStatus = _selectedLeadFilterModel.customFilterIds;
          var leadCategoryTypes = customFilter!
              .map(
                (customStatus) => ItemLeadFilterModel(
                  displayName: customStatus.name ?? "",
                  description: customStatus.id,
                  filterKey: LeadFilterKey.category,
                  isSelected: selectedCustomStatus?.contains(customStatus.id) ?? false,
                  value: customStatus.orderRank,
                ),
              )
              .toList();
          _updateFilterCategory(LeadFilterKey.category, leadCategoryTypes, emit);
        }
      });
    } else {
      leadCategoriesOrders = await getIt<LeadsCardViewRepository>().getMobileCardCategoriesOrders() ?? [];
      defaultTypes = leadCategoriesOrders;
      var leadCategoryTypes = leadCategoriesOrders
          .where((type) => type.categiryType != null && !unusedLeadCategoryTypes.contains(type.categiryType))
          .map((leadCategoryType) => ItemLeadFilterModel(
                displayName: leadCategoryType.categiryType!.description,
                isSelected: leadCategoryType.isDefault ?? false,
                filterKey: LeadFilterKey.category,
                description: leadCategoryType.orderRank.toString(),
                value: leadCategoryType.categiryType,
              ))
          .toList();

      _updateFilterCategory(LeadFilterKey.category, leadCategoryTypes, emit);
    }
  }

  Future<void> _initLeadStatus(Emitter<LeadFilterState> emit) async {
    if (globalSettingModel?.isCustomStatusEnabled ?? false) {
      var customLeadStatus = await getIt<MasterDataRepository>().getCustomStatus();
      if (customLeadStatus?.isNotEmpty ?? false) {
        final selectedCustomStatus = _selectedLeadFilterModel.statusIds;
        var leadCustomStatus = customLeadStatus!
            .map((customStatus) => ItemLeadFilterModel(
                  displayName: customStatus.displayName ?? "",
                  description: customStatus.id,
                  filterKey: LeadFilterKey.status,
                  isSelected: selectedCustomStatus?.contains(customStatus.id) ?? false,
                ))
            .toList()
          ..sort((a, b) => a.displayName.compareTo(b.displayName));
        _updateFilterCategory(LeadFilterKey.status, leadCustomStatus, emit);
      }
    } else {
      var selectedFilterStatusIds = _selectedLeadFilterModel.statusIds;
      final masterLeadStatuses = await _masterDataRepository.getLeadStatuses();
      final leadStatuses = masterLeadStatuses?.map(
        (leadStatusModel) {
          return ItemLeadFilterModel<String>(
            displayName: leadStatusModel.displayName ?? '',
            description: leadStatusModel.id,
            isSelected: selectedFilterStatusIds?.contains(leadStatusModel.id) ?? false,
            filterKey: LeadFilterKey.status,
          );
        },
      ).toList()
        ?..sort((a, b) => a.displayName.compareTo(b.displayName));
      _updateFilterCategory(LeadFilterKey.status, leadStatuses ?? [], emit);
    }
  }

  Future<void> _initSubStatuses(Emitter<LeadFilterState> emit) async {
    try {
      if (globalSettingModel?.isCustomStatusEnabled ?? false) {
        final initialSelectedSubStatusIds = _selectedLeadFilterModel.subStatuses;
        final masterStatuses = await _masterDataRepository.getCustomStatus();
        if (masterStatuses != null && masterStatuses.isNotEmpty) {
          final masterStatusesChildTypes = masterStatuses.where((masterLeadStatusModel) => masterLeadStatusModel.childTypes != null).expand((element) => element.childTypes!);
          final subStatuses = masterStatusesChildTypes.map((e) {
            return ItemLeadFilterModel(displayName: e.displayName ?? '', filterKey: LeadFilterKey.subStatus, description: e.id, isSelected: initialSelectedSubStatusIds?.contains(e.id) ?? false);
          }).toList();
          subStatuses.sort((a, b) => a.displayName.compareTo(b.displayName));
          _updateFilterCategory(LeadFilterKey.subStatus, subStatuses, emit);
        }
      } else {
        final initialSelectedSubStatusIds = _selectedLeadFilterModel.subStatuses;
        final masterStatuses = await _masterDataRepository.getLeadStatuses();
        if (masterStatuses != null && masterStatuses.isNotEmpty) {
          final masterStatusesChildTypes = masterStatuses.where((masterLeadStatusModel) => masterLeadStatusModel.childTypes != null).expand((element) => element.childTypes!);
          final subStatuses = masterStatusesChildTypes.map((e) {
            return ItemLeadFilterModel(displayName: e.displayName ?? '', filterKey: LeadFilterKey.subStatus, description: e.id, isSelected: initialSelectedSubStatusIds?.contains(e.id) ?? false);
          }).toList();
          subStatuses.sort((a, b) => a.displayName.compareTo(b.displayName));
          _updateFilterCategory(LeadFilterKey.subStatus, subStatuses, emit);
        }
      }
    } catch (ex) {
      "Error while initializing sub status ${ex.toString()}".printInConsole();
    }
  }

  _updateSubStatus(Emitter<LeadFilterState> emit, List<ItemLeadFilterModel>? selectedStatus) async {
    if (selectedStatus != null && selectedStatus.isNotEmpty) {
      List<ItemLeadFilterModel> subStatus = [];
      final masterLeadStatus = await _masterDataRepository.getLeadStatuses();
      for (var item in selectedStatus) {
        if (item.isSelected) {
          final statuses = masterLeadStatus?.firstWhereOrNull((e) => e.displayName == item.displayName);
          statuses?.childTypes?.forEach((leadStatusModel) => subStatus.add(ItemLeadFilterModel(displayName: leadStatusModel.displayName ?? '', filterKey: LeadFilterKey.propertySubType, description: leadStatusModel.id)));
        }
      }
      _updateFilterCategory(LeadFilterKey.subStatus, subStatus, emit);
    } else {
      await _initLeadSubSource(emit);
    }
  }

  Future<void> _initLeadFlags(Emitter<LeadFilterState> emit) async {
    var initialSelectedCustomFlags = _selectedLeadFilterModel.customFlags;
    var leadCustomFlagsResponse = await _flagRepository.getFlagsByModule("leads");
    var leadCustomFlags = leadCustomFlagsResponse?.map((e) => ItemLeadFilterModel(displayName: e?.name ?? "", filterKey: LeadFilterKey.customFlags, isSelected: initialSelectedCustomFlags?.contains(e?.name) ?? false)).toList();
    if (leadCustomFlags != null && leadCustomFlags.isNotEmpty) {
      leadCustomFlags.sort((a, b) => a.displayName.compareTo(b.displayName));
      _updateFilterCategory(LeadFilterKey.customFlags, leadCustomFlags, emit);
    }
  }

  Future<void> _initLeadSource(Emitter<LeadFilterState> emit) async {
    var initialSelectedSource = _selectedLeadFilterModel.sources;
    var localLeadSource = await _masterDataRepository.getLeadSource();
    if (localLeadSource == null) return;
    var leadSource = localLeadSource
        .map((e) => ItemLeadFilterModel<int>(
              displayName: e.displayName ?? "",
              description: e.value.toString(),
              value: e.value,
              filterKey: LeadFilterKey.source,
              isSelected: initialSelectedSource?.contains(e.value.toString()) ?? false,
            ))
        .toList();
    if (leadSource.isNotEmpty) {
      leadSource.sort((a, b) => a.displayName.compareTo(b.displayName));
      _updateFilterCategory(LeadFilterKey.source, leadSource, emit);
    }
  }

  Future<void> _initLeadSubSource(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedSubSource = _selectedLeadFilterModel.subSources;
      final leadSubSource = await _getLeadSubSourceUseCase(NoParams());
      leadSubSource.fold((l) => null, (leadSource) {
        if (leadSource != null) {
          _allLeadSource = leadSource.where((element) => element.leadSourceEntity?.isEnabled ?? false).toList();
          List<ItemLeadFilterModel> subTypes = [];
          for (var item in _allLeadSource) {
            if (item.subSources != null) {
              item.subSources?.toSet().toList().forEach((name) {
                subTypes.add(ItemLeadFilterModel(
                  displayName: name,
                  description: item.leadSourceEntity?.displayName,
                  filterKey: LeadFilterKey.subSource,
                  isSelected: initialSelectedSubSource?.contains(name) ?? false,
                ));
              });
            }
          }
          subTypes = subTypes.fold<List<ItemLeadFilterModel>>([], (distinctList, element) {
            if (!distinctList.any((e) => e.displayName == element.displayName)) {
              distinctList.add(element);
            }
            return distinctList;
          }).toList();
          subTypes.sort((a, b) => a.displayName.compareTo(b.displayName));
          _updateFilterCategory(LeadFilterKey.subSource, subTypes, emit);
        }
      });
    } catch (ex) {
      "Error while initializing lead sub sources ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _updateLeasSubSource(Emitter<LeadFilterState> emit, List<ItemLeadFilterModel>? selectedLeadSource) async {
    if (_allLeadSource.isEmpty) await _initLeadSubSource(emit);
    if (selectedLeadSource != null && selectedLeadSource.isNotEmpty) {
      List<ItemLeadFilterModel> subSource = [];
      for (var item in selectedLeadSource) {
        if (item.isSelected) {
          final selectedSource = _allLeadSource.firstWhereOrNull((e) => e.leadSourceEntity?.displayName == item.displayName);
          selectedSource?.subSources?.forEach((name) => subSource.add(ItemLeadFilterModel(displayName: name, filterKey: LeadFilterKey.propertySubType)));
        }
      }
      _updateFilterCategory(LeadFilterKey.subSource, subSource, emit);
    } else {
      await _initLeadSubSource(emit);
    }
  }

  void _initMeetingOrSiteVisitStatus(Emitter<LeadFilterState> emit) {
    try {
      final initialSelectedMeetingStatus = _selectedLeadFilterModel.meetingOrVisitStatuses;
      final meetingStatus = LeadMeetingStatus.values.map((e) => ItemLeadFilterModel(displayName: e.description, filterKey: LeadFilterKey.meetingStatus, isSelected: initialSelectedMeetingStatus?.contains(e) ?? false)).toList();
      meetingStatus.removeWhere((element) => element.displayName == LeadMeetingStatus.none.description);
      meetingStatus.sort((a, b) => a.displayName.compareTo(b.displayName));
      _updateFilterCategory(LeadFilterKey.meetingStatus, meetingStatus, emit);
    } catch (ex) {
      "Error while initializing meeting status ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initializeCurrency(Emitter<LeadFilterState> emit, String? selectedCurrency) async {
    try {
      if (state.currencies.isEmpty) {
        var currencies = await _leadsRepository.getCurrencies();
        List<SelectableItem<String>> allCurrencies = [];
        SelectableItem<String>? selectableItem;
        currencies?.forEach((item) => allCurrencies.add(SelectableItem<String>(title: item, value: item)));
        if (globalSettingModel != null) {
          var defaultSymbol = globalSettingModel?.countries?.firstOrNull?.defaultCurrency ?? "INR";
          selectableItem = allCurrencies.firstWhereOrNull((element) => element.value == defaultSymbol);
        }
        _updateState(emit, allCurrencies: allCurrencies, selectedCurrency: selectableItem);
      }
      if (selectedCurrency != null && state.currencies.isNotEmpty) {
        final selectedItem = state.currencies.firstWhereOrNull((element) => element.value == selectedCurrency);
        _updateState(emit, selectedCurrency: selectedItem);
      }
    } catch (ex, stackTrace) {
      ex.logException(stackTrace);
      "Error while initializing currency".printInConsole();
    }
  }

  void _initEnquiredForFilters(Emitter<LeadFilterState> emit) {
    try {
      final initialSelectedEnquiredFor = _selectedLeadFilterModel.enquiredFor;
      final enquiryTypeFilters = EnquiryType.values.map((e) => ItemLeadFilterModel(displayName: e.description, filterKey: LeadFilterKey.enquiredFor, isSelected: initialSelectedEnquiredFor?.contains(e) ?? false)).toList();
      enquiryTypeFilters.removeWhere((element) => element.displayName == EnquiryType.none.description);
      _updateFilterCategory(LeadFilterKey.enquiredFor, enquiryTypeFilters, emit);
    } catch (ex) {
      "Error while initializing Enquired types ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initAgencyNames(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedAgencyNames = _selectedLeadFilterModel.agencyNames;
      final getAgencyNames = await _masterDataRepository.getAgencyNames();
      final agencyNames = getAgencyNames?.map((e) => ItemLeadFilterModel(displayName: e, filterKey: LeadFilterKey.agencyName, isSelected: initialSelectedAgencyNames?.contains(e) ?? false)).toList();
      if (agencyNames?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.agencyName, agencyNames!, emit);
      }
    } catch (ex) {
      "Error while initializing agency names ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initAssignedToUsers(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedSecondaryUsersIds = _selectedLeadFilterModel.assignedToIds;
      bool hasPermission = (getIt<UsersDataRepository>().checkHasPermission(AppModule.lead, CommandType.viewAllLeads) && getIt<UsersDataRepository>().checkHasPermission(AppModule.users, CommandType.viewForFilter));
      final getAllUsers = hasPermission ? await _usersDataRepository.getAllUsers() : await _usersDataRepository.getAllReportees();
      final assignToUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => ItemLeadFilterModel(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                filterKey: LeadFilterKey.assignedTo,
                isSelected: initialSelectedSecondaryUsersIds?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      assignToUsers?.insert(0, ItemLeadFilterModel(displayName: "You", description: _currentUser?.userId ?? "", filterKey: LeadFilterKey.assignedTo, isSelected: initialSelectedSecondaryUsersIds?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => assignToUsers?.add(ItemLeadFilterModel(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: LeadFilterKey.assignedTo, isSelected: initialSelectedSecondaryUsersIds?.contains(e?.id ?? "") ?? false)));
      if (assignToUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.assignedTo, assignToUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing secondary owners ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initAssignedFromUsers(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedAssignedFromUsersId = _selectedLeadFilterModel.assignFromIds;
      bool hasPermission = (getIt<UsersDataRepository>().checkHasPermission(AppModule.lead, CommandType.viewAllLeads) && getIt<UsersDataRepository>().checkHasPermission(AppModule.users, CommandType.viewForFilter));
      final getAllUsers = hasPermission ? await _usersDataRepository.getAllUsers() : await _usersDataRepository.getAllReportees();
      final assignFromUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => ItemLeadFilterModel(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                filterKey: LeadFilterKey.assignedFrom,
                isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      assignFromUsers?.insert(0, ItemLeadFilterModel(displayName: "You", description: _currentUser?.userId ?? "", filterKey: LeadFilterKey.assignedTo, isSelected: initialSelectedAssignedFromUsersId?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => assignFromUsers?.add(ItemLeadFilterModel(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: LeadFilterKey.assignedTo, isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false)));
      if (assignFromUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.assignedFrom, assignFromUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing assigned from users ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initAssignedSecondaryOwnerFrom(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedAssignedFromUsersId = _selectedLeadFilterModel.secondaryFromIds;
      final getAllUsers = await _usersDataRepository.getAllUsers();
      final assignFromUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => ItemLeadFilterModel(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                filterKey: LeadFilterKey.secondaryOwnerFrom,
                isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      assignFromUsers?.insert(0, ItemLeadFilterModel(displayName: "You", description: _currentUser?.userId ?? "", filterKey: LeadFilterKey.assignedTo, isSelected: initialSelectedAssignedFromUsersId?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => assignFromUsers?.add(ItemLeadFilterModel(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: LeadFilterKey.assignedTo, isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false)));
      if (assignFromUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.secondaryOwnerFrom, assignFromUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing assigned from secondary owner users ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initAssignedSecondaryOwner(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedAssignedFromUsersId = _selectedLeadFilterModel.secondaryUsers;
      final getAllUsers = await _usersDataRepository.getAllUsers();
      final assignFromUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => ItemLeadFilterModel(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                filterKey: LeadFilterKey.secondaryOwner,
                isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      assignFromUsers?.insert(0, ItemLeadFilterModel(displayName: "You", description: _currentUser?.userId ?? "", filterKey: LeadFilterKey.assignedTo, isSelected: initialSelectedAssignedFromUsersId?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => assignFromUsers?.add(ItemLeadFilterModel(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: LeadFilterKey.assignedTo, isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false)));
      if (assignFromUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.secondaryOwner, assignFromUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing assigned from assigned secondary users ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initAssignmentDoneBy(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedAssignedFromUsersId = _selectedLeadFilterModel.doneBy;
      final getAllUsers = await _usersDataRepository.getAllUsers();
      final assignFromUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => ItemLeadFilterModel(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                filterKey: LeadFilterKey.doneBy,
                isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      assignFromUsers?.insert(0, ItemLeadFilterModel(displayName: "You", description: _currentUser?.userId ?? "", filterKey: LeadFilterKey.assignedTo, isSelected: initialSelectedAssignedFromUsersId?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => assignFromUsers?.add(ItemLeadFilterModel(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: LeadFilterKey.assignedTo, isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false)));
      if (assignFromUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.doneBy, assignFromUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing assigned from users ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initBookedByUsers(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedAssignedFromUsersId = _selectedLeadFilterModel.bookedByIds;
      final getAllUsers = await _usersDataRepository.getAllUsers();
      final assignFromUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => ItemLeadFilterModel(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                filterKey: LeadFilterKey.bookedBy,
                isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      assignFromUsers?.insert(0, ItemLeadFilterModel(displayName: "You", description: _currentUser?.userId ?? "", filterKey: LeadFilterKey.bookedBy, isSelected: initialSelectedAssignedFromUsersId?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => assignFromUsers?.add(ItemLeadFilterModel(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: LeadFilterKey.bookedBy, isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false)));
      if (assignFromUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.bookedBy, assignFromUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing assigned from bookedBy  users ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initQualifiedByUsers(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedQualifiedByUsersId = _selectedLeadFilterModel.qualifiedByIds;
      final getAllUsers = await _usersDataRepository.getAllUsers();
      final qualifiedByUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => ItemLeadFilterModel(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                filterKey: LeadFilterKey.qualifiedBy,
                isSelected: initialSelectedQualifiedByUsersId?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      qualifiedByUsers?.insert(0, ItemLeadFilterModel(displayName: "You", description: _currentUser?.userId ?? "", filterKey: LeadFilterKey.qualifiedBy, isSelected: initialSelectedQualifiedByUsersId?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => qualifiedByUsers?.add(ItemLeadFilterModel(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: LeadFilterKey.qualifiedBy, isSelected: initialSelectedQualifiedByUsersId?.contains(e?.id ?? "") ?? false)));
      if (qualifiedByUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.qualifiedBy, qualifiedByUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing qualified by users ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initReferralName(Emitter<LeadFilterState> emit) async {}

  Future<void> _initReferralEmail(Emitter<LeadFilterState> emit) async {}

  Future<void> _initReferralPhoneNo(Emitter<LeadFilterState> emit) async {}

  Future<void> _initCreatedByUser(Emitter<LeadFilterState> emit) async {
    try {
      //change it
      final initialSelectedAssignedFromUsersId = _selectedLeadFilterModel.createdByIds;

      final getAllUsers = await _usersDataRepository.getAllUsers();
      final assignFromUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => ItemLeadFilterModel(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                filterKey: LeadFilterKey.createdBy,
                isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      assignFromUsers?.insert(0, ItemLeadFilterModel(displayName: "You", description: _currentUser?.userId ?? "", filterKey: LeadFilterKey.createdBy, isSelected: initialSelectedAssignedFromUsersId?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => assignFromUsers?.add(ItemLeadFilterModel(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: LeadFilterKey.createdBy, isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false)));
      if (assignFromUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.createdBy, assignFromUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing created by user ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initLastModifiedByUser(Emitter<LeadFilterState> emit) async {
    try {
      //change it
      final initialSelectedAssignedFromUsersId = _selectedLeadFilterModel.lastModifiedByIds;

      final getAllUsers = await _usersDataRepository.getAllUsers();
      final assignFromUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => ItemLeadFilterModel(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                filterKey: LeadFilterKey.lastModifiedBy,
                isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      assignFromUsers?.insert(0, ItemLeadFilterModel(displayName: "You", description: _currentUser?.userId ?? "", filterKey: LeadFilterKey.lastModifiedBy, isSelected: initialSelectedAssignedFromUsersId?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => assignFromUsers?.add(ItemLeadFilterModel(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: LeadFilterKey.lastModifiedBy, isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false)));
      if (assignFromUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.lastModifiedBy, assignFromUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing last modified by user ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initDeletedByUser(Emitter<LeadFilterState> emit) async {
    try {
      //change it
      final initialSelectedAssignedFromUsersId = _selectedLeadFilterModel.archivedByIds;

      final getAllUsers = await _usersDataRepository.getAllUsers();
      final assignFromUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => ItemLeadFilterModel(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                filterKey: LeadFilterKey.deletedBy,
                isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      assignFromUsers?.insert(0, ItemLeadFilterModel(displayName: "You", description: _currentUser?.userId ?? "", filterKey: LeadFilterKey.deletedBy, isSelected: initialSelectedAssignedFromUsersId?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => assignFromUsers?.add(ItemLeadFilterModel(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: LeadFilterKey.deletedBy, isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false)));
      if (assignFromUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.deletedBy, assignFromUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing deleted by user ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initRestoredByUser(Emitter<LeadFilterState> emit) async {
    try {
      //change it
      final initialSelectedAssignedFromUsersId = _selectedLeadFilterModel.restoredByIds;

      final getAllUsers = await _usersDataRepository.getAllUsers();
      final assignFromUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => ItemLeadFilterModel(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                filterKey: LeadFilterKey.restoredBy,
                isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      assignFromUsers?.insert(0, ItemLeadFilterModel(displayName: "You", description: _currentUser?.userId ?? "", filterKey: LeadFilterKey.restoredBy, isSelected: initialSelectedAssignedFromUsersId?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => assignFromUsers?.add(ItemLeadFilterModel(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: LeadFilterKey.restoredBy, isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false)));
      if (assignFromUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.restoredBy, assignFromUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing restored by user ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initOriginalOwners(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedOriginalOwnerUsersId = _selectedLeadFilterModel.originalOwnerIds;

      final getAllUsers = await _usersDataRepository.getAllUsers();
      final assignFromUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => ItemLeadFilterModel(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                filterKey: LeadFilterKey.originalOwner,
                isSelected: initialSelectedOriginalOwnerUsersId?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      assignFromUsers?.insert(0, ItemLeadFilterModel(displayName: "You", description: _currentUser?.userId ?? "", filterKey: LeadFilterKey.originalOwner, isSelected: initialSelectedOriginalOwnerUsersId?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => assignFromUsers?.add(ItemLeadFilterModel(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: LeadFilterKey.originalOwner, isSelected: initialSelectedOriginalOwnerUsersId?.contains(e?.id ?? "") ?? false)));
      if (assignFromUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.originalOwner, assignFromUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing restored by user ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initConvertedFromData(Emitter<LeadFilterState> emit) async {
    try {
      List<ItemLeadFilterModel<String>> dataConvertedValues = [
        ItemLeadFilterModel<String>(displayName: 'Yes', description: 'true', filterKey: LeadFilterKey.dataConverted, isSelected: _selectedLeadFilterModel.dataConverted == "Yes", value: "Yes"),
        ItemLeadFilterModel<String>(displayName: 'No', description: 'false', filterKey: LeadFilterKey.dataConverted, isSelected: _selectedLeadFilterModel.dataConverted == "No", value: "No"),
      ];
      _updateFilterCategory(LeadFilterKey.dataConverted, dataConvertedValues, emit);
    } catch (ex) {
      "Error while initializing qualified by users ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initMeetingDoneByUsers(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedAssignedFromUsersId = _selectedLeadFilterModel.appointmentDoneByUserIds;
      final getAllUsers = await _usersDataRepository.getAllUsers();
      final assignFromUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => ItemLeadFilterModel(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                filterKey: LeadFilterKey.siteVisitOrMeetingDoneBy,
                isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      assignFromUsers?.insert(0, ItemLeadFilterModel(displayName: "You", description: _currentUser?.userId ?? "", filterKey: LeadFilterKey.assignedTo, isSelected: initialSelectedAssignedFromUsersId?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => assignFromUsers?.add(ItemLeadFilterModel(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: LeadFilterKey.assignedTo, isSelected: initialSelectedAssignedFromUsersId?.contains(e?.id ?? "") ?? false)));
      if (assignFromUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.siteVisitOrMeetingDoneBy, assignFromUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing assigned from bookedBy  users ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initChannelPartnerNames(Emitter<LeadFilterState> emit) async {
    try {
      List<String>? initSelectedChannelPartner = _selectedLeadFilterModel.channelPartnerNames;
      final channelPartnersResponse = await _getChannelPartnerNamesUseCase(NoParams());
      channelPartnersResponse.fold(
        (failure) => null,
        (success) {
          if (success != null && success.isNotEmpty) {
            final selectedAgencyNames = initSelectedChannelPartner?.nonNulls.map((e) => e).toList();
            final channelPartners = success
                .map((name) => ItemLeadFilterModel<String>(
                      displayName: name,
                      description: name,
                      filterKey: LeadFilterKey.channelPartnerName,
                      value: name,
                      isSelected: selectedAgencyNames?.contains(name) ?? false,
                    ))
                .toList();
            _updateFilterCategory(LeadFilterKey.channelPartnerName, channelPartners, emit);
          }
        },
      );
    } catch (ex) {
      "Error while initializing channel partner names ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initExcelSheets(Emitter<LeadFilterState> emit) async {
    try {
      List<String>? initSelectedExcelSheets = _selectedLeadFilterModel.excelSheets;
      final excelSheetsResponse = await _getUploadTypeNamesUseCase(NoParams());
      excelSheetsResponse.fold(
        (failure) => null,
        (success) {
          if (success != null && success.isNotEmpty) {
            final selectedAgencyNames = initSelectedExcelSheets?.nonNulls.map((e) => e).toList();
            final channelPartners = success
                .map((name) => ItemLeadFilterModel<String>(
                      displayName: name,
                      description: name,
                      filterKey: LeadFilterKey.channelPartnerName,
                      value: name,
                      isSelected: selectedAgencyNames?.contains(name) ?? false,
                    ))
                .toList();
            _updateFilterCategory(LeadFilterKey.excelSheet, channelPartners, emit);
          }
        },
      );
    } catch (ex) {
      "Error while initializing excel sheets ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initDesignation(Emitter<LeadFilterState> emit) async {
    try {
      List<String>? initSelectedDesignations = _selectedLeadFilterModel.designations;
      final designations = await _usersDataRepository.getAllDesignations();
      if (designations != null && designations.isNotEmpty) {
        final selectedDesignations = initSelectedDesignations?.nonNulls.map((e) => e).toList();
        final processedDesignations = designations
            .map((designation) => ItemLeadFilterModel<DtoWithNameEntity>(
                  displayName: designation?.name ?? '',
                  description: designation?.name ?? '',
                  value: designation,
                  filterKey: LeadFilterKey.designation,
                  isSelected: selectedDesignations?.contains(designation?.id) ?? false,
                ))
            .toList();
        _updateFilterCategory(LeadFilterKey.designation, processedDesignations, emit);
      }
    } catch (ex) {
      "Error while initializing designation ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initProfessions(Emitter<LeadFilterState> emit) async {
    try {
      //change it
      final initialSelectedProfession = _selectedLeadFilterModel.profession;
      final professionTypeFilters = Profession.values
          .map((e) => ItemLeadFilterModel(
                displayName: e.description,
                filterKey: LeadFilterKey.profession,
                value: e,
                isSelected: initialSelectedProfession?.contains(e) ?? false,
              ))
          .toList();
      professionTypeFilters.removeWhere((element) => element.displayName == Profession.none.description);
      _updateFilterCategory(LeadFilterKey.profession, professionTypeFilters, emit);
    } catch (ex) {
      "Error while initializing professions ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initFacebookProperties(Emitter<LeadFilterState> emit) async {
    try {
      List<String>? initSelectedAdditionalProperties = _selectedLeadFilterModel.facebookProperties;
      final additionalPropertiesResponse = await _getAdditionalPropertyKeysUseCase(NoParams());
      additionalPropertiesResponse.fold(
        (failure) => null,
        (success) async {
          if (success != null && success.isNotEmpty) {
            final selectedAgencyNames = initSelectedAdditionalProperties?.nonNulls.map((e) => e).toList();
            final faceBookProperties = success
                .map((name) => ItemLeadFilterModel<String>(
                      displayName: name,
                      description: name,
                      filterKey: LeadFilterKey.facebookProperties,
                      value: name,
                      isSelected: selectedAgencyNames?.contains(name) ?? false,
                    ))
                .toList();
            _updateFilterCategory(LeadFilterKey.facebookProperties, faceBookProperties, emit);
          }
        },
      );
      if (initSelectedAdditionalProperties != null && initSelectedAdditionalProperties.isNotEmpty) {
        _initFacebookPropertyValues(emit, key: initSelectedAdditionalProperties.firstOrNull);
      }
    } catch (ex) {
      "Error while initializing facebook properties ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initFacebookPropertyValues(Emitter<LeadFilterState> emit, {String? key}) async {
    try {
      List<String>? initSelectedAdditionalProperties = _selectedLeadFilterModel.facebookPropertyValues;

      final additionalPropertiesResponse = await _getAdditionalPropertyValuesUseCase(key ?? '');
      additionalPropertiesResponse.fold(
        (failure) => _updateFilterCategory(LeadFilterKey.facebookPropertyValues, [], emit),
        (success) {
          if (success != null) {
            final selectedAgencyNames = initSelectedAdditionalProperties?.nonNulls.map((e) => e).toList();
            final faceBookPropertyValues = success
                .map((name) => ItemLeadFilterModel<String>(
                      displayName: name,
                      description: name,
                      filterKey: LeadFilterKey.facebookPropertyValues,
                      value: name,
                      isSelected: selectedAgencyNames?.contains(name) ?? false,
                    ))
                .toList();
            _updateFilterCategory(LeadFilterKey.facebookPropertyValues, faceBookPropertyValues, emit);
          } else {
            _updateFilterCategory(LeadFilterKey.facebookPropertyValues, [], emit);
          }
        },
      );
    } catch (ex) {
      "Error while initializing facebook properties ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initMinBudgetFilters(Emitter<LeadFilterState> emit) async {
    try {
      await _initializeCurrency(emit, _selectedLeadFilterModel.currency);

      if (_selectedLeadFilterModel.fromMinBudget == null && _selectedLeadFilterModel.toMinBudget == null) emit(state.copyWith(updateSelectMinBudget: false));

      final initialSelectMinBudget = _selectedLeadFilterModel.fromMinBudget ?? _selectedLeadFilterModel.toMinBudget;

      emit(state.copyWith(
        updateSelectMinBudget: initialSelectMinBudget != null ? true : false,
      ));
    } catch (ex) {
      "Error while initializing carpet area units ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initMaxBudgetFilters(Emitter<LeadFilterState> emit) async {
    try {
      await _initializeCurrency(emit, _selectedLeadFilterModel.currency);

      if (_selectedLeadFilterModel.toMaxBudget == null && _selectedLeadFilterModel.fromMaxBudget == null) emit(state.copyWith(updateSelectMaxBudget: false));

      final initialSelectMaxBudget = _selectedLeadFilterModel.fromMaxBudget ?? _selectedLeadFilterModel.toMaxBudget;

      emit(state.copyWith(
        updateSelectMaxBudget: initialSelectMaxBudget != null ? true : false,
      ));
    } catch (ex) {
      "Error while initializing carpet area units ${ex.toString()}".printInConsole();
    }
  }

  void _initDateRanges(Emitter<LeadFilterState> emit) {
    try {
      final initialSelectedDateType = _selectedLeadFilterModel.dateType;
      final dateTypes = DateType.values
          .map((type) {
            if (type != DateType.none) {
              return SelectableItem<DateType>(title: type.description, value: type);
            }
          })
          .nonNulls
          .toList();

      final selectedDateType = dateTypes.firstWhereOrNull((element) => element.value == initialSelectedDateType);
      emit(state.copyWith(dateTypes: dateTypes, selectedDateType: selectedDateType, updateSelectedDateType: initialSelectedDateType != null ? true : false, updateSelectedToDate: (_selectedLeadFilterModel.toDate.isNotNullOrEmpty()) ? true : false, updateSelectedFromDate: (_selectedLeadFilterModel.fromDate.isNotNullOrEmpty()) ? true : false));

      final initialSelectedDateRange = _selectedLeadFilterModel.dateRange ?? _selectedLeadFilterModel.possessionTypeDateRange;
      if (state.selectedDateType?.value != DateType.possessionDate) {
        var dateRangeFilters = DateRange.values.map((e) => ItemLeadFilterModel<DateRange>(displayName: e.description, value: e, filterKey: LeadFilterKey.dateRange, isSelected: initialSelectedDateRange == getEnumFromDescription(DateRange.values, e.description))).toList();
        dateRangeFilters = dateRangeFilters.map((item) => item.displayName == DateRange.customDate.description ? item.copyWith(hasCustomView: true) : item).toList();
        _updateFilterCategory(LeadFilterKey.dateRange, dateRangeFilters, emit);
      } else {
        var dateRangeFilters = PossessionType.values
            .where((e) => e.value != 0) // or use e.value if you have a custom getter
            .map((e) => ItemLeadFilterModel<PossessionType>(
                  displayName: e.description,
                  value: e,
                  filterKey: LeadFilterKey.dateRange,
                  isSelected: initialSelectedDateRange == getEnumFromDescription(PossessionType.values, e.description),
                ))
            .toList();
        dateRangeFilters = dateRangeFilters.map((item) => item.displayName == PossessionType.customDate.description ? item.copyWith(hasCustomView: true, hasMonthFilter: true) : item).toList();
        _updateFilterCategory(LeadFilterKey.dateRange, dateRangeFilters, emit);
      }
    } catch (ex) {
      "Error while initializing date range ${ex.toString()}".printInConsole();
    }
  }

  void updateDateRange(Emitter<LeadFilterState> emit) {
    if (state.selectedDateType?.value != DateType.possessionDate) {
      var dateRangeFilters = DateRange.values
          .map((e) => ItemLeadFilterModel(
                displayName: e.description,
                filterKey: LeadFilterKey.dateRange,
              ))
          .toList();
      dateRangeFilters = dateRangeFilters.map((item) => item.displayName == DateRange.customDate.description ? item.copyWith(hasCustomView: true) : item).toList();
      _updateFilterCategory(LeadFilterKey.dateRange, dateRangeFilters, emit);
    } else {
      List<ItemLeadFilterModel> dateRangeFilters = PossessionType.values
          .where((e) => e.value != 0)
          .map((e) => ItemLeadFilterModel<PossessionType>(
                displayName: e.description,
                value: e,
                filterKey: LeadFilterKey.dateRange,
              ))
          .toList();
      dateRangeFilters = dateRangeFilters.map((item) => item.displayName == PossessionType.customDate.description ? item.copyWith(hasCustomView: true, hasMonthFilter: true) : item).toList();
      _updateFilterCategory(LeadFilterKey.dateRange, dateRangeFilters, emit);
    }
  }

  Future<void> _initIsUntouched(Emitter<LeadFilterState> emit) async {
    String? initialBoolValue = _selectedLeadFilterModel.isUntouched;
    List<ItemLeadFilterModel<String>>? boolValues = [
      ItemLeadFilterModel<String>(displayName: "Yes", value: "Yes", filterKey: LeadFilterKey.unTouched, isSelected: "Yes" == initialBoolValue),
      ItemLeadFilterModel<String>(displayName: "No", value: "No", filterKey: LeadFilterKey.unTouched, isSelected: "No" == initialBoolValue),
    ];
    _updateFilterCategory(LeadFilterKey.unTouched, boolValues, emit);
  }

  Future<void> _initIsShowParentLead(Emitter<LeadFilterState> emit) async {
    String? initialBoolValue = _selectedLeadFilterModel.isShowParentLead;
    List<ItemLeadFilterModel<String>>? boolValues = [
      ItemLeadFilterModel<String>(displayName: "Yes", value: "Yes", filterKey: LeadFilterKey.showParentLead, isSelected: "Yes" == initialBoolValue),
      ItemLeadFilterModel<String>(displayName: "No", value: "No", filterKey: LeadFilterKey.showParentLead, isSelected: "No" == initialBoolValue),
    ];
    _updateFilterCategory(LeadFilterKey.showParentLead, boolValues, emit);
  }

  Future<void> _initIsShowUniqueLead(Emitter<LeadFilterState> emit) async {
    String? initialBoolValue = _selectedLeadFilterModel.isShowUniqueLead;
    List<ItemLeadFilterModel<String>>? boolValues = [
      ItemLeadFilterModel<String>(displayName: "Yes", value: "Yes", filterKey: LeadFilterKey.showUniqueLead, isSelected: "Yes" == initialBoolValue),
      ItemLeadFilterModel<String>(displayName: "No", value: "No", filterKey: LeadFilterKey.showUniqueLead, isSelected: "No" == initialBoolValue),
    ];
    _updateFilterCategory(LeadFilterKey.showUniqueLead, boolValues, emit);
  }

  Future<void> _initShowChildCount(Emitter<LeadFilterState> emit) async {}

  Future<void> _initLongitude(Emitter<LeadFilterState> emit) async {}

  Future<void> _initRadiusInKm(Emitter<LeadFilterState> emit) async {}

  Future<void> _initLatitude(Emitter<LeadFilterState> emit) async {}

  Future<void> _initCarpetAreaUnits(Emitter<LeadFilterState> emit) async {
    try {
      if (_selectedLeadFilterModel.maxCarpetArea == null && _selectedLeadFilterModel.minCarpetArea == null) emit(state.copyWith(updateSelectCarpetArea: false));
      final initialSelectCarpetAreaUnit = _selectedLeadFilterModel.carpetAreaUnitId;
      final initialSelectSaleable = _selectedLeadFilterModel.minCarpetArea ?? _selectedLeadFilterModel.maxCarpetArea;
      List<SelectableItem<MasterAreaUnitsModel>>? areaUnits = await _masterDataRepository.getAreaUnits().then((value) {
        return value?.map((type) => SelectableItem<MasterAreaUnitsModel>(title: type?.unit ?? '', value: type)).nonNulls.toList();
      });
      final selectedCarpetAreaUnit = areaUnits?.firstWhereOrNull((element) => element.value?.id == initialSelectCarpetAreaUnit);
      emit(state.copyWith(
        selectableAreaUnits: areaUnits,
        selectedCarpetAreaUnit: selectedCarpetAreaUnit,
        updateSelectCarpetArea: initialSelectSaleable != null ? true : false,
        updateSelectCarpetAreaUnit: initialSelectCarpetAreaUnit != null ? true : false,
      ));
    } catch (ex) {
      "Error while initializing carpet area units ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initBuildUpAreaUnits(Emitter<LeadFilterState> emit) async {
    try {
      if (_selectedLeadFilterModel.maxBuiltUpArea == null && _selectedLeadFilterModel.minBuiltUpArea == null) emit(state.copyWith(updateSelectBuiltArea: false));
      final initialSelectBuiltUpAreaUnit = _selectedLeadFilterModel.builtUpAreaUnitId;
      final initialSelectSaleable = _selectedLeadFilterModel.minBuiltUpArea ?? _selectedLeadFilterModel.maxBuiltUpArea;

      List<SelectableItem<MasterAreaUnitsModel>>? areas = await _masterDataRepository.getAreaUnits().then((value) {
        return value?.map((type) => SelectableItem<MasterAreaUnitsModel>(title: type?.unit ?? '', value: type)).nonNulls.toList();
      });
      final selectedBuiltUpAreaUnit = areas?.firstWhereOrNull((element) => element.value?.id == initialSelectBuiltUpAreaUnit);
      emit(state.copyWith(
        selectableAreaUnits: areas,
        selectedBuildUpAreaUnit: selectedBuiltUpAreaUnit,
        updateSelectBuiltArea: initialSelectSaleable != null ? true : false,
        updateSelectBuildUpAreaUnit: initialSelectBuiltUpAreaUnit != null ? true : false,
      ));
    } catch (ex) {
      "Error while initializing build up area units ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initSealableAreaUnits(Emitter<LeadFilterState> emit) async {
    try {
      if (_selectedLeadFilterModel.maxSaleAbleArea == null && _selectedLeadFilterModel.minSaleAbleArea == null) emit(state.copyWith(updateSelectSaleableArea: false));
      final initialSelectSaleableAreaUnit = _selectedLeadFilterModel.saleableAreaUnitId;
      final initialSelectSaleable = _selectedLeadFilterModel.minSaleAbleArea ?? _selectedLeadFilterModel.maxSaleAbleArea;
      List<SelectableItem<MasterAreaUnitsModel>>? areaUnits = await _masterDataRepository.getAreaUnits().then((value) {
        return value?.map((type) => SelectableItem<MasterAreaUnitsModel>(title: type?.unit ?? '', value: type)).nonNulls.toList();
      });
      final selectedSaleableAreaUnit = areaUnits?.firstWhereOrNull((element) => element.value?.id == initialSelectSaleableAreaUnit);
      emit(state.copyWith(
        selectableAreaUnits: areaUnits,
        selectedSalableAreaUnit: selectedSaleableAreaUnit,
        updateSelectSaleableArea: initialSelectSaleable != null ? true : false,
        updateSelectSalableAreaUnit: initialSelectSaleableAreaUnit != null ? true : false,
      ));
    } catch (ex) {
      "Error while initializing sealable area units ${ex.toString()}".printInConsole();
    }
  }

  void _initNoOfBHKs(Emitter<LeadFilterState> emit) {
    try {
      final initialSelectedNoOfBHKs = _selectedLeadFilterModel.noOfBHKs;
      final dateRangeFilters = NoOfBHK.values.map((e) => ItemLeadFilterModel<double>(displayName: e.description, value: e.noOfBhk, filterKey: LeadFilterKey.noOfBHK, isSelected: initialSelectedNoOfBHKs?.contains(e.noOfBhk) ?? false)).toList();
      _updateFilterCategory(LeadFilterKey.noOfBHK, dateRangeFilters, emit);
    } catch (ex) {
      "Error while initializing no of bhk ${ex.toString()}".printInConsole();
    }
  }

  void _initFurnishingStatus(Emitter<LeadFilterState> emit) {
    try {
      final initialSelectedFurnishingStatus = _selectedLeadFilterModel.furnished;
      final furnishingStatuses = FurnishStatus.values.where((e) => e != FurnishStatus.none).map((e) => ItemLeadFilterModel<int>(displayName: e.description, value: e.value, filterKey: LeadFilterKey.furnished, isSelected: initialSelectedFurnishingStatus?.contains(e.value) ?? false)).toList();
      _updateFilterCategory(LeadFilterKey.furnished, furnishingStatuses, emit);
    } catch (ex) {
      "Error while initializing Furnishing Status ${ex.toString()}".printInConsole();
    }
  }

  void _initNoOfBeds(Emitter<LeadFilterState> emit) {
    try {
      final initialSelectedNoOfBeds = _selectedLeadFilterModel.beds;
      final beds = Beds.values.map((e) => ItemLeadFilterModel<int>(displayName: e.description, value: e.value, filterKey: LeadFilterKey.beds, isSelected: initialSelectedNoOfBeds?.contains(e.value) ?? false)).toList();
      _updateFilterCategory(LeadFilterKey.beds, beds, emit);
    } catch (ex) {
      "Error while initializing Beds ${ex.toString()}".printInConsole();
    }
  }

  void _initNoOfBaths(Emitter<LeadFilterState> emit) {
    try {
      final initialSelectedNoOfBaths = _selectedLeadFilterModel.baths;
      final baths = NoOfBr.values.map((e) => ItemLeadFilterModel<int>(displayName: e.description, value: e.noOfBr.toInt(), filterKey: LeadFilterKey.baths, isSelected: initialSelectedNoOfBaths?.contains(e.noOfBr.toInt()) ?? false)).toList();
      _updateFilterCategory(LeadFilterKey.baths, baths, emit);
    } catch (ex) {
      "Error while initializing Baths ${ex.toString()}".printInConsole();
    }
  }

  void _initNoOfFloors(Emitter<LeadFilterState> emit) {
    try {
      final initialSelectedNoOfFloors = _selectedLeadFilterModel.floors;
      List<SelectableItem<String>> floors = [
        SelectableItem(title: 'upper basement'),
        SelectableItem(title: 'lower basement'),
        SelectableItem(title: 'ground floor'),
      ];

      for (int i = 1; i <= 200; i++) {
        floors.add(SelectableItem(title: '$i'));
      }
      final allFloors = floors.map((e) => ItemLeadFilterModel<String>(displayName: e.title, value: e.title, filterKey: LeadFilterKey.floors, isSelected: initialSelectedNoOfFloors?.contains(e.title) ?? false)).toList();

      _updateFilterCategory(LeadFilterKey.floors, allFloors, emit);
    } catch (ex) {
      "Error while initializing Floors ${ex.toString()}".printInConsole();
    }
  }

  void _initOfferingTypes(Emitter<LeadFilterState> emit) {
    try {
      final initialSelectedOfferingType = _selectedLeadFilterModel.offerTypes;
      final offerTypes = OfferingType.values.where((e) => e != OfferingType.none).map((e) => ItemLeadFilterModel<int>(displayName: e.description, value: e.value, filterKey: LeadFilterKey.offerTypes, isSelected: initialSelectedOfferingType?.contains(e.value) ?? false)).toList();
      _updateFilterCategory(LeadFilterKey.offerTypes, offerTypes, emit);
    } catch (ex) {
      "Error while initializing Offering Types ${ex.toString()}".printInConsole();
    }
  }

  void _initBHKTypes(Emitter<LeadFilterState> emit) {
    try {
      final initialSelectedBHKTypes = _selectedLeadFilterModel.bhkTypes;
      final bhkTypes = BHKType.values.map((e) => ItemLeadFilterModel(displayName: e.description, filterKey: LeadFilterKey.bHKTypes, isSelected: initialSelectedBHKTypes?.contains(e) ?? false)).toList();
      bhkTypes.removeWhere((element) => element.displayName == BHKType.none.description);
      _updateFilterCategory(LeadFilterKey.bHKTypes, bhkTypes, emit);
    } catch (ex) {
      "Error while initializing bhkTypes ${ex.toString()}".printInConsole();
    }
  }

  void _initPropertyTypes(Emitter<LeadFilterState> emit) {
    try {
      final initialSelectedPropertyTypes = _selectedLeadFilterModel.propertyTypes;
      final propertyTypes = PropertyType.values.map((e) => ItemLeadFilterModel(displayName: e.description, description: e.baseId, filterKey: LeadFilterKey.propertyType, isSelected: initialSelectedPropertyTypes?.contains(e.baseId) ?? false)).toList();
      _updateFilterCategory(LeadFilterKey.propertyType, propertyTypes, emit);
    } catch (ex) {
      "Error while initializing propertyTypes ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initNationality(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedPropertyTypes = _selectedLeadFilterModel.nationality;
      final initialCountry = await _leadsRepository.getLeadNationality();
      var countries = initialCountry?.map((e) => ItemLeadFilterModel(displayName: e, filterKey: LeadFilterKey.nationality, isSelected: initialSelectedPropertyTypes?.contains(e) ?? false)).toList();

      _updateFilterCategory(LeadFilterKey.nationality, countries ?? [], emit);
    } catch (ex) {
      "Error while initializing nationality ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initPropertySubTypes(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedPropertySubTypes = _selectedLeadFilterModel.propertySubTypes;
      final masterPropertyTypes = await _masterDataRepository.getCustomPropertyTypes(isPropertyListingEnabled: getIt<LeadratHomeBloc>().isPropertyListingEnabled);
      if (masterPropertyTypes == null) return;
      _allPropertyTypes = masterPropertyTypes;
      List<ItemLeadFilterModel> subTypes = [];
      for (var item in masterPropertyTypes) {
        item.childTypes?.forEach((element) => subTypes.add(
              ItemLeadFilterModel(displayName: element.displayName ?? "", description: element.id, filterKey: LeadFilterKey.propertySubType, isSelected: initialSelectedPropertySubTypes?.contains(element.id) ?? false),
            ));
      }
      subTypes.sort((a, b) => a.displayName.compareTo(b.displayName));
      _updateFilterCategory(LeadFilterKey.propertySubType, subTypes, emit);
    } catch (ex) {
      "Error while initializing lead property Sub types ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _updatePropertySubTypes(Emitter<LeadFilterState> emit, List<ItemLeadFilterModel>? selectedPropertyTypes) async {
    if (_allPropertyTypes.isEmpty) await _initPropertySubTypes(emit);
    if (selectedPropertyTypes != null) {
      if (selectedPropertyTypes.isNotEmpty) {
        List<ItemLeadFilterModel> subTypes = [];
        for (var item in selectedPropertyTypes) {
          if (item.isSelected) {
            final selectedPropertySubTypes = _allPropertyTypes.firstWhereOrNull((e) => e.displayName == item.displayName);
            selectedPropertySubTypes?.childTypes?.forEach((element) => subTypes.add(ItemLeadFilterModel(displayName: element.displayName ?? "", description: element.id, filterKey: LeadFilterKey.propertySubType)));
          }
        }
        _updateFilterCategory(LeadFilterKey.propertySubType, subTypes, emit);
      } else {
        _initPropertySubTypes(emit);
      }
    }
  }

  Future<void> _initLeadLocations(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedLeadAddresses = _selectedLeadFilterModel.locations;
      final leadAddresses = await _masterDataRepository.getAllLeadAddresses();
      final leadLocations = leadAddresses
          ?.where((address) => address.trim().isNotEmpty) // Filter out empty addresses
          .map((address) => ItemLeadFilterModel(
                displayName: address,
                filterKey: LeadFilterKey.locations,
                isSelected: initialSelectedLeadAddresses?.contains(address) ?? false,
              ))
          .toList();
      if (leadLocations?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.locations, leadLocations!, emit);
      }
    } catch (ex) {
      "Error while initializing lead locations ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initCommunities(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedCommunities = _selectedLeadFilterModel.communities;
      final communities = await _leadsRepository.getCommunities();
      final leadCommunities = communities?.map((community) => ItemLeadFilterModel(displayName: community, filterKey: LeadFilterKey.community, isSelected: initialSelectedCommunities?.contains(community) ?? false)).toList();
      if (leadCommunities?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.community, leadCommunities!, emit);
      }
    } catch (ex) {
      "Error while initializing Communities ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initSubCommunities(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedSubCommunities = _selectedLeadFilterModel.subCommunities;
      final subCommunities = await _leadsRepository.getSubCommunities();
      final leadSubCommunities = subCommunities?.map((subCommunity) => ItemLeadFilterModel(displayName: subCommunity, filterKey: LeadFilterKey.subCommunity, isSelected: initialSelectedSubCommunities?.contains(subCommunity) ?? false)).toList();
      if (leadSubCommunities?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.subCommunity, leadSubCommunities!, emit);
      }
    } catch (ex) {
      "Error while initializing sub communities ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initTowerNames(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedTowerNames = _selectedLeadFilterModel.towerNames;
      final towerNames = await _leadsRepository.getTowerNames();
      final leadTowerNames = towerNames?.map((towerName) => ItemLeadFilterModel(displayName: towerName, filterKey: LeadFilterKey.towerName, isSelected: initialSelectedTowerNames?.contains(towerName) ?? false)).toList();
      if (leadTowerNames?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.towerName, leadTowerNames!, emit);
      }
    } catch (ex) {
      "Error while initializing tower names ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initCountries(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedCountries = _selectedLeadFilterModel.countries;
      final countries = await _leadsRepository.getCountries();
      final leadCountries = countries?.map((country) => ItemLeadFilterModel(displayName: country, filterKey: LeadFilterKey.country, isSelected: initialSelectedCountries?.contains(country) ?? false)).toList();
      if (leadCountries?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.country, leadCountries!, emit);
      }
    } catch (ex) {
      "Error while initializing countries ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initProperties(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedProperties = _selectedLeadFilterModel.properties;
      final propertiesNameWithIdResponse = await _getPropertyNameWithIdUseCase(NoParams());
      propertiesNameWithIdResponse.fold(
        (failure) => null,
        (propertiesNameWithId) {
          final properties = <String>{};
          final allProperties = propertiesNameWithId
              ?.toList()
              .where((item) => properties.add(item.title ?? ""))
              .map((item) => ItemLeadFilterModel(
                    displayName: item.title ?? "",
                    description: item.id,
                    filterKey: LeadFilterKey.properties,
                    isSelected: initialSelectedProperties?.contains(item.title) ?? false,
                  ))
              .toList();
          if (allProperties?.isNotEmpty ?? false) {
            _updateFilterCategory(LeadFilterKey.properties, allProperties ?? [], emit);
          }
        },
      );
    } catch (ex) {
      "Error while initializing lead properties ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initProjects(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedProjects = _selectedLeadFilterModel.projects;
      final projectsNameWithIdResponse = await _getProjectNameWithIdUseCase(NoParams());
      projectsNameWithIdResponse.fold(
        (failure) => null,
        (propertiesNameWithId) {
          final leadLocations = propertiesNameWithId?.map((item) => ItemLeadFilterModel(displayName: item.name ?? "", description: item.id, filterKey: LeadFilterKey.projects, isSelected: initialSelectedProjects?.contains(item.name) ?? false)).toList();
          if (leadLocations?.isNotEmpty ?? false) {
            _updateFilterCategory(LeadFilterKey.projects, leadLocations!, emit);
          }
        },
      );
    } catch (ex) {
      "Error while initializing lead projects ${ex.toString()}".printInConsole();
    }
  }

  void _updateFilterCategory(LeadFilterKey filterKey, List<ItemLeadFilterModel> filters, Emitter<LeadFilterState> emit) {
    try {
      var updatedCategories = state.leadFilterCategories.map((category) => category.filterKey == filterKey ? category.copyWith(filters: filters, isInitialized: true) : category).toList();
      _updateState(emit, leadFilterCategories: updatedCategories);
    } catch (ex) {
      rethrow;
    }
  }

  Future<void> _initNetAreaUnits(Emitter<LeadFilterState> emit) async {
    try {
      if (_selectedLeadFilterModel.maxNetArea == null && _selectedLeadFilterModel.minNetArea == null) emit(state.copyWith(updateSelectNetArea: false));
      final initialSelectedNetAreaUnit = _selectedLeadFilterModel.netAreaUnitId;
      final initialSelectedPropertyArea = _selectedLeadFilterModel.maxNetArea ?? _selectedLeadFilterModel.minNetArea;
      List<SelectableItem<MasterAreaUnitsModel>>? areaUnits = await _masterDataRepository.getAreaUnits().then((value) {
        return value?.map((type) => SelectableItem<MasterAreaUnitsModel>(title: type?.unit ?? '', value: type)).nonNulls.toList();
      });
      final selectedNetAreaUnit = areaUnits?.firstWhereOrNull((element) => element.value?.id == initialSelectedNetAreaUnit);
      emit(state.copyWith(
        selectableAreaUnits: areaUnits,
        selectedNetAreaUnit: selectedNetAreaUnit,
        updateSelectNetArea: initialSelectedPropertyArea != null ? true : false,
        updateSelectNetAreaUnit: initialSelectedNetAreaUnit != null ? true : false,
      ));
    } catch (ex) {
      "Error while initializing net area units ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initPropertyAreaUnits(Emitter<LeadFilterState> emit) async {
    try {
      if (_selectedLeadFilterModel.maxPropertyArea == null && _selectedLeadFilterModel.minPropertyArea == null) emit(state.copyWith(updateSelectPropertyArea: false));
      final initialSelectedPropertyAreaUnit = _selectedLeadFilterModel.propertyAreaUnitId;
      final initialSelectedPropertyArea = _selectedLeadFilterModel.maxPropertyArea ?? _selectedLeadFilterModel.minPropertyArea;
      List<SelectableItem<MasterAreaUnitsModel>>? areaUnits = await _masterDataRepository.getAreaUnits().then((value) {
        return value?.map((type) => SelectableItem<MasterAreaUnitsModel>(title: type?.unit ?? '', value: type)).nonNulls.toList();
      });
      final selectedPropertyAreaUnit = areaUnits?.firstWhereOrNull((element) => element.value?.id == initialSelectedPropertyAreaUnit);
      emit(state.copyWith(
        selectableAreaUnits: areaUnits,
        selectedPropertyAreaUnit: selectedPropertyAreaUnit,
        updateSelectPropertyArea: initialSelectedPropertyArea != null ? true : false,
        updateSelectPropertyAreaUnit: initialSelectedPropertyAreaUnit != null ? true : false,
      ));
    } catch (ex) {
      "Error while initializing sealable area units ${ex.toString()}".printInConsole();
    }
  }

  void _initPurposes(Emitter<LeadFilterState> emit) {
    try {
      final initialSelectedPurposes = _selectedLeadFilterModel.purposes;
      final purposes = PurposeEnum.values.where((e) => e != PurposeEnum.none).map((e) => ItemLeadFilterModel<PurposeEnum>(displayName: e.description, value: e, filterKey: LeadFilterKey.purpose, isSelected: initialSelectedPurposes?.contains(e) ?? false)).toList();
      _updateFilterCategory(LeadFilterKey.purpose, purposes, emit);
    } catch (ex) {
      "Error while initializing Purposes  ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initLeadCities(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedLeadAddresses = _selectedLeadFilterModel.cities;
      if (_leadCities?.isEmpty ?? false) {
        _leadCities = await _leadsRepository.getAllLeadCities();
      }
      final result = _leadCities
          ?.where((city) => city?.trim().isNotEmpty ?? false)
          .map((city) => ItemLeadFilterModel(
                displayName: city ?? '',
                filterKey: LeadFilterKey.city,
                isSelected: initialSelectedLeadAddresses?.contains(city) ?? false,
              ))
          .toList();
      if (result?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.city, result!, emit);
      }
    } catch (ex) {
      "Error while initializing lead city ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initLeadZones(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedLeadAddresses = _selectedLeadFilterModel.zones;
      if (_leadZones?.isEmpty ?? false) {
        _leadZones = await _leadsRepository.getAllLeadZones();
      }
      final result = _leadZones
          ?.where((zone) => zone?.trim().isNotEmpty ?? false)
          .map((zone) => ItemLeadFilterModel(
                displayName: zone ?? '',
                filterKey: LeadFilterKey.zone,
                isSelected: initialSelectedLeadAddresses?.contains(zone) ?? false,
              ))
          .toList();
      if ((result?.isNotEmpty ?? false) && !emit.isDone) {
        _updateFilterCategory(LeadFilterKey.zone, result!, emit);
      }
    } catch (ex) {
      "Error while initializing lead zone ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initLeadStates(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedLeadAddresses = _selectedLeadFilterModel.states;
      if (_leadStates?.isEmpty ?? false) {
        _leadStates = await _leadsRepository.getAllLeadStates();
      }
      final result = _leadStates
          ?.where((state) => state?.trim().isNotEmpty ?? false)
          .map((state) => ItemLeadFilterModel(
                displayName: state ?? '',
                filterKey: LeadFilterKey.state,
                isSelected: initialSelectedLeadAddresses?.contains(state) ?? false,
              ))
          .toList();
      if (result?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.state, result!, emit);
      }
    } catch (ex) {
      "Error while initializing lead state ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initLeadLocality(Emitter<LeadFilterState> emit) async {
    try {
      final initialSelectedLeadAddresses = _selectedLeadFilterModel.localities;
      if (_leadLocalities?.isEmpty ?? false) {
        _leadLocalities = await _leadsRepository.getAllLeadLocality();
      }
      final results = _leadLocalities
          ?.where((locality) => locality?.trim().isNotEmpty ?? false)
          .map((locality) => ItemLeadFilterModel(
                displayName: locality ?? '',
                filterKey: LeadFilterKey.locality,
                isSelected: initialSelectedLeadAddresses?.contains(locality) ?? false,
              ))
          .toList();
      if (results?.isNotEmpty ?? false) {
        _updateFilterCategory(LeadFilterKey.locality, results!, emit);
      }
    } catch (ex) {
      "Error while initializing lead locality ${ex.toString()}".printInConsole();
    }
  }

  void _updateState(
    Emitter<LeadFilterState> emit, {
    List<ItemLeadFilterCategoryModel>? leadFilterCategories,
    LeadFilterModel? leadFilterModel,
    PageState? pageState,
    int? selectedCategoryIndex,
    DateTime? selectedFromDate,
    DateTime? selectedToDate,
    BudgetRangeModel? customBudgetRange,
    bool? isWithHistory,
    bool? isWithTeam,
    List<SelectableItem<String>>? allCurrencies,
    SelectableItem<String>? selectedCurrency,
    List<SelectableItem<DateType>>? dateTypes,
    SelectableItem<DateType>? selectedDateType,
    List<LeadCardViewModel>? categoryOrders,
    List<CustomFilterModel>? leadCustomCategoriesOrders,
  }) {
    if (emit.isDone) return;
    final newState = state.copyWith(
      leadFilterCategories: leadFilterCategories ?? state.leadFilterCategories,
      leadFilterModel: leadFilterModel ?? state.leadFilterModel,
      pageState: pageState ?? PageState.initial,
      selectedCategoryIndex: selectedCategoryIndex ?? state.selectedCategoryIndex,
      searchFilteredCategories: _updateSearchCategory(selectedCategoryIndex, leadFilterCategories),
      selectedFromDate: selectedFromDate,
      selectedToDate: selectedToDate,
      customBudget: state.customBudget,
      isWithHistory: isWithHistory,
      isWithTeam: isWithTeam,
      currencies: allCurrencies,
      selectedCurrency: selectedCurrency,
      errorMessage: "",
      dateTypes: dateTypes,
      selectedDateType: selectedDateType,
      categoryOrders: categoryOrders,
      leadCustomCategoriesOrders: leadCustomCategoriesOrders,
    );
    emit(newState);
  }

  List<ItemLeadFilterCategoryModel>? _updateSearchCategory(int? selectedCategoryIndex, List<ItemLeadFilterCategoryModel>? leadFilterCategories) {
    final isSearching = state.searchFilteredCategories.isNotEmpty ? state.searchFilteredCategories[selectedCategoryIndex ?? state.selectedCategoryIndex].searchController?.text.isNotNullOrEmpty() : false;

    if (selectedFilterItem != null && (isSearching ?? false)) {
      final selectedSearchFilteredCategories = state.searchFilteredCategories[selectedCategoryIndex ?? state.selectedCategoryIndex];
      List<ItemLeadFilterModel>? updatedSearchedFilterList = selectedSearchFilteredCategories.filters?.map((e) {
        if (e.displayName == selectedFilterItem!.displayName) {
          return selectedFilterItem!.copyWith(isSelected: !selectedFilterItem!.isSelected);
        }
        return selectedSearchFilteredCategories.hasMultiSelect ? e : e.copyWith(isSelected: false);
      }).toList();
      var updatedCategoryList = state.searchFilteredCategories.map((e) => e.filterKey == selectedSearchFilteredCategories.filterKey ? selectedSearchFilteredCategories.copyWith(filters: updatedSearchedFilterList) : e).toList();
      return updatedCategoryList;
    }
    return leadFilterCategories;
  }

  String? utcToDateFormat(DateTime? date) {
    if (date == null) return null;
    DateTime utcDate = date.toUniversalTimeStartOfDay();
    return utcDate.toString();
  }

  FutureOr<void> _onToggleWithHistory(ToggleWithHistoryEvent event, Emitter<LeadFilterState> emit) {
    emit(state.copyWith(isWithHistory: !state.isWithHistory));
  }

  FutureOr<void> _onToggleWithTeam(ToggleWithTeamEvent event, Emitter<LeadFilterState> emit) {
    emit(state.copyWith(isWithTeam: !state.isWithTeam));
  }

  FutureOr<void> _onChangeCurrency(ChangeCurrencyEvent event, Emitter<LeadFilterState> emit) {
    _updateState(emit, selectedCurrency: event.selectedCurrency);
  }

  FutureOr<void> _onSelectDateType(SelectDateTypeEvent event, Emitter<LeadFilterState> emit) {
    emit(state.copyWith(selectedDateType: event.selectedDateType, updateSelectedFromDate: false, updateSelectedToDate: false));

    updateDateRange(emit);
  }

  FutureOr<void> _onSelectAreaUnitEvent(SelectAreaUnitEvent event, Emitter<LeadFilterState> emit) {
    switch (event.areaType) {
      case AreaType.carpetArea:
        if (event.selectedArea?.isSelected ?? false) {
          emit(state.copyWith(selectedCarpetAreaUnit: event.selectedArea));
        } else {
          emit(state.copyWith(updateSelectCarpetAreaUnit: false));
        }

      case AreaType.buildUpArea:
        if (event.selectedArea?.isSelected ?? false) {
          emit(state.copyWith(selectedBuildUpAreaUnit: event.selectedArea));
        } else {
          emit(state.copyWith(updateSelectBuildUpAreaUnit: false));
        }
      case AreaType.salableArea:
        if (event.selectedArea?.isSelected ?? false) {
          emit(state.copyWith(selectedSalableAreaUnit: event.selectedArea));
        } else {
          emit(state.copyWith(updateSelectSalableAreaUnit: false));
        }
      case AreaType.propertyArea:
        if (event.selectedArea?.isSelected ?? false) {
          emit(state.copyWith(selectedPropertyAreaUnit: event.selectedArea));
        } else {
          emit(state.copyWith(updateSelectPropertyAreaUnit: false));
        }
      case AreaType.netArea:
        if (event.selectedArea?.isSelected ?? false) {
          emit(state.copyWith(selectedNetAreaUnit: event.selectedArea));
        } else {
          emit(state.copyWith(updateSelectNetAreaUnit: false));
        }
    }
  }

  (String?, String?) _getDateTimeFromRange(DateRange selectedDateRange) {
    switch (selectedDateRange) {
      case DateRange.today:
        return (utcToDateFormat(DateTime.now().toUserTimeZone()), utcToDateFormat(DateTime.now().toUserTimeZone()));
      case DateRange.yesterday:
        return (utcToDateFormat(DateTime.now().toUserTimeZone()!.subtract(const Duration(days: 1))), utcToDateFormat(DateTime.now().toUserTimeZone()!.subtract(const Duration(days: 1))));
      case DateRange.lastSevenDays:
        return (utcToDateFormat(DateTime.now().toUserTimeZone()), utcToDateFormat(DateTime.now().toUserTimeZone()!.subtract(const Duration(days: 7))));
      case DateRange.lastTwentyEightDays:
        return (utcToDateFormat(DateTime.now().toUserTimeZone()), utcToDateFormat(DateTime.now().toUserTimeZone()!.subtract(const Duration(days: 28))));
      case DateRange.customDate:
        return (null, null);
    }
  }

  FutureOr<void> _onAreaUnitChanged(AreaUnitChangedEvent event, Emitter<LeadFilterState> emit) {
    if (event.filterKey == LeadFilterKey.carpetArea) {
      if (event.selectedAreaUnit?.isSelected ?? false) {
        emit(state.copyWith(selectedCarpetAreaUnit: event.selectedAreaUnit));
      } else {
        emit(state.copyWith(updateSelectCarpetAreaUnit: false));
      }
    }
    if (event.filterKey == LeadFilterKey.netArea) {
      if (event.selectedAreaUnit?.isSelected ?? false) {
        emit(state.copyWith(selectedNetAreaUnit: event.selectedAreaUnit));
      } else {
        emit(state.copyWith(updateSelectNetAreaUnit: false));
      }
    }
    if (event.filterKey == LeadFilterKey.propertyArea) {
      if (event.selectedAreaUnit?.isSelected ?? false) {
        emit(state.copyWith(selectedPropertyAreaUnit: event.selectedAreaUnit));
      } else {
        emit(state.copyWith(updateSelectPropertyArea: false));
      }
    }
    if (event.filterKey == LeadFilterKey.buildUpArea) {
      if (event.selectedAreaUnit?.isSelected ?? false) {
        emit(state.copyWith(selectedBuildUpAreaUnit: event.selectedAreaUnit));
      } else {
        emit(state.copyWith(updateSelectBuildUpAreaUnit: false));
      }
    }
    if (event.filterKey == LeadFilterKey.salableArea) {
      if (event.selectedAreaUnit?.isSelected ?? false) {
        emit(state.copyWith(selectedSalableAreaUnit: event.selectedAreaUnit));
      } else {
        emit(state.copyWith(updateSelectSalableAreaUnit: false));
      }
    }
  }

  FutureOr<void> _onAreaSizeInputChangeEvent(AreaSizeInputChangeEvent event, Emitter<LeadFilterState> emit) {
    if (event.filterKey == LeadFilterKey.carpetArea) {
      emit(state.copyWith(carpetArea: event.rangeInput));
    }
    if (event.filterKey == LeadFilterKey.netArea) {
      emit(state.copyWith(netArea: event.rangeInput));
    }
    if (event.filterKey == LeadFilterKey.propertyArea) {
      emit(state.copyWith(propertySize: event.rangeInput));
    }
    if (event.filterKey == LeadFilterKey.buildUpArea) {
      emit(state.copyWith(builtUpArea: event.rangeInput));
    }
    if (event.filterKey == LeadFilterKey.salableArea) {
      emit(state.copyWith(saleableArea: event.rangeInput));
    }
  }

  FutureOr<void> _onOnMinBudgetChangeEvent(OnMinBudgetChangeEvent event, Emitter<LeadFilterState> emit) {
    emit(state.copyWith(minBudget: event.rangeInput));
  }

  FutureOr<void> _onOnMaxBudgetChangeEvent(OnMaxBudgetChangeEvent event, Emitter<LeadFilterState> emit) {
    emit(state.copyWith(maxBudget: event.rangeInput));
  }

  FutureOr<void> _onUpdateCategoryOrders(UpdateCategoryOrdersEvent event, Emitter<LeadFilterState> emit) async {
    if (event.selectedCategories.isEmpty) {
      emit(state.copyWith(
        errorMessage: "At least one category must be selected",
        pageState: PageState.failure,
      ));
      return;
    }

    if (globalSettingModel?.isCustomStatusEnabled ?? false) {
      List<CustomFilterModel> categoryOrders = [];
      for (int i = 0; i < event.selectedCategories.length; i++) {
        final item = event.selectedCategories[i];
        final match = leadCustomCategoriesOrders.firstWhere(
          (x) => x.id == item.description,
        );
        categoryOrders.add(
          CustomFilterModel(
            id: match.id,
            name: item.title,
            isDefault: true,
            orderRank: i,
          ),
        );
      }
      customDefaultLeadStatus = categoryOrders;
    } else {
      List<LeadCardViewModel> categoryOrders = [];
      for (int i = 0; i < event.selectedCategories.length; i++) {
        final item = event.selectedCategories[i];
        final match = leadCategoriesOrders.firstWhere(
          (x) => x.categiryType == item.value,
          orElse: () => null as dynamic,
        );
        categoryOrders.add(
          LeadCardViewModel(
            id: match.id,
            categiryType: item.value,
            isDefault: true,
            orderRank: i,
            title: item.title,
          ),
        );
      }

      defaultTypes = categoryOrders;
      emit(state.copyWith(categories: categoryOrders));
    }
  }
}
