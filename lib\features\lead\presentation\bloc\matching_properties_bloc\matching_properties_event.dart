part of 'matching_properties_bloc.dart';

@immutable
sealed class MatchingPropertiesEvent {}

final class MatchingPropertiesInitialEvent extends MatchingPropertiesEvent {
  final GetLeadEntity leadEntity;
  final List<PropertyWithDegreeMatchedEntity> properties;
  final LeadInfoState? leadInfoState;
  final int totalMatchingProperties;
  final GlobalSettingModel? globalSettingModel;

  MatchingPropertiesInitialEvent({required this.leadEntity, required this.properties, required this.totalMatchingProperties, this.leadInfoState,this.globalSettingModel});
}

final class ToggleLeadDetailsEvent extends MatchingPropertiesEvent {}

final class SelectRadiusEvent extends MatchingPropertiesEvent {
  final SelectableItem<double> selectedItem;

  SelectRadiusEvent(this.selectedItem);
}

final class SelectMatchingPropertyEvent extends MatchingPropertiesEvent {
  final ItemMatchingPropertiesModel selectedProperty;

  SelectMatchingPropertyEvent(this.selectedProperty);
}

final class OnSearchPropertiesEvent extends MatchingPropertiesEvent {
  final String searchText;

  OnSearchPropertiesEvent(this.searchText);
}

final class ClearMatchingRadiusEvent extends MatchingPropertiesEvent {}

final class LoadMoreMatchingPropertiesEvent extends MatchingPropertiesEvent {}

final class SelectAllMatchingPropertiesEvent extends MatchingPropertiesEvent {
  final bool selectAll;

  SelectAllMatchingPropertiesEvent(this.selectAll);
}
