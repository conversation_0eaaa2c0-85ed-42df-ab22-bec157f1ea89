import 'package:collection/collection.dart';
import 'package:fpdart/fpdart.dart';
import 'package:leadrat/core_main/common/data/master_data/data_source/local/masterdata_local_data_source.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_custom_status_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_lead_status_model.dart';
import 'package:leadrat/core_main/common/data/master_data/repository/masterdata_repository.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_model.dart';
import 'package:leadrat/core_main/enums/app_enum/entity_type_enum.dart';
import 'package:leadrat/core_main/errors/failure.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/remote/rest_response/paged_rest_response.dart';
import 'package:leadrat/core_main/utilities/template_utils.dart';
import 'package:leadrat/core_main/utilities/type_def.dart';
import 'package:leadrat/features/lead/data/data_source/remote/leads_remote_data_source.dart';
import 'package:leadrat/features/lead/data/models/add_lead_model.dart';
import 'package:leadrat/features/lead/data/models/appointment_project_model.dart';
import 'package:leadrat/features/lead/data/models/custom_filter_model.dart';
import 'package:leadrat/features/lead/data/models/delete_document_model.dart';
import 'package:leadrat/features/lead/data/models/get_lead_model.dart';
import 'package:leadrat/features/lead/data/models/lead_call_log_model.dart';
import 'package:leadrat/features/lead/data/models/lead_document_model.dart';
import 'package:leadrat/features/lead/data/models/lead_filter_model.dart';
import 'package:leadrat/features/lead/data/models/lead_link_model.dart';
import 'package:leadrat/features/lead/data/models/projects_dto.dart';
import 'package:leadrat/features/lead/data/models/re_assign_lead_model.dart';
import 'package:leadrat/features/lead/data/models/update_appointments_model.dart';
import 'package:leadrat/features/lead/data/models/update_custom_status_model.dart';
import 'package:leadrat/features/lead/data/models/update_lead_status_model.dart';
import 'package:leadrat/features/lead/domain/entities/get_all_leads_wrapper_entity.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';
import 'package:leadrat/features/lead/domain/entities/lead_category_entity.dart';
import 'package:leadrat/features/lead/domain/entities/lead_document_entity.dart';
import 'package:leadrat/features/lead/domain/entities/lead_history_entity.dart';
import 'package:leadrat/features/lead/domain/entities/lead_sub_source_entity.dart';
import 'package:leadrat/features/lead/domain/repository/leads_repository.dart';
import 'package:leadrat/features/properties/domain/entities/get_property_entity.dart';

import '../../../../core_main/common/data/master_data/models/modified_date_model.dart';
import '../../../projects/domain/entities/project_unit_info_entity.dart';
import '../../domain/entities/get_booked_lead_entity.dart';
import '../data_source/local/leads_local_data_source.dart';
import '../models/booked_lead_model.dart';

class LeadsRepositoryImpl implements LeadsRepository {
  final LeadsRemoteDataSource _leadsRemoteDataSource;
  final MasterDataRepository _masterDataRepository;
  final LeadsLocalDataSource _leadsLocalDataSource;
  final MasterDataLocalDataSource _localDataSource;

  LeadsRepositoryImpl(this._leadsRemoteDataSource, this._masterDataRepository, this._localDataSource, this._leadsLocalDataSource);

  @override
  FutureEitherFailure<GetAllLeadsWrapperEntity?> getAllInitialLeads(LeadFilterModel? leadFilterModel, {int pageNumber = 0, int pageSize = 10}) async {
    try {
      final initialLeads = await _leadsRemoteDataSource.getAllInitialLeads(leadFilterModel, pageSize: pageSize, pageNumber: pageNumber);
      return Either.right(initialLeads?.toEntity());
    } catch (exception, stackTrace) {
      exception.logException(stackTrace);
      return Either.left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<GetAllLeadsWrapperCustomEntity?> getAllInitialCustomLeads(LeadFilterModel? leadFilterModel, {int pageNumber = 0, int pageSize = 10}) async {
    try {
      final initialLeads = await _leadsRemoteDataSource.getAllInitialCustomLeads(leadFilterModel, pageSize: pageSize, pageNumber: pageNumber);
      return Either.right(initialLeads?.toEntity());
    } catch (exception, stackTrace) {
      exception.logException(stackTrace);
      return Either.left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<Map<String, Map<String, int>>?> getLeadCommunications(List<String> leadIds) async {
    try {
      final response = await _leadsRemoteDataSource.getLeadsCommunication(leadIds);
      return Right(response);
    } catch (exception, stackTrace) {
      exception.logException(stackTrace);
      return Either.left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<GetLeadEntity?> getLeadDetails(String leadId) async {
    try {
      final response = await _leadsRemoteDataSource.getLeadDetails(leadId);

      return right(response?.toEntity());
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<Map<DateTime, Map<DateTime, List<LeadHistoryEntity>>>?> getLeadHistory(String leadId) async {
    try {
      final response = await _leadsRemoteDataSource.getLeadHistory(leadId);
      if (response == null) {
        return right(null);
      }

      final Map<DateTime, Map<DateTime, List<LeadHistoryEntity>>> convertedResponse = response.map((outerKey, innerMap) {
        final DateTime parsedOuterKey = DateTime.parse(outerKey);
        final Map<DateTime, List<LeadHistoryEntity>> convertedInnerMap = innerMap.map((innerKey, leadHistoryModels) {
          final DateTime parsedInnerKey = DateTime.parse(innerKey);
          final List<LeadHistoryEntity> leadHistoryEntities = leadHistoryModels.map((leadHistoryModel) => leadHistoryModel.toEntity()).toList();
          return MapEntry(parsedInnerKey, leadHistoryEntities);
        });
        return MapEntry(parsedOuterKey, convertedInnerMap);
      });
      return right(convertedResponse);
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<Iterable<LeadDocumentEntity>?> getLeadDocuments(String leadId) async {
    try {
      final response = await _leadsRemoteDataSource.getLeadDocuments(leadId);
      return right(response?.map((leadDocumentModel) => leadDocumentModel.toEntity()));
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<bool?> updateLeadFlag(UpdateLeadFlagModel updateLeadFlagModel) async {
    try {
      final response = await _leadsRemoteDataSource.updateLeadFlag(updateLeadFlagModel);
      return right(response);
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<bool?> checkLeadAssignedByLeadId(String leadId) async {
    try {
      final response = await _leadsRemoteDataSource.checkLeadAssignedByLeadId(leadId);
      return right(response);
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<List<String>?> addLeadDocuments(AddLeadDocumentsModel addLeadDocumentsModel) async {
    try {
      final response = await _leadsRemoteDataSource.addLeadDocuments(addLeadDocumentsModel);
      return right(response);
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<Map<DateTime, List<LeadHistoryEntity>>?> getLeadNoteHistory(String leadId) async {
    try {
      final result = await _leadsRemoteDataSource.getLeadNoteHistory(leadId);
      final data = result?.map((key, value) => MapEntry(key, value.map((e) => e.toEntity()).toList()));
      return right(data);
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<bool?> updateLeadNote(String leadId, String note) async {
    try {
      final result = await _leadsRemoteDataSource.updateLeadNote(leadId, note);
      return right(result);
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<bool?> deleteLeadDocument(DeleteDocumentModel deleteDocumentModel) async {
    try {
      final result = await _leadsRemoteDataSource.deleteLeadDocument(deleteDocumentModel);
      return right(result);
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<bool?> updateLeadContactCount(LeadContactCountModel leadContactCountModel) async {
    try {
      final result = await _leadsRemoteDataSource.updateLeadContactCount(leadContactCountModel);
      return right(result);
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<bool?> updateLeadTemplate(LeadCommunicationModel leadCommunicationModel) async {
    try {
      final result = await _leadsRemoteDataSource.updateLeadTemplate(leadCommunicationModel);
      return right(result);
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<List<LeadSubSourceEntity>?> getLeadSubSource() async {
    try {
      final result = await _leadsRemoteDataSource.getLeadSubSource();
      return right(result?.map((e) => e.toEntity()).toList());
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<List<MasterCustomStatusModel>?> getAllCustomStatuses() async {
    try {
      final response = await _masterDataRepository.getCustomStatus();
      return Either.right(response);
    } catch (exception) {
      return Either.left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<List<MasterLeadStatusModel>?> getAllStatuses() async {
    try {
      final response = await _masterDataRepository.getLeadStatuses();
      return Either.right(response);
    } catch (exception) {
      return Either.left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<bool?> updateLeadStatus(UpdateLeadStatusModel? updateLeadStatusModel) async {
    try {
      final response = await _leadsRemoteDataSource.updateLeadStatus(updateLeadStatusModel);
      return Either.right(response);
    } catch (e) {
      return Either.left(Failure('Error updating the status ${e.toString()}'));
    }
  }

  @override
  FutureEitherFailure<bool?> updateAppointments(UpdateAppointmentsModel? updateAppointmentsModel) async {
    try {
      final response = await _leadsRemoteDataSource.updateAppointments(updateAppointmentsModel);
      return Either.right(response);
    } catch (e) {
      return Either.left(Failure('Error updating the appointments ${e.toString()}'));
    }
  }

  @override
  FutureEitherFailure<bool?> reAssignLead(ReAssignLeadModel? reAssignModel) async {
    try {
      final response = await _leadsRemoteDataSource.reAssignLead(reAssignModel);
      return Either.right(response);
    } catch (e) {
      return Either.left(Failure('Error updating the Re Assign ${e.toString()}'));
    }
  }

  @override
  FutureEitherFailure<String?> addLead(AddLeadModel addLeadModel) async {
    try {
      final result = await _leadsRemoteDataSource.addLeadAsync(addLeadModel);
      return right(result);
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<String?> updateLead(UpdateLeadModel updateLeadModel) async {
    try {
      final result = await _leadsRemoteDataSource.updateLead(updateLeadModel);
      return right(result);
    } catch (exception) {
      return left(Failure("Unable to update the lead"));
    }
  }

  @override
  FutureEitherFailure<String?> bookLead(BookedLeadModel bookedLeadModel) async {
    try {
      final result = await _leadsRemoteDataSource.bookLead(bookedLeadModel);
      return right(result);
    } catch (exception) {
      return left(Failure("Unable to update lead"));
    }
  }

  @override
  FutureEitherFailure<LeadContactModel?> getLeadByContactNo(String countryCode, String contactNo) async {
    try {
      final result = await _leadsRemoteDataSource.getLeadByContactNo(countryCode, contactNo);
      return right(result);
    } catch (exception) {
      return left(Failure("Unable to get lead"));
    }
  }

  @override
  FutureEitherFailure<LeadCategoryEntity?> searchLeads({String? keyword, int pageNumber = 1, int pageSize = 10}) async {
    try {
      final result = await _leadsRemoteDataSource.searchLeads(pageSize: pageSize, pageNumber: pageNumber, keyword: keyword);
      return right(result?.toEntity());
    } catch (exception) {
      return left(Failure("No leads found!"));
    }
  }

  @override
  FutureEitherFailure<PagedResponse<PropertyWithDegreeMatchedEntity?, String>?> matchingProperties({String? leadId, String? searchText, int pageNumber = 1, int pageSize = 10, double? radiusInKms}) async {
    try {
      final result = await _leadsRemoteDataSource.matchingProperties(pageNumber: pageNumber, pageSize: pageSize, leadId: leadId, radiusInKms: radiusInKms, searchText: searchText);
      final entityResponse = PagedResponse<PropertyWithDegreeMatchedEntity?, String>(
        succeeded: result?.succeeded ?? false,
        data: null,
        totalCount: result?.totalCount ?? 0,
        items: result?.items.map((e) => e?.toEntity()).toList() ?? [],
      );
      return right(entityResponse);
    } catch (exception) {
      return left(Failure("No matching properties found!"));
    }
  }

  @override
  FutureEitherFailure<PagedResponse<ProjectWithDegreeMatchedEntity?, String>?> matchingProjects({String? leadId, String? searchText, int pageNumber = 1, int pageSize = 10, double? radiusInKms}) async {
    try {
      final result = await _leadsRemoteDataSource.matchingProjects(pageNumber: pageNumber, pageSize: pageSize, leadId: leadId, radiusInKms: radiusInKms, searchText: searchText);
      final entityResponse = PagedResponse<ProjectWithDegreeMatchedEntity?, String>(
        succeeded: result?.succeeded ?? false,
        data: null,
        totalCount: result?.totalCount ?? 0,
        items: result?.items.map((e) => e?.toEntity()).toList() ?? [],
      );
      return right(entityResponse);
    } catch (exception) {
      return left(Failure("No matching project found!"));
    }
  }

  @override
  FutureEitherFailure<List<AppointmentProjectModel>?> getAppointmentsByProjects(ProjectDto projectDto) async {
    try {
      final result = await _leadsRemoteDataSource.getAppointmentsByProjects(projectDto);
      return right(result);
    } catch (exception) {
      return left(Failure("No projects found!"));
    }
  }

  @override
  FutureEitherFailure<bool?> addProjectsInLead(ProjectDto projectDto) async {
    try {
      final result = await _leadsRemoteDataSource.addProjectsInLead(projectDto);
      return right(result);
    } catch (exception) {
      return left(Failure("Unable to add projects in a lead!"));
    }
  }

  @override
  FutureEitherFailure<List<String>?> getChannelPartnerNames() async {
    try {
      final result = await _leadsRemoteDataSource.getChannelPartnerNames();
      return right(result);
    } catch (exception) {
      return left(Failure("Unable to get channel partner names!"));
    }
  }

  @override
  FutureEitherFailure<List<CustomFilterModel>?> getCustomStatusFilter() async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.mobileCatalogDetail,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.mobileCatalogDetail,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _leadsLocalDataSource.getLeadCustomCategoriesOrders();
      if (localData?.isEmpty ?? true) {
        final response = await _leadsRemoteDataSource.getCustomStatusFilter();
        if (response != null) {
          await _leadsLocalDataSource.saveAllLeadCustomCategoriesOrders(response);
          lastModifiedModel = lastModifiedModel.copyWith(
            lastUpdatedLocallyDate: DateTime.now(),
          );
          await _localDataSource.updateModifiedDateModel(lastModifiedModel);
          return right(response);
        }
      }
      return right(localData);
    } catch (exception) {
      return left(Failure("Unable to get custom filter!"));
    }
  }

  @override
  FutureEitherFailure<GetBookedLeadEntity?> getBookedLead(String? leadId) async {
    try {
      final result = await _leadsRemoteDataSource.getBookedLead(leadId);

      return right(result?.toEntity());
    } catch (exception) {
      return left(Failure('Unable to get data'));
    }
  }

  @override
  FutureEitherFailure<List<LeadHistoryEntity>?> getLeadHistoryBasedOnTimeZone(String leadId) async {
    try {
      final result = await _leadsRemoteDataSource.getLeadHistoryBasedOnTimeZone(leadId);
      return right(result?.map((e) => e.toEntity()).toList());
    } catch (exception) {
      return left(Failure("Unable to get lead history!"));
    }
  }

  @override
  Future<List<String>?> getCommunities() async {
    try {
      return await _leadsRemoteDataSource.getCommunities();
    } catch (exception) {
      return null;
    }
  }

  @override
  Future<List<String>?> getCountries() async {
    try {
      return await _leadsRemoteDataSource.getCountries();
    } catch (exception) {
      return null;
    }
  }

  @override
  Future<List<String>?> getSubCommunities() async {
    try {
      return await _leadsRemoteDataSource.getSubCommunities();
    } catch (exception) {
      return null;
    }
  }

  @override
  Future<List<String>?> getTowerNames() async {
    try {
      return await _leadsRemoteDataSource.getTowerNames();
    } catch (exception) {
      return null;
    }
  }

  @override
  Future<List<String>?> getCurrencies() async {
    try {
      return await _leadsRemoteDataSource.getCurrencies();
    } catch (exception) {
      return null;
    }
  }

  @override
  Future<List<String>?> getCampaignNames() async {
    try {
      return await _leadsRemoteDataSource.getCampaignNames();
    } catch (exception) {
      return null;
    }
  }

  @override
  FutureEitherFailure<String> getTemplateMessageByLead({String? header, String? message, String? footer, UserDetailsModel? userDetails, String? domain, List<GetLeadEntity?>? leadEntity}) async {
    String actualMessage = '';
    for (GetLeadEntity? element in leadEntity!) {
      var entity = await getLeadDetails(element?.id ?? '');
      entity.fold((failure) {}, (entity) {
        if (header != null && header.isNotEmpty) actualMessage = '$actualMessage ${TemplateUtils.getLeadTemplateMessage(entity ?? GetLeadEntity(), header, userDetails, domain)}\n';
        if (message != null && message.isNotEmpty) actualMessage = '$actualMessage${TemplateUtils.getLeadTemplateMessage(entity ?? GetLeadEntity(), message, userDetails, domain)}\n';
        if (footer != null && footer.isNotEmpty) actualMessage = '$actualMessage${TemplateUtils.getLeadTemplateMessage(entity ?? GetLeadEntity(), footer, userDetails, domain)}\n';
        actualMessage += '\n\n\n\n';
      });
    }
    if (actualMessage.isEmpty) {
      return left(Failure('unable get lead templates'));
    } else {
      return right(actualMessage);
    }
  }

  @override
  FutureEitherFailure<bool?> updateClickedLink(LeadLinkModel leadLinkModel) async {
    try {
      final response = await _leadsRemoteDataSource.updateClickedLink(leadLinkModel);
      return right(response);
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<List<String>?> getLeadExcelData() async {
    try {
      final result = await _leadsRemoteDataSource.getLeadExcelData();
      return right(result);
    } catch (exception) {
      return left(Failure("Unable to get upload type names!"));
    }
  }

  @override
  FutureEitherFailure<List<String>?> getAdditionalPropertyKeys() async {
    try {
      final result = await _leadsRemoteDataSource.getAdditionalPropertyKeys();
      return right(result);
    } catch (exception) {
      return left(Failure("Unable to get additional property keys!"));
    }
  }

  @override
  FutureEitherFailure<List<String>?> getAdditionalPropertyValues(String key) async {
    try {
      final result = await _leadsRemoteDataSource.getAdditionalPropertyValues(key);
      return right(result);
    } catch (exception) {
      return left(Failure("Unable to get additional property values!"));
    }
  }

  @override
  Future<List<String>?> getLeadNationality() async {
    try {
      return await _leadsRemoteDataSource.getLeadNationality();
    } catch (exception) {
      return null;
    }
  }

  @override
  Future<List<String>?> getLeadClusterNames() async {
    try {
      return await _leadsRemoteDataSource.getLeadClusterNames();
    } catch (exception) {
      return null;
    }
  }

  @override
  Future<List<String>?> getLeadUnitNames() async {
    try {
      return await _leadsRemoteDataSource.getLeadUnitNames();
    } catch (exception) {
      return null;
    }
  }

  @override
  FutureEitherFailure<bool> updateLeadCallLog(LeadCallLogModel leadCallLogModel) async {
    try {
      return Right(await _leadsRemoteDataSource.updateLeadCallLog(leadCallLogModel) ?? false);
    } catch (ex, stackTrace) {
      ex.logException(stackTrace);
      return Left(Failure(ex.toString()));
    }
  }

  @override
  FutureEitherFailure<bool> customFilterBulkUpdate(UpdateCustomStatusModel updateCustomStatusModel) async {
    try {
      return Right(await _leadsRemoteDataSource.customFilterBulkUpdate(updateCustomStatusModel) ?? false);
    } catch (ex, stackTrace) {
      ex.logException(stackTrace);
      return Left(Failure(ex.toString()));
    }
  }

  @override
  FutureEitherFailure<bool> saveMobileCardCategoriesOrders(List<CustomFilterModel>? items) async {
    try {
      final lastModifiedDateList = _localDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.mobileCatalogDetail,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.mobileCatalogDetail,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      if (items != null) {
        await _leadsLocalDataSource.saveAllLeadCustomCategoriesOrders(items);

        lastModifiedModel = lastModifiedModel.copyWith(
          lastUpdatedLocallyDate: DateTime.now(),
        );

        await _localDataSource.updateModifiedDateModel(lastModifiedModel);
        return right(true);
      }

      // You may decide what to do with localData here; returning false as default
      return right(false);
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return left(Failure('Failed to restore categories order'));
    }
  }

  @override
  Future<List<String?>?> getAllLeadCities() async {
    try {
      return await _leadsRemoteDataSource.getAllLeadCities();
    } catch (exception) {
      return null;
    }
  }

  @override
  Future<List<String?>?> getAllLeadLocality() async {
    try {
      return await _leadsRemoteDataSource.getAllLeadLocality();
    } catch (exception) {
      return null;
    }
  }

  @override
  Future<List<String?>?> getAllLeadStates() async {
    try {
      return await _leadsRemoteDataSource.getAllLeadStates();
    } catch (exception) {
      return null;
    }
  }

  @override
  Future<List<String?>?> getAllLeadZones() async{
    try {
      return await _leadsRemoteDataSource.getAllLeadZones();
    } catch (exception) {
      return null;
    }
  }
}
