import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/common/date_range.dart';
import 'package:leadrat/core_main/enums/common/date_type.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/purpose_enum.dart';
import 'package:leadrat/core_main/enums/common/user_type.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/leads/lead_category_type.dart';
import 'package:leadrat/core_main/enums/leads/lead_meeting_status.dart';
import 'package:leadrat/core_main/enums/leads/lead_visibility.dart';
import 'package:leadrat/core_main/enums/leads/profession.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';

part 'lead_filter_model.g.dart';

@JsonSerializable(explicitToJson: true)
class LeadFilterModel {
  @JsonKey(name: "Source")
  final List<int>? sources;

  @JsonKey(name: "SubSources")
  final List<String>? subSources;

  @JsonKey(name: "FilterTypes")
  final List<LeadCategoryType>? filterTypes;

  @JsonKey(name: "FilterType")
  final LeadCategoryType? filterType;

  @JsonKey(name: "DefaultCategoryTypes")
  final List<LeadCategoryType> defaultCategoryTypes;

  @JsonKey(name: "assignTo")
  final List<String>? assignedToIds;

  @JsonKey(name: "SecondaryUsers")
  final List<String>? secondaryUsers;

  @JsonKey(name: "LeadVisibility")
  final LeadVisibility? leadVisibility;

  final List<EnquiryType>? enquiredFor;

  @JsonKey(name: "Projects")
  final List<String>? projects;

  @JsonKey(name: "Properties")
  final List<String>? properties;

  final DateType? dateType;

  @JsonKey(name: "PossessionTypeDateRange")
  final PossessionType? possessionTypeDateRange;
  @JsonKey(name: "DateRange")
  final DateRange? dateRange;

  final String? fromDate;

  final String? toDate;

  @JsonKey(name: "MeetingOrVisitStatuses")
  final List<LeadMeetingStatus>? meetingOrVisitStatuses;

  @JsonKey(name: "NoOfBHKs")
  final List<double>? noOfBHKs;

  @JsonKey(name: "Furnished")
  final List<int>? furnished;

  @JsonKey(name: "Beds")
  final List<int>? beds;

  @JsonKey(name: "Baths")
  final List<int>? baths;

  @JsonKey(name: "Floors")
  final List<String>? floors;

  @JsonKey(name: "OfferTypes")
  final List<int>? offerTypes;

  @JsonKey(name: "Brs")
  final List<double>? brs;

  @JsonKey(name: "BhkTypes")
  final List<BHKType>? bhkTypes;

  @JsonKey(name: "PropertyType")
  final List<String>? propertyTypes;

  @JsonKey(name: "PropertySubType")
  final List<String>? propertySubTypes;

  @JsonKey(name: "Locations")
  final List<String>? locations;

  @JsonKey(name: "Communities")
  final List<String>? communities;

  @JsonKey(name: "SubCommunities")
  final List<String>? subCommunities;

  @JsonKey(name: "TowerNames")
  final List<String>? towerNames;

  @JsonKey(name: "Countries")
  final List<String>? countries;

  @JsonKey(name: "Pincode")
  final String? pincode;

  @JsonKey(name: "AgencyNames")
  final List<String>? agencyNames;

  @JsonKey(name: "IsWithTeam")
  final bool? isWithTeam;

  @JsonKey(name: "UserType")
  final UserType? userType;

  final List<String>? bookedByIds;

  @JsonKey(name: "DataConverted")
  final String? dataConverted;

  @JsonKey(name: "QualifiedByIds")
  final List<String>? qualifiedByIds;

  @JsonKey(name: "AppointmentDoneByUserIds")
  final List<String>? appointmentDoneByUserIds;

  @JsonKey(name: "ToDateForMeetingOrVisit")
  final String? toDateForMeetingOrVisit;

  @JsonKey(name: "FromDateForMeetingOrVisit")
  final String? fromDateForMeetingOrVisit;

  @JsonKey(name: "AssignedFromIds")
  final List<String>? assignFromIds;

  @JsonKey(name: "CustomFlags")
  final List<String>? customFlags;

  @JsonKey(name: "Currency")
  final String? currency;

  @JsonKey(name: "IsWithHistory")
  final bool? isWithHistory;

  @JsonKey(name: "DoneBy")
  final List<String>? doneBy;

  @JsonKey(name: "SecondaryFromIds")
  final List<String>? secondaryFromIds;

  @JsonKey(name: "GeneralManagerIds")
  final List<String>? generalManagerIds;

  @JsonKey(name: "StatusIds")
  final List<String>? statusIds;

  @JsonKey(name: "SubStatusIds")
  final List<String>? subStatuses;

  @JsonKey(name: "CustomFilterIds")
  final dynamic customFilterIds;

  @JsonKey(name: "CampaignNames")
  final List<String>? campaignNames;

  @JsonKey(name: "ReferralName")
  final String? referralName;

  @JsonKey(name: "ReferralEmail")
  final String? referralEmail;

  @JsonKey(name: "ReferralContactNo")
  final String? referralContactNo;

  @JsonKey(name: "ReferralNumber")
  final String? referralNumber;

  final List<String>? createdByIds;

  final List<String>? lastModifiedByIds;

  final List<String>? archivedByIds;

  final List<String>? restoredByIds;

  final List<String>? sourcingManagers;

  final List<String>? closingManagers;

  @JsonKey(name: "BuiltUpAreaUnitId")
  final String? builtUpAreaUnitId;

  @JsonKey(name: "CarpetAreaUnitId")
  final String? carpetAreaUnitId;

  @JsonKey(name: "SaleableAreaUnitId")
  final String? saleableAreaUnitId;

  @JsonKey(name: "NetAreaUnitId")
  final String? netAreaUnitId;

  @JsonKey(name: "PropertyAreaUnitId")
  final String? propertyAreaUnitId;

  @JsonKey(name: "SaleableArea")
  final double? saleableArea;

  @JsonKey(name: "CarpetArea")
  final double? carpetArea;

  @JsonKey(name: "BuiltUpArea")
  final double? builtUpArea;

  @JsonKey(name: "NetArea")
  final double? netArea;

  @JsonKey(name: "PropertyArea")
  final double? propertyArea;

  @JsonKey(name: "IsUntouched")
  final String? isUntouched;

  @JsonKey(name: "ChannelPartnerNames")
  final List<String>? channelPartnerNames;

  @JsonKey(name: "ExcelSheets")
  final List<String>? excelSheets;

  @JsonKey(name: "Profession")
  final List<Profession>? profession;

  @JsonKey(name: "FacebookProperties")
  final List<String>? facebookProperties;

  @JsonKey(name: "FacebookPropertyValues")
  final List<String>? facebookPropertyValues;

  @JsonKey(name: "designationsId")
  final List<String>? designations;

  @JsonKey(name: "UnitNames")
  final List<String>? unitNames;

  @JsonKey(name: "ClusterNames")
  final List<String>? clusterNames;

  @JsonKey(name: "Nationality")
  final List<String>? nationality;

  @JsonKey(name: "Purposes")
  final List<PurposeEnum>? purposes;

  @JsonKey(name: "DefaultFilterTypes")
  final List<LeadCategoryType>? defaultFilterTypes;

  @JsonKey(name: "MaxBuiltUpArea")
  final double? maxBuiltUpArea;

  @JsonKey(name: "MinBuiltUpArea")
  final double? minBuiltUpArea;

  @JsonKey(name: "MaxSaleableArea")
  final double? maxSaleAbleArea;

  @JsonKey(name: "MinSaleableArea")
  final double? minSaleAbleArea;

  @JsonKey(name: "MaxCarpetArea")
  final double? maxCarpetArea;

  @JsonKey(name: "MinCarpetArea")
  final double? minCarpetArea;

  @JsonKey(name: "MaxPropertyArea")
  final double? maxPropertyArea;

  @JsonKey(name: "MinPropertyArea")
  final double? minPropertyArea;

  @JsonKey(name: "FromMinBudget")
  final double? fromMinBudget;

  @JsonKey(name: "ToMinBudget")
  final double? toMinBudget;

  @JsonKey(name: "FromMaxBudget")
  final double? fromMaxBudget;

  @JsonKey(name: "ToMaxBudget")
  final double? toMaxBudget;

  @JsonKey(name: "MinNetArea")
  final double? minNetArea;

  @JsonKey(name: "MaxNetArea")
  final double? maxNetArea;

  @JsonKey(name: "Cities")
  final List<String>? cities;

  @JsonKey(name: "States")
  final List<String>? states;

  @JsonKey(name: "localities")
  final List<String>? localities;

  @JsonKey(name: "Zones")
  final List<String>? zones;

  @JsonKey(name: "Latitude")
  final double? latitude;

  @JsonKey(name: "Longitude")
  final double? longitude;

  @JsonKey(name: "RadiusInKm")
  final double? radiusInKm;

  @JsonKey(name: "ShowParentLead")
  final String? isShowParentLead;

  @JsonKey(name: "ShowUniqueLead")
  final String? isShowUniqueLead;

  @JsonKey(name: "ShowChildCount")
  final int? showChildCount;

  @JsonKey(name: "OriginalOwnerIds")
  final List<String>? originalOwnerIds;

  LeadFilterModel({
    this.sources,
    this.subSources,
    this.filterTypes,
    this.filterType,
    List<LeadCategoryType>? defaultCategoryTypes,
    this.assignedToIds,
    this.secondaryUsers,
    this.leadVisibility,
    this.enquiredFor,
    this.projects,
    this.properties,
    this.dateType,
    this.dateRange,
    this.fromDate,
    this.toDate,
    this.meetingOrVisitStatuses,
    this.noOfBHKs,
    this.furnished,
    this.baths,
    this.floors,
    this.beds,
    this.offerTypes,
    this.brs,
    this.bhkTypes,
    this.propertyTypes,
    this.propertySubTypes,
    this.locations,
    this.communities,
    this.subCommunities,
    this.towerNames,
    this.countries,
    this.pincode,
    this.agencyNames,
    this.isWithTeam,
    this.userType,
    this.bookedByIds,
    this.dataConverted,
    this.qualifiedByIds,
    this.appointmentDoneByUserIds,
    this.toDateForMeetingOrVisit,
    this.fromDateForMeetingOrVisit,
    this.assignFromIds,
    this.customFlags,
    this.currency,
    this.customFilterIds,
    this.isWithHistory,
    this.doneBy,
    this.secondaryFromIds,
    this.generalManagerIds,
    this.designations,
    this.statusIds,
    this.subStatuses,
    this.referralName,
    this.referralEmail,
    this.referralContactNo,
    this.referralNumber,
    this.createdByIds,
    this.lastModifiedByIds,
    this.archivedByIds,
    this.restoredByIds,
    this.sourcingManagers,
    this.closingManagers,
    this.builtUpAreaUnitId,
    this.carpetAreaUnitId,
    this.saleableAreaUnitId,
    this.netAreaUnitId,
    this.propertyAreaUnitId,
    this.saleableArea,
    this.carpetArea,
    this.builtUpArea,
    this.netArea,
    this.propertyArea,
    this.unitNames,
    this.clusterNames,
    this.nationality,
    this.campaignNames,
    this.isUntouched,
    this.channelPartnerNames,
    this.excelSheets,
    this.profession,
    this.facebookProperties,
    this.facebookPropertyValues,
    this.purposes,
    this.possessionTypeDateRange,
    this.defaultFilterTypes,
    this.maxBuiltUpArea,
    this.minBuiltUpArea,
    this.maxSaleAbleArea,
    this.minSaleAbleArea,
    this.maxCarpetArea,
    this.minCarpetArea,
    this.maxPropertyArea,
    this.minPropertyArea,
    this.fromMinBudget,
    this.toMinBudget,
    this.toMaxBudget,
    this.fromMaxBudget,
    this.maxNetArea,
    this.minNetArea,
    this.cities,
    this.states,
    this.zones,
    this.radiusInKm,
    this.localities,
    this.longitude,
    this.latitude,
    this.isShowParentLead,
    this.isShowUniqueLead,
    this.showChildCount,
    this.originalOwnerIds,
  }) : defaultCategoryTypes = defaultFilterTypes ?? [];

  bool get isDefault {
    return leadVisibility == LeadVisibility.selfWithReportee && (sources?.isEmpty ?? true) && isDefaultCategorySelected();
  }

  @override
  String toString() {
    String result = "";

    if (sources?.isNotEmpty ?? false) {
      sources?.forEach((i) => result += "&source=$i");
    }

    if (subSources?.isNotEmpty ?? false) {
      subSources?.forEach((i) => result += "&SubSources=$i");
    }

    if (filterTypes?.isNotEmpty ?? false) {
      filterTypes?.forEach((i) => result += "&FilterTypes=${i.value}");
    }

    if (campaignNames?.isNotEmpty ?? false) {
      campaignNames?.forEach((i) => result += "&CampaignNames=$i");
    }

    if (filterType != null) {
      result += "&FilterType=${filterType?.value}";
    }

    if (leadVisibility != null && leadVisibility != LeadVisibility.selfWithReportee) {
      result += "&LeadVisibility=${leadVisibility?.value}";
    }

    if (assignedToIds?.isNotEmpty ?? false) {
      if (isWithHistory ?? false) {
        assignedToIds?.forEach((i) => result += "&HistoryAssignedToIds=$i");
      } else {
        assignedToIds?.forEach((i) => result += "&AssignToIds=$i");
      }

      if (isWithTeam != null) {
        result += "&IsWithTeam=$isWithTeam";
      }
    }

    if (secondaryUsers?.isNotEmpty ?? false) {
      secondaryUsers?.forEach((i) => result += "&SecondaryUsers=$i");
    }

    if (properties?.isNotEmpty ?? false) {
      properties?.forEach((i) => result += "&Properties=$i");
    }

    if (projects?.isNotEmpty ?? false) {
      projects?.forEach((i) => result += "&Projects=$i");
    }
    if (minBuiltUpArea != null && minBuiltUpArea! > 0) {
      result += "&MinBuiltUpArea=${minBuiltUpArea.toString().replaceAll(".0", "")}";
    }
    if (minSaleAbleArea != null && minSaleAbleArea! > 0) {
      result += "&MinSaleableArea=${minSaleAbleArea.toString().replaceAll(".0", "")}";
    }
    if (minCarpetArea != null && minCarpetArea! > 0) {
      result += "&MinCarpetArea=${minCarpetArea.toString().replaceAll(".0", "")}";
    }
    if (maxCarpetArea != null && maxCarpetArea! > 0) {
      result += "&MaxCarpetArea=${maxCarpetArea.toString().replaceAll(".0", "")}";
    }
    if (maxSaleAbleArea != null && maxSaleAbleArea! > 0) {
      result += "&MaxSaleableArea=${maxSaleAbleArea.toString().replaceAll(".0", "")}";
    }
    if (maxBuiltUpArea != null && maxBuiltUpArea! > 0) {
      result += "&MaxBuiltUpArea=${maxBuiltUpArea.toString().replaceAll(".0", "")}";
    }
    if (minPropertyArea != null && minPropertyArea! > 0) {
      result += "&MinPropertyArea=${minPropertyArea.toString().replaceAll(".0", "")}";
    }
    if (maxPropertyArea != null && maxPropertyArea! > 0) {
      result += "&MaxPropertyArea=${maxPropertyArea.toString().replaceAll(".0", "")}";
    }

    if (enquiredFor?.isNotEmpty ?? false) {
      enquiredFor?.forEach((i) => result += "&EnquiredFor=${i.value}");
    }

    if (dateType != null && dateType != DateType.none && dateType != DateType.possessionDate) {
      result += "&DateType=${dateType?.value}&FromDate=${fromDate ?? ''}&ToDate=${toDate ?? ''}";
    }
    if (dateType != null && dateType == DateType.possessionDate && possessionTypeDateRange == PossessionType.customDate) {
      result += "&PossesionType=${possessionTypeDateRange?.value}&FromPossesionDate=${fromDate ?? ''}&ToPossesionDate=${toDate ?? ''}";
    }
    if (dateType != null && dateType == DateType.possessionDate && possessionTypeDateRange != PossessionType.customDate) {
      result += "&PossesionType=${possessionTypeDateRange?.value}";
    }

    if (meetingOrVisitStatuses?.isNotEmpty ?? false) {
      meetingOrVisitStatuses?.forEach((i) => result += "&MeetingOrVisitStatuses=${i.value}");
    }

    if (fromDateForMeetingOrVisit?.isNotEmpty ?? false) {
      result += "&FromDateForMeetingOrVisit=$fromDateForMeetingOrVisit";
    }

    if (toDateForMeetingOrVisit?.isNotEmpty ?? false) {
      result += "&ToDateForMeetingOrVisit=$toDateForMeetingOrVisit";
    }

    if (bhkTypes?.isNotEmpty ?? false) {
      bhkTypes?.forEach((i) => result += "&BHKTypes=${i.value}");
    }

    if (noOfBHKs?.isNotEmpty ?? false) {
      noOfBHKs?.forEach((i) => result += "&NoOfBHKs=$i");
    }

    if (furnished?.isNotEmpty ?? false) {
      furnished?.forEach((i) => result += "&Furnished=$i");
    }

    if (beds?.isNotEmpty ?? false) {
      beds?.forEach((i) => result += "&Beds=$i");
    }

    if (baths?.isNotEmpty ?? false) {
      baths?.forEach((i) => result += "&Baths=$i");
    }

    if (floors?.isNotEmpty ?? false) {
      floors?.forEach((i) => result += "&Floors=$i");
    }

    if (offerTypes?.isNotEmpty ?? false) {
      offerTypes?.forEach((i) => result += "&OfferTypes=$i");
    }

    if (brs?.isNotEmpty ?? false) {
      brs?.forEach((i) => result += "&NoOfBHKs=$i");
    }

    if (propertyTypes?.isNotEmpty ?? false) {
      propertyTypes?.forEach((i) => result += "&PropertyType=$i");
    }

    if (propertySubTypes?.isNotEmpty ?? false) {
      propertySubTypes?.forEach((i) => result += "&PropertySubType=$i");
    }

    if (locations?.isNotEmpty ?? false) {
      locations?.forEach((i) => result += "&Locations=$i");
    }

    if (communities?.isNotEmpty ?? false) {
      communities?.forEach((i) => result += "&Communities=$i");
    }

    if (subCommunities?.isNotEmpty ?? false) {
      subCommunities?.forEach((i) => result += "&SubCommunities=$i");
    }

    if (towerNames?.isNotEmpty ?? false) {
      towerNames?.forEach((i) => result += "&TowerNames=$i");
    }

    if (countries?.isNotEmpty ?? false) {
      countries?.forEach((i) => result += "&Countries=$i");
    }

    if (pincode != null && pincode != '') {
      result += "&Pincode=$pincode";
    }

    if (agencyNames?.isNotEmpty ?? false) {
      agencyNames?.forEach((i) => result += "&AgencyNames=$i");
    }

    if (customFlags?.isNotEmpty ?? false) {
      customFlags?.forEach((i) => result += "&CustomFlags=$i");
    }

    if (userType != null && userType != UserType.none) {
      result += "&UserType=${userType?.value}";
    }

    if (bookedByIds?.isNotEmpty ?? false) {
      bookedByIds?.forEach((i) => result += "&BookedByIds=$i");
    }

    if (dataConverted.isNotNullOrEmpty()) {
      final dataConvertedValue = dataConverted == "Yes";
      result += "&DataConverted=$dataConvertedValue";
    }

    if (qualifiedByIds?.isNotEmpty ?? false) {
      qualifiedByIds?.forEach((i) => result += "&QualifiedByIds=$i");
    }

    if (assignFromIds?.isNotEmpty ?? false) {
      assignFromIds?.forEach((i) => result += "&AssignFromIds=$i");
    }

    if (appointmentDoneByUserIds?.isNotEmpty ?? false) {
      appointmentDoneByUserIds?.forEach((i) => result += "&AppointmentDoneByUserIds=$i");
    }

    if (currency != null && currency!.isNotEmpty) {
      result += "&Currency=$currency";
    }

    if ((customFilterIds?.isNotEmpty ?? false) && (customFilterIds is List<String>)) {
      customFilterIds?.forEach((i) => result += "&CustomFilterIds=$i");
    }

    if (secondaryFromIds?.isNotEmpty ?? false) {
      secondaryFromIds?.forEach((i) => result += "&SecondaryFromIds=$i");
    }

    if (doneBy?.isNotEmpty ?? false) {
      doneBy?.forEach((i) => result += "&DoneBy=$i");
    }

    if (isWithHistory ?? false) {
      result += "&IsWithHistory=$isWithHistory";
    }

    if (generalManagerIds?.isNotEmpty ?? false) {
      generalManagerIds?.forEach((i) => result += "&GeneralManagerIds=$i");
    }

    if (statusIds?.isNotEmpty ?? false) {
      statusIds?.forEach((i) => result += "&StatusIds=$i");
    }

    if (subStatuses?.isNotEmpty ?? false) {
      subStatuses?.forEach((i) => result += "&SubStatuses=$i");
    }

    if (referralName != null && referralName != '') {
      result += "&ReferralName=$referralName";
    }

    if (referralEmail != null && referralEmail != '') {
      result += "&ReferralEmail=$referralEmail";
    }

    if (referralNumber != null && referralNumber != '') {
      result += "&ReferralNumber=$referralNumber";
    }

    if (createdByIds?.isNotEmpty ?? false) {
      createdByIds?.forEach((i) => result += "&CreatedByIds=$i");
    }

    if (lastModifiedByIds?.isNotEmpty ?? false) {
      lastModifiedByIds?.forEach((i) => result += "&LastModifiedByIds=$i");
    }

    if (archivedByIds?.isNotEmpty ?? false) {
      archivedByIds?.forEach((i) => result += "&ArchivedByIds=$i");
    }

    if (restoredByIds?.isNotEmpty ?? false) {
      restoredByIds?.forEach((i) => result += "&RestoredByIds=$i");
    }

    if (sourcingManagers?.isNotEmpty ?? false) {
      sourcingManagers?.forEach((i) => result += "&SourcingManagers=$i");
    }

    if (closingManagers?.isNotEmpty ?? false) {
      closingManagers?.forEach((i) => result += "&ClosingManagers=$i");
    }

    if (carpetArea != null && carpetArea! > 0) {
      result += "&CarpetArea=${carpetArea.toString().replaceAll(".0", "")}";
    }

    if (carpetAreaUnitId?.isNotNullOrEmpty() ?? false) {
      result += "&CarpetAreaUnitId=$carpetAreaUnitId";
    }

    if (builtUpArea != null && builtUpArea! > 0) {
      result += "&BuiltUpArea=${builtUpArea.toString().replaceAll(".0", "")}";
    }

    if (builtUpAreaUnitId?.isNotNullOrEmpty() ?? false) {
      result += "&BuiltUpAreaUnitId=$builtUpAreaUnitId";
    }

    if (saleableArea != null && saleableArea! > 0) {
      result += "&SaleableArea=${saleableArea.toString().replaceAll(".0", "")}";
    }

    if (saleableAreaUnitId?.isNotNullOrEmpty() ?? false) {
      result += "&SaleableAreaUnitId=$saleableAreaUnitId";
    }

    if (netArea != null && netArea! > 0) {
      result += "&NetArea=${netArea.toString().replaceAll(".0", "")}";
    }

    if (netAreaUnitId?.isNotNullOrEmpty() ?? false) {
      result += "&NetAreaUnitId=$netAreaUnitId";
    }

    if (propertyArea != null && propertyArea! > 0) {
      result += "&PropertyArea=${propertyArea.toString().replaceAll(".0", "")}";
    }

    if (propertyAreaUnitId?.isNotNullOrEmpty() ?? false) {
      result += "&PropertyAreaUnitId=$propertyAreaUnitId";
    }

    if (isUntouched.isNotNullOrEmpty()) {
      final isUnTouchedValue = isUntouched == "Yes";
      result += "&IsUntouched=$isUnTouchedValue";
    }

    if (designations?.isNotEmpty ?? false) {
      designations?.forEach((i) => result += "&Designations=$i");
    }

    if (channelPartnerNames?.isNotEmpty ?? false) {
      channelPartnerNames?.forEach((i) => result += "&ChannelPartnerNames=$i");
    }

    if (excelSheets?.isNotEmpty ?? false) {
      excelSheets?.forEach((i) => result += "&UploadTypeName=$i");
    }

    if (profession?.isNotEmpty ?? false) {
      profession?.forEach((i) => result += "&Profession=${i.value}");
    }

    if (facebookProperties?.isNotEmpty ?? false) {
      facebookProperties?.forEach((i) => result += "&AdditionalPropertiesKey=$i");
    }

    if (facebookPropertyValues?.isNotEmpty ?? false) {
      facebookPropertyValues?.forEach((i) => result += "&AdditionalPropertiesValue=$i");
    }

    if (purposes?.isNotEmpty ?? false) {
      purposes?.forEach((i) => result += "&Purposes=${i.value}");
    }
    if (fromMinBudget != null && fromMinBudget! > 0) {
      result += "&FromMinBudget=${fromMinBudget.toString().replaceAll(".0", "")}";
    }
    if (toMinBudget != null && toMinBudget! > 0) {
      result += "&ToMinBudget=${toMinBudget.toString().replaceAll(".0", "")}";
    }
    if (fromMaxBudget != null && fromMaxBudget! > 0) {
      result += "&FromMaxBudget=${fromMaxBudget.toString().replaceAll(".0", "")}";
    }
    if (toMaxBudget != null && toMaxBudget! > 0) {
      result += "&ToMaxBudget=${toMaxBudget.toString().replaceAll(".0", "")}";
    }
    if (minNetArea != null && minNetArea! > 0) {
      result += "&MinNetArea=${minNetArea.toString().replaceAll(".0", "")}";
    }
    if (maxNetArea != null && maxNetArea! > 0) {
      result += "&MaxNetArea=${maxNetArea.toString().replaceAll(".0", "")}";
    }
    if (originalOwnerIds?.isNotEmpty ?? false) {
      originalOwnerIds?.forEach((i) => result += "&OriginalOwnerIds=$i");
    }

    if (cities?.isNotEmpty ?? false) {
      cities?.forEach((i) => result += "&Cities=$i");
    }

    if (states?.isNotEmpty ?? false) {
      states?.forEach((i) => result += "&States=$i");
    }

    if (localities?.isNotEmpty ?? false) {
      localities?.forEach((i) => result += "&Localities=$i");
    }

    if (zones?.isNotEmpty ?? false) {
      zones?.forEach((i) => result += "&Zones=$i");
    }

    if (longitude != null) {
      result += "&Longitude=$longitude";
    }

    if (latitude != null) {
      result += "&Latitude=$latitude";
    }

    if (radiusInKm != null) {
      result += "&RadiusInKm=$radiusInKm";
    }

    if (isShowParentLead.isNotNullOrEmpty()) {
      final isShowParentLeadValue = isShowParentLead == "Yes";
      result += "&ShowParentLead=$isShowParentLeadValue";
    }

    if (isShowUniqueLead.isNotNullOrEmpty()) {
      final isShowUniqueLeadValue = isShowUniqueLead == "Yes";
      result += "&ShowUniqueLead=$isShowUniqueLeadValue";
    }

    if (showChildCount != null) {
      result += "&ShowChildCount=$showChildCount";
    }

    result += "&CanAccessAllLeads=${getIt<UsersDataRepository>().checkHasPermission(AppModule.lead, CommandType.viewAllLeads)}";

    return result;
  }

  bool isDefaultCategorySelected() {
    if (filterTypes == null) {
      return true;
    }

    final filterTypesSafe = filterTypes ?? [];
    final defaultCategoryTypesDifference = defaultCategoryTypes.where((type) => !filterTypesSafe.contains(type)).toList();
    final filterTypesDifference = filterTypesSafe.where((type) => !defaultCategoryTypes.contains(type)).toList();

    return defaultCategoryTypesDifference.isEmpty && filterTypesDifference.isEmpty;
  }

  LeadFilterModel copyWith({
    List<int>? sources,
    List<String>? subSources,
    List<LeadCategoryType>? filterTypes,
    LeadCategoryType? filterType,
    List<LeadCategoryType>? defaultCategoryTypes,
    List<String>? assignedToIds,
    List<String>? secondaryUsers,
    LeadVisibility? leadVisibility,
    List<EnquiryType>? enquiredFor,
    List<String>? projects,
    List<String>? properties,
    DateType? dateType,
    DateRange? dateRange,
    PossessionType? possessionTypeDateRange,
    bool updateDateRange = true,
    bool updatePossessionDateRange = true,
    String? fromDate,
    String? toDate,
    List<LeadMeetingStatus>? meetingOrVisitStatuses,
    List<double>? noOfBHKs,
    List<int>? furnished,
    List<int>? beds,
    List<int>? baths,
    List<String>? floors,
    List<int>? offerTypes,
    List<double>? brs,
    List<BHKType>? bhkTypes,
    List<String>? propertyTypes,
    List<String>? propertySubTypes,
    List<String>? locations,
    List<String>? communities,
    List<String>? subCommunities,
    List<String>? towerNames,
    List<String>? countries,
    String? pincode,
    List<String>? agencyNames,
    bool? isWithTeam,
    UserType? userType,
    List<String>? bookedByIds,
    String? dataConverted,
    List<String>? qualifiedByIds,
    List<String>? appointmentDoneByUserIds,
    String? toDateForMeetingOrVisit,
    String? fromDateForMeetingOrVisit,
    List<String>? assignFromIds,
    List<String>? customFlags,
    String? currency,
    bool? isWithHistory,
    List<String>? doneBy,
    List<String>? secondaryFromIds,
    List<String>? customFilterIds,
    String? result,
    List<String>? statusIds,
    List<String>? subStatuses,
    String? referralName,
    String? referralEmail,
    String? referralContactNo,
    String? referralNumber,
    List<String>? createdByIds,
    List<String>? lastModifiedByIds,
    List<String>? archivedByIds,
    List<String>? restoredByIds,
    List<String>? sourcingManagers,
    List<String>? closingManagers,
    String? builtUpAreaUnitId,
    String? carpetAreaUnitId,
    String? saleableAreaUnitId,
    String? netAreaUnitId,
    String? propertyAreaUnitId,
    double? saleableArea,
    double? carpetArea,
    double? builtUpArea,
    double? netArea,
    double? propertyArea,
    String? isUntouched,
    List<String>? channelPartnerNames,
    List<String>? excelSheets,
    List<Profession>? profession,
    List<String>? facebookProperties,
    List<String>? facebookPropertyValues,
    List<String>? designations,
    bool updateNetArea = true,
    bool updatePropertyArea = true,
    bool updateCarpetArea = true,
    bool updateBuildUpArea = true,
    bool updateSalableArea = true,
    bool updateMinBudget = true,
    bool updateCurrency = true,
    bool updateMaxBudget = true,
    bool updateIsUntouchable = true,
    bool updateIsConverted = true,
    List<String>? unitNames,
    List<String>? clusterNames,
    List<String>? nationality,
    List<String>? campaignNames,
    List<PurposeEnum>? purposes,
    bool updateDateType = true,
    double? maxBuiltUpArea,
    double? minBuiltUpArea,
    double? maxSaleAbleArea,
    double? minSaleAbleArea,
    double? maxCarpetArea,
    double? minCarpetArea,
    double? maxPropertyArea,
    double? minPropertyArea,
    double? fromMinBudget,
    double? toMinBudget,
    double? fromMaxBudget,
    double? toMaxBudget,
    double? minNetArea,
    double? maxNetArea,
    List<LeadCategoryType>? defaultFilterTypes,
    List<String>? originalOwnerIds,
    List<String>? cities,
    List<String>? states,
    List<String>? localities,
    List<String>? zones,
    double? latitude,
    double? longitude,
    double? radiusInKm,
    String? isShowParentLead,
    String? isShowUniqueLead,
    int? showChildCount,
  }) {
    return LeadFilterModel(
      sources: sources ?? this.sources,
      subSources: subSources ?? this.subSources,
      filterTypes: filterTypes ?? this.filterTypes,
      filterType: filterType ?? this.filterType,
      defaultCategoryTypes: defaultCategoryTypes ?? this.defaultCategoryTypes,
      assignedToIds: assignedToIds ?? this.assignedToIds,
      secondaryUsers: secondaryUsers ?? this.secondaryUsers,
      leadVisibility: leadVisibility ?? this.leadVisibility,
      enquiredFor: enquiredFor ?? this.enquiredFor,
      projects: projects ?? this.projects,
      properties: properties ?? this.properties,
      dateType: updateDateType ? dateType ?? this.dateType : null,
      dateRange: updateDateRange ? dateRange ?? dateRange : null,
      possessionTypeDateRange: updatePossessionDateRange ? possessionTypeDateRange ?? this.possessionTypeDateRange : null,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      meetingOrVisitStatuses: meetingOrVisitStatuses ?? this.meetingOrVisitStatuses,
      noOfBHKs: noOfBHKs ?? this.noOfBHKs,
      furnished: furnished ?? this.furnished,
      beds: beds ?? this.beds,
      baths: baths ?? this.baths,
      floors: floors ?? this.floors,
      offerTypes: offerTypes ?? this.offerTypes,
      brs: brs ?? this.brs,
      bhkTypes: bhkTypes ?? this.bhkTypes,
      propertyTypes: propertyTypes ?? this.propertyTypes,
      propertySubTypes: propertySubTypes ?? this.propertySubTypes,
      locations: locations ?? this.locations,
      communities: communities ?? this.communities,
      subCommunities: subCommunities ?? this.subCommunities,
      towerNames: towerNames ?? this.towerNames,
      countries: countries ?? this.countries,
      pincode: pincode ?? this.pincode,
      agencyNames: agencyNames ?? this.agencyNames,
      isWithTeam: isWithTeam ?? this.isWithTeam,
      userType: userType ?? this.userType,
      bookedByIds: bookedByIds ?? this.bookedByIds,
      dataConverted: updateIsConverted ? dataConverted ?? this.dataConverted : null,
      qualifiedByIds: qualifiedByIds ?? this.qualifiedByIds,
      appointmentDoneByUserIds: appointmentDoneByUserIds ?? this.appointmentDoneByUserIds,
      toDateForMeetingOrVisit: toDateForMeetingOrVisit ?? this.toDateForMeetingOrVisit,
      fromDateForMeetingOrVisit: fromDateForMeetingOrVisit ?? this.fromDateForMeetingOrVisit,
      assignFromIds: assignFromIds ?? this.assignFromIds,
      customFlags: customFlags ?? this.customFlags,
      currency: updateCurrency ? currency ?? this.currency : null,
      isWithHistory: isWithHistory ?? this.isWithHistory,
      doneBy: doneBy ?? this.doneBy,
      secondaryFromIds: secondaryFromIds ?? this.secondaryFromIds,
      customFilterIds: customFilterIds ?? this.customFilterIds,
      subStatuses: subStatuses ?? this.subStatuses,
      statusIds: statusIds ?? this.statusIds,
      referralName: referralName ?? this.referralName,
      referralEmail: referralEmail ?? this.referralEmail,
      referralContactNo: referralContactNo ?? this.referralContactNo,
      referralNumber: referralNumber ?? this.referralNumber,
      createdByIds: createdByIds ?? this.createdByIds,
      lastModifiedByIds: lastModifiedByIds ?? this.lastModifiedByIds,
      archivedByIds: archivedByIds ?? this.archivedByIds,
      restoredByIds: restoredByIds ?? this.restoredByIds,
      sourcingManagers: sourcingManagers ?? this.sourcingManagers,
      closingManagers: closingManagers ?? this.closingManagers,
      builtUpAreaUnitId: updateBuildUpArea ? builtUpAreaUnitId ?? this.builtUpAreaUnitId : null,
      carpetAreaUnitId: updateCarpetArea ? carpetAreaUnitId ?? this.carpetAreaUnitId : null,
      saleableArea: updateSalableArea ? saleableArea ?? this.saleableArea : null,
      saleableAreaUnitId: updateSalableArea ? saleableAreaUnitId ?? this.saleableAreaUnitId : null,
      carpetArea: updateCarpetArea ? carpetArea ?? this.carpetArea : null,
      builtUpArea: updateBuildUpArea ? builtUpArea ?? this.builtUpArea : null,
      isUntouched: updateIsUntouchable ? isUntouched ?? this.isUntouched : null,
      channelPartnerNames: channelPartnerNames ?? this.channelPartnerNames,
      excelSheets: excelSheets ?? this.excelSheets,
      profession: profession ?? this.profession,
      designations: designations ?? this.designations,
      facebookProperties: facebookProperties ?? this.facebookProperties,
      facebookPropertyValues: facebookPropertyValues ?? this.facebookPropertyValues,
      netAreaUnitId: updateNetArea ? netAreaUnitId ?? this.netAreaUnitId : null,
      netArea: updateNetArea ? netArea ?? this.netArea : null,
      maxNetArea: updateNetArea ? maxNetArea ?? this.maxNetArea : null,
      minNetArea: updateNetArea ? minNetArea ?? this.minNetArea : null,
      propertyAreaUnitId: updatePropertyArea ? propertyAreaUnitId ?? this.propertyAreaUnitId : null,
      propertyArea: updatePropertyArea ? propertyArea ?? this.propertyArea : null,
      unitNames: unitNames ?? this.unitNames,
      clusterNames: clusterNames ?? this.clusterNames,
      nationality: nationality ?? this.nationality,
      campaignNames: campaignNames ?? this.campaignNames,
      purposes: purposes ?? this.purposes,
      maxBuiltUpArea: updateBuildUpArea ? maxBuiltUpArea ?? this.maxBuiltUpArea : null,
      minBuiltUpArea: updateBuildUpArea ? minBuiltUpArea ?? this.minBuiltUpArea : null,
      maxSaleAbleArea: updateSalableArea ? maxSaleAbleArea ?? this.maxSaleAbleArea : null,
      minSaleAbleArea: updateSalableArea ? minSaleAbleArea ?? this.minSaleAbleArea : null,
      maxCarpetArea: updateCarpetArea ? maxCarpetArea ?? this.maxCarpetArea : null,
      minCarpetArea: updateCarpetArea ? minCarpetArea ?? this.minCarpetArea : null,
      maxPropertyArea: updatePropertyArea ? maxPropertyArea ?? this.maxPropertyArea : null,
      minPropertyArea: updatePropertyArea ? minPropertyArea ?? this.minPropertyArea : null,
      fromMinBudget: updateMinBudget ? fromMinBudget ?? this.fromMinBudget : null,
      toMinBudget: updateMinBudget ? toMinBudget ?? this.toMinBudget : null,
      toMaxBudget: updateMaxBudget ? toMaxBudget ?? this.toMaxBudget : null,
      fromMaxBudget: updateMaxBudget ? fromMaxBudget ?? this.fromMaxBudget : null,
      defaultFilterTypes: defaultFilterTypes ?? this.defaultFilterTypes,
      originalOwnerIds: originalOwnerIds ?? this.originalOwnerIds,
      cities: cities ?? this.cities,
      zones: zones ?? this.zones,
      states: states ?? this.states,
      localities: localities ?? this.localities,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      isShowParentLead: isShowParentLead ?? this.isShowParentLead,
      isShowUniqueLead: isShowUniqueLead ?? this.isShowUniqueLead,
      radiusInKm: radiusInKm ?? this.radiusInKm,
      showChildCount: showChildCount ?? this.showChildCount,
    );
  }

  factory LeadFilterModel.fromJson(Map<String, dynamic> json) => _$LeadFilterModelFromJson(json);

  Map<String, dynamic> toJson() => _$LeadFilterModelToJson(this);
}
