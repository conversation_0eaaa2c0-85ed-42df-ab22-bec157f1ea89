part of 'add_lead_bloc.dart';

@immutable
class AddLeadState {
  final PageState pageState;
  final String? errorMessage;
  final String? successMessage;
  final List<ItemSimpleModel<EnquiryType>> enquiredFor;
  final List<ItemSimpleModel<PropertyType>> propertyTypes;
  final List<ItemSimpleModel<String>> propertySubTypes;
  final List<ItemSimpleModel<BHKType>> bhkTypes;
  final List<ItemSimpleModel<double>> noOfBhk;
  final List<ItemSimpleModel<AddressModel>> locations;
  final List<ItemSimpleModel<Profession>> professions;
  final List<SelectableItem<int>> leadSource;
  final SelectableItem<int>? selectedLeadSource;
  final List<SelectableItem<String>> leadSubSource;
  final SelectableItem<String>? selectedLeadSubSource;
  final List<SelectableItem<String>> agencyNames;
  final List<SelectableItem<String>> campaignNames;
  final List<SelectableItem<String>>? selectedAgencyNames;
  final List<SelectableItem<String>>? selectedCampaignNames;
  final List<SelectableItem<String>> assignToUsers;
  final SelectableItem<String>? selectedAssignedUser;
  final List<SelectableItem<String>> secondaryUsers;
  final SelectableItem<String>? selectedSecondaryAssignedUser;
  final List<SelectableItem<MasterAreaUnitsModel>> carpetAreas;
  final List<SelectableItem<MasterAreaUnitsModel>> saleableAreas;
  final List<SelectableItem<MasterAreaUnitsModel>> builtUpAreas;
  final SelectableItem<MasterAreaUnitsModel>? selectedCarpetArea;
  final SelectableItem<MasterAreaUnitsModel>? selectedSaleableArea;
  final SelectableItem<MasterAreaUnitsModel>? selectedBuiltUpArea;
  final List<SelectableItem<String>> properties;
  final List<SelectableItem<String>>? selectedProperties;
  final List<SelectableItem<String>> projects;
  final List<SelectableItem<String>>? selectedProjects;
  final bool isSubTypesExpanded;
  final bool isNoOfBhkExpanded;
  final bool isEmailFieldVisible;
  final bool isAltPhoneFieldVisible;
  final bool isPossessionDateVisible;
  final bool isReferralDetailsVisible;
  final GlobalSettingModel? globalSettingModel;
  final DateTime? possessionDate;
  final String dialogMessage;
  final bool isLeadAlreadyExits;
  final bool isLeadAlreadyExitsOnAltNumber;
  final String? existingLeadId;
  final String? contactNumber;
  final String? altContactNumber;
  final String? referralContact;
  final List<SelectableItem<String>> currencies;
  final SelectableItem<String>? selectedCurrency;
  final String? defaultCountryCode;
  final bool showDialogProgress;
  final List<SelectableItem<String>> channelPartners;
  final List<SelectableItem<String>> selectedChannelPartners;
  final bool isDuplicateLeadBottomSheetVisible;
  final String primaryOrSecondary;
  final List<SelectableItem<PurposeEnum>> purposes;
  final SelectableItem<PurposeEnum>? selectedPurpose;
  final bool? isAssignedLoggedInUser;
  final bool isDuplicateLeadBottomSheetVisibleForAltNumber;
  final List<SelectableItem<String>> sourcingManager;
  final SelectableItem<String>? selectedSourcingManager;
  final List<SelectableItem<String>> closingManager;
  final SelectableItem<String>? selectedClosingManager;
  final ItemSimpleModel<AddressModel>? customerLocations;
  final String? executiveContact;
  final List<SelectableItem<PossessionType?>>? possessionTypeSelectableItems;
  final SelectableItem<PossessionType?>? possessionTypeSelectedItem;
  final bool isPossessionDateCustomSelected;

  const AddLeadState({
    this.pageState = PageState.initial,
    this.errorMessage,
    this.successMessage,
    this.enquiredFor = const [],
    this.propertyTypes = const [],
    this.propertySubTypes = const [],
    this.bhkTypes = const [],
    this.noOfBhk = const [],
    this.properties = const [],
    this.projects = const [],
    this.locations = const [],
    this.professions = const [],
    this.leadSource = const [],
    this.leadSubSource = const [],
    this.agencyNames = const [],
    this.campaignNames = const [],
    this.assignToUsers = const [],
    this.secondaryUsers = const [],
    this.carpetAreas = const [],
    this.saleableAreas = const [],
    this.builtUpAreas = const [],
    this.selectedSaleableArea,
    this.selectedBuiltUpArea,
    this.isSubTypesExpanded = false,
    this.isNoOfBhkExpanded = false,
    this.selectedLeadSource,
    this.selectedLeadSubSource,
    this.selectedAgencyNames,
    this.selectedCampaignNames,
    this.selectedAssignedUser,
    this.selectedSecondaryAssignedUser,
    this.selectedCarpetArea,
    this.selectedProperties,
    this.selectedProjects,
    this.isAltPhoneFieldVisible = false,
    this.isEmailFieldVisible = false,
    this.isPossessionDateVisible = false,
    this.isReferralDetailsVisible = false,
    this.isLeadAlreadyExits = false,
    this.isLeadAlreadyExitsOnAltNumber = false,
    this.existingLeadId,
    this.globalSettingModel,
    this.possessionDate,
    this.dialogMessage = "loading",
    this.contactNumber,
    this.altContactNumber,
    this.referralContact,
    this.currencies = const [],
    this.selectedCurrency,
    this.defaultCountryCode,
    this.showDialogProgress = false,
    this.channelPartners = const [],
    this.selectedChannelPartners = const [],
    this.isDuplicateLeadBottomSheetVisible = false,
    this.primaryOrSecondary = '',
    this.selectedPurpose,
    this.purposes = const [],
    this.isAssignedLoggedInUser,
    this.isDuplicateLeadBottomSheetVisibleForAltNumber = false,
    this.possessionTypeSelectableItems,
    this.possessionTypeSelectedItem,
    this.isPossessionDateCustomSelected = false,
    this.sourcingManager = const [],
    this.selectedSourcingManager,
    this.closingManager = const [],
    this.selectedClosingManager,
    this.customerLocations,
    this.executiveContact,
  });

  AddLeadState initialState() => const AddLeadState(
        pageState: PageState.initial,
        errorMessage: null,
        successMessage: null,
        enquiredFor: [],
        propertyTypes: [],
        propertySubTypes: [],
        bhkTypes: [],
        noOfBhk: [],
        properties: [],
        projects: [],
        locations: [],
        professions: [],
        leadSource: [],
        leadSubSource: [],
        agencyNames: [],
        campaignNames: [],
        assignToUsers: [],
        secondaryUsers: [],
        carpetAreas: [],
        saleableAreas: [],
        builtUpAreas: [],
        isSubTypesExpanded: false,
        isNoOfBhkExpanded: false,
        selectedLeadSource: null,
        selectedLeadSubSource: null,
        selectedAgencyNames: null,
        selectedCampaignNames: null,
        selectedAssignedUser: null,
        selectedSecondaryAssignedUser: null,
        selectedCarpetArea: null,
        selectedBuiltUpArea: null,
        selectedSaleableArea: null,
        selectedProperties: null,
        selectedProjects: null,
        isEmailFieldVisible: false,
        isPossessionDateVisible: false,
        isReferralDetailsVisible: false,
        isAltPhoneFieldVisible: false,
        possessionDate: null,
        dialogMessage: "loading",
        contactNumber: null,
        altContactNumber: null,
        referralContact: null,
        existingLeadId: null,
        currencies: [],
        selectedCurrency: null,
        defaultCountryCode: null,
        showDialogProgress: false,
        channelPartners: [],
        selectedChannelPartners: [],
        globalSettingModel: null,
        purposes: [],
        isAssignedLoggedInUser: null,
        sourcingManager: [],
        selectedSourcingManager: null,
        closingManager: [],
        selectedClosingManager: null,
        customerLocations: null,
        executiveContact: null,
      );

  AddLeadState copyWith({
    PageState? pageState,
    String? errorMessage,
    String? successMessage,
    List<ItemSimpleModel<EnquiryType>>? enquiredFor,
    List<ItemSimpleModel<PropertyType>>? propertyTypes,
    List<ItemSimpleModel<String>>? propertySubTypes,
    List<ItemSimpleModel<BHKType>>? bhkTypes,
    List<ItemSimpleModel<double>>? noOfBhk,
    List<ItemSimpleModel<AddressModel>>? locations,
    List<ItemSimpleModel<Profession>>? professions,
    List<SelectableItem<int>>? leadSource,
    SelectableItem<int>? selectedLeadSource,
    List<SelectableItem<String>>? leadSubSource,
    SelectableItem<String>? selectedLeadSubSource,
    bool updateSubSource = true, // added to make selectedSubSource null
    bool updatePossessionDate = false, // added to make selectedSubSource null
    List<SelectableItem<String>>? agencyNames,
    List<SelectableItem<String>>? campaignNames,
    List<SelectableItem<String>>? selectedAgencyNames,
    List<SelectableItem<String>>? selectedCampaignNames,
    List<SelectableItem<String>>? assignToUsers,
    SelectableItem<String>? selectedAssignedUser,
    List<SelectableItem<String>>? secondaryUsers,
    SelectableItem<String>? selectedSecondaryAssignedUser,
    List<SelectableItem<MasterAreaUnitsModel>>? carpetAreas,
    List<SelectableItem<MasterAreaUnitsModel>>? saleableAreas,
    List<SelectableItem<MasterAreaUnitsModel>>? builtUpAreas,
    SelectableItem<MasterAreaUnitsModel>? selectedCarpetArea,
    SelectableItem<MasterAreaUnitsModel>? selectedSaleableArea,
    SelectableItem<MasterAreaUnitsModel>? selectedBuiltUpArea,
    List<SelectableItem<String>>? properties,
    List<SelectableItem<String>>? selectedProperties,
    List<SelectableItem<String>>? projects,
    List<SelectableItem<String>>? selectedProjects,
    bool? isSubTypesExpanded,
    bool? isNoOfBhkExpanded,
    bool? isEmailFieldVisible,
    bool? isAltPhoneFieldVisible,
    bool? isPossessionDateVisible,
    bool? isReferralDetailsVisible,
    GlobalSettingModel? globalSettingModel,
    DateTime? possessionDate,
    String? dialogMessage,
    bool? isLeadAlreadyExits,
    bool? isLeadAlreadyExitsOnAltNumber,
    String? existingLeadId,
    String? contactNumber,
    String? altContactNumber,
    String? referralContact,
    List<SelectableItem<String>>? currencies,
    SelectableItem<String>? selectedCurrency,
    String? defaultCountryCode,
    bool? showDialogProgress,
    List<SelectableItem<String>>? channelPartners,
    List<SelectableItem<String>>? selectedChannelPartners,
    bool? isDuplicateLeadBottomSheetVisible,
    String? primaryOrSecondary,
    List<SelectableItem<PurposeEnum>>? purposes,
    SelectableItem<PurposeEnum>? selectedPurpose,
    bool? isAssignedLoggedInUser,
    bool? isDuplicateLeadBottomSheetVisibleForAltNumber,
    List<SelectableItem<String>>? sourcingManager,
    SelectableItem<String>? selectedSourcingManager,
    List<SelectableItem<String>>? closingManager,
    SelectableItem<String>? selectedClosingManager,
    ItemSimpleModel<AddressModel>? customerLocations,
    String? executiveContact,
    List<SelectableItem<PossessionType?>>? possessionTypeSelectableItems,
    SelectableItem<PossessionType?>? possessionTypeSelectedItem,
    bool? isPossessionDateCustomSelected,
  }) {
    return AddLeadState(
      pageState: pageState ?? this.pageState,
      errorMessage: errorMessage,
      successMessage: successMessage,
      enquiredFor: enquiredFor ?? this.enquiredFor,
      propertyTypes: propertyTypes ?? this.propertyTypes,
      propertySubTypes: propertySubTypes ?? this.propertySubTypes,
      bhkTypes: bhkTypes ?? this.bhkTypes,
      noOfBhk: noOfBhk ?? this.noOfBhk,
      locations: locations ?? this.locations,
      professions: professions ?? this.professions,
      leadSource: leadSource ?? this.leadSource,
      selectedLeadSource: selectedLeadSource ?? this.selectedLeadSource,
      leadSubSource: leadSubSource ?? this.leadSubSource,
      selectedLeadSubSource: updateSubSource ? selectedLeadSubSource ?? this.selectedLeadSubSource : null,
      agencyNames: agencyNames ?? this.agencyNames,
      campaignNames: campaignNames ?? this.campaignNames,
      selectedAgencyNames: selectedAgencyNames ?? this.selectedAgencyNames,
      selectedCampaignNames: selectedCampaignNames ?? this.selectedCampaignNames,
      assignToUsers: assignToUsers ?? this.assignToUsers,
      selectedAssignedUser: selectedAssignedUser ?? this.selectedAssignedUser,
      secondaryUsers: secondaryUsers ?? this.secondaryUsers,
      selectedSecondaryAssignedUser: selectedSecondaryAssignedUser ?? this.selectedSecondaryAssignedUser,
      carpetAreas: carpetAreas ?? this.carpetAreas,
      saleableAreas: saleableAreas ?? this.saleableAreas,
      builtUpAreas: builtUpAreas ?? this.builtUpAreas,
      selectedCarpetArea: selectedCarpetArea ?? this.selectedCarpetArea,
      selectedSaleableArea: selectedSaleableArea ?? this.selectedSaleableArea,
      selectedBuiltUpArea: selectedBuiltUpArea ?? this.selectedBuiltUpArea,
      properties: properties ?? this.properties,
      selectedProperties: selectedProperties ?? this.selectedProperties,
      projects: projects ?? this.projects,
      selectedProjects: selectedProjects ?? this.selectedProjects,
      isSubTypesExpanded: isSubTypesExpanded ?? this.isSubTypesExpanded,
      isNoOfBhkExpanded: isNoOfBhkExpanded ?? this.isNoOfBhkExpanded,
      isAltPhoneFieldVisible: isAltPhoneFieldVisible ?? this.isAltPhoneFieldVisible,
      isEmailFieldVisible: isEmailFieldVisible ?? this.isEmailFieldVisible,
      isPossessionDateVisible: isPossessionDateVisible ?? this.isPossessionDateVisible,
      isReferralDetailsVisible: isReferralDetailsVisible ?? this.isReferralDetailsVisible,
      globalSettingModel: globalSettingModel ?? this.globalSettingModel,
      dialogMessage: dialogMessage ?? this.dialogMessage,
      possessionDate: updatePossessionDate ? null : possessionDate ?? this.possessionDate,
      isLeadAlreadyExits: isLeadAlreadyExits ?? this.isLeadAlreadyExits,
      isLeadAlreadyExitsOnAltNumber: isLeadAlreadyExitsOnAltNumber ?? this.isLeadAlreadyExitsOnAltNumber,
      contactNumber: contactNumber ?? this.contactNumber,
      altContactNumber: altContactNumber ?? this.altContactNumber,
      referralContact: referralContact ?? this.referralContact,
      existingLeadId: existingLeadId ?? this.existingLeadId,
      currencies: currencies ?? this.currencies,
      selectedCurrency: selectedCurrency ?? this.selectedCurrency,
      defaultCountryCode: defaultCountryCode ?? this.defaultCountryCode,
      showDialogProgress: showDialogProgress ?? this.showDialogProgress,
      channelPartners: channelPartners ?? this.channelPartners,
      selectedChannelPartners: selectedChannelPartners ?? this.selectedChannelPartners,
      isDuplicateLeadBottomSheetVisible: isDuplicateLeadBottomSheetVisible ?? this.isDuplicateLeadBottomSheetVisible,
      primaryOrSecondary: primaryOrSecondary ?? this.primaryOrSecondary,
      purposes: purposes ?? this.purposes,
      selectedPurpose: selectedPurpose ?? this.selectedPurpose,
      isAssignedLoggedInUser: isAssignedLoggedInUser ?? this.isAssignedLoggedInUser,
      isDuplicateLeadBottomSheetVisibleForAltNumber: isDuplicateLeadBottomSheetVisibleForAltNumber ?? this.isDuplicateLeadBottomSheetVisibleForAltNumber,
      possessionTypeSelectableItems: possessionTypeSelectableItems ?? this.possessionTypeSelectableItems,
      possessionTypeSelectedItem: possessionTypeSelectedItem ?? this.possessionTypeSelectedItem,
      isPossessionDateCustomSelected: isPossessionDateCustomSelected ?? this.isPossessionDateCustomSelected,
      sourcingManager: sourcingManager ?? this.sourcingManager,
      selectedSourcingManager: selectedSourcingManager ?? this.selectedSourcingManager,
      closingManager: closingManager ?? this.closingManager,
      selectedClosingManager: selectedClosingManager ?? this.selectedClosingManager,
      customerLocations: customerLocations ?? this.customerLocations,
      executiveContact: executiveContact ?? this.executiveContact,
    );
  }
}
