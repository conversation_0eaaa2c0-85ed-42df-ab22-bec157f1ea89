import 'package:leadrat/core_main/enums/property_enums/property_status.dart';
import 'package:leadrat/core_main/remote/rest_response/paged_rest_response.dart';
import 'package:leadrat/core_main/remote/rest_response/rest_response_wrapper.dart';
import 'package:leadrat/features/lead/data/models/get_lead_model.dart';
import 'package:leadrat/features/properties/data/data_source/properties_remote_data_source.dart';
import 'package:leadrat/features/properties/data/models/add_property_model.dart';
import 'package:leadrat/features/properties/data/models/get_property_model.dart';
import 'package:leadrat/features/properties/data/models/list_property_model.dart';
import 'package:leadrat/features/properties/data/models/properties_with_id_model.dart';
import 'package:leadrat/features/properties/data/models/property_filter_model.dart';
import 'package:leadrat/features/properties/data/models/property_lead_count_model.dart';
import 'package:leadrat/features/properties/data/models/re_assign_property_model.dart';
import 'package:leadrat/features/properties/data/models/update_property_assignedTo_model.dart';
import 'package:leadrat/features/properties/data/models/update_property_share_count_model.dart';

import '../../../../core_main/remote/leadrat_rest_service.dart';
import '../../../../core_main/resources/common/rest_resources.dart';
import '../models/get_all_property_model.dart';

class PropertiesRemoteDataSourceImpl extends LeadratRestService implements PropertiesRemoteDataSource {
  @override
  Future<PagedResponse<GetAllPropertyModel, String>?> getAllProperties(int pageNumber, PropertyFilterModel? propertyFilterModel) async {
    final restRequest = createGetRequest(PropertyRestResources.getPropertiesByPageNo(pageNumber, propertyFilterModel == null ? await PropertyFilterModel().getFiltersUrl() : await propertyFilterModel.getFiltersUrl()));

    try {
      final response = await executeRequestAsync<PagedResponse<GetAllPropertyModel, String>?>(
        restRequest,
        (json) => PagedResponse<GetAllPropertyModel, String>.fromJson(
          json,
          (data) => GetAllPropertyModel.fromJson(data),
          (json) => json as String,
        ),
      );
      return response;
    } catch (e) {
      rethrow;
    }
    // return response;
  }

  @override
  Future<PagedResponse<GetAllPropertyModel, String>?> searchProperties(String searchText, int pageSize, {bool isPropertyListingEnabled = false}) async {
    final restRequest = createGetRequest(
      isPropertyListingEnabled ? PropertyRestResources.getPropertiesListingBySearchText(searchText, pageSize) : PropertyRestResources.getPropertiesBySearchText(searchText, pageSize),
    );
    final response = await executeRequestAsync<PagedResponse<GetAllPropertyModel, String>?>(
      restRequest,
      (json) => PagedResponse<GetAllPropertyModel, String>.fromJson(
        json,
        (data) => GetAllPropertyModel.fromJson(data),
        (json) => json as String,
      ),
    );
    return response;
  }

  @override
  Future<GetPropertyModel?> getPropertyById(String propertyId) async {
    final restRequest = createGetRequest(PropertyRestResources.getPropertiesByID(propertyId));
    final response = await executeRequestAsync<ResponseWrapper<GetPropertyModel?>>(
      restRequest,
      (json) => ResponseWrapper<GetPropertyModel?>.fromJson(
        json,
        (data) => fromJsonObject(data, GetPropertyModel.fromJson),
      ),
    );
    return response.data;
  }

  @override
  Future<List<PropertyLeadCountModel?>?> getMatchingAssociateLeadsCount(String propertyId) async {
    final restRequest = createGetRequest(PropertyRestResources.getMatchingAssociateLeadsByPropertyId(propertyId));
    final response = await executeRequestAsync<ResponseWrapper<List<PropertyLeadCountModel>?>>(
      restRequest,
      (json) => ResponseWrapper<List<PropertyLeadCountModel>?>.fromJson(
        json,
        (data) => fromJsonList<PropertyLeadCountModel>(data, PropertyLeadCountModel.fromJson),
      ),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getOwnerNames() async {
    final restRequest = createGetRequest(PropertyRestResources.getOwnerNames());
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(
        json,
        (data) => (data as List).map((item) => item as String).toList(),
      ),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getAddresses() async {
    final restRequest = createGetRequest(PropertyRestResources.getAddresses());
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(
        json,
        (data) => (data as List).map((item) => item as String).toList(),
      ),
    );
    return response.data;
  }

  @override
  Future<PagedResponse<LeadWithDegreeMatchedModel, String>?> getMatchingLeads({int? pageNumber, int? pageSize, required String propertyId, bool? isPropertyListingEnabled}) async {
    final matchingPropertyResources = PropertyRestResources.getMatchingLeadsByPropertyId(pageNumber: pageNumber ?? 1, pageSize: pageSize ?? 10, propertyId: propertyId);
    final matchingPropertyListingResources = PropertyRestResources.getMatchingLeadsByPropertyListingId(pageNumber: pageNumber ?? 1, pageSize: pageSize ?? 10, propertyId: propertyId);
    final restRequest = createGetRequest((isPropertyListingEnabled ?? false) ? matchingPropertyListingResources : matchingPropertyResources);
    try {
      final response = await executeRequestAsync<PagedResponse<LeadWithDegreeMatchedModel, String>?>(
        restRequest,
        (json) => PagedResponse<LeadWithDegreeMatchedModel, String>.fromJson(
          json,
          (data) => LeadWithDegreeMatchedModel.fromJson(data),
          (json) => json as String,
        ),
      );
      return response;
    } catch (ex) {
      rethrow;
    }
  }

  @override
  Future<List<PropertiesWithIdModel>?> getPropertyNameWithId() async {
    final restRequest = createGetRequest(PropertyRestResources.getPropertiesWithIds);
    final response = await executeRequestAsync<ResponseWrapper<List<PropertiesWithIdModel>?>>(
      restRequest,
      (json) => ResponseWrapper<List<PropertiesWithIdModel>?>.fromJson(json, (data) => fromJsonList(data, PropertiesWithIdModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<bool?> reAssignProperty(ReAssignPropertyModel? reAssignModel) async {
    final restRequest = createPutRequest(PropertyRestResources.propertyReassign, body: reAssignModel?.toJson() ?? '');
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(restRequest, (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool));
    return response.data;
  }

  @override
  Future<bool?> updatePropertyAssignedTo(UpdatePropertyAssignedToModel? updatePropertyAssignedToModel) async {
    final restRequest = createPutRequest(PropertyRestResources.updatePropertyAssignedTo(updatePropertyAssignedToModel?.propertyId ?? ''), body: updatePropertyAssignedToModel?.toJson() ?? '');
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(restRequest, (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool));
    return response.data;
  }

  @override
  Future<AddPropertyModel?> addProperty(AddPropertyModel? addPropertyModel) async {
    try {
      final json = addPropertyModel?.toJson().toString();

      final restRequest = createPostRequest(PropertyRestResources.addProperty, body: addPropertyModel?.toJson() ?? '');

      final response = await executeRequestAsync<ResponseWrapper<AddPropertyModel?>>(restRequest, (json) => ResponseWrapper<AddPropertyModel?>.fromJson(json, (data) => fromJsonObject(data, AddPropertyModel.fromJson)));
      return response.data;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<AddPropertyModel?> updateProperty(AddPropertyModel? updatePropertyModel) async {
    try {
      final restRequest = createPutRequest(PropertyRestResources.updateProperty(updatePropertyModel?.id ?? ''), body: updatePropertyModel?.toJson() ?? '');

      final response = await executeRequestAsync<ResponseWrapper<AddPropertyModel?>>(restRequest, (json) => ResponseWrapper<AddPropertyModel?>.fromJson(json, (data) => fromJsonObject(data, AddPropertyModel.fromJson)));
      return response.data;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<String?> deleteProperty(String? propertyId) async {
    final restRequest = createDeleteRequest(PropertyRestResources.deleteProperty(propertyId ?? ''));
    final response = await executeRequestAsync<ResponseWrapper<String?>>(restRequest, (json) => ResponseWrapper<String?>.fromJson(json, (data) => data as String));
    return response.data;
  }

  @override
  Future<bool?> archiveProperty(List<String?> archivePropertyList) async {
    final restRequest = createDeleteRequest(PropertyRestResources.softDeleteProperty, body: {
      'ids': archivePropertyList,
    });
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restRequest,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );
    return response.data;
  }

  @override
  Future<bool?> updatePropertyShareCount(UpdatePropertyShareCountModel updatePropertyShareCountModel) async {
    final restRequest = createPutRequest(PropertyRestResources.updatePropertyShareCount, body: updatePropertyShareCountModel.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restRequest,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );

    return response.data;
  }

  @override
  Future<Map<PropertyVisibility, int>?> getPropertyListingCount({PropertyFilterModel? propertyFilterModel}) async {
    final String resource = "${PropertyRestResources.getAllListingCountProperties}${await propertyFilterModel?.getFiltersUrl() ?? ''}";
    final restRequest = createGetRequest(resource);
    final response = await executeRequestAsync<ResponseWrapper<Map<String, int>>>(
      restRequest,
      (json) => ResponseWrapper<Map<String, int>>.fromJson(json, (data) => Map<String, int>.from(data)),
    );

    final mappedResponse = mapStringToEnum<int, PropertyVisibility>(response.data, PropertyVisibility.fromString, (value) => value as int);
    return mappedResponse;
  }

  @override
  Future<PagedResponse<GetAllPropertyModel, String>?> getAllPropertyListing(int pageNumber, PropertyFilterModel? propertyFilterModel, [int pageSize = 10]) async {
    final restRequest = createGetRequest(PropertyRestResources.getAllListingProperties(pageNumber, pageSize, await propertyFilterModel?.getFiltersUrl() ?? ''));
    final response = await executeRequestAsync<PagedResponse<GetAllPropertyModel, String>?>(
      restRequest,
      (json) => PagedResponse<GetAllPropertyModel, String>.fromJson(json, (data) => GetAllPropertyModel.fromJson(data), (json) => json as String),
    );
    return response;
  }

  @override
  Future<bool?> deListProperties(ListPropertyModel models) async {
    final restRequest = createPostRequest(PropertyRestResources.propertyDeList, body: models.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(restRequest, (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool?));
    return response.data;
  }

  @override
  Future<bool?> listProperties(ListPropertyModel models) async {
    final restRequest = createPostRequest(PropertyRestResources.propertyPublish, body: models.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(restRequest, (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool?));
    return response.data;
  }

  @override
  Future<List<String>?> getCurrencies() async {
    final restRequest = createGetRequest(PropertyRestResources.getCurrency);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }
}
