import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'value')
enum DateType {
  none(0, "None"),
  receivedDate(1, "Created Date"),
  scheduledDate(2, "Scheduled Date"),
  modifiedDate(3, "Modified Date"),
  assignedDate(5, "Picked Date"),
  bookedDate(6, "Booked Date"),
  assignedFromDate(7, "Assigned Date"),
  possessionDate(8, "Possession Date"),
  siteVisitOrMeetingDoneByDate(10, "Site Visit Or Meeting Done Date");

  final int value;
  final String description;

  const DateType(this.value, this.description);
}
