import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/common/constants/app_analytics_constants.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/widgets/filter_first_date_month_picker.dart';
import 'package:leadrat/core_main/common/widgets/filter_last_date_month_picker.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_form_button.dart';
import 'package:leadrat/core_main/common/widgets/min_max_validator_form.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/budget_enum.dart';
import 'package:leadrat/core_main/enums/common/date_range.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/property_enums/property_date_type.dart';
import 'package:leadrat/core_main/enums/property_enums/property_filter_keys.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/debouncer.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/lead/presentation/widgets/budget_range_slider_widget.dart';
import 'package:leadrat/features/lead/presentation/widgets/filter_custom_date_picker_widget.dart';
import 'package:leadrat/features/properties/presentation/bloc/property_filter_bloc/property_filter_bloc.dart';
import 'package:leadrat/features/properties/presentation/items/item_property_filter_model.dart';
import 'package:leadrat/features/properties/presentation/widgets/range_input_widget.dart';

class PropertiesFilterPage extends LeadratStatefulWidget {
  const PropertiesFilterPage({
    super.key,
  });

  @override
  State<PropertiesFilterPage> createState() => _LeadFilterPageState();
}

class _LeadFilterPageState extends LeadratState<PropertiesFilterPage> {
  @override
  void initState() {
    getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobilePropertyFilterPageView);
    propertyFilterBloc.add(PropertyFilterInitialEvent());
    super.initState();
  }

  TextEditingController areaController = TextEditingController();

  final debouncer = Debouncer(milliseconds: 2000);

  PropertyFilterBloc propertyFilterBloc = getIt<PropertyFilterBloc>();

  @override
  Widget buildContent(BuildContext context) {
    return BlocConsumer<PropertyFilterBloc, PropertyFilterState>(
      listener: (context, state) {
        if (state.pageState == PageState.failure) {
          LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: state.errorMessage ?? "something went wrong");
        }
      },
      builder: (context, state) {
        final selectedCategoryItem = state.itemPropertyFilterCategoryModels?.where((element) => element.isSelected).firstOrNull;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            Container(
              decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(ImageResources.imageAppBarPattern), alignment: Alignment.bottomRight)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Text("filter", style: LexendTextStyles.lexend18Bold.copyWith(color: ColorPalette.tertiaryTextColor)),
                  ),
                  GestureDetector(
                      onTap: () => propertyFilterBloc.add(PropertyFilterResetEvent()),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Text("reset", style: LexendTextStyles.lexend14Bold.copyWith(color: ColorPalette.tertiaryTextColor)),
                      )),
                ],
              ),
            ),
            if (state.itemPropertyFilterCategoryModels != null)
              Expanded(
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Container(
                      color: ColorPalette.primaryDarkColor,
                      width: context.width(35),
                      child: ListView.builder(
                        itemCount: state.itemPropertyFilterCategoryModels?.length,
                        itemBuilder: (context, index) {
                          final filterCategory = state.itemPropertyFilterCategoryModels![index];
                          return GestureDetector(
                            onTap: () => propertyFilterBloc.add(FilterCategorySelectEvent(itemPropertyFilterModel: filterCategory)),
                            child: Container(
                              key: ValueKey(index),
                              padding: const EdgeInsets.all(22),
                              decoration: BoxDecoration(
                                color: filterCategory.isSelected ? ColorPalette.primaryDarkColor : ColorPalette.darkToneInk,
                                border: Border(
                                  top: BorderSide(color: index == 0 ? ColorPalette.transparent : ColorPalette.lightBackground, width: .35),
                                  bottom: BorderSide(color: filterCategory.isSelected ? ColorPalette.lightBackground : ColorPalette.lightBackground, width: .35),
                                  right: BorderSide(color: filterCategory.isSelected ? ColorPalette.transparent : ColorPalette.lightBackground, width: .7),
                                ),
                              ),
                              child: Text('${filterCategory.categoryName} ${filterCategory.selectedFilterValues() != null ? ' (${filterCategory.selectedFilterValues()?.length})' : ""}', style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.tertiaryTextColor)),
                            ),
                          );
                        },
                      ),
                    ),
                    //Filters
                    Container(
                      width: context.width(65),
                      padding: const EdgeInsets.only(right: 14, left: 8),
                      color: ColorPalette.primaryDarkColor,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          if (selectedCategoryItem != null) ..._buildCustomHeaderView(context, selectedCategoryItem, state),
                          if (selectedCategoryItem != null && selectedCategoryItem.hasSearch) ..._buildSearchWidget(context, selectedCategoryItem, state),
                          if (selectedCategoryItem != null && selectedCategoryItem.isMultiSelect) ..._buildSelectAllItem(context, selectedCategoryItem),
                          if ((selectedCategoryItem?.hasCustomView ?? false)) _buildCustomFilterWidget(context, state, selectedCategoryItem),
                          if (selectedCategoryItem?.hasCount ?? false) MinMaxValidatorForm(minCountController: selectedCategoryItem?.minCountController, maxCountController: selectedCategoryItem?.maxCountController),
                          Expanded(
                            child: selectedCategoryItem?.isInitialized ?? false
                                ? _buildFilterList(context, state, selectedCategoryItem!)
                                : const Center(
                                    child: CircularProgressIndicator(
                                    color: ColorPalette.primaryGreen,
                                  )),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            Container(
              padding: const EdgeInsets.only(top: 15, right: 24, left: 14, bottom: 15),
              decoration: BoxDecoration(color: ColorPalette.primaryDarkColor, border: Border(top: BorderSide(color: ColorPalette.tertiaryTextColor.withOpacity(.3), width: .8))),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                      child: LeadratFormButton(
                          onPressed: () {
                            getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobilePropertyFilterCancelButtonClick);
                            propertyFilterBloc.add(PropertyFilterCancelEvent());
                            Navigator.of(context).pop();
                          },
                          buttonText: "Close")),
                  const SizedBox(width: 14),
                  Expanded(
                      child: LeadratFormButton(
                    onPressed: () {
                      getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobilePropertyFilterApplyButtonClick);
                      _applyPropertyFilter(context, state);
                    },
                    buttonText: "Apply",
                  )),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  //Lead Filter ListView With checkbox and radio tile
  Widget _buildFilterList(BuildContext context, PropertyFilterState state, ItemPropertyFilterCategoryModel category) {
    final leadFilters = category.propertyFilterValues;
    return ListView.builder(
      itemCount: leadFilters?.length ?? 0,
      itemBuilder: (context, index) {
        final categoryFilters = leadFilters!;
        final filter = categoryFilters[index];
        return !filter.visible
            ? const SizedBox()
            : Column(
                children: [
                  if (category.isMultiSelect)
                    CheckboxListTile(
                      key: ValueKey(index),
                      value: filter.isSelected,
                      onChanged: (value) {
                        propertyFilterBloc.add(FilterValueMultiSelectEvent(itemPropertyFilterModel: category, valueIndex: filter));
                      },
                      visualDensity: const VisualDensity(horizontal: -4, vertical: -2),
                      side: const BorderSide(color: ColorPalette.gray500, width: 1),
                      checkboxShape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
                      title: Text(filter.displayName ?? "", style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.primary, overflow: TextOverflow.ellipsis)),
                      controlAffinity: ListTileControlAffinity.leading,
                      contentPadding: EdgeInsets.zero,
                      activeColor: ColorPalette.primaryGreen,
                    )
                  else
                    RadioListTile(
                      key: ValueKey(index),
                      value: filter.isSelected,
                      onChanged: (value) {
                        propertyFilterBloc.add(FilterValueSingleSelectEvent(itemPropertyFilterModel: category, filterValue: filter));
                      },
                      visualDensity: const VisualDensity(horizontal: -4, vertical: -2),
                      title: Text(filter.displayName ?? "", style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.primary)),
                      controlAffinity: ListTileControlAffinity.leading,
                      contentPadding: EdgeInsets.zero,
                      activeColor: ColorPalette.primaryGreen,
                      groupValue: true,
                    ),
                  if (filter.value == BudgetEnum.customRange && (filter.isSelected ?? false)) _buildFilterCustomView(filter, state),
                  if (filter.value == DateRange.customDate && (filter.isSelected ?? false)) _customDatePicker(),
                  if (filter.value == PossessionType.customDate && (filter.isSelected ?? false)) _customMonthPicker(),
                ],
              );
      },
    );
  }

  _buildSearchWidget(BuildContext context, ItemPropertyFilterCategoryModel selectedCategoryItem, PropertyFilterState state) {
    return [
      if (selectedCategoryItem.hasSearch)
        SizedBox(
          height: 65,
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 10, 14, 14),
            child: TextField(
              controller: selectedCategoryItem.textEditingController,
              onChanged: (value) => debouncer.run(() => propertyFilterBloc.add(PropertiesSearchFiltersItemsEvent(itemPropertyFilterModel: selectedCategoryItem, searchText: value.toString()))),
              style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.gray500),
              cursorColor: ColorPalette.primaryGreen,
              decoration: InputDecoration(
                fillColor: ColorPalette.primaryColor,
                filled: true,
                border: const OutlineInputBorder(borderSide: BorderSide(color: ColorPalette.primaryLightColor)),
                enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: const BorderSide(color: ColorPalette.primaryLightColor)),
                focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: const BorderSide(color: ColorPalette.primaryLightColor)),
                contentPadding: const EdgeInsets.fromLTRB(14, 5, 5, 10),
                hintStyle: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.gray700),
                hintText: selectedCategoryItem.searchHintText ?? "type here",
                suffixIcon: const Icon(CupertinoIcons.search),
              ),
            ),
          ),
        ),
      const Divider(color: ColorPalette.tertiaryTextColor, thickness: .5, indent: 10, endIndent: 20),
    ];
  }

  Widget _buildFilterCustomView(PropertyFilterValue<dynamic> category, PropertyFilterState state) {
    return BudgetRangeSliderWidget(startValue: category.budgetRangeModel?.minBudget?.toDouble() ?? 0, endValue: category.budgetRangeModel?.maxBudget?.toDouble() ?? 1000, onRangeChanged: (startValue, endValue) => propertyFilterBloc.add(CustomPriceChangeEvent(startValue.round(), endValue.round())));
  }

  Widget _customDatePicker() {
    return BlocBuilder<PropertyFilterBloc, PropertyFilterState>(
      builder: (context, state) {
        return Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 10, 14, 10),
              child: FilterCustomDatePicker(
                labelText: 'from',
                selectedDate: state.fromDate,
                onDateSelected: (selectedDate) {
                  if (selectedDate != null) propertyFilterBloc.add(PropertySelectFromOrToDateEvent(selectedDate: selectedDate, isFromDate: true));
                },
              ),
            ),
            Text("&", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.gray600)),
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 10, 14, 0),
              child: FilterCustomDatePicker(
                labelText: 'to',
                selectedDate: state.toDate,
                onDateSelected: (selectedDate) {
                  if (selectedDate != null) propertyFilterBloc.add(PropertySelectFromOrToDateEvent(selectedDate: selectedDate, isFromDate: false));
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _customMonthPicker() {
    return BlocBuilder<PropertyFilterBloc, PropertyFilterState>(
      builder: (context, state) {
        return Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 10, 14, 10),
              child: FilterFirstDateMonthPicker(
                labelText: 'from',
                selectedDate: state.fromDate,
                maxDate: state.toDate,
                onDateSelected: (selectedDate) {
                  if (selectedDate != null) context.read<PropertyFilterBloc>().add(PropertySelectFromOrToDateEvent(selectedDate: selectedDate, isFromDate: true));
                },
              ),
            ),
            Text("&", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.gray600)),
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 10, 14, 0),
              child: FilterCustomMonthPicker(
                labelText: 'to',
                minDate: state.fromDate,
                selectedDate: state.toDate,
                onDateSelected: (selectedDate) {
                  if (selectedDate != null) context.read<PropertyFilterBloc>().add(PropertySelectFromOrToDateEvent(selectedDate: selectedDate, isFromDate: false));
                },
              ),
            ),
          ],
        );
      },
    );
  }

  _buildCustomHeaderView(BuildContext context, ItemPropertyFilterCategoryModel selectedCategoryItem, PropertyFilterState state) {
    return [
      if (selectedCategoryItem.propertyFilterKey == PropertyFilterKey.dateRange)
        Padding(
          padding: const EdgeInsets.fromLTRB(14, 0, 14, 10),
          child: SelectableItemBottomSheet<PropertyDateType>(
            title: "select date type",
            selectableItems: state.dateTypes ?? [],
            onItemSelected: (selectedItem) => propertyFilterBloc.add(PropertySelectDateTypeEvent(selectedDateType: selectedItem)),
            selectedItem: state.selectedDateType,
          ),
        ),
    ];
  }

  Widget _buildCustomFilterWidget(BuildContext context, PropertyFilterState state, ItemPropertyFilterCategoryModel? selectedCategoryItem) {
    if (PropertyFilterKey.builtUpArea == selectedCategoryItem?.propertyFilterKey) {
      return _buildCustomBuiltUpAreaWidget(context, state, selectedCategoryItem);
    } else if (PropertyFilterKey.carpetArea == selectedCategoryItem?.propertyFilterKey) {
      return _buildCustomCarpetAreaWidget(context, state, selectedCategoryItem);
    } else if (PropertyFilterKey.saleableArea == selectedCategoryItem?.propertyFilterKey) {
      return _buildCustomSaleableAreaWidget(context, state, selectedCategoryItem);
    } else if (PropertyFilterKey.propertySize == selectedCategoryItem?.propertyFilterKey) {
      return _buildCustomPropertySizeWidget(context, state, selectedCategoryItem);
    } else if (PropertyFilterKey.minBudget == selectedCategoryItem?.propertyFilterKey) {
      return _buildMinBudgetWidget(context, state, selectedCategoryItem);
    }
    // else if (PropertyFilterKey.maxBudget == selectedCategoryItem?.propertyFilterKey) {
    //   return _buildMaxBudgetWidget(context, state, selectedCategoryItem);
    // }

    return const SizedBox.shrink();
  }

  Widget _buildCustomPropertySizeWidget(BuildContext context, PropertyFilterState state, ItemPropertyFilterCategoryModel? selectedCategoryItem) {
    return Column(
      children: [
        SelectableItemBottomSheet<MasterAreaUnitsModel>(
          title: "select unit",
          canDeselectSingleItem: true,
          selectableItems: state.areaUnits,
          onItemSelected: (selectedItem) => context.read<PropertyFilterBloc>().add(SelectPropertyAreaEvent(selectedItem)),
          selectedItem: state.selectedPropertyAreaUnit,
          child: Container(
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 10).copyWith(top: 7),
            margin: const EdgeInsets.only(top: 10),
            decoration: BoxDecoration(
              color: ColorPalette.primaryColor,
              border: Border.all(width: 1, color: ColorPalette.primaryLightColor),
              borderRadius: BorderRadius.circular(5),
            ),
            alignment: Alignment.topCenter,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    state.selectedPropertyAreaUnit?.title ?? 'select unit',
                    style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.white),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const Icon(Icons.keyboard_arrow_down, size: 23, color: ColorPalette.white),
              ],
            ),
          ),
        ),
        const SizedBox(height: 10),
        RangeInputWidget(
          key: ValueKey(selectedCategoryItem?.propertyFilterKey),
          onRangeChanged: (rangeInput) => context.read<PropertyFilterBloc>().add(OnPropertySizeChangeEvent(rangeInput)),
          initialMaxValue: state.propertySize?.max,
          initialMinValue: state.propertySize?.min,
          moduleName: 'property size',
        ),
      ],
    );
  }

  Widget _buildCustomBuiltUpAreaWidget(BuildContext context, PropertyFilterState state, ItemPropertyFilterCategoryModel? selectedCategoryItem) {
    return Column(
      children: [
        SelectableItemBottomSheet<MasterAreaUnitsModel>(
          title: "select unit",
          canDeselectSingleItem: true,
          selectableItems: state.areaUnits,
          onItemSelected: (selectedItem) => context.read<PropertyFilterBloc>().add(SelectBuiltUpAreaEvent(selectedItem)),
          selectedItem: state.selectedBuiltUpAreaUnit,
          child: Container(
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 10).copyWith(top: 7),
            margin: const EdgeInsets.only(top: 10),
            decoration: BoxDecoration(
              color: ColorPalette.primaryColor,
              border: Border.all(width: 1, color: ColorPalette.primaryLightColor),
              borderRadius: BorderRadius.circular(5),
            ),
            alignment: Alignment.topCenter,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    state.selectedBuiltUpAreaUnit?.title ?? 'select unit',
                    style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.white),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const Icon(Icons.keyboard_arrow_down, size: 23, color: ColorPalette.white),
              ],
            ),
          ),
        ),
        const SizedBox(height: 10),
        RangeInputWidget(
          key: ValueKey(selectedCategoryItem?.propertyFilterKey),
          onRangeChanged: (rangeInput) => context.read<PropertyFilterBloc>().add(OnBuiltUpAreaChangeEvent(rangeInput)),
          initialMaxValue: state.builtUpArea?.max,
          initialMinValue: state.builtUpArea?.min,
          moduleName: 'builtup area',
        ),
      ],
    );
  }

  Widget _buildCustomCarpetAreaWidget(BuildContext context, PropertyFilterState state, ItemPropertyFilterCategoryModel? selectedCategoryItem) {
    return Column(
      children: [
        SelectableItemBottomSheet<MasterAreaUnitsModel>(
          title: "select unit",
          canDeselectSingleItem: true,
          selectableItems: state.areaUnits,
          onItemSelected: (selectedItem) => context.read<PropertyFilterBloc>().add(SelectCarpetAreaEvent(selectedItem)),
          selectedItem: state.selectedCarpetAreaUnit,
          child: Container(
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 10).copyWith(top: 7),
            margin: const EdgeInsets.only(top: 10),
            decoration: BoxDecoration(
              color: ColorPalette.primaryColor,
              border: Border.all(width: 1, color: ColorPalette.primaryLightColor),
              borderRadius: BorderRadius.circular(5),
            ),
            alignment: Alignment.topCenter,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    state.selectedCarpetAreaUnit?.title ?? 'select unit',
                    style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.white),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const Icon(Icons.keyboard_arrow_down, size: 23, color: ColorPalette.white),
              ],
            ),
          ),
        ),
        const SizedBox(height: 10),
        RangeInputWidget(
          key: ValueKey(selectedCategoryItem?.propertyFilterKey),
          onRangeChanged: (rangeInput) => context.read<PropertyFilterBloc>().add(OnCarpetAreaChangeEvent(rangeInput)),
          initialMaxValue: state.carpetArea?.max,
          initialMinValue: state.carpetArea?.min,
          moduleName: 'carpet area',
        ),
      ],
    );
  }

  Widget _buildCustomSaleableAreaWidget(BuildContext context, PropertyFilterState state, ItemPropertyFilterCategoryModel? selectedCategoryItem) {
    return Column(
      children: [
        SelectableItemBottomSheet<MasterAreaUnitsModel>(
          title: "select unit",
          canDeselectSingleItem: true,
          selectableItems: state.areaUnits,
          onItemSelected: (selectedItem) => context.read<PropertyFilterBloc>().add(SelectSaleableAreaEvent(selectedItem)),
          selectedItem: state.selectedSaleableAreaUnit,
          child: Container(
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 10).copyWith(top: 7),
            margin: const EdgeInsets.only(top: 10),
            decoration: BoxDecoration(
              color: ColorPalette.primaryColor,
              border: Border.all(width: 1, color: ColorPalette.primaryLightColor),
              borderRadius: BorderRadius.circular(5),
            ),
            alignment: Alignment.topCenter,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    state.selectedSaleableAreaUnit?.title ?? 'select unit',
                    style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.white),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const Icon(Icons.keyboard_arrow_down, size: 23, color: ColorPalette.white),
              ],
            ),
          ),
        ),
        const SizedBox(height: 10),
        RangeInputWidget(
          key: ValueKey(selectedCategoryItem?.propertyFilterKey),
          onRangeChanged: (rangeInput) => context.read<PropertyFilterBloc>().add(OnSaleableAreaChangeEvent(rangeInput)),
          initialMaxValue: state.saleableArea?.max,
          initialMinValue: state.saleableArea?.min,
          moduleName: 'saleable area',
        ),
      ],
    );
  }

  void _applyPropertyFilter(BuildContext context, PropertyFilterState state) {
    if (!(state.saleableArea?.isValid ?? true)) {
      LeadratCustomSnackbar.show(message: "Please enter the valid saleable area", context: context);
      return;
    }
    if (!(state.carpetArea?.isValid ?? true)) {
      LeadratCustomSnackbar.show(message: "Please enter the valid carpet area", context: context);
      return;
    }
    if (!(state.builtUpArea?.isValid ?? true)) {
      LeadratCustomSnackbar.show(message: "Please enter the valid built-up area", context: context);
      return;
    }
    if (!(state.propertySize?.isValid ?? true)) {
      LeadratCustomSnackbar.show(message: "Please enter the valid property size", context: context);
      return;
    }
    propertyFilterBloc.add(PropertyFilterApplyEvent());
    Navigator.of(context).pop();
  }

  Widget _buildCustomAreaWidget(BuildContext context, PropertyFilterState state, ItemPropertyFilterCategoryModel? selectedCategoryItem) {
    final (AreaType? areaType, double? area, SelectableItem<MasterAreaUnitsModel>? areaUnit) getAreDetails = propertyFilterBloc.getAreaDetails(selectedCategoryItem);
    areaController.text = getAreDetails.$2 == null ? '' : getAreDetails.$2.toString().replaceAll(RegExp(r'\.0$'), '') ?? '';
    return getAreDetails.$1 == null
        ? const SizedBox()
        : Column(
            children: [
              SelectableItemBottomSheet<MasterAreaUnitsModel>(
                title: "select unit",
                selectableItems: state.areaUnits,
                onItemSelected: (selectedItem) => propertyFilterBloc.add(AreaUnitChanged(areaType: getAreDetails.$1!, selectedAreaUnit: selectedItem)),
                selectedItem: getAreDetails.$3,
                child: Container(
                  height: 40,
                  padding: const EdgeInsets.symmetric(horizontal: 10).copyWith(top: 7),
                  margin: const EdgeInsets.only(top: 10),
                  decoration: BoxDecoration(
                    color: ColorPalette.primaryColor,
                    border: Border.all(width: 1, color: ColorPalette.primaryLightColor),
                    borderRadius: BorderRadius.circular(5),
                  ),
                  alignment: Alignment.topCenter,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Text(
                          getAreDetails.$3?.title ?? 'select unit',
                          style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.white),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const Icon(Icons.keyboard_arrow_down, size: 23, color: ColorPalette.white),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 10),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 40,
                      child: Text(
                        "Area",
                        textAlign: TextAlign.start,
                        style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.gray600),
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextField(
                            controller: areaController,
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            decoration: InputDecoration(
                              hintText: "ex 12345",
                              hintStyle: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.gray600),
                              contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
                              isCollapsed: true,
                            ),
                            style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.white),
                            onChanged: (value) {
                              propertyFilterBloc.add(AreaValueChanged(areaType: getAreDetails.$1!, input: double.parse(value)));
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              )
            ],
          );
  }

  _buildSelectAllItem(BuildContext context, ItemPropertyFilterCategoryModel selectedCategoryItem) {
    return [
      const SizedBox(height: 10),
      CheckboxListTile(
        value: (selectedCategoryItem.isAllSelected) ? null : false,
        onChanged: (value) {
          propertyFilterBloc.add(FilterValueMultiSelectEvent(itemPropertyFilterModel: selectedCategoryItem, isSelectAll: true));
        },
        tristate: true,
        visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
        side: const BorderSide(color: ColorPalette.gray500, width: 1),
        checkboxShape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        title: Text((selectedCategoryItem.isAllSelected) ? "Deselect all" : "Select all", style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.primary)),
        controlAffinity: ListTileControlAffinity.leading,
        contentPadding: EdgeInsets.zero,
        activeColor: ColorPalette.primaryGreen,
      ),
      const Divider(color: ColorPalette.tertiaryTextColor, thickness: .5, indent: 10, endIndent: 20),
    ];
  }

  Widget _buildMinBudgetWidget(BuildContext context, PropertyFilterState state, ItemPropertyFilterCategoryModel? selectedCategoryItem) {
    return Column(
      mainAxisSize: MainAxisSize.min, // Allows the column to shrink-wrap its content
      children: [
        SelectableItemBottomSheet<String>(
          title: "Select Currency",
          selectableItems: state.currencies ?? [],
          onItemSelected: (selectedItem) => context.read<PropertyFilterBloc>().add(PropertiesChangeCurrencyEvent(selectedCurrency: selectedItem)),
          selectedItem: state.selectedCurrency,
          child: Container(
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 10).copyWith(top: 7),
            margin: const EdgeInsets.only(top: 10),
            decoration: BoxDecoration(
              color: ColorPalette.primaryColor,
              border: Border.all(width: 1, color: ColorPalette.primaryLightColor),
              borderRadius: BorderRadius.circular(5),
            ),
            alignment: Alignment.topCenter,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  state.selectedCurrency?.title ?? 'Select Currency',
                  style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.white),
                ),
                const Icon(Icons.keyboard_arrow_down, size: 23, color: ColorPalette.white),
              ],
            ),
          ),
        ),
        const SizedBox(height: 10),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: SizedBox(
            width: double.infinity, // Ensures Row gets a bounded width
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                const SizedBox(width: 20),
                Expanded(
                  // Ensures that RangeInputWidgetLead does not cause unbounded width issues
                  child: RangeInputWidget(
                    key: ValueKey(selectedCategoryItem?.propertyFilterKey),
                    onRangeChanged: (rangeInput) => context.read<PropertyFilterBloc>().add(OnMinBudgetChangeEvent(rangeInput)),
                    initialMaxValue: state.minBudget?.max,
                    initialMinValue: state.minBudget?.min,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
