import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/enums/property_enums/listing_management_filter_key.dart';
import 'package:leadrat/core_main/extensions/enum_extension.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';

class ItemListingPropertyFilterCategoryModel {
  final ListingManagementFilterKey filterKey;
  final bool hasCustomHeaderView;
  final bool hasSearch;
  final String? searchHintText;
  final List<ItemListingPropertyFilterModel>? filters;
  final bool isSelected;
  final bool hasSelectAll;
  final bool hasMultiSelect;
  final bool isInitialized;
  final bool isCustomCategoryName;
  final String? categoryName;
  final bool hasCustomFilters;
  String? displayName;
  late bool isAllSelected;
  final TextEditingController? searchController;
  final TextEditingController? minCountController;
  final TextEditingController? maxCountController;
  final bool hasCount;

  ItemListingPropertyFilterCategoryModel({
    required this.filterKey,
    this.categoryName,
    this.hasCustomHeaderView = false,
    this.hasSearch = false,
    this.isSelected = false,
    this.hasMultiSelect = true,
    this.hasSelectAll = false,
    this.isInitialized = false,
    this.searchHintText,
    this.searchController,
    this.filters,
    this.isCustomCategoryName = false,
    this.hasCustomFilters = false,
    this.minCountController,
    this.maxCountController,
    this.hasCount = false,
  }) {
    displayName = displayName ?? _setDisplayName();
    isAllSelected = filters?.every((element) => element.isSelected) ?? false;
  }

  String _setDisplayName() {
    final name = isCustomCategoryName ? categoryName ?? '' : filterKey.description;
    final selectedFilterLength = filters?.where((element) => element.isSelected).length ?? 0;
    if (selectedFilterLength > 0 && hasMultiSelect) {
      return "$name ($selectedFilterLength)";
    } else {
      return name;
    }
  }

  ItemListingPropertyFilterCategoryModel copyWith({
    ListingManagementFilterKey? filterKey,
    bool? hasCustomView,
    bool? hasSearch,
    String? searchHintText,
    List<ItemListingPropertyFilterModel>? filters,
    bool? isSelected,
    bool? hasSelectAll,
    bool? hasMultiSelect,
    bool? isInitialized,
    bool? isAllSelected,
    TextEditingController? searchController,
    TextEditingController? pinCodeController,
    bool? isCustomCategoryName,
    bool? hasCustomHeaderView,
    String? categoryName,
    bool? hasCustomFilters,
    TextEditingController? minCountController,
    TextEditingController? maxCountController,
    bool? hasCount,
    bool resetCount = false,
  }) {
    return ItemListingPropertyFilterCategoryModel(
      filterKey: filterKey ?? this.filterKey,
      hasCustomHeaderView: hasCustomView ?? this.hasCustomHeaderView,
      hasSearch: hasSearch ?? this.hasSearch,
      searchHintText: searchHintText ?? this.searchHintText,
      filters: filters ?? this.filters,
      isSelected: isSelected ?? this.isSelected,
      hasSelectAll: hasSelectAll ?? this.hasSelectAll,
      hasMultiSelect: hasMultiSelect ?? this.hasMultiSelect,
      isInitialized: isInitialized ?? this.isInitialized,
      searchController: searchController ?? this.searchController,
      isCustomCategoryName: isCustomCategoryName ?? this.isCustomCategoryName,
      categoryName: categoryName ?? this.categoryName,
      hasCustomFilters: hasCustomFilters ?? this.hasCustomFilters,
      hasCount: hasCount ?? this.hasCount,
      maxCountController: resetCount ? TextEditingController() : maxCountController ?? this.maxCountController,
      minCountController: resetCount ? TextEditingController() : minCountController ?? this.minCountController,
    );
  }
}

class ItemListingPropertyFilterModel<T> {
  final ListingManagementFilterKey? filterKey;
  final String displayName;
  final String? description;
  final bool isActive;
  final bool isSelectAll;
  final bool hasSubFilters;
  final bool isRequired;
  final dynamic category;
  final bool isSelected;
  final bool hasCustomView;
  final bool hasMonthFilter;
  final List<ItemSimpleModel>? subFilters;
  final T? value;

  ItemListingPropertyFilterModel({
    this.filterKey,
    required this.displayName,
    this.description,
    this.isActive = true,
    this.isSelectAll = false,
    this.hasSubFilters = false,
    this.isRequired = false,
    this.isSelected = false,
    this.hasCustomView = false,
    this.category,
    this.subFilters,
    this.value,
    this.hasMonthFilter = false,
  });

  ItemListingPropertyFilterModel<T> copyWith({
    ListingManagementFilterKey? filterKey,
    String? displayName,
    String? description,
    bool? isActive,
    bool? isSelectAll,
    bool? hasSubFilters,
    bool? isRequired,
    dynamic category,
    bool? isSelected,
    bool? hasCustomView,
    List<ItemSimpleModel>? subFilters,
    T? value,
    bool? hasMonthFilter,
  }) {
    return ItemListingPropertyFilterModel<T>(
      filterKey: filterKey ?? this.filterKey,
      displayName: displayName ?? this.displayName,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      isSelectAll: isSelectAll ?? this.isSelectAll,
      hasSubFilters: hasSubFilters ?? this.hasSubFilters,
      isRequired: isRequired ?? this.isRequired,
      category: category ?? this.category,
      isSelected: isSelected ?? this.isSelected,
      hasCustomView: hasCustomView ?? this.hasCustomView,
      subFilters: subFilters ?? this.subFilters,
      value: value ?? this.value,
      hasMonthFilter: hasMonthFilter ?? this.hasMonthFilter,
    );
  }

  static List<T>? getSelectedEnums<T extends Enum>(List<ItemListingPropertyFilterCategoryModel> categories, ListingManagementFilterKey key, List<T> enumValues, {bool useDescription = false}) {
    try {
      var selectedItems = categories.firstWhereOrNull((category) => category.filterKey == key)?.filters?.where((filter) => filter.isSelected).toList();
      if (selectedItems?.isEmpty ?? true) return null;

      return selectedItems!.map((item) => useDescription ? getEnumFromValue<T>(enumValues, int.tryParse(item.description ?? "")) : getEnumFromDescription<T>(enumValues, item.displayName)).whereType<T>().toList();
    } catch (exception) {
      "Error while selecting the SelectedEnums, ${exception.toString()}".printInConsole();
      return null;
    }
  }

  static List<String>? getSelectedDisplayNames(List<ItemListingPropertyFilterCategoryModel> categories, ListingManagementFilterKey key) {
    try {
      var selectedItems = categories.firstWhereOrNull((category) => category.filterKey == key)?.filters?.where((filter) => filter.isSelected).toList();
      return selectedItems?.isNotEmpty ?? false ? selectedItems!.map((e) => e.displayName).toList() : null;
    } catch (exception) {
      "Error while selecting the displayNames, ${exception.toString()}".printInConsole();
      return null;
    }
  }

  static List<String>? getSelectedDescription(List<ItemListingPropertyFilterCategoryModel> categories, ListingManagementFilterKey key) {
    try {
      var selectedItems = categories.firstWhereOrNull((category) => category.filterKey == key)?.filters?.where((filter) => filter.isSelected).toList();
      return selectedItems?.isNotEmpty ?? false ? selectedItems!.map((e) => e.description ?? "").toList() : null;
    } catch (exception) {
      "Error while selecting the descriptions, ${exception.toString()}".printInConsole();
      return null;
    }
  }

  static List<T>? getSelectedValue<T>(List<ItemListingPropertyFilterCategoryModel> categories, ListingManagementFilterKey key) {
    try {
      var selectedItems = categories.firstWhereOrNull((category) => category.filterKey == key)?.filters?.where((filter) => filter.isSelected).toList();
      return selectedItems?.isNotEmpty ?? false ? selectedItems!.map<T>((e) => e.value).toList() : null;
    } catch (exception) {
      "Error while selecting the values, ${exception.toString()}".printInConsole();
      return null;
    }
  }
}
