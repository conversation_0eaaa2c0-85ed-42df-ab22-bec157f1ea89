part of 'custom_add_lead_bloc.dart';

@immutable
class CustomAddLeadState {
  final PageState pageState;
  final String? errorMessage;
  final String? successMessage;
  final List<ItemSimpleModel<EnquiryType>> enquiredFor;
  final List<ItemSimpleModel<PropertyType>> propertyTypes;
  final List<ItemSimpleModel<String>> propertySubTypes;
  final List<ItemSimpleModel<Beds>> beds;
  final List<ItemSimpleModel<double>> noOfBhk;
  final List<ItemSimpleModel<int>> baths;
  final List<ItemSimpleModel<AddressModel>> locations;
  final ItemSimpleModel<AddressModel>? customerLocations;
  final List<ItemSimpleModel<Profession>> professions;
  final List<SelectableItem<int>> leadSource;
  final List<SelectableItem<Country>> nationality;
  final SelectableItem<Country>? selectedCountry;
  final SelectableItem<int>? selectedLeadSource;
  final List<SelectableItem<String>> leadSubSource;
  final SelectableItem<String>? selectedLeadSubSource;
  final List<SelectableItem<String>> agencyNames;
  final List<SelectableItem<String>>? selectedAgencyNames;
  final List<SelectableItem<String>>? selectedFloors;
  final List<SelectableItem<String>> assignToUsers;
  final List<SelectableItem<String>> sourcingManager;
  final List<SelectableItem<String>> closingManager;
  final SelectableItem<String>? selectedAssignedUser;
  final SelectableItem<String>? selectedSourcingManager;
  final SelectableItem<String>? selectedClosingManager;
  final List<SelectableItem<String>> secondaryUsers;
  final SelectableItem<String>? selectedSecondaryAssignedUser;
  final List<SelectableItem<MasterAreaUnitsModel>> carpetAreas;
  final List<SelectableItem<MasterAreaUnitsModel>> saleableAreas;
  final List<SelectableItem<MasterAreaUnitsModel>> builtUpAreas;
  final List<SelectableItem<MasterAreaUnitsModel>> netAreas;
  final List<SelectableItem<MasterAreaUnitsModel>> propertyAreas;
  final SelectableItem<MasterAreaUnitsModel>? selectedCarpetArea;
  final SelectableItem<MasterAreaUnitsModel>? selectedSaleableArea;
  final SelectableItem<MasterAreaUnitsModel>? selectedBuiltUpArea;
  final SelectableItem<MasterAreaUnitsModel>? selectedNetArea;
  final SelectableItem<MasterAreaUnitsModel>? selectedPropertyArea;
  final List<SelectableItem<String>> properties;
  final List<SelectableItem<String>>? selectedProperties;
  final List<SelectableItem<String>> projects;
  final List<SelectableItem<String>>? selectedProjects;
  final bool isSubTypesExpanded;
  final bool isNoOfBhkExpanded;
  final bool isEmailFieldVisible;
  final bool isAltPhoneFieldVisible;
  final bool isPossessionDateVisible;
  final bool isReferralDetailsVisible;
  final GlobalSettingModel? globalSettingModel;
  final DateTime? possessionDate;
  final String dialogMessage;
  final bool isLeadAlreadyExits;
  final String? existingLeadId;
  final String? contactNumber;
  final String? altContactNumber;
  final String? referralContact;
  final List<SelectableItem<String>> currencies;
  final SelectableItem<String>? selectedCurrency;
  final String? defaultCountryCode;
  final bool showDialogProgress;
  final List<SelectableItem<String>> channelPartners;
  final List<SelectableItem<String>> campaignNames;
  final List<SelectableItem<String>>? selectedCampaignNames;
  final List<SelectableItem<String>> selectedChannelPartners;
  final List<ItemSimpleModel<FurnishStatus>> furnished;
  final List<ItemSimpleModel<OfferingType>> offerType;
  final List<SelectableItem<String>> floors;
  final bool isDuplicateLeadBottomSheetVisible;
  final bool isDuplicateLeadBottomSheetVisibleForAltNumber;
  final String primaryOrSecondary;
  final List<SelectableItem<PurposeEnum>> purposes;
  final SelectableItem<PurposeEnum>? selectedPurpose;
  final bool? isAssignedLoggedInUser;
  final bool isLeadAlreadyExitsOnAltNumber;
  final List<SelectableItem<PossessionType?>>? possessionTypeSelectableItems;
  final SelectableItem<PossessionType?>? possessionTypeSelectedItem;
  final bool isPossessionDateCustomSelected;
  final String? executiveContact;

  const CustomAddLeadState({
    this.pageState = PageState.initial,
    this.errorMessage,
    this.successMessage,
    this.enquiredFor = const [],
    this.propertyTypes = const [],
    this.propertySubTypes = const [],
    this.beds = const [],
    this.noOfBhk = const [],
    this.baths = const [],
    this.properties = const [],
    this.projects = const [],
    this.locations = const [],
    this.customerLocations,
    this.professions = const [],
    this.leadSource = const [],
    this.leadSubSource = const [],
    this.agencyNames = const [],
    this.assignToUsers = const [],
    this.sourcingManager = const [],
    this.closingManager = const [],
    this.secondaryUsers = const [],
    this.carpetAreas = const [],
    this.saleableAreas = const [],
    this.builtUpAreas = const [],
    this.isSubTypesExpanded = false,
    this.isNoOfBhkExpanded = false,
    this.selectedLeadSource,
    this.selectedLeadSubSource,
    this.selectedAgencyNames,
    this.selectedFloors,
    this.selectedAssignedUser,
    this.selectedSourcingManager,
    this.selectedClosingManager,
    this.selectedSecondaryAssignedUser,
    this.selectedCarpetArea,
    this.selectedBuiltUpArea,
    this.selectedSaleableArea,
    this.selectedProperties,
    this.selectedProjects,
    this.isAltPhoneFieldVisible = false,
    this.isEmailFieldVisible = false,
    this.isPossessionDateVisible = false,
    this.isReferralDetailsVisible = false,
    this.isLeadAlreadyExits = false,
    this.isLeadAlreadyExitsOnAltNumber = false,
    this.existingLeadId,
    this.globalSettingModel,
    this.possessionDate,
    this.dialogMessage = "loading",
    this.contactNumber,
    this.altContactNumber,
    this.referralContact,
    this.currencies = const [],
    this.selectedCurrency,
    this.defaultCountryCode,
    this.showDialogProgress = false,
    this.channelPartners = const [],
    this.selectedCampaignNames = const [],
    this.campaignNames = const [],
    this.selectedChannelPartners = const [],
    this.furnished = const [],
    this.offerType = const [],
    this.floors = const [],
    this.isDuplicateLeadBottomSheetVisible = false,
    this.isDuplicateLeadBottomSheetVisibleForAltNumber = false,
    this.primaryOrSecondary = '',
    this.netAreas = const [],
    this.propertyAreas = const [],
    this.selectedNetArea,
    this.selectedPropertyArea,
    this.nationality = const [],
    this.selectedCountry,
    this.selectedPurpose,
    this.purposes = const [],
    this.isAssignedLoggedInUser,
    this.possessionTypeSelectableItems,
    this.possessionTypeSelectedItem,
    this.isPossessionDateCustomSelected = false,
    this.executiveContact,
  });

  CustomAddLeadState initialState() => const CustomAddLeadState(
        pageState: PageState.initial,
        errorMessage: null,
        successMessage: null,
        enquiredFor: [],
        propertyTypes: [],
        propertySubTypes: [],
        beds: [],
        noOfBhk: [],
        baths: [],
        properties: [],
        projects: [],
        locations: [],
        customerLocations: null,
        professions: [],
        leadSource: [],
        leadSubSource: [],
        agencyNames: [],
        assignToUsers: [],
        sourcingManager: [],
        closingManager: [],
        secondaryUsers: [],
        carpetAreas: [],
        saleableAreas: [],
        builtUpAreas: [],
        netAreas: [],
        propertyAreas: [],
        isSubTypesExpanded: false,
        isNoOfBhkExpanded: false,
        selectedLeadSource: null,
        selectedLeadSubSource: null,
        selectedAgencyNames: null,
        selectedFloors: null,
        selectedAssignedUser: null,
        selectedSourcingManager: null,
        selectedClosingManager: null,
        selectedSecondaryAssignedUser: null,
        selectedCarpetArea: null,
        selectedSaleableArea: null,
        selectedBuiltUpArea: null,
        selectedPropertyArea: null,
        selectedNetArea: null,
        selectedProperties: null,
        selectedProjects: null,
        isEmailFieldVisible: false,
        isPossessionDateVisible: false,
        isReferralDetailsVisible: false,
        isAltPhoneFieldVisible: false,
        possessionDate: null,
        dialogMessage: "loading",
        contactNumber: null,
        altContactNumber: null,
        referralContact: null,
        existingLeadId: null,
        currencies: [],
        selectedCurrency: null,
        defaultCountryCode: null,
        showDialogProgress: false,
        channelPartners: [],
        campaignNames: [],
        selectedCampaignNames: [],
        selectedChannelPartners: [],
        furnished: [],
        offerType: [],
        floors: [],
        nationality: [],
        selectedCountry: null,
        purposes: [],
        isAssignedLoggedInUser: null,
        executiveContact: null,
      );

  CustomAddLeadState copyWith({
    PageState? pageState,
    String? errorMessage,
    String? successMessage,
    List<ItemSimpleModel<EnquiryType>>? enquiredFor,
    List<ItemSimpleModel<PropertyType>>? propertyTypes,
    List<ItemSimpleModel<String>>? propertySubTypes,
    List<ItemSimpleModel<Beds>>? bhkTypes,
    List<ItemSimpleModel<double>>? noOfBhk,
    List<ItemSimpleModel<int>>? baths,
    List<ItemSimpleModel<Beds>>? beds,
    List<ItemSimpleModel<AddressModel>>? locations,
    ItemSimpleModel<AddressModel>? customerLocations,
    List<ItemSimpleModel<Profession>>? professions,
    List<SelectableItem<int>>? leadSource,
    SelectableItem<int>? selectedLeadSource,
    List<SelectableItem<String>>? leadSubSource,
    SelectableItem<String>? selectedLeadSubSource,
    bool updateSubSource = true, // added to make selectedSubSource null
    bool updatePossessionDate = false, // added to make selectedSubSource null
    List<SelectableItem<String>>? agencyNames,
    List<SelectableItem<String>>? selectedAgencyNames,
    List<SelectableItem<String>>? selectedFloors,
    List<SelectableItem<String>>? assignToUsers,
    List<SelectableItem<String>>? sourcingManager,
    List<SelectableItem<String>>? closingManager,
    SelectableItem<String>? selectedAssignedUser,
    SelectableItem<String>? selectedSourcingManager,
    SelectableItem<String>? selectedClosingManager,
    List<SelectableItem<String>>? secondaryUsers,
    SelectableItem<String>? selectedSecondaryAssignedUser,
    List<SelectableItem<MasterAreaUnitsModel>>? carpetAreas,
    List<SelectableItem<MasterAreaUnitsModel>>? saleableAreas,
    List<SelectableItem<MasterAreaUnitsModel>>? builtUpAreas,
    List<SelectableItem<MasterAreaUnitsModel>>? netAreas,
    List<SelectableItem<MasterAreaUnitsModel>>? propertyAreas,
    SelectableItem<MasterAreaUnitsModel>? selectedCarpetArea,
    SelectableItem<MasterAreaUnitsModel>? selectedSaleableArea,
    SelectableItem<MasterAreaUnitsModel>? selectedBuiltUpArea,
    SelectableItem<MasterAreaUnitsModel>? selectedNetArea,
    SelectableItem<MasterAreaUnitsModel>? selectedPropertyArea,
    List<SelectableItem<String>>? properties,
    List<SelectableItem<String>>? selectedProperties,
    List<SelectableItem<String>>? projects,
    List<SelectableItem<String>>? selectedProjects,
    bool? isSubTypesExpanded,
    bool? isNoOfBhkExpanded,
    bool? isEmailFieldVisible,
    bool? isAltPhoneFieldVisible,
    bool? isPossessionDateVisible,
    bool? isReferralDetailsVisible,
    GlobalSettingModel? globalSettingModel,
    DateTime? possessionDate,
    String? dialogMessage,
    bool? isLeadAlreadyExits,
    bool? isLeadAlreadyExitsOnAltNumber,
    String? existingLeadId,
    String? contactNumber,
    String? altContactNumber,
    String? referralContact,
    List<SelectableItem<String>>? currencies,
    SelectableItem<String>? selectedCurrency,
    String? defaultCountryCode,
    bool? showDialogProgress,
    List<SelectableItem<String>>? channelPartners,
    List<SelectableItem<String>>? campaignNames,
    List<SelectableItem<String>>? selectedCampaignNames,
    List<SelectableItem<String>>? selectedChannelPartners,
    List<ItemSimpleModel<FurnishStatus>>? furnished,
    List<ItemSimpleModel<OfferingType>>? offerType,
    List<SelectableItem<String>>? floors,
    bool? isDuplicateLeadBottomSheetVisible,
    bool? isDuplicateLeadBottomSheetVisibleForAltNumber,
    String? primaryOrSecondary,
    List<SelectableItem<Country>>? nationality,
    SelectableItem<Country>? selectedCountry,
    List<SelectableItem<PurposeEnum>>? purposes,
    SelectableItem<PurposeEnum>? selectedPurpose,
    bool? isAssignedLoggedInUser,
    List<SelectableItem<PossessionType?>>? possessionTypeSelectableItems,
    SelectableItem<PossessionType?>? possessionTypeSelectedItem,
    bool? isPossessionDateCustomSelected,
    String? executiveContact,
  }) {
    return CustomAddLeadState(
      pageState: pageState ?? PageState.initial,
      errorMessage: errorMessage,
      successMessage: successMessage,
      enquiredFor: enquiredFor ?? this.enquiredFor,
      propertyTypes: propertyTypes ?? this.propertyTypes,
      propertySubTypes: propertySubTypes ?? this.propertySubTypes,
      beds: beds ?? this.beds,
      noOfBhk: noOfBhk ?? this.noOfBhk,
      baths: baths ?? this.baths,
      locations: locations ?? this.locations,
      customerLocations: customerLocations ?? this.customerLocations,
      professions: professions ?? this.professions,
      leadSource: leadSource ?? this.leadSource,
      selectedLeadSource: selectedLeadSource ?? this.selectedLeadSource,
      leadSubSource: leadSubSource ?? this.leadSubSource,
      selectedLeadSubSource: updateSubSource ? selectedLeadSubSource ?? this.selectedLeadSubSource : null,
      agencyNames: agencyNames ?? this.agencyNames,
      selectedAgencyNames: selectedAgencyNames ?? this.selectedAgencyNames,
      selectedFloors: selectedFloors ?? this.selectedFloors,
      assignToUsers: assignToUsers ?? this.assignToUsers,
      sourcingManager: sourcingManager ?? this.sourcingManager,
      closingManager: closingManager ?? this.closingManager,
      selectedAssignedUser: selectedAssignedUser ?? this.selectedAssignedUser,
      selectedSourcingManager: selectedSourcingManager ?? this.selectedSourcingManager,
      selectedClosingManager: selectedClosingManager ?? this.selectedClosingManager,
      secondaryUsers: secondaryUsers ?? this.secondaryUsers,
      selectedSecondaryAssignedUser: selectedSecondaryAssignedUser ?? this.selectedSecondaryAssignedUser,
      carpetAreas: carpetAreas ?? this.carpetAreas,
      saleableAreas: saleableAreas ?? this.saleableAreas,
      builtUpAreas: builtUpAreas ?? this.builtUpAreas,
      selectedCarpetArea: selectedCarpetArea ?? this.selectedCarpetArea,
      selectedSaleableArea: selectedSaleableArea ?? this.selectedSaleableArea,
      selectedBuiltUpArea: selectedBuiltUpArea ?? this.selectedBuiltUpArea,
      selectedNetArea: selectedNetArea ?? this.selectedNetArea,
      selectedPropertyArea: selectedPropertyArea ?? this.selectedPropertyArea,
      propertyAreas: propertyAreas ?? this.propertyAreas,
      netAreas: netAreas ?? this.netAreas,
      properties: properties ?? this.properties,
      selectedProperties: selectedProperties ?? this.selectedProperties,
      projects: projects ?? this.projects,
      selectedProjects: selectedProjects ?? this.selectedProjects,
      isSubTypesExpanded: isSubTypesExpanded ?? this.isSubTypesExpanded,
      isNoOfBhkExpanded: isNoOfBhkExpanded ?? this.isNoOfBhkExpanded,
      isAltPhoneFieldVisible: isAltPhoneFieldVisible ?? this.isAltPhoneFieldVisible,
      isEmailFieldVisible: isEmailFieldVisible ?? this.isEmailFieldVisible,
      isPossessionDateVisible: isPossessionDateVisible ?? this.isPossessionDateVisible,
      isReferralDetailsVisible: isReferralDetailsVisible ?? this.isReferralDetailsVisible,
      globalSettingModel: globalSettingModel ?? this.globalSettingModel,
      dialogMessage: dialogMessage ?? this.dialogMessage,
      possessionDate: updatePossessionDate ? null : possessionDate ?? this.possessionDate,
      isLeadAlreadyExits: isLeadAlreadyExits ?? this.isLeadAlreadyExits,
      isLeadAlreadyExitsOnAltNumber: isLeadAlreadyExitsOnAltNumber ?? this.isLeadAlreadyExitsOnAltNumber,
      contactNumber: contactNumber ?? this.contactNumber,
      altContactNumber: altContactNumber ?? this.altContactNumber,
      referralContact: referralContact ?? this.referralContact,
      existingLeadId: existingLeadId ?? this.existingLeadId,
      currencies: currencies ?? this.currencies,
      selectedCurrency: selectedCurrency ?? this.selectedCurrency,
      defaultCountryCode: defaultCountryCode ?? this.defaultCountryCode,
      showDialogProgress: showDialogProgress ?? this.showDialogProgress,
      channelPartners: channelPartners ?? this.channelPartners,
      campaignNames: campaignNames ?? this.campaignNames,
      selectedCampaignNames: selectedCampaignNames ?? this.selectedCampaignNames,
      selectedChannelPartners: selectedChannelPartners ?? this.selectedChannelPartners,
      furnished: furnished ?? this.furnished,
      offerType: offerType ?? this.offerType,
      floors: floors ?? this.floors,
      isDuplicateLeadBottomSheetVisible: isDuplicateLeadBottomSheetVisible ?? this.isDuplicateLeadBottomSheetVisible,
      isDuplicateLeadBottomSheetVisibleForAltNumber: isDuplicateLeadBottomSheetVisibleForAltNumber ?? this.isDuplicateLeadBottomSheetVisibleForAltNumber,
      primaryOrSecondary: primaryOrSecondary ?? this.primaryOrSecondary,
      nationality: nationality ?? this.nationality,
      selectedCountry: selectedCountry ?? this.selectedCountry,
      purposes: purposes ?? this.purposes,
      selectedPurpose: selectedPurpose ?? this.selectedPurpose,
      isAssignedLoggedInUser: isAssignedLoggedInUser ?? this.isAssignedLoggedInUser,
      possessionTypeSelectableItems: possessionTypeSelectableItems ?? this.possessionTypeSelectableItems,
      possessionTypeSelectedItem: possessionTypeSelectedItem ?? this.possessionTypeSelectedItem,
      isPossessionDateCustomSelected: isPossessionDateCustomSelected ?? this.isPossessionDateCustomSelected,
      executiveContact: executiveContact ?? this.executiveContact,
    );
  }
}
