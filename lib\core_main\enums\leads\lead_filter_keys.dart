enum LeadFilterKey {
  source("Source"),
  subSource("Sub-Source"),
  budget("Budget"),
  category("Categories"),
  leadVisibility("Showing Leads"),
  assignedTo("Assigned To"),
  secondaryOwnerFrom("Secondary Assigned From"),
  doneBy("Assignment Done By"),
  projects("Projects"),
  properties("Properties"),
  enquiredFor("Enquired For"),
  dateRange("Date Range"),
  meetingStatus("Meeting / Site visit status"),
  propertyType("Property Type"),
  propertySubType("Property Sub-Type"),
  noOfBHK("BHK"),
  bHKTypes("BHK Type"),
  locations("Locations"),
  city("City"),
  state("State"),
  zone("Zone"),
  locality("Locality"),
  latitude("Latitude"),
  longitude("Longitude"),
  radius("Radius"),
  agencyName("Agency Names"),
  secondaryOwner("Secondary Owner"),
  bookedBy("Booked By"),
  siteVisitOrMeetingDoneBy("Site Visit Or Meeting Done By"),
  assignedFrom("Assigned From"),
  customFlags("Tags"),
  status("Status"),
  subStatus("Sub Status"),
  withTeam("With Team"),
  withHistory("With History"),
  community("Community"),
  subCommunity("Sub Community"),
  towerName("Tower Name"),
  country("Country"),
  pinCode("Pin Code"),
  beds("Beds"),
  baths("Baths"),
  floors("Floors"),
  offerTypes("Offer Types"),
  furnished("Furnished"),
  dataConverted("Data Converted"),
  qualifiedBy("Qualified By"),
  createdBy("Created By"),
  lastModifiedBy("LastModified By"),
  deletedBy("Deleted By"),
  restoredBy("Restored By"),
  referralName("Referral Name"),
  referralEmail("Referral Email"),
  referralPhoneNo("Referral PhoneNo"),
  carpetArea("Carpet Area"),
  buildUpArea("Built-Up Area"),
  salableArea("Saleable Area"),
  netArea("Net Area"),
  propertyArea("Property Area"),
  unTouched("Un Touched"),
  channelPartnerName("Channel Partner Name"),
  excelSheet("Excel Sheet"),
  designation("Designation"),
  profession("Profession"),
  facebookProperties("Facebook Properties"),
  facebookPropertyValues("Facebook Property Values"),
  unitNumberOrName("Unit Number/Name"),
  clusterName("Cluster Name"),
  nationality("Nationality"),
  campaigns("Campaigns"),
  minBudget("Min Budget"),
  maxBudget("Max Budget"),
  showParentLead("Show Parent Lead"),
  showChildCount("Show Child Count"),
  showUniqueLead("Show Unique Lead"),
  purpose("Purpose"),
  originalOwner("Original Owner");

  final String description;

  const LeadFilterKey(this.description);
}
