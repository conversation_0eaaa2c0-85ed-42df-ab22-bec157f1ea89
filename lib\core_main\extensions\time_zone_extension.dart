import 'package:intl/intl.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';

extension TimeZoneExtensions on DateTime? {
  /// Converts the datetime to the user's time zone
  DateTime? toUserTimeZone() {
    final timeZoneInfo = getIt<UsersDataRepository>().getLoggedInUser()?.timeZoneInfo;

    if (this == null || timeZoneInfo == null) {
      return null;
    }

    if (timeZoneInfo.baseUTcOffset != null && timeZoneInfo.timeZoneDisplay != null && timeZoneInfo.timeZoneId != null && timeZoneInfo.timeZoneName != null) {
      final utcTime = this?.toUtc();
      final match = RegExp(r'UTC([+\-])(\d{2}):(\d{2})').firstMatch(timeZoneInfo.timeZoneDisplay ?? "");

      if (match != null) {
        final operatorSign = match.group(1)!;
        final hoursOffset = int.parse(match.group(2)!);
        final minutesOffset = int.parse(match.group(3)!);
        final totalOffset = Duration(hours: hoursOffset, minutes: minutesOffset);

        if (operatorSign == "+") {
          return utcTime?.add(totalOffset);
        } else if (operatorSign == "-") {
          return utcTime?.subtract(totalOffset);
        }
      }
    }

    return this?.toLocal();
  }

  /// Formats the datetime in the user's time zone with optional format
  String? formatInUserTimeZone({String? format}) {
    final convertedDateTime = toUserTimeZone();

    if (convertedDateTime == null) return null;

    try {
      return DateFormat(format ?? 'yyyy-MM-dd HH:mm:ss').format(convertedDateTime);
    } catch (e) {
      return null;
    }
  }

  /// Converts datetime to a standard ISO 8601 format in user's time zone
  String? toIso8601StringInUserTimeZone() {
    final convertedDateTime = toUserTimeZone();

    if (convertedDateTime == null) return null;

    return convertedDateTime.toIso8601String();
  }

  /// Converts the DateTime to the user's time zone and adjusts it, similar to the `GetBasedOnTimeZone` method in C#.
  DateTime? getBasedOnTimeZone() {
    final user = getIt<UsersDataRepository>().getLoggedInUser()?.timeZoneInfo;

    if (this == null || user == null) {
      return null;
    }

    if (user.baseUTcOffset != null && user.timeZoneDisplay != null && user.timeZoneId != null && user.timeZoneName != null) {
      final utcTime = this ?? DateTime.utc(1970, 1, 1);
      final match = RegExp(r'UTC([+\-])(\d{2}):(\d{2})').firstMatch(user.timeZoneDisplay ?? "");

      if (match != null) {
        final operatorSign = match.group(1)!;
        final hoursOffset = int.parse(match.group(2)!);
        final minutesOffset = int.parse(match.group(3)!);
        final totalOffset = Duration(hours: hoursOffset, minutes: minutesOffset);

        final newDate = operatorSign != "+" ? utcTime.add(totalOffset) : utcTime.subtract(totalOffset);
        return newDate.toUtcFormat();
      }
    }

    return this?.toUtc();
  }
}
