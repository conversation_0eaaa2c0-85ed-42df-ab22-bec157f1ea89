part of 'matching_properties_bloc.dart';

@immutable
class MatchingPropertiesState {
  final PageState pageState;
  final String? errorMessage;
  final List<ItemMatchingPropertiesModel> matchingProperties;
  final List<ItemMatchingPropertiesModel> selectedMatchingProperties;
  final List<String>? selectedPropertyIds;
  final String? selectedPropertyTitle;
  final GetLeadEntity? leadEntity;
  final int totalMatchingProperties;
  final bool isLeadDetailsVisible;
  final List<SelectableItem<double>> selectableRadiusInKms;
  final SelectableItem<double>? selectedRadius;
  final LeadInfoState? leadInfoState;
  final bool isLoadingMore;
  final bool isAllPropertiesSelected;
  final GlobalSettingModel? globalSettingModel;

  const MatchingPropertiesState({
    this.pageState = PageState.initial,
    this.errorMessage,
    this.matchingProperties = const [],
    this.selectedMatchingProperties = const [],
    this.leadEntity,
    this.totalMatchingProperties = 0,
    this.isLeadDetailsVisible = false,
    this.selectableRadiusInKms = const [],
    this.selectedRadius,
    this.selectedPropertyTitle,
    this.selectedPropertyIds,
    this.leadInfoState,
    this.isLoadingMore = false,
    this.isAllPropertiesSelected = false,
    this.globalSettingModel,
  });

  MatchingPropertiesState copyWith({
    PageState? pageState,
    String? errorMessage,
    List<ItemMatchingPropertiesModel>? matchingProperties,
    List<ItemMatchingPropertiesModel>? selectedMatchingProperties,
    GetLeadEntity? leadEntity,
    int? totalMatchingProperties,
    bool? isLeadDetailsVisible,
    List<SelectableItem<double>>? selectableRadiusInKms,
    SelectableItem<double>? selectedRadius,
    List<String>? selectedPropertyIds,
    String? selectedPropertyTitle,
    LeadInfoState? leadInfoState,
    bool? isLoadingMore,
    bool? isAllPropertiesSelected,
    bool resetSelectedRadius = false,
    GlobalSettingModel? globalSettingModel,
  }) {
    return MatchingPropertiesState(
      pageState: pageState ?? this.pageState,
      errorMessage: errorMessage,
      matchingProperties: matchingProperties ?? this.matchingProperties,
      selectedMatchingProperties: selectedMatchingProperties ?? this.selectedMatchingProperties,
      leadEntity: leadEntity ?? this.leadEntity,
      totalMatchingProperties: totalMatchingProperties ?? this.totalMatchingProperties,
      isLeadDetailsVisible: isLeadDetailsVisible ?? this.isLeadDetailsVisible,
      selectableRadiusInKms: selectableRadiusInKms ?? this.selectableRadiusInKms,
      selectedRadius: resetSelectedRadius ? null : (selectedRadius ?? this.selectedRadius),
      selectedPropertyIds: selectedPropertyIds ?? this.selectedPropertyIds,
      selectedPropertyTitle: selectedPropertyTitle ?? this.selectedPropertyTitle,
      leadInfoState: leadInfoState ?? this.leadInfoState,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      isAllPropertiesSelected: isAllPropertiesSelected ?? this.isAllPropertiesSelected,
      globalSettingModel: globalSettingModel ?? this.globalSettingModel,
    );
  }
}
