import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/constants/app_analytics_constants.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/repository/global_setting_repository.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/contact_type_enum.dart';
import 'package:leadrat/core_main/enums/common/date_range.dart';
import 'package:leadrat/core_main/enums/common/no_of_beds.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/leads/lead_category_type.dart';
import 'package:leadrat/core_main/enums/leads/lead_filter_keys.dart';
import 'package:leadrat/core_main/enums/leads/lead_visibility.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/features/lead/data/models/lead_card_view_model.dart';
import 'package:leadrat/features/lead/data/models/lead_filter_model.dart';
import 'package:leadrat/features/lead/domain/entities/get_all_lead_entity.dart';
import 'package:leadrat/features/lead/domain/entities/lead_appointment_entity.dart';
import 'package:leadrat/features/lead/domain/repository/leads_card_view_repository.dart';
import 'package:leadrat/features/lead/domain/repository/leads_repository.dart';
import 'package:leadrat/features/lead/domain/usecase/get_all_initial_custom_leads_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_all_initial_leads_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_leads_communication_use_case.dart';
import 'package:leadrat/features/lead/presentation/item/item_lead_category_model.dart';
import 'package:leadrat/features/lead/presentation/item/item_lead_model.dart';

import '../../../../../core_main/di/injection_container.dart';
import '../../../data/models/custom_filter_model.dart';
import 'manage_leads_pagination_state.dart';

part 'manage_leads_event.dart';

part 'manage_leads_state.dart';

class ManageLeadsBloc extends Bloc<ManageLeadsEvent, ManageLeadsState> {
  final GetAllInitialCustomLeadsUseCase _getAllInitialCustomLeadsUseCase;
  final GetAllInitialLeadsUseCase _getAllInitialLeadsUseCase;
  final GetLeadsCommunicationUseCase _getLeadsCommunicationUseCase;
  final GlobalSettingRepository _globalSettingRepository;
  final AppAnalysisRepository _appAnalysisRepository;
  Map<LeadCategoryType, ManageLeadsPaginationState> categoryPaginationState = {};
  Map<String, ManageLeadsPaginationState> customCategoryPaginationState = {};
  List<ItemSimpleModel<LeadFilterKey>> selectedFilters = [];
  late LeadFilterModel leadFilter;
  GlobalSettingModel? globalSettings;
  List<LeadCardViewModel>? leadsCategoryOrders = [];
  List<CustomFilterModel>? customLeadStatus = [];
  final LeadsCardViewRepository _leadCardViewRepository;

  ManageLeadsBloc(
    this._getAllInitialLeadsUseCase,
    this._getLeadsCommunicationUseCase,
    this._globalSettingRepository,
    this._getAllInitialCustomLeadsUseCase,
    this._appAnalysisRepository,
    this._leadCardViewRepository,
  ) : super(const ManageLeadsState()) {
    on<ManageLeadsInitialEvent>(_onManageLeadsInitial);
    on<GetAllInitialLeadsEvent>(_onGetAllInitialLeads);
    on<GetAllInitialCustomLeadsEvent>(_onGetAllInitialCustomLeads);
    on<SetupLeadsWithCommunicationEvent>(_onSetupLeadsWithCommunication);
    on<SetupCustomLeadsWithCommunicationEvent>(_onSetupCustomLeadsWithCommunication);
    on<LoadMoreLeadsEvent>(_onLoadMoreLeads);
    on<LoadMoreCustomLeadsEvent>(_onLoadMoreCustomLeads);
    on<RefreshLeadsEvent>(_onRefreshLeads);
    on<RefreshCustomStatusLeadsEvent>(_onRefreshCustomStatusLeads);
    on<RemoveFilterEvent>(_onRemoveFilter);
  }

  FutureOr<void> _onManageLeadsInitial(ManageLeadsInitialEvent event, Emitter<ManageLeadsState> emit) async {
    emit(state.copyWith(pageState: PageState.loading, leadsCategory: [], leadsCategoryCustom: [], totalLeads: null, errorMessage: null, isCustomStatusEnabled: false, isCustomLeadFormEnabled: false, categoryPaginationState: {}, customCategoryPaginationState: {}, selectedFilters: selectedFilters));
    globalSettings = await _globalSettingRepository.getGlobalSettings();
    if (!(globalSettings?.isCustomStatusEnabled ?? false)) {
      leadsCategoryOrders = await _leadCardViewRepository.getMobileCardCategoriesOrders();
      final defaultCategories = leadsCategoryOrders?.where((j) => j.isDefault ?? false).map((i) => i.categiryType).whereType<LeadCategoryType>().toList();
      leadFilter = event.leadFilter ??
          LeadFilterModel(
            defaultCategoryTypes: defaultCategories,
            filterTypes: defaultCategories,
            leadVisibility: LeadVisibility.selfWithReportee,
            defaultFilterTypes: defaultCategories,
          );
    } else {
      var customStatus = await getIt<LeadsRepository>().getCustomStatusFilter();
      customStatus.fold((l) => null, (customFilter) {
        customLeadStatus = customFilter;
      });
      List<CustomFilterModel> defaultCategories = [];

      if (customLeadStatus?.isNotEmpty ?? false) {
        defaultCategories = customLeadStatus!.where((i) => i.isDefault ?? false).toList()..sort((a, b) => (a.orderRank ?? 0).compareTo(b.orderRank ?? 0));
      }
      leadFilter = event.leadFilter ??
          LeadFilterModel(
            customFilterIds: defaultCategories.where((i) => i.id?.isNotEmpty ?? false).map((toElement) => toElement.id ?? '').toList() ?? [],
            leadVisibility: LeadVisibility.selfWithReportee,
          );
    }

    categoryPaginationState = {};
    customCategoryPaginationState = {};
    selectedFilters = _initSelectedFilters();
    emit(state.copyWith(pageState: PageState.loading, leadsCategory: [], leadsCategoryCustom: [], totalLeads: null, errorMessage: null, isCustomStatusEnabled: globalSettings?.isCustomStatusEnabled, isCustomLeadFormEnabled: globalSettings?.isCustomLeadFormEnabled, categoryPaginationState: {}, customCategoryPaginationState: {}, selectedFilters: selectedFilters));
    add((globalSettings?.isCustomStatusEnabled ?? false) ? GetAllInitialCustomLeadsEvent(getAllInitialCustomLeadsParams: null) : GetAllInitialLeadsEvent(getAllInitialLeadsParams: null));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileManageLeadsPageManageLeadsView);
  }

  FutureOr<void> _onGetAllInitialLeads(GetAllInitialLeadsEvent event, Emitter<ManageLeadsState> emit) async {
    final initialLeads = await _getAllInitialLeadsUseCase(event.getAllInitialLeadsParams ?? GetAllInitialLeadsParams(leadFilterModel: leadFilter, pageSize: 10, pageNumber: 1));
    try {
      initialLeads.fold(
        (failure) {
          emit(state.copyWith(pageState: PageState.failure));
        },
        (result) {
          List<ItemLeadCategoryModel> leadsCategory = [];
          Iterable<GetAllLeadEntity>? getAllLeads = [];
          final getAllLeadsWrapperEntity = result;

          if ((result?.totalLeadsCount ?? 0) == 0 || (result?.leads?.isEmpty ?? false)) {
            emit(state.copyWith(pageState: PageState.success, totalLeads: getAllLeadsWrapperEntity?.totalLeadsCount));
          } else if (getAllLeadsWrapperEntity != null && (getAllLeadsWrapperEntity.leads?.isNotEmpty ?? false)) {
            getAllLeadsWrapperEntity.leads?.forEach((key, value) {
              final totalLeadCount = (value.totalCount != null && value.totalCount! > 0) ? "(${value.totalCount})" : "";
              final title = "${value.leadFilter?.description ?? ""} $totalLeadCount";
              getAllLeads = value.leads;
              final leads = value.leads?.map((lead) {
                return ItemLeadModel(lead: lead, isDualOwnership: globalSettings?.isDualOwnershipEnabled ?? false, isCustomStatusEnabled: globalSettings?.isCustomStatusEnabled ?? false);
              }).toList();

              final existingCategoryIndex = state.leadsCategory?.indexWhere((category) => category.leadCategoryType == value.leadFilter);
              if (existingCategoryIndex != null && existingCategoryIndex != -1 && value.leadFilter != null) {
                _updatedExistingLeads(existingCategoryIndex: existingCategoryIndex, leadFilter: value.leadFilter!, updatedLeads: leads, state: state, emit: emit, title: title);
                add(SetupLeadsWithCommunicationEvent(getAllLeads?.toList()));
              } else {
                leadsCategory.add(ItemLeadCategoryModel(leads: leads, title: title, leadCategoryType: value.leadFilter, totalLeadCount: value.totalCount ?? 0));
                add(SetupLeadsWithCommunicationEvent(getAllLeads?.toList()));
              }
            });
          }
          if (leadsCategory.isNotEmpty) {
            final defaultCategoryTypes = leadsCategoryOrders?.where((i) => i.isDefault ?? false).map((item) => item.categiryType).whereType<LeadCategoryType>().toList() ?? [];
            leadsCategory.sort((a, b) {
              int indexA = defaultCategoryTypes.indexOf(a.leadCategoryType!);
              int indexB = defaultCategoryTypes.indexOf(b.leadCategoryType!);
              return indexA.compareTo(indexB);
            });
            emit(state.copyWith(pageState: PageState.success, leadsCategory: leadsCategory, totalLeads: getAllLeadsWrapperEntity?.totalLeadsCount));
          }
        },
      );
    } catch (exception, stackTrace) {
      exception.logException(stackTrace);
    }
  }

  FutureOr<void> _onGetAllInitialCustomLeads(GetAllInitialCustomLeadsEvent event, Emitter<ManageLeadsState> emit) async {
    final initialLeads = await _getAllInitialCustomLeadsUseCase(event.getAllInitialCustomLeadsParams ?? GetAllInitialLeadsCustomParams(pageSize: 10, pageNumber: 1, leadFilterModel: leadFilter));
    try {
      initialLeads.fold(
        (failure) => emit(state.copyWith(pageState: PageState.failure)),
        (result) {
          List<ItemLeadCategoryCustomModel> leadsCategoryCustom = [];
          Iterable<GetAllLeadEntity>? getAllLeads = [];
          final getAllLeadsWrapperEntity = result;

          if ((result?.totalLeadsCount ?? 0) == 0 || (result?.leads?.isEmpty ?? false)) {
            emit(state.copyWith(pageState: PageState.success, totalLeads: getAllLeadsWrapperEntity?.totalLeadsCount));
          } else if (getAllLeadsWrapperEntity != null && (getAllLeadsWrapperEntity.leads?.isNotEmpty ?? false)) {
            getAllLeadsWrapperEntity.leads?.forEach((key, value) {
              final totalLeadCount = (value.totalCount != null && value.totalCount! > 0) ? "(${value.totalCount})" : "";
              final title = "${value.leadFilter ?? ""} $totalLeadCount";
              getAllLeads = value.leads;
              final leads = value.leads?.map((lead) {
                return ItemLeadModel(lead: lead, isDualOwnership: globalSettings?.isDualOwnershipEnabled ?? false, isCustomStatusEnabled: globalSettings?.isCustomStatusEnabled ?? false);
              }).toList();

              final existingCategoryIndex = state.leadsCategoryCustom?.indexWhere((category) => category.filterId == value.filterId);
              if (existingCategoryIndex != null && existingCategoryIndex != -1 && value.leadFilter != null) {
                _updatedExistingCustomStatusLeads(existingCategoryIndex: existingCategoryIndex, filterId: value.filterId!, updatedLeads: leads, state: state, emit: emit, title: title);
                add(SetupCustomLeadsWithCommunicationEvent(getAllLeads?.toList()));
              } else {
                leadsCategoryCustom.add(ItemLeadCategoryCustomModel(leads: leads, title: title, filterId: value.filterId ?? "", totalLeadCount: value.totalCount ?? 0));
                add(SetupCustomLeadsWithCommunicationEvent(getAllLeads?.toList()));
              }
            });
          }
          if (leadsCategoryCustom.isNotEmpty) {
            emit(state.copyWith(pageState: PageState.success, leadsCategoryCustom: leadsCategoryCustom, totalLeads: getAllLeadsWrapperEntity?.totalLeadsCount));
          }
        },
      );
    } catch (exception, stackTrace) {
      exception.logException(stackTrace);
    }
  }

  FutureOr<void> _onLoadMoreLeads(LoadMoreLeadsEvent event, Emitter<ManageLeadsState> emit) async {
    final categoryType = event.leadCategoryType;
    if (categoryType == null) return;
    if (event.totalLeadsInitiated >= event.totalCategoryLeadCount) return;
    final paginationState = getPaginationStateForCategory(categoryType);
    if (paginationState.isFetching) return;
    categoryPaginationState[categoryType] = paginationState.copyWith(isFetching: true);
    emit(state.copyWith(categoryPaginationState: categoryPaginationState));
    final nextPageNumber = paginationState.pageNumber + 1;
    add(GetAllInitialLeadsEvent(
        getAllInitialLeadsParams: GetAllInitialLeadsParams(
      pageNumber: nextPageNumber,
      pageSize: 10,
      leadFilterModel: leadFilter.copyWith(filterTypes: [categoryType]),
    )));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileManageLeadsPageCategoryDownScrollScroll);
  }

  FutureOr<void> _onLoadMoreCustomLeads(LoadMoreCustomLeadsEvent event, Emitter<ManageLeadsState> emit) async {
    final customFilterId = event.customFilterId;
    if (customFilterId == null) return;
    if (event.totalLeadsInitiated >= event.totalCategoryLeadCount) return;
    final paginationState = getPaginationStateForCategory(customFilterId);
    if (paginationState.isFetching) return;
    customCategoryPaginationState[customFilterId] = paginationState.copyWith(isFetching: true);
    emit(state.copyWith(customCategoryPaginationState: customCategoryPaginationState));
    final nextPageNumber = paginationState.pageNumber + 1;
    add(GetAllInitialCustomLeadsEvent(
        getAllInitialCustomLeadsParams: GetAllInitialLeadsCustomParams(
      pageSize: 10,
      pageNumber: nextPageNumber,
      leadFilterModel: leadFilter.copyWith(customFilterIds: [customFilterId]),
    )));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileManageLeadsPageCategoryDownScrollScroll);
  }

  FutureOr<void> _onRefreshLeads(RefreshLeadsEvent event, Emitter<ManageLeadsState> emit) async {
    final categoryType = event.leadCategoryType;
    if (categoryType == null) return;
    final paginationState = getPaginationStateForCategory(categoryType);
    if (paginationState.isRefreshing) return;
    categoryPaginationState[categoryType] = paginationState.copyWith(isRefreshing: true);
    if (event.leadCategoryType != null) {
      final tempFilterModel = leadFilter.copyWith(filterTypes: [event.leadCategoryType!]);
      add(GetAllInitialLeadsEvent(getAllInitialLeadsParams: GetAllInitialLeadsParams(pageNumber: 1, pageSize: 10, leadFilterModel: tempFilterModel)));
    }
  }

  FutureOr<void> _onRefreshCustomStatusLeads(RefreshCustomStatusLeadsEvent event, Emitter<ManageLeadsState> emit) async {
    final customFilterId = event.customFilterId;
    if (customFilterId == null) return;
    final paginationState = getPaginationStateForCategory(customFilterId);
    if (paginationState.isRefreshing) return;
    customCategoryPaginationState[customFilterId] = paginationState.copyWith(isRefreshing: true);
    if (event.customFilterId != null) {
      final tempFilterModel = leadFilter.copyWith(customFilterIds: [event.customFilterId!]);
      add(GetAllInitialCustomLeadsEvent(getAllInitialCustomLeadsParams: GetAllInitialLeadsCustomParams(pageNumber: 1, pageSize: 10, leadFilterModel: tempFilterModel)));
    }
  }

  FutureOr<void> _onSetupLeadsWithCommunication(SetupLeadsWithCommunicationEvent event, Emitter<ManageLeadsState> emit) async {
    List<String> leadIds = [];
    event.getAllLeads?.forEach((lead) => leadIds.add(lead.id ?? ''));

    final leadCommunications = await _getLeadsCommunicationUseCase(leadIds);
    leadCommunications.fold(
      (failure) => emit(state.copyWith(errorMessage: "Failed to load leads communication")),
      (leadCommunicationResult) {
        if (leadCommunicationResult != null) {
          final updatedLeadsCategory = state.leadsCategory?.map((leadCategory) {
            final updatedLeadEntities = leadCategory.leads?.map((getAllLeadEntity) {
              if (leadCommunicationResult.containsKey(getAllLeadEntity.lead.id)) {
                final updatedLead = getAllLeadEntity.lead.copyWith(contactRecords: leadCommunicationResult[getAllLeadEntity.lead.id]);
                var updatedItemLeadModel = getAllLeadEntity.copyWith(lead: updatedLead);
                final contactRecord = updatedLead.contactRecords?[ContactType.call.description];
                if (contactRecord != null && contactRecord > 0) {
                  if (!(updatedItemLeadModel.leadAppointments.any((element) => element.isCall))) {
                    updatedItemLeadModel.leadAppointments.add(LeadAppointmentEntity(title: "Calls - $contactRecord", itemColor: const Color(0xFF03a9f3), isCall: true));
                  }
                }
                return updatedItemLeadModel;
              }
              return getAllLeadEntity;
            }).toList();

            return leadCategory.copyWith(leads: updatedLeadEntities);
          }).toList();

          emit(state.copyWith(leadsCategory: updatedLeadsCategory));
        }
      },
    );
  }

  FutureOr<void> _onSetupCustomLeadsWithCommunication(SetupCustomLeadsWithCommunicationEvent event, Emitter<ManageLeadsState> emit) async {
    List<String> leadIds = [];
    event.getAllLeads?.forEach((lead) => leadIds.add(lead.id ?? ''));

    final leadCommunications = await _getLeadsCommunicationUseCase(leadIds);
    leadCommunications.fold(
      (failure) => emit(state.copyWith(errorMessage: "Failed to load leads communication")),
      (leadCommunicationResult) {
        if (leadCommunicationResult != null) {
          final updatedLeadsCategory = state.leadsCategoryCustom?.map((leadCategory) {
            final updatedLeadEntities = leadCategory.leads?.map((getAllLeadEntity) {
              if (leadCommunicationResult.containsKey(getAllLeadEntity.lead.id)) {
                final updatedLead = getAllLeadEntity.lead.copyWith(contactRecords: leadCommunicationResult[getAllLeadEntity.lead.id]);
                var updatedItemLeadModel = getAllLeadEntity.copyWith(lead: updatedLead);
                final contactRecord = updatedLead.contactRecords?[ContactType.call.description];
                if (contactRecord != null && contactRecord > 0) {
                  if (!(updatedItemLeadModel.leadAppointments.any((element) => element.isCall))) {
                    updatedItemLeadModel.leadAppointments.add(LeadAppointmentEntity(title: "Calls - $contactRecord", itemColor: const Color(0xFF03a9f3), isCall: true));
                  }
                }
                return updatedItemLeadModel;
              }
              return getAllLeadEntity;
            }).toList();

            return leadCategory.copyWith(leads: updatedLeadEntities);
          }).toList();

          emit(state.copyWith(leadsCategoryCustom: updatedLeadsCategory));
        }
      },
    );
  }

  void _updatedExistingLeads({
    required ManageLeadsState state,
    required int existingCategoryIndex,
    required LeadCategoryType leadFilter,
    required List<ItemLeadModel>? updatedLeads,
    required Emitter<ManageLeadsState> emit,
    String? title,
  }) {
    final paginationState = getPaginationStateForCategory(leadFilter);
    if (paginationState.isRefreshing) {
      /// Clearing existing leads from the lead category
      final updatedLeadsCategory = state.leadsCategory?.map((leadCategory) {
        if (leadCategory.leadCategoryType == leadFilter) {
          return leadCategory.copyWith(leads: updatedLeads, title: title ?? leadCategory.title);
        }
        return leadCategory;
      }).toList();
      categoryPaginationState[leadFilter] = paginationState.copyWith(pageNumber: 1, isFetching: false, isRefreshing: false);
      emit(state.copyWith(pageState: PageState.success, leadsCategory: updatedLeadsCategory));
    } else {
      final updatedLeadsCategory = state.leadsCategory?.map((e) => e.leadCategoryType == leadFilter ? e.copyWith(leads: paginationState.isFetching ? _mergeUniqueLeadsById([...?e.leads], updatedLeads) : e.leads, title: title ?? e.title) : e).toList();
      categoryPaginationState[leadFilter] = paginationState.copyWith(isFetching: false, isRefreshing: false, pageNumber: ++paginationState.pageNumber);
      emit(state.copyWith(pageState: PageState.success, leadsCategory: updatedLeadsCategory));
    }
  }

  void _updatedExistingCustomStatusLeads({
    required ManageLeadsState state,
    required int existingCategoryIndex,
    required String filterId,
    required List<ItemLeadModel>? updatedLeads,
    required Emitter<ManageLeadsState> emit,
    String? title,
  }) {
    final paginationState = getPaginationStateForCategory(filterId);
    if (paginationState.isRefreshing) {
      /// Clearing existing leads from the lead category
      final updatedLeadsCategory = state.leadsCategoryCustom?.map((leadCategory) {
        if (leadCategory.filterId == filterId) {
          return leadCategory.copyWith(leads: updatedLeads, title: title ?? leadCategory.title);
        }
        return leadCategory;
      }).toList();
      customCategoryPaginationState[filterId] = paginationState.copyWith(pageNumber: 1, isFetching: false, isRefreshing: false);
      emit(state.copyWith(pageState: PageState.success, leadsCategoryCustom: updatedLeadsCategory));
    } else {
      final updatedLeadsCategory = state.leadsCategoryCustom?.map((e) {
        return e.filterId == filterId ? e.copyWith(leads: [...?e.leads, ...?updatedLeads], title: title ?? e.title) : e;
      }).toList();
      customCategoryPaginationState[filterId] = paginationState.copyWith(isFetching: false, isRefreshing: false, pageNumber: ++paginationState.pageNumber);
      emit(state.copyWith(pageState: PageState.success, leadsCategoryCustom: updatedLeadsCategory));
    }
  }

  ManageLeadsPaginationState getPaginationStateForCategory(dynamic key) {
    if (globalSettings?.isCustomStatusEnabled ?? false) {
      if (!customCategoryPaginationState.containsKey(key)) {
        customCategoryPaginationState[key as String] = ManageLeadsPaginationState();
      }
      return customCategoryPaginationState[key]!;
    } else {
      if (!categoryPaginationState.containsKey(key)) {
        categoryPaginationState[key as LeadCategoryType] = ManageLeadsPaginationState();
      }
      return categoryPaginationState[key]!;
    }
  }

  List<ItemSimpleModel<LeadFilterKey>> _initSelectedFilters() {
    final selectedLeadFilterModel = leadFilter;
    List<ItemSimpleModel<LeadFilterKey>> selectedFilters = [];
    if (selectedLeadFilterModel.leadVisibility != null && selectedLeadFilterModel.leadVisibility != LeadVisibility.selfWithReportee) {
      final selectedLeadVisibility = selectedLeadFilterModel.leadVisibility?.description;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.leadVisibility.description, description: ' - ${selectedLeadVisibility ?? ''}', value: LeadFilterKey.leadVisibility));
    }
    if (selectedLeadFilterModel.customFlags?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.customFlags?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.customFlags.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.customFlags));
    }
    if (selectedLeadFilterModel.sources?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.sources?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.source.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.source));
    }
    if (selectedLeadFilterModel.subSources?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.subSources?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.subSource.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.subSource));
    }
    if (selectedLeadFilterModel.meetingOrVisitStatuses?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.meetingOrVisitStatuses?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.meetingStatus.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.meetingStatus));
    }
    if (selectedLeadFilterModel.enquiredFor?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.enquiredFor?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.enquiredFor.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.enquiredFor));
    }
    if (selectedLeadFilterModel.agencyNames?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.agencyNames?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.agencyName.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.agencyName));
    }
    if (selectedLeadFilterModel.campaignNames?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.campaignNames?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.campaigns.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.campaigns));
    }
    if (selectedLeadFilterModel.dateRange != null || selectedLeadFilterModel.possessionTypeDateRange != null) {
      if (selectedLeadFilterModel.dateRange != null) {
        final isCustomDateRange = (selectedLeadFilterModel.dateRange?.description == DateRange.customDate.description);
        final dateFormat = DateFormat('dd-MM-yyyy');
        var dateRange = isCustomDateRange ? "from: ${dateFormat.format(DateTime.parse(selectedLeadFilterModel.fromDate!).toLocal())} - to: ${dateFormat.format(DateTime.parse(selectedLeadFilterModel.toDate!).toLocal())}" : selectedLeadFilterModel.dateRange?.description ?? "";
        selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: " $dateRange", value: LeadFilterKey.dateRange));
      } else if (selectedLeadFilterModel.possessionTypeDateRange != null) {
        final isCustomDateRange = (selectedLeadFilterModel.possessionTypeDateRange?.description == PossessionType.customDate.description);
        final dateFormat = DateFormat('MMM-yyyy');
        var dateRange = isCustomDateRange ? "Possession: from: ${dateFormat.format(DateTime.parse(selectedLeadFilterModel.fromDate!).toLocal())} - to: ${dateFormat.format(DateTime.parse(selectedLeadFilterModel.toDate!).toLocal())}" : 'Possession Type: ${selectedLeadFilterModel.possessionTypeDateRange?.description ?? ""}';
        selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: " $dateRange", value: LeadFilterKey.dateRange));
      }
    }
    if (selectedLeadFilterModel.noOfBHKs?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.noOfBHKs?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.noOfBHK.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.noOfBHK));
    }
    if (selectedLeadFilterModel.furnished?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.furnished?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.furnished.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.furnished));
    }
    if (selectedLeadFilterModel.beds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.beds?.length ?? 0;
      final desc = (selectedLeadFilterModel.beds ?? []).map((b) => Beds.values.firstWhere((e) => e.value == b).description).join(', ');

      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.beds.description, description: selectedFilterCount >= 1 ? ':$desc count(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.beds));
    }
    if (selectedLeadFilterModel.baths?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.baths?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.baths.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.baths));
    }
    if (selectedLeadFilterModel.floors?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.floors?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.floors.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.floors));
    }
    if (selectedLeadFilterModel.offerTypes?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.offerTypes?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.offerTypes.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.offerTypes));
    }

    if (selectedLeadFilterModel.bhkTypes?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.bhkTypes?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.bHKTypes.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.bHKTypes));
    }
    if (selectedLeadFilterModel.locations?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.locations?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.locations.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.locations));
    }
    if (selectedLeadFilterModel.communities?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.communities?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.community.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.community));
    }
    if (selectedLeadFilterModel.subCommunities?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.subCommunities?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.subCommunity.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.subCommunity));
    }
    if (selectedLeadFilterModel.towerNames?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.towerNames?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.towerName.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.towerName));
    }
    if (selectedLeadFilterModel.countries?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.countries?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.country.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.country));
    }
    if (selectedLeadFilterModel.pincode?.isNotEmpty ?? false) {
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.pinCode.description, description: selectedLeadFilterModel.pincode, value: LeadFilterKey.pinCode));
    }

    if (selectedLeadFilterModel.builtUpArea != null) {
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.buildUpArea.description, description: selectedLeadFilterModel.builtUpArea.doubleToWord(), value: LeadFilterKey.buildUpArea));
    }
    if (selectedLeadFilterModel.saleableArea != null) {
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.salableArea.description, description: selectedLeadFilterModel.saleableArea.doubleToWord(), value: LeadFilterKey.salableArea));
    }
    if (selectedLeadFilterModel.referralName?.isNotEmpty ?? false) {
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.referralName.description, description: selectedLeadFilterModel.referralName, value: LeadFilterKey.referralName));
    }
    if (selectedLeadFilterModel.referralEmail?.isNotEmpty ?? false) {
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.referralEmail.description, description: selectedLeadFilterModel.referralEmail, value: LeadFilterKey.referralEmail));
    }
    if (selectedLeadFilterModel.referralNumber?.isNotEmpty ?? false) {
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.referralPhoneNo.description, description: selectedLeadFilterModel.referralNumber, value: LeadFilterKey.referralPhoneNo));
    }
    if (selectedLeadFilterModel.createdByIds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.createdByIds?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.createdBy.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.createdBy));
    }
    if (selectedLeadFilterModel.lastModifiedByIds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.lastModifiedByIds?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.lastModifiedBy.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.lastModifiedBy));
    }
    if (selectedLeadFilterModel.archivedByIds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.archivedByIds?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.deletedBy.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.deletedBy));
    }
    if (selectedLeadFilterModel.restoredByIds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.restoredByIds?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.restoredBy.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.restoredBy));
    }
    if (selectedLeadFilterModel.originalOwnerIds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.originalOwnerIds?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.originalOwner.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.restoredBy));
    }
    if (selectedLeadFilterModel.propertyTypes?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.propertyTypes?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.propertyType.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.propertyType));
    }
    if (selectedLeadFilterModel.propertySubTypes?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.propertySubTypes?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.propertySubType.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.propertySubType));
    }
    if (selectedLeadFilterModel.properties?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.properties?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.properties.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.properties));
    }
    if (selectedLeadFilterModel.projects?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.projects?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.projects.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.projects));
    }
    if (selectedLeadFilterModel.assignFromIds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.assignFromIds?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.assignedFrom.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.assignedFrom));
    }
    if (selectedLeadFilterModel.assignedToIds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.assignedToIds?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.assignedTo.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.assignedTo));
    }
    if (selectedLeadFilterModel.secondaryFromIds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.secondaryFromIds?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.secondaryOwnerFrom.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.secondaryOwnerFrom));
    }
    if (selectedLeadFilterModel.secondaryUsers?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.secondaryUsers?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.secondaryOwner.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.secondaryOwner));
    }
    if (selectedLeadFilterModel.bookedByIds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.bookedByIds?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.bookedBy.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.bookedBy));
    }
    if (selectedLeadFilterModel.dataConverted?.isNotNullOrEmpty() ?? false) {
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.dataConverted.description, description: '- ${selectedLeadFilterModel.dataConverted}', value: LeadFilterKey.dataConverted));
    }
    if (selectedLeadFilterModel.qualifiedByIds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.qualifiedByIds?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.qualifiedBy.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.qualifiedBy));
    }
    if (selectedLeadFilterModel.appointmentDoneByUserIds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.appointmentDoneByUserIds?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.siteVisitOrMeetingDoneBy.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.siteVisitOrMeetingDoneBy));
    }
    if (selectedLeadFilterModel.doneBy?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.doneBy?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.doneBy.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.doneBy));
    }
    if (selectedLeadFilterModel.statusIds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.statusIds?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.status.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.status));
    }
    if (selectedLeadFilterModel.subStatuses?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.subStatuses?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.subStatus.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.subStatus));
    }
    if (selectedLeadFilterModel.isWithTeam ?? false) {
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: 'With Team', value: LeadFilterKey.withTeam));
    }
    if (selectedLeadFilterModel.isWithHistory ?? false) {
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: 'With History', value: LeadFilterKey.withHistory));
    }
    if (selectedLeadFilterModel.netArea != null) {
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.netArea.description, description: selectedLeadFilterModel.netArea.doubleToWord(), value: LeadFilterKey.netArea));
    }
    if ((selectedLeadFilterModel.minPropertyArea != null && selectedLeadFilterModel.minPropertyArea != 0) || (selectedLeadFilterModel.maxPropertyArea != null && selectedLeadFilterModel.maxPropertyArea != 0)) {
      String description = '';
      if (selectedLeadFilterModel.minPropertyArea != null && selectedLeadFilterModel.maxPropertyArea != null) {
        description = 'min:${selectedLeadFilterModel.minPropertyArea}-max${selectedLeadFilterModel.maxPropertyArea}';
      } else if (selectedLeadFilterModel.minPropertyArea != null) {
        description = 'min:${selectedLeadFilterModel.minPropertyArea}';
      } else if (selectedLeadFilterModel.maxPropertyArea != null) {
        description = 'max${selectedLeadFilterModel.maxPropertyArea}';
      }
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.propertyArea.description, description: description, value: LeadFilterKey.propertyArea));
    }
    if (selectedLeadFilterModel.unitNames?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.unitNames?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.unitNumberOrName.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.unitNumberOrName));
    }
    if (selectedLeadFilterModel.clusterNames?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.clusterNames?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.clusterName.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.clusterName));
    }
    if (selectedLeadFilterModel.nationality?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.nationality?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.nationality.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.nationality));
    }
    if (selectedLeadFilterModel.channelPartnerNames?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.channelPartnerNames?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.channelPartnerName.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.channelPartnerName));
    }
    if (selectedLeadFilterModel.designations?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.designations?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.designation.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.designation));
    }
    if (selectedLeadFilterModel.excelSheets?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.excelSheets?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: '${LeadFilterKey.excelSheet.description}--${selectedLeadFilterModel.excelSheets?.first ?? ''}', description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.excelSheet));
    }
    if (selectedLeadFilterModel.profession?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.profession?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.profession.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.profession));
    }
    if (selectedLeadFilterModel.facebookProperties?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.facebookProperties?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.facebookProperties.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.facebookProperties));
    }
    if (selectedLeadFilterModel.facebookPropertyValues?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.facebookPropertyValues?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.facebookPropertyValues.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.facebookPropertyValues));
    }
    if (selectedLeadFilterModel.isUntouched != null) {
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.unTouched.description, description: selectedLeadFilterModel.isUntouched.toString(), value: LeadFilterKey.unTouched));
    }
    if (selectedLeadFilterModel.purposes?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.purposes?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.purpose.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.purpose));
    }
    if ((selectedLeadFilterModel.minCarpetArea != null && selectedLeadFilterModel.minCarpetArea != 0) || (selectedLeadFilterModel.maxCarpetArea != null && selectedLeadFilterModel.maxCarpetArea != 0)) {
      String description = '';
      if (selectedLeadFilterModel.minCarpetArea != null && selectedLeadFilterModel.maxCarpetArea != null) {
        description = 'min:${selectedLeadFilterModel.minCarpetArea}-max${selectedLeadFilterModel.maxCarpetArea}';
      } else if (selectedLeadFilterModel.minCarpetArea != null) {
        description = 'min:${selectedLeadFilterModel.minCarpetArea}';
      } else if (selectedLeadFilterModel.maxCarpetArea != null) {
        description = 'max${selectedLeadFilterModel.maxCarpetArea}';
      }
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.carpetArea.description, description: description, value: LeadFilterKey.carpetArea));
    }
    if ((selectedLeadFilterModel.minNetArea != null && selectedLeadFilterModel.minNetArea != 0) || (selectedLeadFilterModel.maxNetArea != null && selectedLeadFilterModel.maxNetArea != 0)) {
      String description = '';
      if (selectedLeadFilterModel.minNetArea != null && selectedLeadFilterModel.maxNetArea != null) {
        description = 'min:${selectedLeadFilterModel.minNetArea}-max${selectedLeadFilterModel.maxNetArea}';
      } else if (selectedLeadFilterModel.minNetArea != null) {
        description = 'min:${selectedLeadFilterModel.minNetArea}';
      } else if (selectedLeadFilterModel.maxNetArea != null) {
        description = 'max${selectedLeadFilterModel.maxNetArea}';
      }
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.netArea.description, description: description, value: LeadFilterKey.netArea));
    }
    if ((selectedLeadFilterModel.toMinBudget != null && selectedLeadFilterModel.toMinBudget != 0) || (selectedLeadFilterModel.fromMinBudget != null && selectedLeadFilterModel.fromMinBudget != 0)) {
      String description = '';
      if (selectedLeadFilterModel.toMinBudget != null && selectedLeadFilterModel.fromMinBudget != null) {
        description = 'max:${selectedLeadFilterModel.toMinBudget}-min${selectedLeadFilterModel.fromMinBudget} ${selectedLeadFilterModel.currency ?? ''}';
      } else if (selectedLeadFilterModel.toMinBudget != null) {
        description = 'max:${selectedLeadFilterModel.toMinBudget} ${selectedLeadFilterModel.currency ?? ''}';
      } else if (selectedLeadFilterModel.fromMinBudget != null) {
        description = 'min:${selectedLeadFilterModel.fromMinBudget} ${selectedLeadFilterModel.currency ?? ''}';
      }

      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.minBudget.description, description: description, value: LeadFilterKey.minBudget));
    }
    if ((selectedLeadFilterModel.fromMaxBudget != null && selectedLeadFilterModel.fromMaxBudget != 0) || (selectedLeadFilterModel.toMaxBudget != null && selectedLeadFilterModel.toMaxBudget != 0)) {
      String description = '';
      if (selectedLeadFilterModel.toMaxBudget != null && selectedLeadFilterModel.fromMaxBudget != null) {
        description = 'max:${selectedLeadFilterModel.toMaxBudget}-min${selectedLeadFilterModel.fromMaxBudget} ${selectedLeadFilterModel.currency ?? ''}';
      } else if (selectedLeadFilterModel.fromMaxBudget != null) {
        description = 'min:${selectedLeadFilterModel.fromMaxBudget} ${selectedLeadFilterModel.currency ?? ''}';
      } else if (selectedLeadFilterModel.toMaxBudget != null) {
        description = 'max:${selectedLeadFilterModel.toMaxBudget} ${selectedLeadFilterModel.currency ?? ''}';
      }
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.maxBudget.description, description: description, value: LeadFilterKey.maxBudget));
    }
    if ((selectedLeadFilterModel.minSaleAbleArea != null && selectedLeadFilterModel.minSaleAbleArea != 0) || (selectedLeadFilterModel.maxSaleAbleArea != null && selectedLeadFilterModel.maxSaleAbleArea != 0)) {
      String description = '';
      if (selectedLeadFilterModel.minSaleAbleArea != null && selectedLeadFilterModel.maxSaleAbleArea != null) {
        description = 'min:${selectedLeadFilterModel.minSaleAbleArea}-max${selectedLeadFilterModel.maxSaleAbleArea}';
      } else if (selectedLeadFilterModel.minSaleAbleArea != null) {
        description = 'min:${selectedLeadFilterModel.minSaleAbleArea}';
      } else if (selectedLeadFilterModel.maxSaleAbleArea != null) {
        description = 'max${selectedLeadFilterModel.maxSaleAbleArea}';
      }
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.salableArea.description, description: description, value: LeadFilterKey.salableArea));
    }
    if ((selectedLeadFilterModel.minBuiltUpArea != null && selectedLeadFilterModel.minBuiltUpArea != 0) || (selectedLeadFilterModel.maxBuiltUpArea != null && selectedLeadFilterModel.maxBuiltUpArea != 0)) {
      String description = '';
      if (selectedLeadFilterModel.minBuiltUpArea != null && selectedLeadFilterModel.maxBuiltUpArea != null) {
        description = 'min:${selectedLeadFilterModel.minBuiltUpArea}-max${selectedLeadFilterModel.maxBuiltUpArea}';
      } else if (selectedLeadFilterModel.minBuiltUpArea != null) {
        description = 'min:${selectedLeadFilterModel.minBuiltUpArea}';
      } else if (selectedLeadFilterModel.maxBuiltUpArea != null) {
        description = 'max${selectedLeadFilterModel.maxBuiltUpArea}';
      }
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.buildUpArea.description, description: description, value: LeadFilterKey.buildUpArea));
    }
    if (selectedLeadFilterModel.cities?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.cities?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.city.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.city));
    }
    if (selectedLeadFilterModel.zones?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.zones?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.zone.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.zone));
    }
    if (selectedLeadFilterModel.states?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.states?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.state.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.state));
    }
    if (selectedLeadFilterModel.localities?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedLeadFilterModel.localities?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.locality.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: LeadFilterKey.locality));
    }
    if (selectedLeadFilterModel.isShowUniqueLead?.isNotNullOrEmpty() ?? false) {
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.showUniqueLead.description, description: '- ${selectedLeadFilterModel.isShowUniqueLead}', value: LeadFilterKey.showUniqueLead));
    }
    if (selectedLeadFilterModel.isShowParentLead?.isNotNullOrEmpty() ?? false) {
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.showParentLead.description, description: '- ${selectedLeadFilterModel.isShowParentLead}', value: LeadFilterKey.showParentLead));
    }
    if (selectedLeadFilterModel.showChildCount != null) {
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.showChildCount.description, description: '- ${selectedLeadFilterModel.showChildCount}', value: LeadFilterKey.showChildCount));
    }
    if (selectedLeadFilterModel.longitude != null) {
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.longitude.description, description: '- ${selectedLeadFilterModel.longitude}', value: LeadFilterKey.longitude));
    }
    if (selectedLeadFilterModel.latitude != null) {
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.latitude.description, description: '- ${selectedLeadFilterModel.latitude}', value: LeadFilterKey.latitude));
    }
    if (selectedLeadFilterModel.radiusInKm != null) {
      selectedFilters.add(ItemSimpleModel<LeadFilterKey>(title: LeadFilterKey.radius.description, description: '- ${selectedLeadFilterModel.radiusInKm}', value: LeadFilterKey.radius));
    }

    return selectedFilters;
  }

  FutureOr<void> _onRemoveFilter(RemoveFilterEvent event, Emitter<ManageLeadsState> emit) {
    if (event.leadFilterKey == null) return null;
    switch (event.leadFilterKey!) {
      case LeadFilterKey.source:
        leadFilter = leadFilter.copyWith(sources: []);
        break;
      case LeadFilterKey.subSource:
        leadFilter = leadFilter.copyWith(subSources: []);
        break;
      case LeadFilterKey.category:
        final defaultCategoryTypes = leadsCategoryOrders?.where((i) => i.isDefault ?? false).map((item) => item.categiryType).whereType<LeadCategoryType>().toList();
        leadFilter = leadFilter.copyWith(filterTypes: defaultCategoryTypes, customFilterIds: []);
        break;
      case LeadFilterKey.leadVisibility:
        leadFilter = leadFilter.copyWith(leadVisibility: LeadVisibility.selfWithReportee);
        break;
      case LeadFilterKey.projects:
        leadFilter = leadFilter.copyWith(projects: []);
        break;
      case LeadFilterKey.properties:
        leadFilter = leadFilter.copyWith(properties: []);
        break;
      case LeadFilterKey.enquiredFor:
        leadFilter = leadFilter.copyWith(enquiredFor: []);
        break;
      case LeadFilterKey.dateRange:
        leadFilter = leadFilter.copyWith(fromDate: '', toDate: '', dateRange: null, updateDateRange: false, updateDateType: false, updatePossessionDateRange: false);
        break;
      case LeadFilterKey.meetingStatus:
        leadFilter = leadFilter.copyWith(meetingOrVisitStatuses: []);
        break;
      case LeadFilterKey.propertyType:
        leadFilter = leadFilter.copyWith(propertyTypes: []);
        break;
      case LeadFilterKey.propertySubType:
        leadFilter = leadFilter.copyWith(propertySubTypes: []);
        break;
      case LeadFilterKey.noOfBHK:
        leadFilter = leadFilter.copyWith(noOfBHKs: []);
        break;
      case LeadFilterKey.furnished:
        leadFilter = leadFilter.copyWith(furnished: []);
        break;
      case LeadFilterKey.beds:
        leadFilter = leadFilter.copyWith(beds: []);
        break;
      case LeadFilterKey.baths:
        leadFilter = leadFilter.copyWith(baths: []);
        break;
      case LeadFilterKey.floors:
        leadFilter = leadFilter.copyWith(floors: []);
        break;
      case LeadFilterKey.offerTypes:
        leadFilter = leadFilter.copyWith(offerTypes: []);
        break;
      case LeadFilterKey.bHKTypes:
        leadFilter = leadFilter.copyWith(bhkTypes: []);
        break;
      case LeadFilterKey.locations:
        leadFilter = leadFilter.copyWith(locations: []);
        break;
      case LeadFilterKey.agencyName:
        leadFilter = leadFilter.copyWith(agencyNames: []);
        break;
      case LeadFilterKey.bookedBy:
        leadFilter = leadFilter.copyWith(bookedByIds: []);
        break;
      case LeadFilterKey.dataConverted:
        leadFilter = leadFilter.copyWith(updateIsConverted: false);
        break;
      case LeadFilterKey.qualifiedBy:
        leadFilter = leadFilter.copyWith(qualifiedByIds: []);
        break;
      case LeadFilterKey.siteVisitOrMeetingDoneBy:
        leadFilter = leadFilter.copyWith(appointmentDoneByUserIds: []);
        break;
      case LeadFilterKey.customFlags:
        leadFilter = leadFilter.copyWith(customFlags: []);
        break;
      case LeadFilterKey.doneBy:
        leadFilter = leadFilter.copyWith(doneBy: []);
        break;
      case LeadFilterKey.assignedTo:
        leadFilter = leadFilter.copyWith(assignedToIds: []);
        break;
      case LeadFilterKey.assignedFrom:
        leadFilter = leadFilter.copyWith(assignFromIds: []);
        break;
      case LeadFilterKey.secondaryOwner:
        leadFilter = leadFilter.copyWith(secondaryUsers: []);
        break;
      case LeadFilterKey.secondaryOwnerFrom:
        leadFilter = leadFilter.copyWith(secondaryFromIds: []);
        break;
      case LeadFilterKey.status:
        leadFilter = leadFilter.copyWith(statusIds: []);
        break;
      case LeadFilterKey.subStatus:
        leadFilter = leadFilter.copyWith(subStatuses: []);
        break;
      case LeadFilterKey.withTeam:
        leadFilter = leadFilter.copyWith(isWithTeam: false);
        break;
      case LeadFilterKey.withHistory:
        leadFilter = leadFilter.copyWith(isWithHistory: false);
        break;
      case LeadFilterKey.community:
        leadFilter = leadFilter.copyWith(communities: []);
        break;
      case LeadFilterKey.subCommunity:
        leadFilter = leadFilter.copyWith(subCommunities: []);
        break;
      case LeadFilterKey.towerName:
        leadFilter = leadFilter.copyWith(towerNames: []);
        break;
      case LeadFilterKey.country:
        leadFilter = leadFilter.copyWith(countries: []);
        break;
      case LeadFilterKey.pinCode:
        leadFilter = leadFilter.copyWith(pincode: '');
        break;

      case LeadFilterKey.createdBy:
        leadFilter = leadFilter.copyWith(createdByIds: []);
        break;
      case LeadFilterKey.lastModifiedBy:
        leadFilter = leadFilter.copyWith(lastModifiedByIds: []);
        break;
      case LeadFilterKey.deletedBy:
        leadFilter = leadFilter.copyWith(archivedByIds: []);
        break;
      case LeadFilterKey.restoredBy:
        leadFilter = leadFilter.copyWith(restoredByIds: []);
        break;
      case LeadFilterKey.originalOwner:
        leadFilter = leadFilter.copyWith(originalOwnerIds: []);
        break;
      case LeadFilterKey.referralName:
        leadFilter = leadFilter.copyWith(referralName: '');
        break;
      case LeadFilterKey.referralEmail:
        leadFilter = leadFilter.copyWith(referralEmail: '');
        break;
      case LeadFilterKey.referralPhoneNo:
        leadFilter = leadFilter.copyWith(referralNumber: '');
        break;
      case LeadFilterKey.carpetArea:
        leadFilter = leadFilter.copyWith(
          carpetAreaUnitId: '',
          updateCarpetArea: false,
        );
        break;
      case LeadFilterKey.buildUpArea:
        leadFilter = leadFilter.copyWith(builtUpAreaUnitId: '', updateBuildUpArea: false);
        break;
      case LeadFilterKey.salableArea:
        leadFilter = leadFilter.copyWith(saleableAreaUnitId: '', updateSalableArea: false);
        break;
      case LeadFilterKey.unTouched:
        leadFilter = leadFilter.copyWith(isUntouched: null, updateIsUntouchable: false);
        break;
      case LeadFilterKey.channelPartnerName:
        leadFilter = leadFilter.copyWith(channelPartnerNames: []);
        break;
      case LeadFilterKey.excelSheet:
        leadFilter = leadFilter.copyWith(excelSheets: []);
        break;
      case LeadFilterKey.designation:
        leadFilter = leadFilter.copyWith(designations: []);
        break;
      case LeadFilterKey.profession:
        leadFilter = leadFilter.copyWith(profession: []);
        break;
      case LeadFilterKey.facebookProperties:
        leadFilter = leadFilter.copyWith(facebookProperties: []);
        break;
      case LeadFilterKey.facebookPropertyValues:
        leadFilter = leadFilter.copyWith(facebookPropertyValues: []);
        break;
      case LeadFilterKey.campaigns:
        leadFilter = leadFilter.copyWith(campaignNames: []);
        break;
      case LeadFilterKey.netArea:
        leadFilter = leadFilter.copyWith(netAreaUnitId: '', netArea: null, updateNetArea: false);
        break;
      case LeadFilterKey.propertyArea:
        leadFilter = leadFilter.copyWith(propertyAreaUnitId: '', updatePropertyArea: false);
        break;
      case LeadFilterKey.unitNumberOrName:
        leadFilter = leadFilter.copyWith(unitNames: []);
        break;
      case LeadFilterKey.clusterName:
        leadFilter = leadFilter.copyWith(clusterNames: []);
        break;
      case LeadFilterKey.nationality:
        leadFilter = leadFilter.copyWith(nationality: []);
        break;
      case LeadFilterKey.purpose:
        leadFilter = leadFilter.copyWith(purposes: []);
        break;
      case LeadFilterKey.minBudget:
        leadFilter = leadFilter.copyWith(updateMinBudget: false, updateCurrency: false);
        break;
      case LeadFilterKey.maxBudget:
        leadFilter = leadFilter.copyWith(updateMaxBudget: false, updateCurrency: false);
        break;
      case LeadFilterKey.city:
        leadFilter = leadFilter.copyWith(cities: []);
        break;
      case LeadFilterKey.zone:
        leadFilter = leadFilter.copyWith(zones: []);
        break;
      case LeadFilterKey.state:
        leadFilter = leadFilter.copyWith(states: []);
        break;
      case LeadFilterKey.locality:
        leadFilter = leadFilter.copyWith(localities: []);
        break;
      case LeadFilterKey.latitude:
        leadFilter = leadFilter.copyWith(latitude: null);
        break;
      case LeadFilterKey.longitude:
        leadFilter = leadFilter.copyWith(longitude: null);
        break;
      case LeadFilterKey.radius:
        leadFilter = leadFilter.copyWith(radiusInKm: null);
        break;
      case LeadFilterKey.showChildCount:
        leadFilter = leadFilter.copyWith(showChildCount: null);
        break;
      case LeadFilterKey.showParentLead:
        leadFilter = leadFilter.copyWith(isShowParentLead: '');
        break;
      case LeadFilterKey.showUniqueLead:
        leadFilter = leadFilter.copyWith(isShowUniqueLead: '');
        break;
      default:
        break;
    }
    selectedFilters.removeWhere((element) => element.value == event.leadFilterKey);
    add(ManageLeadsInitialEvent(leadFilter: leadFilter));
  }

  List<ItemLeadModel> _mergeUniqueLeadsById(List<ItemLeadModel>? existing, List<ItemLeadModel>? incoming) {
    try {
      final Map<String, ItemLeadModel> leadMap = {
        for (var lead in existing ?? []) lead.lead.id: lead,
        for (var lead in incoming ?? []) lead.lead.id: lead,
      };
      return leadMap.values.toList();
    } catch (ex) {
      rethrow;
    }
  }
}
