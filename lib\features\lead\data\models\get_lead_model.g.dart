// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_lead_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetLeadModel _$GetLeadModelFromJson(Map<String, dynamic> json) => GetLeadModel(
      id: json['id'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastModifiedBy: json['lastModifiedBy'] as String?,
      possessionDate: json['possessionDate'] == null
          ? null
          : DateTime.parse(json['possessionDate'] as String),
      name: json['name'] as String?,
      contactNo: json['contactNo'] as String?,
      alternateContactNo: json['alternateContactNo'] as String?,
      email: json['email'] as String?,
      notes: json['notes'] as String?,
      scheduledDate: json['scheduledDate'] == null
          ? null
          : DateTime.parse(json['scheduledDate'] as String),
      revertDate: json['revertDate'] == null
          ? null
          : DateTime.parse(json['revertDate'] as String),
      chosenProject: json['chosenProject'] as String?,
      chosenProperty: json['chosenProperty'] as String?,
      bookedUnderName: json['bookedUnderName'] as String?,
      leadNumber: json['leadNumber'] as String?,
      shareCount: (json['shareCount'] as num?)?.toInt(),
      soldPrice: json['soldPrice'] as String?,
      referralEmail: json['referralEmail'] as String?,
      rating: json['rating'] as String?,
      leadTags: json['leadTags'] == null
          ? null
          : LeadTagModel.fromJson(json['leadTags'] as Map<String, dynamic>),
      projects: (json['projects'] as List<dynamic>?)
          ?.map((e) => TempProjectModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      properties: (json['properties'] as List<dynamic>?)
          ?.map((e) => TempPropertyModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      projectsList: (json['projectsList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      propertiesList: (json['propertiesList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      assignTo: json['assignTo'] as String?,
      assignedFrom: json['assignedFrom'] as String?,
      referralName: json['referralName'] as String?,
      referralContactNo: json['referralContactNo'] as String?,
      agencyName: json['agencyName'] as String?,
      agencies: (json['agencies'] as List<dynamic>?)
          ?.map((e) => DTOWithNameModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      campaigns: (json['campaigns'] as List<dynamic>?)
          ?.map((e) => DTOWithNameModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      campaignName: json['campaignName'] as String?,
      companyName: json['companyName'] as String?,
      unmatchedBudget: (json['unmatchedBudget'] as num?)?.toDouble(),
      purchasedFrom: json['purchasedFrom'] as String?,
      closingManager: json['closingManager'] as String?,
      sourcingManager: json['sourcingManager'] as String?,
      address: json['address'] == null
          ? null
          : AddressModel.fromJson(json['address'] as Map<String, dynamic>),
      profession: $enumDecodeNullable(_$ProfessionEnumMap, json['profession']),
      channelPartnerName: json['channelPartnerName'] as String?,
      channelPartnerList: (json['channelPartnerList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      channelPartnerExecutiveName:
          json['channelPartnerExecutiveName'] as String?,
      channelPartnerContactNo: json['channelPartnerContactNo'] as String?,
      channelPartners: (json['channelPartners'] as List<dynamic>?)
          ?.map((e) => ChannelPartnerModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      existingProjects: (json['existingProjects'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      designation: json['designation'] as String?,
      secondaryUserId: json['secondaryUserId'] as String?,
      statusId: json['statusId'] as String?,
      bookedDate: json['bookedDate'] == null
          ? null
          : DateTime.parse(json['bookedDate'] as String),
      unitTypeId: json['unitTypeId'] as String?,
      agreementValue: (json['agreementValue'] as num?)?.toDouble(),
      landline: json['landline'] as String?,
      serialNumber: json['serialNumber'] as String?,
      contactRecords: (json['contactRecords'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toInt()),
      ),
      documents: (json['documents'] as List<dynamic>?)
          ?.map((e) => LeadDocumentModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      status: json['status'] == null
          ? null
          : LeadStatusCustomModel.fromJson(
              json['status'] as Map<String, dynamic>),
      customFlags: (json['customFlags'] as List<dynamic>?)
          ?.map((e) => CustomFlagModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      enquiry: json['enquiry'] == null
          ? null
          : EnquiryModel.fromJson(json['enquiry'] as Map<String, dynamic>),
      callRecordingUrls:
          (json['callRecordingUrls'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(
            int.parse(k),
            (e as Map<String, dynamic>?)?.map(
              (k, e) => MapEntry(
                  int.parse(k),
                  (e as Map<String, dynamic>?)?.map(
                    (k, e) => MapEntry(k, e as String),
                  )),
            )),
      ),
      links: (json['links'] as List<dynamic>?)
          ?.map((e) => LinkModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      duplicateLeadVersion: json['duplicateLeadVersion'] as String?,
      confidentialNotes: json['confidentialNotes'] as String?,
      additionalProperties:
          (json['additionalProperties'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String?),
      ),
      nationality: json['nationality'] as String?,
      leadCallLogs: (json['leadCallLogs'] as List<dynamic>?)
          ?.map((e) => LeadCallLogModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      purpose: $enumDecodeNullable(_$PurposeEnumEnumMap, json['purpose']),
      possesionType:
          $enumDecodeNullable(_$PossessionTypeEnumMap, json['possesionType']),
    );

Map<String, dynamic> _$GetLeadModelToJson(GetLeadModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.createdOn?.toIso8601String() case final value?)
        'createdOn': value,
      if (instance.createdBy case final value?) 'createdBy': value,
      if (instance.lastModifiedOn?.toIso8601String() case final value?)
        'lastModifiedOn': value,
      if (instance.lastModifiedBy case final value?) 'lastModifiedBy': value,
      if (instance.name case final value?) 'name': value,
      if (instance.contactNo case final value?) 'contactNo': value,
      if (instance.alternateContactNo case final value?)
        'alternateContactNo': value,
      if (instance.email case final value?) 'email': value,
      if (instance.notes case final value?) 'notes': value,
      if (instance.scheduledDate?.toIso8601String() case final value?)
        'scheduledDate': value,
      if (instance.possessionDate?.toIso8601String() case final value?)
        'possessionDate': value,
      if (instance.revertDate?.toIso8601String() case final value?)
        'revertDate': value,
      if (instance.chosenProject case final value?) 'chosenProject': value,
      if (instance.chosenProperty case final value?) 'chosenProperty': value,
      if (instance.bookedUnderName case final value?) 'bookedUnderName': value,
      if (instance.leadNumber case final value?) 'leadNumber': value,
      if (instance.landline case final value?) 'landline': value,
      if (instance.shareCount case final value?) 'shareCount': value,
      if (instance.soldPrice case final value?) 'soldPrice': value,
      if (instance.rating case final value?) 'rating': value,
      if (instance.leadTags case final value?) 'leadTags': value,
      if (instance.projects case final value?) 'projects': value,
      if (instance.properties case final value?) 'properties': value,
      if (instance.projectsList case final value?) 'projectsList': value,
      if (instance.propertiesList case final value?) 'propertiesList': value,
      if (instance.assignTo case final value?) 'assignTo': value,
      if (instance.assignedFrom case final value?) 'assignedFrom': value,
      if (instance.secondaryUserId case final value?) 'secondaryUserId': value,
      if (instance.referralName case final value?) 'referralName': value,
      if (instance.referralContactNo case final value?)
        'referralContactNo': value,
      if (instance.agencyName case final value?) 'agencyName': value,
      if (instance.agencies case final value?) 'agencies': value,
      if (instance.campaigns case final value?) 'campaigns': value,
      if (instance.campaignName case final value?) 'campaignName': value,
      if (instance.companyName case final value?) 'companyName': value,
      if (instance.unmatchedBudget case final value?) 'unmatchedBudget': value,
      if (instance.purchasedFrom case final value?) 'purchasedFrom': value,
      if (instance.closingManager case final value?) 'closingManager': value,
      if (instance.sourcingManager case final value?) 'sourcingManager': value,
      if (instance.address case final value?) 'address': value,
      if (_$ProfessionEnumMap[instance.profession] case final value?)
        'profession': value,
      if (instance.channelPartnerName case final value?)
        'channelPartnerName': value,
      if (instance.channelPartnerList case final value?)
        'channelPartnerList': value,
      if (instance.channelPartnerExecutiveName case final value?)
        'channelPartnerExecutiveName': value,
      if (instance.channelPartnerContactNo case final value?)
        'channelPartnerContactNo': value,
      if (instance.channelPartners case final value?) 'channelPartners': value,
      if (instance.existingProjects case final value?)
        'existingProjects': value,
      if (instance.designation case final value?) 'designation': value,
      if (instance.referralEmail case final value?) 'referralEmail': value,
      if (instance.statusId case final value?) 'statusId': value,
      if (instance.bookedDate?.toIso8601String() case final value?)
        'bookedDate': value,
      if (instance.unitTypeId case final value?) 'unitTypeId': value,
      if (instance.agreementValue case final value?) 'agreementValue': value,
      if (instance.serialNumber case final value?) 'serialNumber': value,
      if (instance.contactRecords case final value?) 'contactRecords': value,
      if (instance.documents case final value?) 'documents': value,
      if (instance.status case final value?) 'status': value,
      if (instance.customFlags case final value?) 'customFlags': value,
      if (instance.enquiry case final value?) 'enquiry': value,
      if (instance.callRecordingUrls?.map((k, e) => MapEntry(
              k.toString(), e?.map((k, e) => MapEntry(k.toString(), e))))
          case final value?)
        'callRecordingUrls': value,
      if (instance.links case final value?) 'links': value,
      if (instance.duplicateLeadVersion case final value?)
        'duplicateLeadVersion': value,
      if (instance.confidentialNotes case final value?)
        'confidentialNotes': value,
      if (instance.additionalProperties case final value?)
        'additionalProperties': value,
      if (instance.nationality case final value?) 'nationality': value,
      if (_$PurposeEnumEnumMap[instance.purpose] case final value?)
        'purpose': value,
      if (instance.leadCallLogs case final value?) 'leadCallLogs': value,
      if (_$PossessionTypeEnumMap[instance.possesionType] case final value?)
        'possesionType': value,
    };

const _$ProfessionEnumMap = {
  Profession.none: 0,
  Profession.salaried: 1,
  Profession.business: 2,
  Profession.selfEmployed: 3,
  Profession.doctor: 4,
  Profession.retired: 5,
  Profession.housewife: 6,
  Profession.student: 7,
  Profession.unemployed: 8,
  Profession.others: 9,
};

const _$PurposeEnumEnumMap = {
  PurposeEnum.none: 0,
  PurposeEnum.investment: 1,
  PurposeEnum.selfUse: 2,
};

const _$PossessionTypeEnumMap = {
  PossessionType.none: 0,
  PossessionType.underConstruction: 1,
  PossessionType.sixMonths: 2,
  PossessionType.oneYear: 3,
  PossessionType.twoYear: 4,
  PossessionType.customDate: 5,
};

ChannelPartnerModel _$ChannelPartnerModelFromJson(Map<String, dynamic> json) =>
    ChannelPartnerModel(
      id: json['id'] as String?,
      firmName: json['firmName'] as String?,
    );

Map<String, dynamic> _$ChannelPartnerModelToJson(
        ChannelPartnerModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'firmName': instance.firmName,
    };

TempProjectModel _$TempProjectModelFromJson(Map<String, dynamic> json) =>
    TempProjectModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      serialNo: json['serialNo'] as String?,
    );

Map<String, dynamic> _$TempProjectModelToJson(TempProjectModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.serialNo case final value?) 'serialNo': value,
    };

TempPropertyModel _$TempPropertyModelFromJson(Map<String, dynamic> json) =>
    TempPropertyModel(
      id: json['id'] as String?,
      title: json['title'] as String?,
      serialNo: json['serialNo'] as String?,
    );

Map<String, dynamic> _$TempPropertyModelToJson(TempPropertyModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.title case final value?) 'title': value,
      if (instance.serialNo case final value?) 'serialNo': value,
    };

CustomFlagModel _$CustomFlagModelFromJson(Map<String, dynamic> json) =>
    CustomFlagModel(
      flag: json['flag'] == null
          ? null
          : ViewFlagModel.fromJson(json['flag'] as Map<String, dynamic>),
      isSelected: json['isSelected'] as bool?,
    );

Map<String, dynamic> _$CustomFlagModelToJson(CustomFlagModel instance) =>
    <String, dynamic>{
      'flag': instance.flag,
      'isSelected': instance.isSelected,
    };

UpdateLeadFlagModel _$UpdateLeadFlagModelFromJson(Map<String, dynamic> json) =>
    UpdateLeadFlagModel(
      id: json['id'] as String?,
      flagId: json['flagId'] as String?,
    );

Map<String, dynamic> _$UpdateLeadFlagModelToJson(
        UpdateLeadFlagModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'flagId': instance.flagId,
    };

LeadContactCountModel _$LeadContactCountModelFromJson(
        Map<String, dynamic> json) =>
    LeadContactCountModel(
      id: json['id'] as String?,
      contactType:
          $enumDecodeNullable(_$ContactTypeEnumMap, json['contactType']),
    );

Map<String, dynamic> _$LeadContactCountModelToJson(
        LeadContactCountModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'contactType': _$ContactTypeEnumMap[instance.contactType],
    };

const _$ContactTypeEnumMap = {
  ContactType.whatsApp: 0,
  ContactType.call: 1,
  ContactType.email: 2,
  ContactType.sms: 3,
  ContactType.pushNotification: 4,
  ContactType.links: 5,
};

LeadCommunicationModel _$LeadCommunicationModelFromJson(
        Map<String, dynamic> json) =>
    LeadCommunicationModel(
      contactType:
          $enumDecodeNullable(_$ContactTypeEnumMap, json['contactType']),
      message: json['message'] as String?,
      leadId: json['leadId'] as String?,
    );

Map<String, dynamic> _$LeadCommunicationModelToJson(
        LeadCommunicationModel instance) =>
    <String, dynamic>{
      'contactType': _$ContactTypeEnumMap[instance.contactType],
      'message': instance.message,
      'leadId': instance.leadId,
    };

LeadSubSourceModel _$LeadSubSourceModelFromJson(Map<String, dynamic> json) =>
    LeadSubSourceModel(
      source: json['source'] == null
          ? null
          : MasterLeadSourceModel.fromJson(
              json['source'] as Map<String, dynamic>),
      subSources: (json['subSources'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$LeadSubSourceModelToJson(LeadSubSourceModel instance) =>
    <String, dynamic>{
      'source': instance.source,
      'subSources': instance.subSources,
    };

LeadContactModel _$LeadContactModelFromJson(Map<String, dynamic> json) =>
    LeadContactModel(
      canNavigate: json['canNavigate'] as bool?,
      id: json['id'] as String?,
    );

Map<String, dynamic> _$LeadContactModelToJson(LeadContactModel instance) =>
    <String, dynamic>{
      'canNavigate': instance.canNavigate,
      'id': instance.id,
    };

LeadWithDegreeMatchedModel _$LeadWithDegreeMatchedModelFromJson(
        Map<String, dynamic> json) =>
    LeadWithDegreeMatchedModel(
      totalNoOfFields: (json['totalNoOfFields'] as num?)?.toInt(),
      noOfFieldsMatched: (json['noOfFieldsMatched'] as num?)?.toInt(),
      percentageOfFieldsMatched: json['percentageOfFieldsMatched'] as String?,
      lead: json['lead'] == null
          ? null
          : GetLeadModel.fromJson(json['lead'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$LeadWithDegreeMatchedModelToJson(
        LeadWithDegreeMatchedModel instance) =>
    <String, dynamic>{
      'totalNoOfFields': instance.totalNoOfFields,
      'noOfFieldsMatched': instance.noOfFieldsMatched,
      'percentageOfFieldsMatched': instance.percentageOfFieldsMatched,
      'lead': instance.lead,
    };

LinkModel _$LinkModelFromJson(Map<String, dynamic> json) => LinkModel(
      id: json['id'] as String?,
      url: json['url'] as String?,
      type: json['type'] as String?,
      clickedCount: (json['clickedCount'] as num?)?.toInt(),
    );

Map<String, dynamic> _$LinkModelToJson(LinkModel instance) => <String, dynamic>{
      'id': instance.id,
      'url': instance.url,
      'type': instance.type,
      'clickedCount': instance.clickedCount,
    };
