import 'package:leadrat/core_main/common/data/master_data/models/master_custom_status_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_lead_status_model.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_model.dart';
import 'package:leadrat/core_main/remote/rest_response/paged_rest_response.dart';
import 'package:leadrat/core_main/utilities/type_def.dart';
import 'package:leadrat/features/lead/data/models/add_lead_model.dart';
import 'package:leadrat/features/lead/data/models/appointment_project_model.dart';
import 'package:leadrat/features/lead/data/models/custom_filter_model.dart';
import 'package:leadrat/features/lead/data/models/delete_document_model.dart';
import 'package:leadrat/features/lead/data/models/get_lead_model.dart';
import 'package:leadrat/features/lead/data/models/lead_call_log_model.dart';
import 'package:leadrat/features/lead/data/models/lead_document_model.dart';
import 'package:leadrat/features/lead/data/models/lead_filter_model.dart';
import 'package:leadrat/features/lead/data/models/lead_link_model.dart';
import 'package:leadrat/features/lead/data/models/projects_dto.dart';
import 'package:leadrat/features/lead/data/models/re_assign_lead_model.dart';
import 'package:leadrat/features/lead/data/models/update_appointments_model.dart';
import 'package:leadrat/features/lead/data/models/update_custom_status_model.dart';
import 'package:leadrat/features/lead/data/models/update_lead_status_model.dart';
import 'package:leadrat/features/lead/domain/entities/get_all_leads_wrapper_entity.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';
import 'package:leadrat/features/lead/domain/entities/lead_category_entity.dart';
import 'package:leadrat/features/lead/domain/entities/lead_document_entity.dart';
import 'package:leadrat/features/lead/domain/entities/lead_history_entity.dart';
import 'package:leadrat/features/lead/domain/entities/lead_sub_source_entity.dart';
import 'package:leadrat/features/projects/domain/entities/project_unit_info_entity.dart';
import 'package:leadrat/features/properties/domain/entities/get_property_entity.dart';

import '../../data/models/booked_lead_model.dart';
import '../entities/get_booked_lead_entity.dart';

abstract interface class LeadsRepository {
  FutureEitherFailure<GetAllLeadsWrapperEntity?> getAllInitialLeads(LeadFilterModel? leadFilterModel, {int pageNumber = 0, int pageSize = 10});

  FutureEitherFailure<GetAllLeadsWrapperCustomEntity?> getAllInitialCustomLeads(LeadFilterModel? leadFilterModel, {int pageNumber = 0, int pageSize = 10});

  FutureEitherFailure<Map<String, Map<String, int>>?> getLeadCommunications(List<String> leadIds);

  FutureEitherFailure<GetLeadEntity?> getLeadDetails(String leadId);

  FutureEitherFailure<Map<DateTime, Map<DateTime, List<LeadHistoryEntity>>>?> getLeadHistory(String leadId);

  FutureEitherFailure<List<LeadHistoryEntity>?> getLeadHistoryBasedOnTimeZone(String leadId);

  FutureEitherFailure<Iterable<LeadDocumentEntity>?> getLeadDocuments(String leadId);

  FutureEitherFailure<bool?> updateLeadFlag(UpdateLeadFlagModel updateLeadFlagModel);

  FutureEitherFailure<List<String>?> addLeadDocuments(AddLeadDocumentsModel addLeadDocumentsModel);

  FutureEitherFailure<Map<DateTime, List<LeadHistoryEntity>>?> getLeadNoteHistory(String leadId);

  FutureEitherFailure<bool?> updateLeadNote(String leadId, String note);

  FutureEitherFailure<bool?> deleteLeadDocument(DeleteDocumentModel deleteDocumentModel);

  FutureEitherFailure<bool?> updateLeadContactCount(LeadContactCountModel leadContactCountModel);

  FutureEitherFailure<bool?> updateLeadTemplate(LeadCommunicationModel leadCommunicationModel);

  FutureEitherFailure<List<LeadSubSourceEntity>?> getLeadSubSource();

  FutureEitherFailure<List<MasterCustomStatusModel>?> getAllCustomStatuses();

  FutureEitherFailure<List<MasterLeadStatusModel>?> getAllStatuses();

  FutureEitherFailure<bool?> updateLeadStatus(UpdateLeadStatusModel? updateLeadStatusModel);

  FutureEitherFailure<bool?> updateAppointments(UpdateAppointmentsModel? updateAppointmentsModel);

  FutureEitherFailure<bool?> reAssignLead(ReAssignLeadModel? reAssignModel);

  FutureEitherFailure<String?> addLead(AddLeadModel addLeadModel);

  FutureEitherFailure<String?> updateLead(UpdateLeadModel updateLeadModel);

  FutureEitherFailure<LeadContactModel?> getLeadByContactNo(String countryCode, String contactNo);

  FutureEitherFailure<LeadCategoryEntity?> searchLeads({String? keyword, int pageNumber = 1, int pageSize = 10});

  FutureEitherFailure<PagedResponse<PropertyWithDegreeMatchedEntity?, String>?> matchingProperties({String? leadId, String? searchText, int pageNumber = 1, int pageSize = 10, double? radiusInKms});

  FutureEitherFailure<List<AppointmentProjectModel>?> getAppointmentsByProjects(ProjectDto projectDto);

  FutureEitherFailure<PagedResponse<ProjectWithDegreeMatchedEntity?, String>?> matchingProjects({String? leadId, String? searchText, int pageNumber = 1, int pageSize = 10, double? radiusInKms});

  FutureEitherFailure<bool?> addProjectsInLead(ProjectDto projectDto);

  FutureEitherFailure<List<String>?> getChannelPartnerNames();

  FutureEitherFailure<List<CustomFilterModel>?> getCustomStatusFilter();

  FutureEitherFailure<String?> bookLead(BookedLeadModel bookedLeadModel);

  FutureEitherFailure<GetBookedLeadEntity?> getBookedLead(String? leadId);

  Future<List<String>?> getCommunities();

  Future<List<String>?> getSubCommunities();

  Future<List<String>?> getTowerNames();

  Future<List<String>?> getCountries();

  Future<List<String>?> getCurrencies();

  FutureEitherFailure<bool?> checkLeadAssignedByLeadId(String leadId);

  FutureEitherFailure<String> getTemplateMessageByLead({String? header, String? message, String? footer, UserDetailsModel? userDetails, String? domain, List<GetLeadEntity?>? leadEntity});

  Future<List<String>?> getCampaignNames();

  FutureEitherFailure<bool?> updateClickedLink(LeadLinkModel leadLinkModel);

  FutureEitherFailure<List<String>?> getLeadExcelData();

  FutureEitherFailure<List<String>?> getAdditionalPropertyKeys();

  FutureEitherFailure<List<String>?> getAdditionalPropertyValues(String key);

  Future<List<String>?> getLeadNationality();

  Future<List<String>?> getLeadClusterNames();

  Future<List<String>?> getLeadUnitNames();

  FutureEitherFailure<bool> updateLeadCallLog(LeadCallLogModel leadCallLogModel);

  FutureEitherFailure<bool> customFilterBulkUpdate(UpdateCustomStatusModel updateCustomStatusModel);

  FutureEitherFailure<bool> saveMobileCardCategoriesOrders(List<CustomFilterModel>? items);

  Future<List<String?>?> getAllLeadCities();

  Future<List<String?>?> getAllLeadZones();

  Future<List<String?>?> getAllLeadStates();

  Future<List<String?>?> getAllLeadLocality();

}
