import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/models/copy_withvalue_model.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/taxation_mode.dart';
import 'package:leadrat/core_main/enums/property_enums/completion_status.dart';
import 'package:leadrat/core_main/enums/property_enums/lockin_period_type.dart';
import 'package:leadrat/core_main/enums/property_enums/notice_period_type.dart';
import 'package:leadrat/core_main/enums/property_enums/property_status.dart';
import 'package:leadrat/core_main/enums/property_enums/security_deposite_type.dart';
import 'package:leadrat/features/properties/data/models/property_attribute_model.dart';
import 'package:leadrat/features/properties/data/models/property_brochure_model.dart';
import 'package:leadrat/features/properties/data/models/property_dimension_model.dart';
import 'package:leadrat/features/properties/data/models/property_image_model.dart';
import 'package:leadrat/features/properties/data/models/property_taginfo_model.dart';
import 'package:leadrat/features/properties/domain/entities/get_property_entity.dart';

import '../../../../core_main/common/models/address_model.dart';
import '../../../../core_main/common/models/booked_details_model.dart';

part 'add_property_model.g.dart';

@JsonSerializable(includeIfNull: false, explicitToJson: true)
class AddPropertyModel {
  final String? id;
  final String? title;
  final int? saleType;
  final int? enquiredFor;
  final String? notes;
  final int? furnishStatus;
  final int? status;
  final String? rating;
  final int? shareCount;
  final DateTime? possessionDate;
  final bool? isGOListingEnabled;
  final int? facing;
  final String? goPropertyId;
  final double? noOfBHK;
  final int? bhkType;
  final PropertyMonetaryInfoModel? monetaryInfo;
  final PropertyOwnerDetailsModel? ownerDetails;
  final PropertyDimensionModel? dimension;
  final PropertyTagInfoModel? tagInfo;
  final List<PropertyImageModel>? images;
  final List<PropertyAttributeModel>? attributes;
  final List<String>? amenities;
  final List<PropertyBrochureModel>? brochures;
  final List<int>? noOfFloorsOccupied;
  final bool? isWaterMarkEnabled;

  final int? whatsAppShareCount;
  final int? callShareCount;
  final int? emailShareCount;
  final int? smsShareCount;
  final String? aboutProperty;
  final AddressModel? address;
  final List<String>? links;
  final String? project;
  final List<String>? assignedTo;
  final List<String>? listingOnBehalf;
  final bool? shouldVisisbleOnListing;
  final String? placeId;
  final String? propertyTypeId;
  final Map<String, List<String>>? imageUrls;
  final String? serialNo;
  final String? coWorkingOperator;
  final String? coWorkingOperatorName;
  final String? coWorkingOperatorPhone;

  final LockInPeriodType? lockInPeriod;
  final NoticePeriodType? noticePeriod;
  final TenantContactInfo? tenantContactInfo;
  final List<CreateListingAddressModel>? listingAddresses;
  final String? dldPermitNumber;
  final String? refrenceNo;
  final String? dtcmPermit;
  final OfferingType? offeringType;
  final CompletionStatus? completionStatus;
  final String? language;
  final String? titleWithLanguage;
  final String? aboutPropertyWithLanguage;
  final List<String>? view360Url;
  final TaxationMode? taxationMode;
  final PossessionType? possesionType;
  final double? securityDepositAmount;
  final String? securityDepositUnit;
  List<PropertyOwnerDetailsModel>? propertyOwnerDetails;

  AddPropertyModel({
    this.serialNo,
    this.id,
    this.title,
    this.saleType,
    this.enquiredFor,
    this.notes,
    this.furnishStatus,
    this.status,
    this.rating,
    this.shareCount,
    this.possessionDate,
    this.isGOListingEnabled,
    this.facing,
    this.goPropertyId,
    this.noOfBHK,
    this.bhkType,
    this.monetaryInfo,
    this.ownerDetails,
    this.dimension,
    this.tagInfo,
    this.images,
    this.attributes,
    this.amenities,
    this.brochures,
    this.whatsAppShareCount,
    this.callShareCount,
    this.emailShareCount,
    this.smsShareCount,
    this.aboutProperty,
    this.address,
    this.links,
    this.project,
    this.assignedTo,
    this.listingOnBehalf,
    this.shouldVisisbleOnListing,
    this.placeId,
    this.propertyTypeId,
    this.imageUrls,
    this.noOfFloorsOccupied,
    this.tenantContactInfo,
    this.noticePeriod,
    this.lockInPeriod,
    this.coWorkingOperator,
    this.coWorkingOperatorName,
    this.coWorkingOperatorPhone,
    this.listingAddresses,
    this.dldPermitNumber,
    this.refrenceNo,
    this.dtcmPermit,
    this.offeringType,
    this.completionStatus,
    this.language,
    this.titleWithLanguage,
    this.aboutPropertyWithLanguage,
    this.view360Url,
    this.taxationMode,
    this.isWaterMarkEnabled,
    this.possesionType,
    this.securityDepositAmount,
    this.securityDepositUnit,
    this.propertyOwnerDetails,
  });

  factory AddPropertyModel.fromJson(Map<String, dynamic> json) => _$AddPropertyModelFromJson(json);

  Map<String, dynamic> toJson() => _$AddPropertyModelToJson(this);

  AddPropertyModel copyWith({
    String? id,
    String? title,
    int? saleType,
    int? enquiredFor,
    String? notes,
    int? furnishStatus,
    int? status,
    String? rating,
    int? shareCount,
    DateTime? possessionDate,
    bool updatePossessionDate=false,
    bool? isGOListingEnabled,
    int? facing,
    String? goPropertyId,
    double? noOfBHK,
    int? bhkType,
    PropertyMonetaryInfoModel? monetaryInfo,
    PropertyOwnerDetailsModel? ownerDetails,
    PropertyDimensionModel? dimension,
    PropertyTagInfoModel? tagInfo,
    List<PropertyImageModel>? images,
    List<PropertyAttributeModel>? attributes,
    List<String>? amenities,
    List<PropertyBrochureModel>? brochures,
    int? whatsAppShareCount,
    int? callShareCount,
    int? emailShareCount,
    int? smsShareCount,
    String? aboutProperty,
    AddressModel? address,
    List<String>? links,
    String? project,
    List<String>? assignedTo,
    List<String>? listingOnBehalf,
    bool? shouldVisisbleOnListing,
    CopyWithValue<String>? placeId,
    String? propertyTypeId,
    Map<String, List<String>>? imageUrls,
    String? coWorkingOperator,
    String? coWorkingOperatorName,
    String? coWorkingOperatorPhone,
    LockInPeriodType? lockInPeriod,
    NoticePeriodType? noticePeriod,
    List<int>? noOfFloorsOccupied,
    List<CreateListingAddressModel>? listingAddresses,
    String? dldPermitNumber,
    String? refrenceNo,
    String? dtcmPermit,
    OfferingType? offeringType,
    CompletionStatus? completionStatus,
    String? language,
    String? titleWithLanguage,
    String? aboutPropertyWithLanguage,
    List<String>? view360Url,
    TenantContactInfo? tenantContactInfo,
    TaxationMode? taxationMode,
    bool? isWaterMarkEnabled,
    bool? canMakeImageUrlsNull,
    PossessionType? possesionType,
    double? securityDepositAmount,
    String? securityDepositUnit,
    List<PropertyOwnerDetailsModel>? propertyOwnerDetails,
  }) {
    return AddPropertyModel(
      coWorkingOperator: coWorkingOperator ?? this.coWorkingOperator,
      coWorkingOperatorName: coWorkingOperatorName ?? this.coWorkingOperatorName,
      coWorkingOperatorPhone: coWorkingOperatorPhone ?? this.coWorkingOperatorPhone,
      lockInPeriod: lockInPeriod ?? this.lockInPeriod,
      noticePeriod: noticePeriod ?? this.noticePeriod,
      tenantContactInfo: tenantContactInfo ?? this.tenantContactInfo,
      id: id ?? this.id,
      title: title ?? this.title,
      saleType: saleType ?? this.saleType,
      enquiredFor: enquiredFor ?? this.enquiredFor,
      notes: notes ?? this.notes,
      furnishStatus: furnishStatus ?? this.furnishStatus,
      status: status ?? this.status,
      rating: rating ?? this.rating,
      shareCount: shareCount ?? this.shareCount,
      possessionDate:updatePossessionDate?null: possessionDate ?? this.possessionDate,
      isGOListingEnabled: isGOListingEnabled ?? this.isGOListingEnabled,
      facing: facing ?? this.facing,
      goPropertyId: goPropertyId ?? this.goPropertyId,
      noOfBHK: noOfBHK ?? this.noOfBHK,
      bhkType: bhkType ?? this.bhkType,
      monetaryInfo: monetaryInfo ?? this.monetaryInfo,
      ownerDetails: ownerDetails ?? this.ownerDetails,
      dimension: dimension ?? this.dimension,
      tagInfo: tagInfo ?? this.tagInfo,
      images: images ?? this.images,
      attributes: attributes ?? this.attributes,
      amenities: amenities ?? this.amenities,
      brochures: brochures ?? this.brochures,
      whatsAppShareCount: whatsAppShareCount ?? this.whatsAppShareCount,
      callShareCount: callShareCount ?? this.callShareCount,
      emailShareCount: emailShareCount ?? this.emailShareCount,
      smsShareCount: smsShareCount ?? this.smsShareCount,
      aboutProperty: aboutProperty ?? this.aboutProperty,
      address: address ?? this.address,
      links: links ?? this.links,
      project: project ?? this.project,
      assignedTo: assignedTo ?? this.assignedTo,
      shouldVisisbleOnListing: shouldVisisbleOnListing ?? this.shouldVisisbleOnListing,
      placeId: placeId != null && placeId.canUpdateValue ? placeId.value : this.placeId,
      propertyTypeId: propertyTypeId ?? this.propertyTypeId,
      imageUrls: canMakeImageUrlsNull != null && canMakeImageUrlsNull ? null : imageUrls ?? this.imageUrls,
      noOfFloorsOccupied: noOfFloorsOccupied ?? this.noOfFloorsOccupied,
      listingAddresses: listingAddresses ?? this.listingAddresses,
      dldPermitNumber: dldPermitNumber ?? this.dldPermitNumber,
      refrenceNo: refrenceNo ?? this.refrenceNo,
      dtcmPermit: dtcmPermit ?? this.dtcmPermit,
      offeringType: offeringType ?? this.offeringType,
      completionStatus: completionStatus ?? this.completionStatus,
      language: language ?? this.language,
      titleWithLanguage: titleWithLanguage ?? this.titleWithLanguage,
      aboutPropertyWithLanguage: aboutPropertyWithLanguage ?? this.aboutPropertyWithLanguage,
      view360Url: view360Url ?? this.view360Url,
      taxationMode: taxationMode ?? this.taxationMode,
      isWaterMarkEnabled: isWaterMarkEnabled ?? this.isWaterMarkEnabled,
      possesionType: possesionType ?? this.possesionType,
      securityDepositAmount: securityDepositAmount ?? this.securityDepositAmount,
      securityDepositUnit: securityDepositUnit ?? this.securityDepositUnit,
        propertyOwnerDetails: propertyOwnerDetails ?? this.propertyOwnerDetails,
      listingOnBehalf: listingOnBehalf ?? this.listingOnBehalf,
    );
  }

  AddPropertyModel reset({
    bool? isCoWorkingOfficeSpaceSelected,
    bool? isOfficeSpaceSelected,
    String? id,
    String? title,
    int? saleType,
    int? enquiredFor,
    String? notes,
    int? furnishStatus,
    int? status,
    String? rating,
    int? shareCount,
    DateTime? possessionDate,
    bool? isGOListingEnabled,
    int? facing,
    String? goPropertyId,
    double? noOfBHK,
    int? bhkType,
    PropertyMonetaryInfoModel? monetaryInfo,
    PropertyOwnerDetailsModel? ownerDetails,
    PropertyDimensionModel? dimension,
    PropertyTagInfoModel? tagInfo,
    List<PropertyImageModel>? images,
    List<PropertyAttributeModel>? attributes,
    List<String>? amenities,
    List<PropertyBrochureModel>? brochures,
    int? whatsAppShareCount,
    int? callShareCount,
    int? emailShareCount,
    int? smsShareCount,
    String? aboutProperty,
    AddressModel? address,
    List<String>? links,
    String? project,
    List<String>? assignedTo,
    List<String>? listingOnBehalf,
    bool? shouldVisisbleOnListing,
    String? placeId,
    String? propertyTypeId,
    Map<String, List<String>>? imageUrls,
    List<int>? noOfFloorsOccupied,
    String? coWorkingOperator,
    String? coWorkingOperatorName,
    String? coWorkingOperatorPhone,
    LockInPeriodType? lockInPeriod,
    NoticePeriodType? noticePeriod,
    TenantContactInfo? tenantContactInfoModel,
    List<CreateListingAddressModel>? listingAddresses,
    String? dldPermitNumber,
    String? refrenceNo,
    String? dtcmPermit,
    OfferingType? offeringType,
    CompletionStatus? completionStatus,
    String? language,
    String? titleWithLanguage,
    String? aboutPropertyWithLanguage,
    List<String>? view360Url,
    TaxationMode? taxationMode,
    bool? isWaterMarkEnabled,
    PossessionType? possesionType,
    double? securityDepositAmount,
    String? securityDepositUnit,
    List<PropertyOwnerDetailsModel>? propertyOwnerDetails,
  }) {
    bool isOfficeSpaceSelectedAndNotNull = isOfficeSpaceSelected != null && isOfficeSpaceSelected == true;
    bool isCoWorkingOfficeSpaceSelectedAndNotNull = isCoWorkingOfficeSpaceSelected != null && isCoWorkingOfficeSpaceSelected == true;
    bool isOfficeOrCoWorkingSpaceSelected = isOfficeSpaceSelectedAndNotNull || isCoWorkingOfficeSpaceSelectedAndNotNull;
    return AddPropertyModel(
        coWorkingOperator: isCoWorkingOfficeSpaceSelectedAndNotNull ? coWorkingOperator ?? this.coWorkingOperator : null,
        coWorkingOperatorName: isCoWorkingOfficeSpaceSelectedAndNotNull ? coWorkingOperatorName ?? this.coWorkingOperatorName : null,
        coWorkingOperatorPhone: isCoWorkingOfficeSpaceSelectedAndNotNull ? coWorkingOperatorPhone ?? this.coWorkingOperatorPhone : null,
        lockInPeriod: isOfficeOrCoWorkingSpaceSelected ? lockInPeriod ?? this.lockInPeriod : null,
        noticePeriod: isOfficeOrCoWorkingSpaceSelected ? noticePeriod ?? this.noticePeriod : null,
        tenantContactInfo: isOfficeOrCoWorkingSpaceSelected ? tenantContactInfoModel ?? tenantContactInfo : null,
        id: id ?? this.id,
        title: title ?? this.title,
        saleType: saleType ?? this.saleType,
        enquiredFor: enquiredFor ?? this.enquiredFor,
        notes: notes ?? this.notes,
        furnishStatus: furnishStatus ?? this.furnishStatus,
        status: status ?? this.status,
        rating: rating ?? this.rating,
        shareCount: shareCount ?? this.shareCount,
        possessionDate: possessionDate ?? this.possessionDate,
        isGOListingEnabled: isGOListingEnabled ?? this.isGOListingEnabled,
        facing: facing ?? this.facing,
        goPropertyId: goPropertyId ?? this.goPropertyId,
        noOfBHK: noOfBHK ?? this.noOfBHK,
        bhkType: bhkType ?? this.bhkType,
        monetaryInfo: monetaryInfo ?? this.monetaryInfo?.reset(isOfficeOrCoWorkingSpaceSelected: isOfficeOrCoWorkingSpaceSelected),
        ownerDetails: ownerDetails ?? this.ownerDetails,
        dimension: dimension ?? this.dimension?.reset(isOfficeOrCoWorkingSpaceSelected: isOfficeOrCoWorkingSpaceSelected),
        tagInfo: tagInfo ?? this.tagInfo,
        images: images ?? this.images,
        attributes: attributes ?? this.attributes,
        amenities: amenities ?? this.amenities,
        brochures: brochures ?? this.brochures,
        whatsAppShareCount: whatsAppShareCount ?? this.whatsAppShareCount,
        callShareCount: callShareCount ?? this.callShareCount,
        emailShareCount: emailShareCount ?? this.emailShareCount,
        smsShareCount: smsShareCount ?? this.smsShareCount,
        aboutProperty: aboutProperty ?? this.aboutProperty,
        address: address ?? this.address,
        links: links ?? this.links,
        project: project ?? this.project,
        assignedTo: assignedTo ?? this.assignedTo,
        shouldVisisbleOnListing: shouldVisisbleOnListing ?? this.shouldVisisbleOnListing,
        placeId: placeId ?? this.placeId,
        propertyTypeId: propertyTypeId ?? this.propertyTypeId,
        imageUrls: imageUrls ?? this.imageUrls,
        noOfFloorsOccupied: isOfficeOrCoWorkingSpaceSelected ? noOfFloorsOccupied ?? this.noOfFloorsOccupied : null,
        listingAddresses: listingAddresses ?? this.listingAddresses,
        dldPermitNumber: dldPermitNumber ?? this.dldPermitNumber,
        refrenceNo: refrenceNo ?? this.refrenceNo,
        dtcmPermit: dtcmPermit ?? this.dtcmPermit,
        offeringType: offeringType ?? this.offeringType,
        completionStatus: completionStatus ?? this.completionStatus,
        language: language ?? this.language,
        titleWithLanguage: titleWithLanguage ?? this.titleWithLanguage,
        aboutPropertyWithLanguage: aboutPropertyWithLanguage ?? this.aboutPropertyWithLanguage,
        view360Url: view360Url ?? this.view360Url,
        taxationMode: taxationMode ?? this.taxationMode,
        isWaterMarkEnabled: isWaterMarkEnabled ?? this.isWaterMarkEnabled,
        possesionType: possesionType ?? this.possesionType,
      securityDepositUnit: securityDepositUnit??this.securityDepositUnit,
      securityDepositAmount: securityDepositAmount??this.securityDepositAmount,
      listingOnBehalf: listingOnBehalf ?? this.listingOnBehalf,
      propertyOwnerDetails: propertyOwnerDetails ?? this.propertyOwnerDetails,
    );
  }
}

@JsonSerializable(includeIfNull: false, explicitToJson: true)
class TenantContactInfo {
  String? id;
  String? name;
  String? phone;
  String? designation;

  TenantContactInfo({
    this.id,
    this.name,
    this.phone,
    this.designation,
  });

  factory TenantContactInfo.fromJson(Map<String, dynamic> json) => _$TenantContactInfoFromJson(json);

  Map<String, dynamic> toJson() => _$TenantContactInfoToJson(this);

  TenantContactInfo copyWith({
    String? id,
    String? name,
    String? phone,
    String? designation,
  }) {
    return TenantContactInfo(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      designation: designation ?? this.designation,
    );
  }

  TenantContactInfoEntity toEntity() {
    return TenantContactInfoEntity(
      id: id,
      name: name,
      phone: phone,
      designation: designation,
    );
  }
}

@JsonSerializable(includeIfNull: false, explicitToJson: true)
class CreateListingAddressModel {
  final String? listingSourceId;
  final String? locationId;

  CreateListingAddressModel({this.listingSourceId, this.locationId});

  factory CreateListingAddressModel.fromJson(Map<String, dynamic> json) => _$CreateListingAddressModelFromJson(json);

  Map<String, dynamic> toJson() => _$CreateListingAddressModelToJson(this);

  @override
  bool operator ==(Object other) => identical(this, other) || other is CreateListingAddressModel && runtimeType == other.runtimeType && locationId == other.locationId && listingSourceId == other.listingSourceId;

  @override
  int get hashCode => Object.hash(locationId, listingSourceId);
}
