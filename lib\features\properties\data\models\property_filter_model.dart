import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_amenity_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_type_model.dart';
import 'package:leadrat/core_main/common/data/user/models/get_all_users_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/models/budget_range_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/common/date_range.dart';
import 'package:leadrat/core_main/enums/common/no_of_bhk.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/property_type_enum.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/property_enums/completion_status.dart';
import 'package:leadrat/core_main/enums/property_enums/facing.dart';
import 'package:leadrat/core_main/enums/property_enums/feature.dart';
import 'package:leadrat/core_main/enums/property_enums/floors.dart';
import 'package:leadrat/core_main/enums/property_enums/furnish_status.dart';
import 'package:leadrat/core_main/enums/property_enums/property_date_type.dart';
import 'package:leadrat/core_main/enums/property_enums/property_size.dart';
import 'package:leadrat/core_main/enums/property_enums/property_status.dart';
import 'package:leadrat/core_main/enums/property_enums/sale_type.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/features/projects/domain/entities/project_entity.dart';

class PropertyFilterModel {
  PropertyVisibility? propertyVisibility;
  EnquiryType? enquiryType;
  List<SaleType>? saleTypes;
  List<PropertyType>? propertyTypes;
  List<MasterPropertyTypeModel>? propertySubTypes;
  PropertyDateType? dateType;
  DateRange? dateRange;
  DateTime? fromDate;
  DateTime? toDate;
  Facing? facing;
  List<FurnishStatus>? furnishStatus;
  List<NoOfBHK>? noOfBHKs;
  List<BHKType>? bhkTypes;
  BudgetRangeModel? budgetFilters;
  List<Floors>? floors;
  List<String>? locations;
  List<ProjectEntity>? projects;
  List<GetAllUsersModel>? assignTo;
  List<CustomAmenityModel>? amenities;
  PropertySize? propertySize;
  String? currency;
  String? ownerName;
  List<Feature>? noOfBathrooms;
  List<Feature>? noOfLivingRooms;
  List<Feature>? noOfBalconies;
  List<Feature>? noOfBedrooms;
  List<Feature>? noOfKitchens;
  List<Feature>? noOfUtilities;
  OfferingType? offeringType;
  CompletionStatus? completionStatus;
  List<String>? communities;
  List<String>? subCommunities;
  double? minPropertySize;
  double? maxPropertySize;
  String? propertySizeUnit;
  double? minCarpetArea;
  double? maxCarpetArea;
  String? carpetAreaUnit;
  double? minBuiltUpArea;
  double? maxBuiltUpArea;
  String? builtUpAreaUnit;
  double? minSaleableArea;
  double? maxSaleableArea;
  String? saleableAreaUnit;
  double? saleableArea;
  double? carpetArea;
  double? builtUpArea;
  double? propertyArea;
  final double? fromMinBudget;
  final double? toMinBudget;
  final double? fromMaxBudget;
  final double? toMaxBudget;
  String? minLeadCount;
  String? maxLeadCount;
  String? minProspectCount;
  String? maxProspectCount;
  final PossessionType? possessionTypeDateRange;

  PropertyFilterModel({
    this.builtUpArea,
    this.carpetArea,
    this.saleableArea,
    this.propertyVisibility,
    this.enquiryType,
    this.saleTypes,
    this.propertyTypes,
    this.propertySubTypes,
    this.dateType,
    this.dateRange,
    this.fromDate,
    this.toDate,
    this.facing,
    this.furnishStatus,
    this.noOfBHKs,
    this.bhkTypes,
    this.budgetFilters,
    this.floors,
    this.locations,
    this.projects,
    this.assignTo,
    this.amenities,
    this.propertySize,
    this.currency,
    this.ownerName,
    this.noOfBathrooms,
    this.noOfLivingRooms,
    this.noOfBalconies,
    this.noOfBedrooms,
    this.noOfKitchens,
    this.noOfUtilities,
    this.completionStatus,
    this.offeringType,
    this.communities,
    this.subCommunities,
    this.minPropertySize,
    this.maxPropertySize,
    this.propertySizeUnit,
    this.minCarpetArea,
    this.maxCarpetArea,
    this.carpetAreaUnit,
    this.minBuiltUpArea,
    this.maxBuiltUpArea,
    this.builtUpAreaUnit,
    this.minSaleableArea,
    this.maxSaleableArea,
    this.saleableAreaUnit,
    this.propertyArea,
    this.fromMinBudget,
    this.toMinBudget,
    this.toMaxBudget,
    this.fromMaxBudget,
    this.minLeadCount,
    this.maxLeadCount,
    this.minProspectCount,
    this.maxProspectCount,
    this.possessionTypeDateRange,
  });

  Future<String> getFiltersUrl() async {
    String filtersUrl = '';
    if (enquiryType != null) {
      filtersUrl = '$filtersUrl&EnquiredFor=${enquiryType?.value ?? ''}';
    }
    if (saleTypes != null) {
      saleTypes?.forEach((element) => filtersUrl = '$filtersUrl&SaleTypes=${element.value}');
    }
    if (locations != null) {
      locations?.forEach((element) => filtersUrl = '$filtersUrl&Locations=$element');
    }
    if (projects != null) {
      projects?.forEach((element) => filtersUrl = '$filtersUrl&Projects=${element.name}');
    }
    if (getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.viewAssigned) && !(getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.view))) {
      filtersUrl = '$filtersUrl&UserIds=${getIt<UsersDataRepository>().getLoggedInUser()?.userId}';
    } else if (assignTo != null) {
      assignTo?.forEach((element) => filtersUrl = '$filtersUrl&UserIds=${element.id}');
    }
    if (amenities != null) {
      amenities?.forEach((element) => filtersUrl = '$filtersUrl&Amenities=${element.id}');
    }
    if (noOfBalconies != null) {
      noOfBalconies?.forEach((element) => filtersUrl = '$filtersUrl&NoOfBalconies=${element.value}');
    }

    if (noOfBedrooms != null) {
      noOfBedrooms?.forEach((element) => filtersUrl = '$filtersUrl&NoOfBedrooms=${element.value}');
    }
    if (noOfKitchens != null) {
      noOfKitchens?.forEach((element) => filtersUrl = '$filtersUrl&NoOfKitchens=${element.value}');
    }
    if (noOfLivingRooms != null) {
      noOfLivingRooms?.forEach((element) => filtersUrl = '$filtersUrl&NoOfLivingrooms=${element.value}');
    }
    if (noOfUtilities != null) {
      noOfUtilities?.forEach((element) => filtersUrl = '$filtersUrl&NoOfUtilites=${element.value}');
    }
    if (noOfBathrooms != null) {
      noOfBathrooms?.forEach((element) => filtersUrl = '$filtersUrl&noOfBathrooms=${element.value}');
    }
    if (ownerName != null) {
      filtersUrl = '$filtersUrl&OwnerName=$ownerName';
    }
    if (floors != null) {
      floors?.forEach((element) => filtersUrl = '$filtersUrl&NoOfFloor=${element.value}');
    }
    if (propertyTypes != null) {
      propertyTypes?.forEach((element) => filtersUrl = '$filtersUrl&PropertyTypes=${element.baseId}');
    }
    if (propertySubTypes != null) {
      propertySubTypes?.forEach((element) => filtersUrl = '$filtersUrl&PropertySubTypes=${element.id}');
    }
    if (furnishStatus != null) {
      furnishStatus?.forEach((element) => filtersUrl = '$filtersUrl&FurnishStatuses=${element.value}');
    }
    if (noOfBHKs != null) {
      noOfBHKs?.forEach((element) => filtersUrl = '$filtersUrl&NoOfBHK=${element.noOfBhk.toString().replaceAll(".0", "")}');
    }
    if (bhkTypes?.isNotEmpty ?? false) {
      bhkTypes?.forEach((i) => filtersUrl += "&BHKTypes=${i.value}");
    }
    if (budgetFilters != null) {
      filtersUrl = '$filtersUrl&MaxPrice=${budgetFilters?.maxBudget}&MinPrice=${budgetFilters?.minBudget}&Currency=$currency';
    }
    if (propertySize != null) {
      filtersUrl = '$filtersUrl&PropertySize.Area=${propertySize?.value}';
    }
    if (dateRange != null&& dateType != PropertyDateType.possessionDate) {
      DateTime? tempFromDate;
      DateTime? tempToDate;

      if (dateRange != DateRange.customDate) {
        tempFromDate = getFromDate(dateRange?.value ?? 0);
        tempToDate = DateTime.now().toUserTimeZone();
      } else if (dateRange == DateRange.customDate) {
        tempFromDate = fromDate;
        tempToDate = toDate;
      }
      if (dateType != null) {
        filtersUrl = '$filtersUrl&DateType=${dateType?.value}';
      }
      filtersUrl = '$filtersUrl&FromDate=${tempFromDate?.toUniversalTimeStartOfDay() ?? ''}&ToDate=${tempToDate?.toUniversalTimeStartOfDay() ?? ''}';
    }
    if (dateType != null && dateType == PropertyDateType.possessionDate && possessionTypeDateRange == PossessionType.customDate) {
      filtersUrl += "&PossesionType=${possessionTypeDateRange?.value}&FromPossesionDate=${fromDate?.toUniversalTimeStartOfDay() ?? ''}&ToPossesionDate=${toDate?.toUniversalTimeStartOfDay() ?? ''}";
    }
    if (dateType != null && dateType == PropertyDateType.possessionDate && possessionTypeDateRange != PossessionType.customDate) {
      filtersUrl += "&PossesionType=${possessionTypeDateRange?.value}";
    }
    if (facing != null) {
      filtersUrl = '$filtersUrl&Facing=${facing?.value}';
    }
    if (propertyVisibility != null) {
      filtersUrl += "&PropertyVisiblity=${propertyVisibility?.index}";
    }

    if (offeringType != OfferingType.none && offeringType != null) {
      filtersUrl += "&FirstLevelFilter=${offeringType?.index}";
    }
    if (completionStatus != CompletionStatus.none && completionStatus != null) {
      filtersUrl += "&CompletionStatus=${completionStatus?.index}";
    }
    if (communities?.isNotEmpty ?? false) {
      communities?.forEach((i) => filtersUrl += "&Communities=$i");
    }
    if (subCommunities?.isNotEmpty ?? false) {
      subCommunities?.forEach((i) => filtersUrl += "&SubCommunities=$i");
    }
    if (minPropertySize != null && minPropertySize! > 0) {
      filtersUrl += "&MinPropertySize=${minPropertySize.toString().replaceAll(".0", "")}";
    }

    if (maxPropertySize != null && maxPropertySize! > 0) {
      filtersUrl += "&MaxPropertySize=${maxPropertySize.toString().replaceAll(".0", "")}";
    }

    if (propertySizeUnit?.isNotNullOrEmpty() ?? false) {
      filtersUrl += "&PropertySizeUnit=$propertySizeUnit";
    }

    if (minCarpetArea != null && minCarpetArea! > 0) {
      filtersUrl += "&MinCarpetArea=${minCarpetArea.toString().replaceAll(".0", "")}";
    }
    if (saleableArea != null && saleableArea! > 0) {
      filtersUrl += "&PropertySize.SaleableArea=${saleableArea.toString().replaceAll(".0", "")}";
    }
    if (carpetArea != null && carpetArea! > 0) {
      filtersUrl += "&PropertySize.CarpetArea=${carpetArea.toString().replaceAll(".0", "")}";
    }
    if (builtUpArea != null && builtUpArea! > 0) {
      filtersUrl += "&PropertySize.BuildUpArea=${builtUpArea.toString().replaceAll(".0", "")}";
    }

    if (maxCarpetArea != null && maxCarpetArea! > 0) {
      filtersUrl += "&MaxCarpetArea=${maxCarpetArea.toString().replaceAll(".0", "")}";
    }

    if (carpetAreaUnit?.isNotNullOrEmpty() ?? false) {
      filtersUrl += "&CarpetAreaUnit=$carpetAreaUnit";
    }

    if (minBuiltUpArea != null && minBuiltUpArea! > 0) {
      filtersUrl += "&MinBuitUpArea=${minBuiltUpArea.toString().replaceAll(".0", "")}";
    }

    if (maxBuiltUpArea != null && maxBuiltUpArea! > 0) {
      filtersUrl += "&MaxBuitUpArea=${maxBuiltUpArea.toString().replaceAll(".0", "")}";
    }

    if (builtUpAreaUnit?.isNotNullOrEmpty() ?? false) {
      filtersUrl += "&BuitUpAreaUnit=$builtUpAreaUnit";
    }

    if (minSaleableArea != null && minSaleableArea! > 0) {
      filtersUrl += "&MinSaleableArea=${minSaleableArea.toString().replaceAll(".0", "")}";
    }

    if (maxSaleableArea != null && maxSaleableArea! > 0) {
      filtersUrl += "&MaxSaleableArea=${maxSaleableArea.toString().replaceAll(".0", "")}";
    }

    if (saleableAreaUnit?.isNotNullOrEmpty() ?? false) {
      filtersUrl += "&SaleableAreaUnit=$saleableAreaUnit";
    }
    if (propertyArea != null && propertyArea! > 0) {
      filtersUrl = '$filtersUrl&PropertySize.Area=${propertyArea.toString().replaceAll(".0", "")}';
    }
    if (fromMinBudget != null && fromMinBudget! > 0) {
      filtersUrl += "&MinPrice=${fromMinBudget.toString().replaceAll(".0", "")}";
    }
    if (toMinBudget != null && toMinBudget! > 0) {
      filtersUrl += "&MaxPrice=${toMinBudget.toString().replaceAll(".0", "")}";
    }
    if (fromMaxBudget != null && fromMaxBudget! > 0) {
      filtersUrl += "&FromMaxPrice=${fromMaxBudget.toString().replaceAll(".0", "")}";
    }
    if (toMaxBudget != null && toMaxBudget! > 0) {
      filtersUrl += "&ToMaxPrice=${toMaxBudget.toString().replaceAll(".0", "")}";
    }
    if (currency != null && (fromMinBudget != null || toMinBudget != null)) {
      filtersUrl += "&Currency=$currency";
    }
    if (minLeadCount?.isNotNullOrEmpty() ?? false) {
      filtersUrl += "&MinLeadCount=${int.parse(minLeadCount!)}";
    }
    if (maxLeadCount?.isNotNullOrEmpty() ?? false) {
      filtersUrl += "&MaxLeadCount=${int.parse(maxLeadCount!)}";
    }
    if (minProspectCount?.isNotNullOrEmpty() ?? false) {
      filtersUrl += "&MinProspectCount=${int.parse(minProspectCount!)}";
    }
    if (maxProspectCount?.isNotNullOrEmpty() ?? false) {
      filtersUrl += "&MaxProspectCount=${int.parse(maxProspectCount!)}";
    }
    return filtersUrl;
  }

  DateTime getFromDate(int days) {
    int tempDays = days;
    switch (days) {
      case 0:
        tempDays = 0;
        break;
      case 1:
        tempDays = 1;
        break;
      case 2:
        tempDays = 7;
        break;
      case 3:
        tempDays = 28;
        break;
    }
    DateTime now = DateTime.now().toUserTimeZone()!;
    return now.subtract(Duration(days: tempDays));
  }
}
