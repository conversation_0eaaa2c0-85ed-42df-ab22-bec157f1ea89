import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/common/constants/app_analytics_constants.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/widgets/confirm_action_bottom_modal.dart';
import 'package:leadrat/core_main/common/widgets/leading_icon_with_text.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_form.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_form_button.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_month_picker.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_text_form_field.dart';
import 'package:leadrat/core_main/common/widgets/lrb_phone_field/lrb_phone_field.dart';
import 'package:leadrat/core_main/common/widgets/preview_dialogue_widget.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/dialog_manager.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';
import 'package:leadrat/features/lead/presentation/bloc/add_lead_bloc/add_lead_bloc.dart';
import 'package:leadrat/features/lead/presentation/pages/lead_info_page.dart';
import 'package:leadrat/features/search_location/presentation/pages/search_location_page.dart';

class AddLeadPage extends LeadratStatefulWidget {
  final GlobalSettingModel? globalSettingModel;
  final GetLeadEntity? getLeadEntity;

  const AddLeadPage({super.key, this.globalSettingModel, this.getLeadEntity});

  @override
  State<AddLeadPage> createState() => _AddLeadPageState();
}

class _AddLeadPageState extends LeadratState<AddLeadPage> {
  final GlobalKey<FormState> _addLeadFormKey = GlobalKey<FormState>();
  final addLeadBLoc = getIt<AddLeadBloc>();
  final userDataRepository = getIt<UsersDataRepository>();
  bool canUpdateBasicInfo = true;
  bool isEditing = false;

  @override
  void initState() {
    addLeadBLoc.initTextController();
    isEditing = widget.getLeadEntity != null && (widget.getLeadEntity?.id.isNotNullOrEmpty() ?? false);
    canUpdateBasicInfo = isEditing ? getIt<UsersDataRepository>().checkHasPermission(AppModule.lead, CommandType.updateBasicInfo) : true;
    addLeadBLoc.add(AddLeadInitialEvent(globalSettingModel: widget.globalSettingModel, getLeadEntity: widget.getLeadEntity));
    getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadPageView);
    super.initState();
  }

  @override
  void dispose() {
    addLeadBLoc.add(ResetStateEvent());
    addLeadBLoc.disposeTextController();
    super.dispose();
  }

  @override
  Widget buildContent(BuildContext context) {
    return BlocConsumer<AddLeadBloc, AddLeadState>(
      listener: (context, state) {
        if (state.showDialogProgress) {
          DialogManager().showTransparentProgressDialog(context, message: "loading");
        } else {
          DialogManager().hideTransparentProgressDialog();
        }
        if (state.successMessage != null && (state.successMessage?.isNotEmpty ?? false)) {
          DialogManager().hideTransparentProgressDialog();
          LeadratCustomSnackbar.show(context: context, message: state.successMessage!, type: SnackbarType.success);
        } else if (state.errorMessage != null && (state.errorMessage?.isNotNullOrEmpty() ?? false)) {
          DialogManager().hideTransparentProgressDialog();
          LeadratCustomSnackbar.show(context: context, message: state.errorMessage!, type: SnackbarType.error);
        }
        if (state.pageState == PageState.initial && !(state.showDialogProgress)) {
          DialogManager().hideTransparentProgressDialog();
        } else if (state.pageState == PageState.failure && (state.errorMessage?.isNotEmpty ?? false)) {
          DialogManager().hideTransparentProgressDialog();
          LeadratCustomSnackbar.show(context: context, message: state.errorMessage ?? "unable to save lead", type: SnackbarType.error);
        } else if (state.pageState == PageState.loading) {
          DialogManager().showTransparentProgressDialog(context, message: state.dialogMessage);
        } else if (state.pageState == PageState.success) {
          Navigator.of(context).pop();
          DialogManager().hideTransparentProgressDialog();
        }
      },
      builder: (context, state) {
        return LeadratForm(
          formKey: _addLeadFormKey,
          appBar: AppBar(backgroundColor: ColorPalette.white, toolbarHeight: 0),
          padding: EdgeInsets.zero,
          wrapWithScrollView: false,
          leadingButton: LeadratFormButton(
              onPressed: () {
                Navigator.pop(context);
                getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonBackClick);
              },
              buttonText: "back"),
          trailingButton: LeadratFormButton(
              onPressed: () {
                if (state.isLeadAlreadyExits || state.isLeadAlreadyExitsOnAltNumber) {
                  if (state.isAssignedLoggedInUser == false) {
                    LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: 'The existing lead is not assigned to you');
                  } else {
                    Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => LeadInfoPage(GetLeadEntity(id: state.existingLeadId), null)));
                  }
                } else {
                  if (state.isDuplicateLeadBottomSheetVisible || state.isDuplicateLeadBottomSheetVisibleForAltNumber) {
                    confirmActionBottomModal(
                      imageVector: ImageResources.imageConvertToLead,
                      title: 'a lead with same ${state.primaryOrSecondary} number is already exists',
                      subTitle: 'Do you want to create a duplicate lead?',
                      cancelButtonText: 'show leadInfo',
                      successButtonText: 'create duplicate',
                      onCancel: () {
                        if (state.isAssignedLoggedInUser == false) {
                          Navigator.pop(context);
                          LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: 'The existing lead is not assigned to you');
                        } else {
                          Navigator.pop(context);
                          Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                              builder: (context) => LeadInfoPage(GetLeadEntity(id: state.existingLeadId ?? ''), null),
                            ),
                          );
                        }
                      },
                      onSuccess: () {
                        Navigator.pop(context);
                        addLeadBLoc.add(CreateLeadEvent());
                      },
                    );
                  } else if (_addLeadFormKey.currentState?.validate() ?? false) {
                    addLeadBLoc.add(CreateLeadEvent());
                  }
                }
                getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonSaveAndFinishClick);
              },
              isEnabled: state.pageState != PageState.loading,
              buttonText: (state.isLeadAlreadyExits || state.isLeadAlreadyExitsOnAltNumber) ? "show lead info" : "save & finish",
              isTrailingVisible: true),
          child: Column(
            children: [
              if (canUpdateBasicInfo)
                Container(
                  color: ColorPalette.lightPurple,
                  margin: const EdgeInsets.only(top: 10),
                  padding: const EdgeInsets.fromLTRB(24, 0, 14, 0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text("${isEditing ? 'edit' : 'add'} lead", style: LexendTextStyles.lexend14Medium.copyWith(color: ColorPalette.lightBackground)),
                      IconButton(onPressed: () => addLeadBLoc.add(PickContactEvent()), icon: SvgPicture.asset(ImageResources.iconContact, height: 24)),
                    ],
                  ),
                ),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSection(ImageResources.iconEmptyProfile, "lead info"),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(24, 10, 14, 0),
                        child: LeadratTextFormField(
                          labelText: "name",
                          isRequired: true,
                          controller: addLeadBLoc.leadNameController,
                          hintText: "ex. sachin",
                          isEnabled: canUpdateBasicInfo,
                          validator: (value) {
                            if ((value?.length ?? 0) > 75) return "Name cannot be more than 75 characters";
                            if (value == null || value.trim().isEmpty) return "Name is required field";
                            return null;
                          },
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(24, 5, 14, 0),
                        child: LrbPhoneField(
                          labelText: "phone",
                          hintText: "ex. 8861xxxxxx",
                          isRequired: true,
                          controller: addLeadBLoc.phoneController,
                          selectedDialCode: state.defaultCountryCode,
                          isEnabled: canUpdateBasicInfo,
                          onValidationChanged: (isValid, countryCode, phoneNumber) {
                            if (isValid && countryCode != null && phoneNumber != null) {
                              addLeadBLoc.add(CheckLeadContactAlreadyExistsEvent("+$countryCode", phoneNumber));
                            } else {
                              addLeadBLoc.add(OnLeadContactChangedEvent(countryCode ?? '', phoneNumber ?? ''));
                            }
                          },
                        ),
                      ),
                      if (!state.isAltPhoneFieldVisible)
                        Transform.translate(
                          offset: const Offset(0, -20),
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(24, 5, 14, 0),
                            child: LeadingIconWithText(
                                icon: ImageResources.iconAddOutlined,
                                iconHeight: 18,
                                textStyle: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.primaryGreen),
                                text: "add alternate number",
                                onTap: () {
                                  if (canUpdateBasicInfo) {
                                    addLeadBLoc.add(ToggleAltPhoneFieldEvent());
                                  } else {
                                    LeadratCustomSnackbar.show(context: context, message: 'you don\'t have permission to add alternate number', type: SnackbarType.error);
                                  }
                                }),
                          ),
                        ),
                      if (state.isAltPhoneFieldVisible) ...[
                        Padding(
                          padding: const EdgeInsets.fromLTRB(24, 0, 14, 0),
                          child: LrbPhoneField(
                            labelText: "alt phone",
                            hintText: "ex. 8861xxxxxx",
                            selectedDialCode: state.defaultCountryCode,
                            isRequired: false,
                            controller: addLeadBLoc.altPhoneController,
                            isEnabled: canUpdateBasicInfo,
                            validation: (phoneNumber, countryCode) {
                              if (phoneNumber == null || phoneNumber.isEmpty) {
                                addLeadBLoc.add(ToggleAltPhoneFieldEvent(hideAltPhoneField: state.isAltPhoneFieldVisible));
                                return null;
                              }
                              if (countryCode != null && phoneNumber == addLeadBLoc.phoneController?.text) {
                                return "Alternate number should be different";
                              }
                              return null;
                            },
                            onValidationChanged: (isValid, countryCode, phoneNumber) {
                              if (isValid && countryCode != null && phoneNumber != null) {
                                addLeadBLoc.add(CheckAltContactAlreadyExistsEvent("+$countryCode", phoneNumber));
                              }
                              addLeadBLoc.add(OnAltContactChangedEvent(countryCode ?? '', phoneNumber ?? ''));
                            },
                          ),
                        ),
                        Transform.translate(
                          offset: const Offset(0, -20),
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(24, 0, 14, 0),
                            child: LeadingIconWithText(
                              iconWidget: const Icon(Icons.remove_circle_outline_rounded, color: ColorPalette.fadedRed, size: 20),
                              textStyle: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.fadedRed),
                              text: "remove alt phone",
                              onTap: () {
                                if (canUpdateBasicInfo) {
                                  addLeadBLoc.add(ToggleAltPhoneFieldEvent());
                                } else {
                                  LeadratCustomSnackbar.show(context: context, message: 'you don\'t have permission to remove alt phone number', type: SnackbarType.error);
                                }
                              },
                            ),
                          ),
                        ),
                      ],
                      Transform.translate(
                        offset: const Offset(0, -20),
                        child: ListTile(
                          contentPadding: const EdgeInsets.fromLTRB(24, 0, 14, 0),
                          title: Text("email", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
                          trailing: LeadingIconWithText(
                            icon: ImageResources.iconAddOutlined,
                            iconHeight: 18,
                            iconWidget: state.isEmailFieldVisible ? const Icon(Icons.remove_circle_outline_rounded, color: ColorPalette.fadedRed, size: 20) : null,
                            textStyle: LexendTextStyles.lexend10SemiBold.copyWith(color: state.isEmailFieldVisible ? ColorPalette.fadedRed : ColorPalette.primaryGreen),
                            text: state.isEmailFieldVisible ? "remove email" : "add email",
                            onTap: () {
                              if (canUpdateBasicInfo) {
                                addLeadBLoc.add(ToggleEmailFieldEvent());
                              } else {
                                LeadratCustomSnackbar.show(context: context, message: 'you don\'t have permission to ${state.isEmailFieldVisible ? "remove" : "add"} email', type: SnackbarType.error);
                              }
                            },
                          ),
                        ),
                      ),
                      if (state.isEmailFieldVisible)
                        Transform.translate(
                          offset: const Offset(0, -20),
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(24, 0, 14, 0),
                            child: LeadratTextFormField(
                              labelText: null,
                              controller: addLeadBLoc.emailController,
                              hintText: "ex. <EMAIL>",
                              isEnabled: canUpdateBasicInfo,
                              validator: (value) {
                                if ((value?.isValidEmail() ?? false) || (value?.isEmpty ?? false))  return null;
                                return "Enter a valid email address";
                              },
                            ),
                          ),
                        ),
                      if (!state.isReferralDetailsVisible)
                        Transform.translate(
                          offset: const Offset(0, -20),
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(24, 0, 14, 0),
                            child: LeadingIconWithText(
                              icon: ImageResources.iconAddOutlined,
                              textStyle: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.primaryGreen),
                              text: "add referral details",
                              onTap: () => addLeadBLoc.add(ToggleReferralFieldsEvent()),
                            ),
                          ),
                        ),
                      if (state.isReferralDetailsVisible) ...[
                        Padding(
                          padding: const EdgeInsets.fromLTRB(24, 0, 14, 0),
                          child: LeadratTextFormField(labelText: "referral name", hintText: "ex. sachin chavan", controller: addLeadBLoc.referralNameController),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(24, 0, 14, 0),
                          child: LrbPhoneField(
                            labelText: "referral phone number",
                            hintText: "ex. 9901XXXXXX",
                            isRequired: false,
                            controller: addLeadBLoc.referralPhoneController,
                            selectedDialCode: state.defaultCountryCode,
                            onValidationChanged: (isValid, countryCode, phoneNumber) {
                              if (isValid && countryCode != null && phoneNumber != null) {
                                addLeadBLoc.add(OnReferralContactChangedEvent(countryCode, phoneNumber));
                              }
                            },
                          ),
                        ),
                        const SizedBox(height: 14),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(24, 0, 14, 0),
                          child: LeadratTextFormField(
                            labelText: 'referral email',
                            controller: addLeadBLoc.referralEmailController,
                            hintText: "ex. <EMAIL>",
                            validator: (value) {
                              if ((value?.isValidEmail() ?? false) || (value?.isEmpty ?? false)) return null;
                              return "Enter a valid email address";
                            },
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(24, 0, 14, 0),
                          child: LeadingIconWithText(
                            iconWidget: const Icon(Icons.remove_circle_outline_rounded, color: ColorPalette.fadedRed, size: 20),
                            iconHeight: 18,
                            textStyle: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.fadedRed),
                            text: "remove",
                            onTap: () => addLeadBLoc.add(ToggleReferralFieldsEvent()),
                          ),
                        ),
                      ],
                      _buildSection(ImageResources.iconEnquiry, "enquiry info"),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(24, 0, 14, 0),
                        child: _buildEnquiryInfo(state),
                      ),
                      _buildSection(ImageResources.iconAdditionalInfo, "additional info"),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(24, 10, 14, 0),
                        child: buildAdditionalInfo(state),
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(24, 10, 14, 0),
                        child: buildOthers(state),
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }

  _buildEnquiryInfo(AddLeadState state) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("enquired for", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
        const SizedBox(height: 10),
        Wrap(
          spacing: 8,
          children: state.enquiredFor.map(
            (enquiredFor) {
              return RawChip(
                onPressed: () => addLeadBLoc.add(ToggleEnquiredForEvent(enquiredFor)),
                label: Text(enquiredFor.title, style: LexendTextStyles.lexend9SemiBold.copyWith(color: enquiredFor.isSelected ? ColorPalette.white : ColorPalette.tertiaryTextColor), softWrap: true, overflow: TextOverflow.ellipsis),
                backgroundColor: enquiredFor.isSelected ? ColorPalette.primaryGreen : ColorPalette.white,
                visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
                shape: StadiumBorder(side: BorderSide(color: enquiredFor.isSelected ? ColorPalette.leadratBgGreen : ColorPalette.veryLightGray)),
              );
            },
          ).toList(),
        ),
        const SizedBox(height: 18),
        Text("property type", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
        const SizedBox(height: 10),
        Wrap(
          spacing: 8,
          children: state.propertyTypes.map(
            (type) {
              return RawChip(
                onPressed: () => addLeadBLoc.add(TogglePropertyTypeEvent(type)),
                label: Text(type.title, style: LexendTextStyles.lexend9SemiBold.copyWith(color: type.isSelected ? ColorPalette.white : ColorPalette.tertiaryTextColor), softWrap: true, overflow: TextOverflow.ellipsis),
                backgroundColor: type.isSelected ? ColorPalette.primaryGreen : ColorPalette.white,
                visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
                shape: StadiumBorder(side: BorderSide(color: type.isSelected ? ColorPalette.leadratBgGreen : ColorPalette.veryLightGray)),
              );
            },
          ).toList(),
        ),
        if (state.propertySubTypes.isNotEmpty) ...[
          const SizedBox(height: 18),
          Text.rich(TextSpan(style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor), children: [
            const TextSpan(text: "property sub-type"),
            TextSpan(text: " *", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.fadedRed)),
          ])),
          const SizedBox(height: 10),
          _buildPropertySubTypes(state),
        ],
        if (state.noOfBhk.isNotEmpty) ...[
          const SizedBox(height: 18),
          Text("BHK", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
          const SizedBox(height: 10),
          _buildNoOfBhk(state),
        ],
        if (state.bhkTypes.isNotEmpty) ...[
          const SizedBox(height: 18),
          Text("BHK Type", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
          const SizedBox(height: 10),
          Wrap(
            spacing: 8,
            children: state.bhkTypes.map(
              (type) {
                return RawChip(
                  onPressed: () => addLeadBLoc.add(ToggleBhkTypeEvent(type)),
                  label: Text(type.title, style: LexendTextStyles.lexend9SemiBold.copyWith(color: type.isSelected ? ColorPalette.white : ColorPalette.tertiaryTextColor), softWrap: true, overflow: TextOverflow.ellipsis),
                  backgroundColor: type.isSelected ? ColorPalette.primaryGreen : ColorPalette.white,
                  visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
                  shape: StadiumBorder(side: BorderSide(color: type.isSelected ? ColorPalette.leadratBgGreen : ColorPalette.veryLightGray)),
                );
              },
            ).toList(),
          ),
        ],
        const SizedBox(height: 18),
        Row(
          children: [
            Expanded(
              child: LeadratTextFormField(
                labelText: "min. budget",
                controller: addLeadBLoc.minBudgetController,
                keyboardType: TextInputType.number,
                hintText: "ex. 100",
                showBudgetInWords: true,
                currency: state.selectedCurrency?.value,
                defaultPrefixIcon: false,
                prefixIcon: SelectableItemBottomSheet(
                    title: "select currency",
                    selectableItems: state.currencies,
                    selectedItem: state.selectedCurrency,
                    onItemSelected: (selectedValue) {
                      addLeadBLoc.add(SelectCurrency(selectedValue));
                    },
                    child: Container(
                      margin: const EdgeInsets.all(4),
                      padding: const EdgeInsets.all(14),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(2),
                        color: ColorPalette.primaryTextColor,
                      ),
                      child: Text(state.selectedCurrency?.title ?? "INR", maxLines: 1, overflow: TextOverflow.fade, style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.darkToneInk)),
                    )),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) return null;
                  final budget = int.tryParse(value);
                  if (budget == null || budget <= 0) return 'Invalid data';
                  final minBudget = int.tryParse(addLeadBLoc.minBudgetController?.text ?? '0');
                  final maxBudget = int.tryParse(value);
                  if (minBudget != null && maxBudget != null && minBudget > maxBudget) return "mix budget can't be larger than max budget";
                  return null;
                },
              ),
            ),
            const SizedBox(width: 10),
            Expanded(
              child: LeadratTextFormField(
                labelText: "max. budget",
                controller: addLeadBLoc.maxBudgetController,
                keyboardType: TextInputType.number,
                hintText: "ex. 1000000",
                showBudgetInWords: true,
                currency: state.selectedCurrency?.value,
                validator: (value) {
                  if (value == null || value.isEmpty) return null;
                  final budget = int.tryParse(value);
                  if (budget == null || budget <= 0) return 'Invalid data';
                  final minBudget = int.tryParse(addLeadBLoc.minBudgetController?.text ?? '0');
                  final maxBudget = int.tryParse(value);
                  if (minBudget != null && maxBudget != null && minBudget > maxBudget) return "max budget can't be less than min budget";
                  return null;
                },
                defaultPrefixIcon: false,
                prefixIcon: SelectableItemBottomSheet(
                    title: "select currency",
                    selectableItems: state.currencies,
                    selectedItem: state.selectedCurrency,
                    onItemSelected: (selectedValue) {
                      addLeadBLoc.add(SelectCurrency(selectedValue));
                    },
                    child: Container(
                      margin: const EdgeInsets.all(4),
                      padding: const EdgeInsets.all(14),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(2),
                        color: ColorPalette.primaryTextColor,
                      ),
                      child: Text(state.selectedCurrency?.title ?? "INR", maxLines: 1, overflow: TextOverflow.fade, style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.darkToneInk)),
                    )),
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text("select properties", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
            const SizedBox(width: 10),
            Flexible(
              child: SelectableItemBottomSheet(
                title: "select properties",
                selectableItems: state.properties,
                initialSelectedItems: state.selectedProperties,
                isMultipleSelection: true,
                onItemsSelected: (selectedItems) => addLeadBLoc.add(SelectPropertiesEvent(selectedItems)),
                canSearchItems: true,
                canAddNewItems: true,
              ),
            ),
          ],
        ),
        if (state.selectedProperties?.isNotEmpty ?? false) ...[
          const SizedBox(height: 10),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Wrap(
              spacing: 5,
              children: state.selectedProperties!.map((property) {
                return RawChip(
                  onDeleted: () => addLeadBLoc.add(RemovePropertyEvent(property)),
                  deleteIcon: const Center(child: Icon(Icons.close_rounded)),
                  label: Text(property.title, style: LexendTextStyles.lexend9SemiBold.copyWith(color: ColorPalette.primaryColor), softWrap: true, overflow: TextOverflow.ellipsis),
                  backgroundColor: ColorPalette.primaryTextColor,
                  visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
                  shape: const StadiumBorder(side: BorderSide(color: ColorPalette.primary)),
                );
              }).toList(),
            ),
          ),
        ],
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text("select projects", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
            const SizedBox(width: 10),
            Flexible(
              child: SelectableItemBottomSheet(
                title: "select projects",
                selectableItems: state.projects,
                initialSelectedItems: state.selectedProjects,
                isMultipleSelection: true,
                onItemsSelected: (selectedItems) => addLeadBLoc.add(SelectProjectsEvent(selectedItems)),
                canSearchItems: true,
                canAddNewItems: true,
              ),
            ),
          ],
        ),
        if (state.selectedProjects?.isNotEmpty ?? false) ...[
          const SizedBox(height: 10),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Wrap(
              spacing: 5,
              children: state.selectedProjects!.map((projects) {
                return RawChip(
                  onDeleted: () => addLeadBLoc.add(RemoveProjectsEvent(projects)),
                  deleteIcon: const Center(child: Icon(Icons.close_rounded)),
                  label: Text(projects.title, style: LexendTextStyles.lexend9SemiBold.copyWith(color: ColorPalette.primaryColor), softWrap: true, overflow: TextOverflow.ellipsis),
                  backgroundColor: ColorPalette.primaryTextColor,
                  visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
                  shape: const StadiumBorder(side: BorderSide(color: ColorPalette.primary)),
                );
              }).toList(),
            ),
          ),
        ],
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text("location", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
            Padding(
              padding: const EdgeInsets.only(top: 10, bottom: 10, left: 10),
              child: LeadingIconWithText(
                icon: ImageResources.iconSearch,
                iconColor: ColorPalette.primaryGreen,
                iconHeight: 18,
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => SearchLocationPage(
                          initialAddress: isEditing ? state.locations.firstOrNull?.value : null,
                          onLocationSelected: (address) {
                            addLeadBLoc.add(AddLocationEvent(address));
                            FocusManager.instance.primaryFocus?.unfocus();
                          },
                        ),
                      ));
                  getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonLocationClick);
                },
                textStyle: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.primaryGreen),
                text: "search location",
              ),
            ),
          ],
        ),
        if (state.locations.isNotEmpty) ...[
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Wrap(
              spacing: 5,
              children: state.locations.map((address) {
                return RawChip(
                  onDeleted: () => addLeadBLoc.add(RemoveLocationEvent(address)),
                  deleteIcon: const Center(child: Icon(Icons.close_rounded)),
                  label: Text(address.title, style: LexendTextStyles.lexend9SemiBold.copyWith(color: ColorPalette.primaryColor), softWrap: true, overflow: TextOverflow.ellipsis),
                  backgroundColor: ColorPalette.primaryTextColor,
                  visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
                  shape: const StadiumBorder(side: BorderSide(color: ColorPalette.primary)),
                );
              }).toList(),
            ),
          ),
        ],
        const SizedBox(height: 5),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
              child: LeadratTextFormField(
                labelText: "carpet area",
                hintText: "ex.10",
                controller: addLeadBLoc.carpetAreaController,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if ((value?.isEmpty ?? false) || value == null) return null;
                  var carpet = double.tryParse(value);
                  if (carpet == null || carpet <= 0.0) return 'Invalid carpet area';
                  return null;
                },
              ),
            ),
            const SizedBox(width: 10),
            SelectableItemBottomSheet(
                title: "select carpet area",
                childPadding: const EdgeInsets.only(bottom: 4),
                selectableItems: state.carpetAreas,
                selectedItem: state.selectedCarpetArea,
                onItemSelected: (selectedItem) {
                  addLeadBLoc.add(SelectCarpetAreaEvent(selectedItem));
                }),
          ],
        ),
        const SizedBox(height: 5),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
                child: LeadratTextFormField(
              labelText: "saleable area",
              hintText: "ex.10",
              controller: addLeadBLoc.saleableAreaAreaController,
              keyboardType: TextInputType.number,
              validator: (value) {
                if ((value?.isEmpty ?? false) || value == null) return null;
                var saleable = double.tryParse(value);
                if (saleable == null || saleable <= 0.0) return 'Invalid saleable area';
                return null;
              },
            )),
            const SizedBox(width: 10),
            SelectableItemBottomSheet(
                title: "select saleable area",
                childPadding: const EdgeInsets.only(bottom: 4),
                selectableItems: state.saleableAreas,
                selectedItem: state.selectedSaleableArea,
                onItemSelected: (selectedItem) {
                  addLeadBLoc.add(SelectSaleableAreaEvent(selectedItem));
                }),
          ],
        ),
        const SizedBox(height: 5),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
                child: LeadratTextFormField(
              labelText: "built up area",
              hintText: "ex.10",
              controller: addLeadBLoc.builtUpAreaController,
              keyboardType: TextInputType.number,
              validator: (value) {
                if ((value?.isEmpty ?? false) || value == null) return null;
                var builtUp = double.tryParse(value);
                if (builtUp == null || builtUp <= 0.0) return 'Invalid builtUp area';
                return null;
              },
            )),
            const SizedBox(width: 10),
            SelectableItemBottomSheet(
                title: "select built up area",
                childPadding: const EdgeInsets.only(bottom: 4),
                selectableItems: state.builtUpAreas,
                selectedItem: state.selectedBuiltUpArea,
                onItemSelected: (selectedItem) {
                  addLeadBLoc.add(SelectBuiltUpAreaEvent(selectedItem));
                }),
          ],
        ),
        const SizedBox(height: 5),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text("purpose", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
            const SizedBox(width: 10),
            Flexible(
              child: SelectableItemBottomSheet(
                title: "select purpose",
                selectableItems: state.purposes,
                isEnabled: state.purposes.isNotEmpty,
                selectedItem: state.selectedPurpose,
                disabledSnackBarMessage: 'no purpose found',
                canSearchItems: true,
                onItemSelected: (selectedItem) {
                  addLeadBLoc.add(SelectPurposeEvent(selectedItem));
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LeadingIconWithText(
          icon: ImageResources.iconAddOutlined,
          iconWidget: state.isPossessionDateVisible ? const Icon(Icons.remove_circle_outline_rounded, color: ColorPalette.fadedRed, size: 20) : null,
          textStyle: LexendTextStyles.lexend10SemiBold.copyWith(color: state.isPossessionDateVisible ? ColorPalette.fadedRed : ColorPalette.primaryGreen),
          text: state.isPossessionDateVisible ? "remove possession date" : "add possession date",
          onTap: () => addLeadBLoc.add(TogglePossessionDateEvent()),
        ),
        const SizedBox(height: 10),
        if (state.isPossessionDateVisible) ...[
          Text("possession needed by", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
          const SizedBox(height: 10),
          SelectableItemBottomSheet<PossessionType?>(
            title: "select possession type ",
            selectableItems: state.possessionTypeSelectableItems ?? [],
            selectedItem: state.possessionTypeSelectedItem,
            onItemSelected: (selectedValue) {
              addLeadBLoc.add(SelectPossessionType(selectedValue));
            },
            child: Container(
              width: double.infinity,
              height: 50,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(5),
                border: Border.all(
                  color: const Color(0xFFE0E0E0),
                  width: 1.5,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: MediaQuery.of(context).size.width * 0.15,
                    margin: const EdgeInsets.all(2),
                    height: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2),
                      color: ColorPalette.primaryTextColor,
                    ),
                    child: const Icon(Icons.calendar_today),
                  ),
                  const SizedBox(
                    width: 7,
                  ),
                  Text(
                    state.possessionTypeSelectedItem?.title ?? '',
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.primaryDarkColor,
                    ),
                  )
                ],
              ),
            ),
          ),
          if (state.isPossessionDateCustomSelected) ...[
            const SizedBox(
              height: 20,
            ),
            Text(
              "possession date",
              style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
            ),
            const SizedBox(
              height: 10,
            ),
            LeadratMonthPicker(
              selectedDate: state.possessionDate,
              onMonthSelected: (value) {
                addLeadBLoc.add(SelectPossessionDateEvent(value));
              },
            ),
          ],
        ],
        const SizedBox(height: 10),
      ],
    );
  }

  buildAdditionalInfo(AddLeadState state) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!isEditing || (userDataRepository.checkHasPermission(AppModule.lead, CommandType.updateSource))) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text("lead source", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
              const SizedBox(width: 10),
              Flexible(
                child: SelectableItemBottomSheet(
                    title: "select lead source",
                    selectableItems: state.leadSource,
                    canSearchItems: true,
                    selectedItem: state.selectedLeadSource,
                    isEnabled: isEditing ? (state.globalSettingModel?.isLeadSourceEditable ?? false) : true,
                    disabledSnackBarMessage: "You don't have permission to modify the source",
                    onItemSelected: (selectedItem) {
                      addLeadBLoc.add(SelectLeadSourceEvent(selectedItem));
                    }),
              ),
            ],
          ),
          if (state.leadSubSource.isNotEmpty) ...[
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text("lead sub-source", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
                const SizedBox(width: 10),
                Flexible(
                  child: SelectableItemBottomSheet(
                      title: "select lead sub-source",
                      selectableItems: state.leadSubSource,
                      canSearchItems: true,
                      selectedItem: state.selectedLeadSubSource,
                      onItemSelected: (selectedItem) {
                        addLeadBLoc.add(SelectLeadSubSourceEvent(selectedItem));
                      }),
                ),
              ],
            ),
          ],
        ],
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text("agency name", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
            const SizedBox(width: 10),
            Flexible(
              child: SelectableItemBottomSheet(
                title: "select agency name",
                isMultipleSelection: true,
                selectableItems: state.agencyNames,
                canSearchItems: true,
                onItemsSelected: (selectedItem) {
                  addLeadBLoc.add(SelectAgencyNameEvent(selectedItem));
                },
                initialSelectedItems: state.selectedAgencyNames,
              ),
            ),
          ],
        ),
        if (state.selectedAgencyNames?.isNotEmpty ?? false) ...[
          const SizedBox(height: 10),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Wrap(
              spacing: 5,
              children: state.selectedAgencyNames!.map((agencyName) {
                return RawChip(
                  onDeleted: () => addLeadBLoc.add(RemoveAgencyNameEvent(agencyName)),
                  deleteIcon: const Center(child: Icon(Icons.close_rounded)),
                  label: Text(agencyName.title, style: LexendTextStyles.lexend9SemiBold.copyWith(color: ColorPalette.primaryColor), softWrap: true, overflow: TextOverflow.ellipsis),
                  backgroundColor: ColorPalette.primaryTextColor,
                  visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
                  shape: const StadiumBorder(side: BorderSide(color: ColorPalette.primary)),
                );
              }).toList(),
            ),
          ),
        ],
        const SizedBox(height: 20),
        if (userDataRepository.checkHasPermission(AppModule.lead, CommandType.assign)) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text((state.globalSettingModel?.isDualOwnershipEnabled ?? false) ? "assign lead to" : "assign primary", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
              const SizedBox(width: 10),
              Flexible(
                child: SelectableItemBottomSheet(
                    title: "select user ",
                    canSearchItems: true,
                    selectableItems: state.assignToUsers,
                    selectedItem: state.selectedAssignedUser,
                    onItemSelected: (selectedItem) {
                      addLeadBLoc.add(SelectAssignedUserEvent(selectedItem));
                    }),
              ),
            ],
          ),
          const SizedBox(height: 10),
          if ((state.globalSettingModel?.isDualOwnershipEnabled ?? false) && state.selectedAssignedUser != null)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text("assign secondary", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
                const SizedBox(width: 10),
                Flexible(
                  child: SelectableItemBottomSheet(
                      title: "select user ",
                      canSearchItems: true,
                      selectedItem: state.selectedSecondaryAssignedUser,
                      selectableItems: state.secondaryUsers,
                      onItemSelected: (selectedItem) {
                        addLeadBLoc.add(SelectSecondaryUserEvent(selectedItem));
                      }),
                ),
              ],
            ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text("customer location", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
              Padding(
                padding: const EdgeInsets.only(top: 10, bottom: 10, left: 10),
                child: LeadingIconWithText(
                  icon: ImageResources.iconSearch,
                  iconColor: ColorPalette.primaryGreen,
                  iconHeight: 18,
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => SearchLocationPage(
                            onLocationSelected: (address) {
                              addLeadBLoc.add(AddCustomerLocationEvent(address));
                              FocusManager.instance.primaryFocus?.unfocus();
                            },
                            isCustomLeadFormEnabled: true,
                          ),
                        ));
                    getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonCustomerLocationClick);
                  },
                  textStyle: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.primaryGreen),
                  text: "search location",
                ),
              ),
            ],
          ),
          if (state.customerLocations != null && state.customerLocations?.title != 'None') ...[
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: RawChip(
                onDeleted: () => addLeadBLoc.add(RemoveCustomerLocationEvent(state.customerLocations)),
                deleteIcon: const Center(child: Icon(Icons.close_rounded)),
                label: Text(state.customerLocations?.title ?? 'NA', style: LexendTextStyles.lexend9SemiBold.copyWith(color: ColorPalette.primaryColor), softWrap: true, overflow: TextOverflow.ellipsis),
                backgroundColor: ColorPalette.primaryTextColor,
                visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
                shape: const StadiumBorder(side: BorderSide(color: ColorPalette.primary)),
              ),
            ),
          ],
        ],
        const SizedBox(height: 10),
        Text("profession", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
        const SizedBox(height: 10),
        Wrap(
          spacing: 8,
          children: state.professions.map(
            (profession) {
              return RawChip(
                onPressed: () => addLeadBLoc.add(ToggleProfessionEvent(profession)),
                label: Text(profession.title, style: LexendTextStyles.lexend9SemiBold.copyWith(color: profession.isSelected ? ColorPalette.white : ColorPalette.tertiaryTextColor), softWrap: true, overflow: TextOverflow.ellipsis),
                backgroundColor: profession.isSelected ? ColorPalette.primaryGreen : ColorPalette.white,
                visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
                shape: StadiumBorder(side: BorderSide(color: profession.isSelected ? ColorPalette.leadratBgGreen : ColorPalette.veryLightGray)),
              );
            },
          ).toList(),
        ),
        const SizedBox(height: 18),
        LeadratTextFormField(labelText: "designation", hintText: "ex. customer support", controller: addLeadBLoc.designationController),
        LeadratTextFormField(labelText: "company name", hintText: "ex. eyz", controller: addLeadBLoc.companyNameController),
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text("channel partner name", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
            const SizedBox(width: 10),
            Flexible(
              child: SelectableItemBottomSheet(
                title: "select channel partner name",
                isMultipleSelection: true,
                selectableItems: state.channelPartners,
                canSearchItems: true,
                onItemsSelected: (selectedItem) {
                  addLeadBLoc.add(SelectChannelPartnerEvent(selectedItem));
                },
                initialSelectedItems: state.selectedChannelPartners,
              ),
            ),
          ],
        ),
        if (state.selectedChannelPartners.isNotEmpty) ...[
          const SizedBox(height: 10),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Wrap(
              spacing: 5,
              children: state.selectedChannelPartners.map((channelPartnerName) {
                return RawChip(
                  onDeleted: () => addLeadBLoc.add(RemoveChannelPartnerNameEvent(channelPartnerName)),
                  deleteIcon: const Center(child: Icon(Icons.close_rounded)),
                  label: Text(channelPartnerName.title, style: LexendTextStyles.lexend9SemiBold.copyWith(color: ColorPalette.primaryColor), softWrap: true, overflow: TextOverflow.ellipsis),
                  backgroundColor: ColorPalette.primaryTextColor,
                  visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
                  shape: const StadiumBorder(side: BorderSide(color: ColorPalette.primary)),
                );
              }).toList(),
            ),
          ),
        ],
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text("campaign name", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor)),
            const SizedBox(width: 10),
            Flexible(
              child: SelectableItemBottomSheet(
                title: "select campaign name",
                isMultipleSelection: true,
                selectableItems: state.campaignNames,
                canSearchItems: true,
                isEnabled: state.campaignNames.isNotEmpty,
                disabledSnackBarMessage: 'no campaign found',
                onItemsSelected: (selectedItem) {
                  addLeadBLoc.add(SelectCampaignNameEvent(selectedItem));
                },
                initialSelectedItems: state.selectedCampaignNames,
              ),
            ),
          ],
        ),
        if (state.selectedCampaignNames?.isNotEmpty ?? false) ...[
          const SizedBox(height: 10),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Wrap(
              spacing: 5,
              children: state.selectedCampaignNames!.map((campaignName) {
                return RawChip(
                  onDeleted: () => addLeadBLoc.add(RemoveCampaignNameEvent(campaignName)),
                  deleteIcon: const Center(child: Icon(Icons.close_rounded)),
                  label: Text(campaignName.title, style: LexendTextStyles.lexend9SemiBold.copyWith(color: ColorPalette.primaryColor), softWrap: true, overflow: TextOverflow.ellipsis),
                  backgroundColor: ColorPalette.primaryTextColor,
                  visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
                  shape: const StadiumBorder(side: BorderSide(color: ColorPalette.primary)),
                );
              }).toList(),
            ),
          ),
        ],
      ],
    );
  }

  Widget buildOthers(AddLeadState state) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "sourcing manager",
              style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
            ),
            const SizedBox(width: 10),
            Flexible(
              child: SelectableItemBottomSheet(
                title: "select user",
                canSearchItems: true,
                selectableItems: state.sourcingManager,
                selectedItem: state.selectedSourcingManager,
                onItemSelected: (selectedItem) {
                  addLeadBLoc.add(SelectSourcingManagerEvent(selectedItem));
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "closing manager",
              style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
            ),
            const SizedBox(width: 10),
            Flexible(
              child: SelectableItemBottomSheet(
                title: "select user",
                canSearchItems: true,
                selectableItems: state.closingManager,
                selectedItem: state.selectedClosingManager,
                onItemSelected: (selectedItem) {
                  addLeadBLoc.add(SelectClosingManagerEvent(selectedItem));
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),
        LeadratTextFormField(
          labelText: "notes",
          hintText: "ex. you can add additional information about your lead and their requirements...",
          maxLines: 4,
          controller: addLeadBLoc.notesController,
          isRequired: (state.globalSettingModel?.leadNotesSetting?.isNotesMandatoryEnabled ?? false) && (state.globalSettingModel?.leadNotesSetting?.isNotesMandatoryOnAddLead ?? false),
          previewFunction: () => previewDialogueWidget(
            context,
            controller: addLeadBLoc.notesController,
            previewText: "Notes",
          ),
        ),
      ],
    );
  }

  Widget _buildSection(String iconResources, String title) {
    return Container(
      width: context.width(100),
      color: ColorPalette.whiteSolid,
      margin: const EdgeInsets.only(top: 10, bottom: 10),
      padding: const EdgeInsets.fromLTRB(24, 8, 14, 8),
      child: LeadingIconWithText(
        icon: iconResources,
        textStyle: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.lightDarkBackground),
        text: title,
      ),
    );
  }

  _buildPropertySubTypes(AddLeadState state) {
    final allPropertySubtypes = state.propertySubTypes;
    final int extraTagCount = allPropertySubtypes.length - 4;
    final bool showAllPropertySubTypes = state.isSubTypesExpanded;

    return Wrap(spacing: 8, children: [
      ...state.propertySubTypes.take(showAllPropertySubTypes ? allPropertySubtypes.length : 4).map(
        (subType) {
          return RawChip(
            onPressed: () => addLeadBLoc.add(TogglePropertySubTypeEvent(subType)),
            label: Text(subType.title, style: LexendTextStyles.lexend9SemiBold.copyWith(color: subType.isSelected ? ColorPalette.white : ColorPalette.tertiaryTextColor), softWrap: true, overflow: TextOverflow.ellipsis),
            backgroundColor: subType.isSelected ? ColorPalette.primaryGreen : ColorPalette.white,
            visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
            shape: StadiumBorder(side: BorderSide(color: subType.isSelected ? ColorPalette.leadratBgGreen : ColorPalette.veryLightGray)),
          );
        },
      ).toList(),
      if (!showAllPropertySubTypes && extraTagCount > 0)
        RawChip(
            onPressed: () => addLeadBLoc.add(ToggleSubTypesExpandedEvent()),
            label: Text("view all", style: LexendTextStyles.lexend9SemiBold.copyWith(color: ColorPalette.primaryGreen), softWrap: true, overflow: TextOverflow.ellipsis),
            backgroundColor: ColorPalette.white,
            visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
            shape: const StadiumBorder(
              side: BorderSide(color: ColorPalette.leadratBgGreen),
            )),
      if (showAllPropertySubTypes && allPropertySubtypes.length > 4)
        RawChip(
            onPressed: () => addLeadBLoc.add(ToggleSubTypesExpandedEvent()),
            label: Text("view less", style: LexendTextStyles.lexend9SemiBold.copyWith(color: ColorPalette.primaryGreen), softWrap: true, overflow: TextOverflow.ellipsis),
            backgroundColor: ColorPalette.white,
            visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
            shape: const StadiumBorder(
              side: BorderSide(color: ColorPalette.leadratBgGreen),
            )),
    ]);
  }

  _buildNoOfBhk(AddLeadState state) {
    final allNoOfBhk = state.noOfBhk;
    final int extraTagCount = allNoOfBhk.length - 10;
    final bool showAllNoOfBhk = state.isNoOfBhkExpanded;

    return Wrap(spacing: 8, children: [
      ...state.noOfBhk.take(showAllNoOfBhk ? allNoOfBhk.length : 10).map(
        (bhk) {
          return RawChip(
            onPressed: () => addLeadBLoc.add(ToggleNoOfBhkEvent(bhk)),
            label: Text(bhk.title, style: LexendTextStyles.lexend9SemiBold.copyWith(color: bhk.isSelected ? ColorPalette.white : ColorPalette.tertiaryTextColor), softWrap: true, overflow: TextOverflow.ellipsis),
            backgroundColor: bhk.isSelected ? ColorPalette.primaryGreen : ColorPalette.white,
            visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
            shape: StadiumBorder(side: BorderSide(color: bhk.isSelected ? ColorPalette.leadratBgGreen : ColorPalette.veryLightGray)),
          );
        },
      ).toList(),
      if (!showAllNoOfBhk && extraTagCount > 0)
        RawChip(
          onPressed: () => addLeadBLoc.add(ToggleNoOfBhkExpandedEvent()),
          label: Text("5+ BHK", style: LexendTextStyles.lexend9SemiBold.copyWith(color: ColorPalette.tertiaryTextColor), softWrap: true, overflow: TextOverflow.ellipsis),
          backgroundColor: ColorPalette.white,
          visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
          shape: const StadiumBorder(
            side: BorderSide(color: ColorPalette.veryLightGray),
          ),
        ),
      if (showAllNoOfBhk)
        RawChip(
            onPressed: () => addLeadBLoc.add(ToggleNoOfBhkExpandedEvent()),
            label: Text("view less", style: LexendTextStyles.lexend9SemiBold.copyWith(color: ColorPalette.primaryGreen), softWrap: true, overflow: TextOverflow.ellipsis),
            backgroundColor: ColorPalette.white,
            visualDensity: const VisualDensity(horizontal: -2, vertical: -1),
            shape: const StadiumBorder(
              side: BorderSide(color: ColorPalette.leadratBgGreen),
            )),
    ]);
  }
}
