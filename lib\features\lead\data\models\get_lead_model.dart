import 'dart:core';

import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/data/flags/models/flag_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_lead_source_model.dart';
import 'package:leadrat/core_main/common/models/address_model.dart';
import 'package:leadrat/core_main/common/models/dto_with_name_model.dart';
import 'package:leadrat/core_main/common/models/enquiry_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/common/contact_type_enum.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/purpose_enum.dart';
import 'package:leadrat/core_main/enums/leads/profession.dart';
import 'package:leadrat/core_main/mapper/lead_mapper.dart';
import 'package:leadrat/features/lead/data/models/lead_call_log_model.dart';
import 'package:leadrat/features/lead/data/models/lead_document_model.dart';
import 'package:leadrat/features/lead/data/models/lead_status_model.dart';
import 'package:leadrat/features/lead/data/models/lead_tag_model.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';
import 'package:leadrat/features/lead/domain/entities/lead_sub_source_entity.dart';

part 'get_lead_model.g.dart';

@JsonSerializable(includeIfNull: false)
class GetLeadModel {
  final String? id;
  final DateTime? createdOn;
  final String? createdBy;
  final DateTime? lastModifiedOn;
  final String? lastModifiedBy;
  final String? name;
  final String? contactNo;
  final String? alternateContactNo;
  final String? email;
  final String? notes;
  final DateTime? scheduledDate;
  final DateTime? possessionDate;
  final DateTime? revertDate;
  final String? chosenProject;
  final String? chosenProperty;
  final String? bookedUnderName;
  final String? leadNumber;
  final String? landline;
  final int? shareCount;
  final String? soldPrice;
  final String? rating;
  final LeadTagModel? leadTags;
  final List<TempProjectModel>? projects;
  final List<TempPropertyModel>? properties;
  final List<String>? projectsList;
  final List<String>? propertiesList;
  final String? assignTo;
  final String? assignedFrom;
  final String? secondaryUserId;
  final String? referralName;
  final String? referralContactNo;
  final String? agencyName;
  final List<DTOWithNameModel>? agencies;
  final List<DTOWithNameModel>? campaigns;
  final String? campaignName;
  final String? companyName;
  final double? unmatchedBudget;
  final String? purchasedFrom;
  final String? closingManager;
  final String? sourcingManager;
  final AddressModel? address;
  final Profession? profession;
  final String? channelPartnerName;
  final List<String>? channelPartnerList;
  final String? channelPartnerExecutiveName;
  final String? channelPartnerContactNo;
  final List<ChannelPartnerModel>? channelPartners;
  final List<String>? existingProjects;
  final String? designation;
  final String? referralEmail;
  final String? statusId;
  final DateTime? bookedDate;
  final String? unitTypeId;
  final double? agreementValue;
  final String? serialNumber;
  final Map<String, int>? contactRecords;
  final List<LeadDocumentModel>? documents;
  final LeadStatusCustomModel? status;
  final List<CustomFlagModel>? customFlags;
  final EnquiryModel? enquiry;
  final Map<int, Map<int, Map<String, String>?>?>? callRecordingUrls;
  final List<LinkModel>? links;
  final String? duplicateLeadVersion;
  final String? confidentialNotes;
  final Map<String, String?>? additionalProperties;
  final String? nationality;
  final PurposeEnum? purpose;
  final List<LeadCallLogModel>? leadCallLogs;
  final PossessionType? possesionType;

  GetLeadModel({
    this.id,
    this.createdOn,
    this.createdBy,
    this.lastModifiedOn,
    this.lastModifiedBy,
    this.possessionDate,
    this.name,
    this.contactNo,
    this.alternateContactNo,
    this.email,
    this.notes,
    this.scheduledDate,
    this.revertDate,
    this.chosenProject,
    this.chosenProperty,
    this.bookedUnderName,
    this.leadNumber,
    this.shareCount,
    this.soldPrice,
    this.referralEmail,
    this.rating,
    this.leadTags,
    this.projects,
    this.properties,
    this.projectsList,
    this.propertiesList,
    this.assignTo,
    this.assignedFrom,
    this.referralName,
    this.referralContactNo,
    this.agencyName,
    this.agencies,
    this.campaigns,
    this.campaignName,
    this.companyName,
    this.unmatchedBudget,
    this.purchasedFrom,
    this.closingManager,
    this.sourcingManager,
    this.address,
    this.profession,
    this.channelPartnerName,
    this.channelPartnerList,
    this.channelPartnerExecutiveName,
    this.channelPartnerContactNo,
    this.channelPartners,
    this.existingProjects,
    this.designation,
    this.secondaryUserId,
    this.statusId,
    this.bookedDate,
    this.unitTypeId,
    this.agreementValue,
    this.landline,
    this.serialNumber,
    this.contactRecords,
    this.documents,
    this.status,
    this.customFlags,
    this.enquiry,
    this.callRecordingUrls,
    this.links,
    this.duplicateLeadVersion,
    this.confidentialNotes,
    this.additionalProperties,
    this.nationality,
    this.leadCallLogs,
    this.purpose,
    this.possesionType,
  });

  factory GetLeadModel.fromJson(Map<String, dynamic> json) => _$GetLeadModelFromJson(json);

  Map<String, dynamic> toJson() => _$GetLeadModelToJson(this);

  GetLeadEntity toEntity() {
    final leadMapper = getIt<LeadEntityMapper>();
    return GetLeadEntity(
      id: id,
      possessionDate: possessionDate,
      name: name,
      contactNo: contactNo,
      alternateContactNo: alternateContactNo,
      landline: landline,
      email: email,
      notes: notes,
      scheduledDate: scheduledDate,
      bookedDate: bookedDate,
      revertDate: revertDate,
      chosenProject: chosenProject,
      chosenProperty: chosenProperty,
      bookedUnderName: bookedUnderName,
      leadNumber: leadNumber,
      shareCount: shareCount,
      soldPrice: soldPrice,
      rating: rating,
      referralContactNo: referralContactNo,
      referralName: referralName,
      referralEmail: referralEmail,
      leadTags: leadTags?.toEntity(),
      contactRecords: contactRecords,
      channelPartnerList: channelPartnerList,
      channelPartners: channelPartners,
      companyName: companyName,
      agencyName: agencyName,
      profession: profession,
      channelPartnerName: channelPartnerName,
      channelPartnerExecutiveName: channelPartnerExecutiveName,
      channelPartnerContactNo: channelPartnerContactNo,
      designation: designation,
      serialNumber: serialNumber,
      documents: documents?.map((e) => e.toEntity()).toList(),
      status: status?.toEntity(),
      createdBy: leadMapper.getUser(createdBy),
      assignedFromUser: leadMapper.getUser(assignedFrom),
      assignedUser: leadMapper.getUser(assignTo),
      bookUnderName: bookedUnderName,
      closingManagerUser: leadMapper.getUser(closingManager),
      createdByUser: leadMapper.getUser(createdBy),
      lastModifiedBy: leadMapper.getUser(lastModifiedBy),
      secondaryUser: leadMapper.getUser(secondaryUserId),
      sourcingManager: leadMapper.getUser(sourcingManager),
      createdOn: createdOn,
      lastModifiedOn: lastModifiedOn,
      customFlags: customFlags,
      enquiry: enquiry?.toEntity(),
      carpetAreaUnit: leadMapper.getAreaUnit(enquiry?.carpetAreaUnitId),
      builtUpAreaUnit: leadMapper.getAreaUnit(enquiry?.builtUpAreaUnitId),
      saleableAreaUnit: leadMapper.getAreaUnit(enquiry?.saleableAreaUnitId),
      propertyAreaUnit: leadMapper.getAreaUnit(enquiry?.propertyAreaUnitId),
      netAreaUnit: leadMapper.getAreaUnit(enquiry?.netAreaUnitId),
      projects: projects,
      agencies: agencies,
      campaigns: campaigns,
      campaignName: campaignName,
      properties: properties,
      callRecordingUrls: callRecordingUrls,
      address: address,
      links: links,
      duplicateLeadVersion: duplicateLeadVersion,
      confidentialNotes: confidentialNotes,
      additionalProperties: additionalProperties,
      nationality: nationality,
      leadCallLogs: leadCallLogs,
      purpose: purpose,
      possesionType: possesionType,
    );
  }
}

@JsonSerializable()
class ChannelPartnerModel {
  final String? id;
  final String? firmName;

  ChannelPartnerModel({this.id, this.firmName});

  factory ChannelPartnerModel.fromJson(Map<String, dynamic> json) => _$ChannelPartnerModelFromJson(json);

  Map<String, dynamic> toJson() => _$ChannelPartnerModelToJson(this);

  ChannelPartnerModel copyWith({
    String? id,
    String? firmName,
  }) {
    return ChannelPartnerModel(
      id: id ?? this.id,
      firmName: firmName ?? this.firmName,
    );
  }
}

@JsonSerializable(includeIfNull: false)
class TempProjectModel {
  final String? id;
  final String? name;
  final String? serialNo;

  TempProjectModel({this.id, this.name, this.serialNo});

  factory TempProjectModel.fromJson(Map<String, dynamic> json) => _$TempProjectModelFromJson(json);

  Map<String, dynamic> toJson() => _$TempProjectModelToJson(this);
}

@JsonSerializable(includeIfNull: false)
class TempPropertyModel {
  final String? id;
  final String? title;
  final String? serialNo;

  TempPropertyModel({this.id, this.title, this.serialNo});

  factory TempPropertyModel.fromJson(Map<String, dynamic> json) => _$TempPropertyModelFromJson(json);

  Map<String, dynamic> toJson() => _$TempPropertyModelToJson(this);
}

@JsonSerializable()
class CustomFlagModel {
  final ViewFlagModel? flag;
  final bool? isSelected;

  CustomFlagModel({this.flag, this.isSelected});

  factory CustomFlagModel.fromJson(Map<String, dynamic> json) => _$CustomFlagModelFromJson(json);

  Map<String, dynamic> toJson() => _$CustomFlagModelToJson(this);
}

@JsonSerializable()
class UpdateLeadFlagModel {
  final String? id;
  final String? flagId;

  UpdateLeadFlagModel({this.id, this.flagId});

  factory UpdateLeadFlagModel.fromJson(Map<String, dynamic> json) => _$UpdateLeadFlagModelFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateLeadFlagModelToJson(this);
}

@JsonSerializable()
class LeadContactCountModel {
  final String? id;
  final ContactType? contactType;

  LeadContactCountModel({this.id, this.contactType});

  factory LeadContactCountModel.fromJson(Map<String, dynamic> json) => _$LeadContactCountModelFromJson(json);

  Map<String, dynamic> toJson() => _$LeadContactCountModelToJson(this);
}

@JsonSerializable()
class LeadCommunicationModel {
  final ContactType? contactType;
  final String? message;
  final String? leadId;

  LeadCommunicationModel({this.contactType, this.message, this.leadId});

  factory LeadCommunicationModel.fromJson(Map<String, dynamic> json) => _$LeadCommunicationModelFromJson(json);

  Map<String, dynamic> toJson() => _$LeadCommunicationModelToJson(this);
}

@JsonSerializable()
class LeadSubSourceModel {
  final MasterLeadSourceModel? source;
  final List<String>? subSources;

  LeadSubSourceModel({
    this.source,
    this.subSources,
  });

  factory LeadSubSourceModel.fromJson(Map<String, dynamic> json) => _$LeadSubSourceModelFromJson(json);

  Map<String, dynamic> toJson() => _$LeadSubSourceModelToJson(this);

  LeadSubSourceEntity toEntity() {
    return LeadSubSourceEntity(
      leadSourceEntity: source?.toEntity(),
      subSources: subSources,
    );
  }
}

@JsonSerializable()
class LeadContactModel {
  final bool? canNavigate;
  final String? id;

  LeadContactModel({this.canNavigate, this.id});

  factory LeadContactModel.fromJson(Map<String, dynamic> json) => _$LeadContactModelFromJson(json);

  Map<String, dynamic> toJson() => _$LeadContactModelToJson(this);
}

@JsonSerializable()
class LeadWithDegreeMatchedModel {
  final int? totalNoOfFields;
  final int? noOfFieldsMatched;
  final String? percentageOfFieldsMatched;
  final GetLeadModel? lead;

  LeadWithDegreeMatchedModel({this.totalNoOfFields, this.noOfFieldsMatched, this.percentageOfFieldsMatched, this.lead});

  LeadWithDegreeMatchedEntity toEntity() {
    return LeadWithDegreeMatchedEntity(
      noOfFieldsMatched: noOfFieldsMatched,
      percentageOfFieldsMatched: percentageOfFieldsMatched,
      leadEntity: lead?.toEntity(),
      totalNoOfFields: totalNoOfFields,
    );
  }

  factory LeadWithDegreeMatchedModel.fromJson(Map<String, dynamic> json) => _$LeadWithDegreeMatchedModelFromJson(json);

  Map<String, dynamic> toJson() => _$LeadWithDegreeMatchedModelToJson(this);
}

@JsonSerializable()
class LinkModel {
  final String? id;
  final String? url;
  final String? type;
  final int? clickedCount;

  LinkModel({
    this.id,
    this.url,
    this.type,
    this.clickedCount,
  });

  factory LinkModel.fromJson(Map<String, dynamic> json) => _$LinkModelFromJson(json);

  Map<String, dynamic> toJson() => _$LinkModelToJson(this);
}
