// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'property_monetaryinfo_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PropertyMonetaryInfoModel _$PropertyMonetaryInfoModelFromJson(
        Map<String, dynamic> json) =>
    PropertyMonetaryInfoModel(
      id: json['id'] as String?,
      monthlyRentAmount: (json['monthlyRentAmount'] as num?)?.toInt(),
      expectedPrice: (json['expectedPrice'] as num?)?.toInt(),
      isNegotiable: json['isNegotiable'] as bool?,
      brokerage: (json['brokerage'] as num?)?.toDouble(),
      brokerageUnit: (json['brokerageUnit'] as num?)?.toInt(),
      currency: json['currency'] as String?,
      brokerageCurrency: json['brokerageCurrency'] as String?,
      depositAmount: (json['depositAmount'] as num?)?.toInt(),
      maintenanceCost: (json['maintenanceCost'] as num?)?.toInt(),
      isPriceVissible: json['isPriceVissible'] as bool?,
      noOfChequesAllowed: (json['noOfChequesAllowed'] as num?)?.toInt(),
      escalationPercentage: (json['escalationPercentage'] as num?)?.toInt(),
      paymentFrequency: $enumDecodeNullable(
          _$PaymentFrequencyEnumMap, json['paymentFrequency'],
          unknownValue: PaymentFrequency.none),
      serviceChange: (json['serviceChange'] as num?)?.toDouble(),
      depositAmountUnit: json['depositAmountUnit'] as String?,
    );

Map<String, dynamic> _$PropertyMonetaryInfoModelToJson(
        PropertyMonetaryInfoModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'expectedPrice': instance.expectedPrice,
      'isNegotiable': instance.isNegotiable,
      'brokerage': instance.brokerage,
      'brokerageUnit': instance.brokerageUnit,
      'currency': instance.currency,
      'brokerageCurrency': instance.brokerageCurrency,
      'depositAmount': instance.depositAmount,
      'maintenanceCost': instance.maintenanceCost,
      'isPriceVissible': instance.isPriceVissible,
      'noOfChequesAllowed': instance.noOfChequesAllowed,
      'monthlyRentAmount': instance.monthlyRentAmount,
      'escalationPercentage': instance.escalationPercentage,
      'paymentFrequency': _$PaymentFrequencyEnumMap[instance.paymentFrequency],
      'serviceChange': instance.serviceChange,
      'depositAmountUnit': instance.depositAmountUnit,
    };

const _$PaymentFrequencyEnumMap = {
  PaymentFrequency.none: 0,
  PaymentFrequency.daily: 1,
  PaymentFrequency.weekly: 2,
  PaymentFrequency.monthly: 3,
  PaymentFrequency.yearly: 4,
};
