import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/enums/leads/meeting_or_site_visit_enum.dart';

import '../../../../core_main/common/models/address_model.dart';
import 'images_with_name_model.dart';

part 'update_appointments_model.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class UpdateAppointmentsModel {
  final String? leadId;
  final MeetingOrSiteVisitEnum? meetingOrSiteVisit;
  final bool? isDone;
  final double? latitude;
  final double? longitude;
  final String? projectName;
  final String? executiveName;
  final String? executiveContactNo;
  final String? image;
  final List<ImagesWithNameModel?>? imagesWithName;
  final bool? isManual;
  final String? notes;
  final AddressModel? address;
  final String? userId;

  UpdateAppointmentsModel({
    this.leadId,
    this.meetingOrSiteVisit,
    this.isDone,
    this.latitude,
    this.longitude,
    this.projectName,
    this.executiveName,
    this.executiveContactNo,
    this.image,
    this.imagesWithName,
    this.isManual,
    this.notes,
    this.address,
    this.userId,
  });

  factory UpdateAppointmentsModel.fromJson(Map<String, dynamic> json) => _$UpdateAppointmentsModelFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateAppointmentsModelToJson(this);
}
