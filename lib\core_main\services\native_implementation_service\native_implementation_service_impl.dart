import 'dart:async';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:fpdart/fpdart.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image/image.dart' as img;
import 'package:image_picker/image_picker.dart';
import 'package:leadrat/core_main/enums/app_enum/file_type.dart';
import 'package:leadrat/core_main/enums/app_enum/white_labeled_enum.dart';
import 'package:leadrat/core_main/errors/failure.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/main.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../utilities/type_def.dart';
import 'native_implementation_service.dart';

class NativeImplementationServiceImpl implements NativeImplementationService {
  static const MethodChannel _channel = MethodChannel('native_actions');
  static const MethodChannel _appConfigChannel = MethodChannel('app_config');

  @override
  FutureEitherFailure<String?> launchSMS(String uri, String message) async {
    try {
      final String? result = await _channel.invokeMethod('launchSMS', {
        'uri': uri,
        'message': message,
      });

      return Either.right(result);
    } on PlatformException {
      return Either.left(Failure("Failed to launch SMS"));
    }
  }

  @override
  Future<XFile?> openCamera() async {
    if (Platform.isAndroid) {
// Check if the camera permission is granted

      var cameraPermissionStatus = await Permission.camera.status;

      if (cameraPermissionStatus.isPermanentlyDenied) {
        await openAppSettings();

        return null;
      } else if (!cameraPermissionStatus.isGranted) {
// Request camera permission

        cameraPermissionStatus = await Permission.camera.request();

        if (!cameraPermissionStatus.isGranted) {
          return null;
        }
      }

      try {
// Try using the native camera implementation first

        final String? result = await _channel.invokeMethod('openCamera');

        if (result != null && result != "NO_CAMERA_AVAILABLE") {
          return await uriToStream(result);
        }
      } catch (e) {
// If native camera fails, fallback to image_picker

        try {
          final ImagePicker picker = ImagePicker();

          final XFile? photo = await picker.pickImage(
            source: ImageSource.camera,
            preferredCameraDevice: CameraDevice.front,
          );

          return photo;
        } catch (pickerError) {
// If both methods fail, return null

          return null;
        }
      }
    } else if (Platform.isIOS) {
      try {
        final String? result = await _channel.invokeMethod('openCamera');

        if (result != null && result != "NO_CAMERA_AVAILABLE") {
// Strip 'file://' prefix from the path if present

          final String filePath = result.replaceFirst('file://', '');

// Convert the image path into an XFile

          final XFile capturedImage = XFile(filePath);

          return capturedImage;
        }

        return null;
      } on PlatformException {
        return null;
      }
    }

    return null;
  }

  Future<XFile?> uriToStream(String uriString) async {
// Remove the 'content://' part and convert it to a valid file path

    try {
      if (uriString.startsWith('content://')) {
        String? filePath = await _getPathFromUri(uriString); // Convert URI to file path

        if (filePath != null) {
// Convert the file path to an XFile

          XFile xFile = XFile(filePath);

          return xFile;
        } else {
          "Error: File path is null".printInConsole();
        }
      }
    } on PlatformException catch (e, stackTrace) {
      "Error: ${e.message}".logException(stackTrace);
    }

    return null;
  }

  Future<String?> _getPathFromUri(String uriString) async {
// Use MethodChannel to get the actual file path from the content URI

    final String? filePath = await _channel.invokeMethod('getPathFromUri', {'uri': uriString});

    return filePath;
  }

  @override
  Future<String?> getImageExtension(XFile? image) async {
    final imageBytes = await image?.readAsBytes();

// Try decoding as JPEG

    if (img.decodeJpg(imageBytes ?? Uint8List(0)) != null) {
      return 'jpg';
    }

// Try decoding as PNG

    else if (img.decodePng(imageBytes ?? Uint8List(0)) != null) {
      return 'png';
    }

// Try decoding as GIF

    else if (img.decodeGif(imageBytes ?? Uint8List(0)) != null) {
      return 'gif';
    }

// Try decoding as WebP

    else if (img.decodeWebP(imageBytes ?? Uint8List(0)) != null) {
      return 'webp';
    }

    return 'jpg';
  }

  @override
  Future<Either<Failure, List<XFile>?>> openGallery({bool isMultiSelection = false}) async {
    if (Platform.isAndroid) {
      try {
        var rawResult;
        if (isMultiSelection) {
          rawResult = await _channel.invokeMethod('openGalleryMultipleImageSelection');
        } else {
          rawResult = await _channel.invokeMethod('openGallery');
        }
        final List<String>? result = rawResult?.whereType<String>().toList();
        if (result?.isNotEmpty ?? false) {
          List<XFile>? xFiles = result?.map((file) => XFile(Uri.parse(file).toString())).toList();
          List<XFile> filteredFiles = await _getFilteredImages(xFiles ?? []);
          return Either.right(filteredFiles);
        } else {
          return Either.left(Failure("image picker failed"));
        }
      } catch (e) {
        return Either.left(Failure("image picker failed"));
      }
    } else if (Platform.isIOS) {
      try {
// Permission granted, proceed with opening the gallery
        var rawResult;
        if (isMultiSelection) {
          rawResult = await _channel.invokeMethod('openGalleryMultipleImageSelection');
        } else {
          rawResult = await _channel.invokeMethod('openGallery');
        }

        final List<String>? result = rawResult?.whereType<String>().toList();
// Check if the result (file path) is not null
        if (result?.isNotEmpty ?? false) {
          List<XFile>? xFiles = result?.map((file) => XFile(file.replaceFirst('file://', '').toString())).toList();
          List<XFile> filteredFiles = await _getFilteredImages(xFiles ?? []);
          return Either.right(filteredFiles);
        } else {
          return Either.left(Failure("No file selected"));
        }
      } on PlatformException catch (e) {
        return Either.left(Failure("Failed to open gallery application: ${e.message}"));
      }
    }

    return Either.left(Failure("Error in recognising the platform"));
  }

  @override
  Future<Either<Failure, XFile?>> openDocuments({FileType fileType = FileType.all}) async {
    if (Platform.isAndroid) {
      try {
// Permission granted, proceed with opening the camera

        final result = await _channel.invokeMethod('openDocuments', {
          'fileType': fileType.toString(),
        });

        if (result?.first != null) {
          return Either.right(await uriToStream(result!.first!));
        }
      } catch (e) {
        return Either.left(Failure("Failed to open documents application"));
      }
    } else if (Platform.isIOS) {
      try {
// Permission granted, proceed with opening the documents

        final String? result = await _channel.invokeMethod('openDocuments');

        if (result != null) {
// Strip 'file://' prefix from the path if present

          final String filePath = result.replaceFirst('file://', '');

// Convert the image path into an XFile

          final XFile capturedImage = XFile(filePath);

          return Either.right(capturedImage);
        } else {
          return Either.left(Failure("Failed to open documents application"));
        }
      } on PlatformException {
        return Either.left(Failure("Failed to open documents application"));
      }
    }

    return Either.left(Failure("Platform unrecognisable"));
  }

  @override
  Future<Either<Failure, List<XFile?>?>> openMultiDocumentsSelection({FileType fileType = FileType.all}) async {
    if (Platform.isAndroid) {
      try {
// Permission granted, proceed with opening the camera

        final rawResult = await _channel.invokeMethod('openDocumentsMultipleDocumentSelection', {
          'fileType': fileType.toString(),
        });

        if (rawResult != null && rawResult.isNotEmpty) {
          final List<String>? result = rawResult?.whereType<String>().toList();
          if (result?.isNotEmpty ?? false) {
            final filteredFiles = await Future.wait((result?.map((e) async => await uriToStream(e)).nonNulls)!);
            return Either.right(filteredFiles);
          }
        }
      } catch (e) {
        return Either.left(Failure("Failed to open documents application"));
      }
    } else if (Platform.isIOS) {
      try {
// Permission granted, proceed with opening the documents

        final rawResult = await _channel.invokeMethod('openDocumentsMultipleDocumentSelection');
        if (rawResult != null && rawResult.isNotEmpty) {
          final List<String>? result = rawResult?.whereType<String>().toList();
          if (result?.isNotEmpty ?? false) {
            final filteredFiles = result?.map((e) {
              final String filePath = e.replaceFirst('file://', '');
              return XFile(filePath);
            }).toList();

            return Either.right(filteredFiles);
          }
        } else {
          return Either.left(Failure("Failed to open documents application"));
        }
      } on PlatformException {
        return Either.left(Failure("Failed to open documents application"));
      }
    }

    return Either.left(Failure("Platform unrecognisable"));
  }

  @override
  Future<Position?> getCurrentLocation({LocationAccuracy desiredAccuracy = LocationAccuracy.medium, bool forceAndroidLocationManager = false, Duration? timeLimit = const Duration(minutes: 1)}) async {
    if (Platform.isAndroid) {
// Check if storage permission is granted

      var locationPermissionStatus = await Permission.location.status;

      if (locationPermissionStatus.isPermanentlyDenied) {
        await openAppSettings();

        return null;
      } else if (!locationPermissionStatus.isGranted) {
// Request storage permission

        locationPermissionStatus = await Permission.location.request();

        if (!locationPermissionStatus.isGranted) {
// If permission is still not granted, return a failure

          return null;
        }
      }
    } else if (Platform.isIOS) {
      String? permissionStatus = await _channel.invokeMethod('requestLocationPermission');

      if (permissionStatus != "LOCATION_PERMISSION_GRANTED") {
        permissionStatus = await _channel.invokeMethod('requestLocationPermission');

        if (permissionStatus != "LOCATION_PERMISSION_GRANTED") {
          return null;
        }
      }
    }

    try {
      var location = await Geolocator.getCurrentPosition(desiredAccuracy: desiredAccuracy, forceAndroidLocationManager: forceAndroidLocationManager, timeLimit: timeLimit);

      return location;
    } on TimeoutException {
      throw TimeoutException("Unable to fetch location. Please try again.");
    } catch (e) {
      return null;
    }
  }

  @override
  Future<Placemark?> getCurrentLocationPlaceMarks(double? latitude, double? longitude) async {
    try {
      List<Placemark> placeMarks = await placemarkFromCoordinates(latitude ?? 0.0, longitude ?? 0.0);

      var currentPlaceMark = placeMarks[0];

      return currentPlaceMark;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<bool> isBusinessWhatsappInstalled() async {
    try {
      if (Platform.isIOS) {
        return await launchUrl(Uri.parse('whatsappbusiness://'), mode: LaunchMode.externalNonBrowserApplication);
      } else if (Platform.isAndroid) {
        try {
          final bool? result = await _channel.invokeMethod('checkBusinessWhatsappInstalled');

          return result ?? false;
        } catch (e) {
          return false;
        }
      }

      return false;
    } catch (ex) {
      return false;
    }
  }

  @override
  FutureEitherFailure<String?> launchWhatsApp({required String phoneNumber, required String message, required String packageName}) async {
    try {
      if (Platform.isAndroid) {
        final String? result = await _channel.invokeMethod('launchWhatsApp', {
          'phoneNumber': phoneNumber,
          'message': message,
          'packageName': packageName,
        });

        return Either.right(result);
      }

      return Either.right('');
    } on PlatformException {
      return Either.left(Failure("Failed to launch WhatsApp"));
    }
  }

  @override
  Future<void> sendIntent({String? tenantId, String? userId, bool? isLoggedIn}) async {
    try {
      await _channel.invokeMethod('sendIntent', {
        "tenantId": tenantId ?? 'unknown',
        "userId": userId ?? '00000000-0000-0000-0000-000000000000',
        "isLoggedIn": isLoggedIn != null ? isLoggedIn.toString() : "false",
      });
    } catch (e) {
      print("Error sending intent: $e");
    }
  }

  Future<bool> _isImageSizeExceeded(XFile? image) async {
    if (image != null) {
      File file = File(image.path);
      int fileSizeInBytes = await file.length();
      double fileSizeInMB = fileSizeInBytes / (1024 * 1024); // Convert bytes to MB
      if (fileSizeInMB > 15) {
        return true;
      }
    }
    return false;
  }

  Future<List<XFile>> _getFilteredImages(List<XFile> xFiles) async {
    List<XFile> filteredFiles = [];
    for (XFile file in xFiles) {
      bool isExceeded = await _isImageSizeExceeded(file);
      if (!isExceeded) {
        filteredFiles.add(file);
      } else {
        LeadratCustomSnackbar.show(message: "File size exceeds 15 MB! Please select a smaller file.", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      }
    }
    return filteredFiles;
  }

  @override
  Future<bool> changeAppBranding({required WhiteLabeledEnum whiteLabel}) async {
    try {
      final bool iconResult = await _appConfigChannel.invokeMethod('changeAppIcon', {'iconName': whiteLabel.brandName});
      final bool nameResult = !Platform.isAndroid ? true : await _appConfigChannel.invokeMethod('changeAppName', {'appName': whiteLabel.brandName});
      return iconResult && nameResult;
    } on PlatformException catch (e) {
      e.logException();
      return false;
    }
  }

  @override
  Future<void> disableMainActivity() async {
    try {
      await _appConfigChannel.invokeMethod('confirmDisableMainActivity');
    } catch (ex, stackTrace) {
      ex.logException(stackTrace);
    }
  }
}
