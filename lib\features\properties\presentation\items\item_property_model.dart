import 'package:leadrat/core_main/common/entites/property_type_entity.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/property_enums/listing_status.dart';
import 'package:leadrat/core_main/enums/property_enums/property_status.dart';
import 'package:leadrat/features/properties/domain/entities/get_all_property_entity.dart';

import '../../../../core_main/enums/property_enums/brokerage_unit.dart';
import '../../../../core_main/extensions/string_extension.dart';
import '../../domain/entities/property_images_type_entity.dart';

class ItemPropertyModel {
  String title = '';
  String subTitle = '';
  String description = '';
  String brokerageDescription = '';
  String image = '';
  String expectedPriceDesc = '';
  GetAllPropertyEntity? propertyItem;
  String id = '';
  bool isSelected;
  String? enquiredFor;
  PropertyStatus? status = PropertyStatus.sold;
  bool isPropertyListed = false;
  bool isPropertySold = false;

  ItemPropertyModel({this.propertyItem, this.isSelected = false}) {
    title = propertyItem?.title ?? "";
    id = propertyItem?.id ?? "";
    subTitle = getSubTitle(noOfBhks: propertyItem?.noOfBHKs, propertyChildModel: propertyItem?.propertyType, isEnabled: isPropertyListed);
    description = getDescription(propertyItem?.propertyType, propertyItem?.area, propertyItem?.areaUnit);
    brokerageDescription = getBrokerage(propertyItem?.brokerageCurrency, propertyItem?.brokerage);
    image = getImage(propertyItem?.images);
    getExpectedPriceDescription(propertyItem?.expectedPrice);
    status = propertyItem?.status;
    enquiredFor = enquiredForString(propertyItem?.enquiredFor);
    isPropertyListed = propertyItem?.listingStatus == ListingStatus.approved;
    isPropertySold = propertyItem?.listingStatus == ListingStatus.sold || propertyItem?.status == PropertyStatus.sold;
  }

  ItemPropertyModel copyWith({
    GetAllPropertyEntity? propertyItem,
    bool? isSelected,
  }) {
    return ItemPropertyModel(
      propertyItem: propertyItem ?? this.propertyItem,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  String enquiredForString(EnquiryType? enquiredfor) {
    if (enquiredfor == EnquiryType.rent) {
      return 'rent/lease';
    } else if (enquiredfor == EnquiryType.sale) {
      return 'sale';
    }
    return '';
  }

  String getDescription(PropertyTypeEntity? propertyChildModel, double? area, String? areaUnits) {
    String description = '';
    if (propertyChildModel != null && propertyChildModel.displayName != null) {
      description = '${propertyChildModel.displayName}';
    }
    if (area != null) {
      description = '$description${description.isEmpty ? "" : ","} ${area.doubleToWord()}';
      if (areaUnits.isNotNullOrEmpty()) {
        description = '$description $areaUnits';
      }
    }

    return description;
  }

  String getBrokerage(String? brokerageUnit, double? brokerage) {
    String brokerageDescription = '';
    String brokerageValue = '0';
    if (brokerage != null && brokerage != 0) {
      brokerageValue = (brokerage % 1 == 0) ? brokerage.toInt().toString() : brokerage.toString();
    }

    if (brokerageValue != '0') {
      if (brokerageUnit == BrokerageUnit.rupees.description) {
        brokerageDescription = '$brokerageUnit $brokerageValue';
      } else if (brokerageUnit == BrokerageUnit.percentage.description) {
        brokerageDescription = '$brokerageValue $brokerageUnit';
      } else {
        brokerageDescription = '$brokerageUnit $brokerageValue';
      }
    }

    return brokerageDescription;
  }

  String getImage(List<PropertyImageEntity>? images) {
    String image = '';
    if (images != null && images.isNotEmpty) {
      image = '${images[0].imageFilePath}';
    }
    return image;
  }

  getExpectedPriceDescription(double? expectedPrice) {
    if (expectedPrice != null && expectedPrice != 0) {
      expectedPriceDesc = expectedPrice.toDouble().budgetToWord(propertyItem?.currency);
    }
  }
}

String getSubTitle({double? noOfBhks, PropertyTypeEntity? propertyChildModel, required bool isEnabled}) {
  String subTitle = '';
  if (noOfBhks != null && noOfBhks != 0) {
    subTitle = noOfBhks == 0.5
        ? isEnabled
            ? 'Studio'
            : '1 RK'
        : isEnabled
            ? "${convertNumber(noOfBhks)} BR"
            : "${convertNumber(noOfBhks)} BHK";
  }

  if (propertyChildModel != null && propertyChildModel.childType != null && propertyChildModel.childType?.displayName != null) {
    subTitle = '$subTitle${propertyChildModel.childType?.displayName}';
  }
  return subTitle;
}

String convertNumber(double number) {
  if (number % 1 == 0) {
    return number.toInt().toString();
  } else {
    return number.toStringAsFixed(1);
  }
}
