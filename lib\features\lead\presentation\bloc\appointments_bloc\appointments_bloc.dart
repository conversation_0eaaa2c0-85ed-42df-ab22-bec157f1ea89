import 'dart:async';
import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_picker/image_picker.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/data/user/models/get_all_users_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/file_type.dart';
import 'package:leadrat/core_main/enums/common/no_of_bhk.dart';
import 'package:leadrat/core_main/enums/leads/meeting_or_site_visit_enum.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:leadrat/core_main/extensions/integer_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/lead/data/models/images_with_name_model.dart';
import 'package:leadrat/features/lead/data/models/projects_dto.dart';
import 'package:leadrat/features/lead/data/models/update_appointments_model.dart';
import 'package:leadrat/features/lead/domain/usecase/add_projects_in_lead_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_apppointments_by_projects_usecase.dart';
import 'package:leadrat/features/lead/domain/usecase/update_appointments_use_case.dart';
import 'package:leadrat/features/lead/presentation/bloc/lead_info_bloc/lead_info_bloc.dart';
import 'package:leadrat/features/lead/presentation/bloc/manage_leads_bloc/manage_leads_bloc.dart';
import 'package:leadrat/main.dart';

import '../../../../../core_main/common/constants/hive_model_constants.dart';
import '../../../../../core_main/common/data/global_settings/models/global_setting_model.dart';
import '../../../../../core_main/common/data/master_data/models/master_area_unit_model.dart';
import '../../../../../core_main/common/models/address_model.dart';
import '../../../../../core_main/enums/app_enum/blob_folder_names.dart';
import '../../../../../core_main/enums/app_enum/select_file_enum.dart';
import '../../../../../core_main/enums/leads/lead_document_type.dart';
import '../../../../../core_main/services/local_storage_service/local_storage_service.dart';
import '../../../../../core_main/services/native_implementation_service/native_implementation_service.dart';
import '../../../../../core_main/utilities/dialog_manager.dart';
import '../../../../../core_main/utilities/file_picker_util.dart';
import '../../../../projects/domain/repository/project_repository.dart';
import '../../../../user_profile/domain/usecase/upload_document_usecase.dart';
import '../../../domain/entities/get_lead_entity.dart';
import '../../item/appointment_done_item.dart';

part 'appointments_event.dart';
part 'appointments_state.dart';

class AppointmentsBloc extends Bloc<AppointmentsEvent, AppointmentsState> {
  final UploadDocumentUseCase _uploadDocumentUseCase;
  final UpdateAppointmentsUseCase _updateAppointmentsUseCase;
  final NativeImplementationService _nativeImplementationService;
  final ProjectRepository _projectRepository;
  final GetAppointmentsProjectsUseCase _getAppointmentsProjectsUseCase;
  final AddProjectsInLeadUseCase _addProjectsInLeadUseCase;
  final UsersDataRepository _usersDataRepository;
  List<SelectableItem<String?>>? allProjects = [];
  List<SelectableItem<String?>>? duplicateProjects = [];
  final List<SelectableItem<String?>>? leadDocuments = [];
  List<GetAllUsersModel?>? _allUsers = [];

  AppointmentsBloc(
    this._uploadDocumentUseCase,
    this._projectRepository,
    this._nativeImplementationService,
    this._updateAppointmentsUseCase,
    this._getAppointmentsProjectsUseCase,
    this._addProjectsInLeadUseCase,
    this._usersDataRepository,
  ) : super(const AppointmentsState()) {
    on<InitAppointmentsPageEvent>(_initAppointmentsPage);
    on<GetAllProjectsEvent>(_getAllProjects);
    on<UpdateLeadProjectsEvent>(_updateLeadProjects);
    on<ProjectSelectedEvent>(_onProjectSelected);
    on<AddLeadDocumentsEvent>(_addLeadDocuments);
    on<AddLocationEvent>(_addLocation);
    on<AppointmentDoneEvent>(_appointmentDone);
    on<AppointmentNotDoneEvent>(_appointmentNotDone);
    on<ClearStateEvent>(_clearState);
    on<SelectAssignedUserEvent>(_onSelectAssignedUser);
  }

  FutureOr<void> _initAppointmentsPage(InitAppointmentsPageEvent event, Emitter<AppointmentsState> emit) async {
    add(ClearStateEvent());
    allProjects = [];
    leadDocuments?.clear();
    _allUsers = await _usersDataRepository.getAssignUser();

    emit(state.copyWith(pageState: AppointmentsPageState.loading));
    var leadEnquiry = event.leadEntity?.enquiry;
    if (leadEnquiry != null) {
      String? noOfBhk = getEnumFromNoOfBhk(NoOfBHK.values, leadEnquiry.bHKs?.firstOrNull)?.description;
      if (leadEnquiry.bHKs != null && (leadEnquiry.bHKs?.length ?? 0) > 1 && noOfBhk != null) noOfBhk += " +${((leadEnquiry.bHKs?.length ?? 0) - 1).toString()}";
      var enquiryDescription = [leadEnquiry.enquiredFor?.description != "None" ? (leadEnquiry.enquiredFor?.description ?? '') : '', leadEnquiry.propertyType?.displayName ?? '', leadEnquiry.propertyType?.childType?.displayName ?? '', noOfBhk ?? ''].where((item) => item.isNotEmpty).join(' • ');
      var price = leadEnquiry.upperBudget?.convertCurrencyFormat();
      var allAreaUnits = getIt<LocalStorageService>().getAllItems<MasterAreaUnitsModel>(HiveModelConstants.masterAreaUnitBoxName);
      var area = allAreaUnits.firstWhere((areaUnit) => areaUnit.id == leadEnquiry.carpetAreaUnitId, orElse: () => MasterAreaUnitsModel(id: null));

      var areaUnitId = '';
      var location = '';
      List<SelectableItem<String?>>? projects = [];
      if (area.id != null) {
        areaUnitId = area.unit ?? '';
      }
      if (leadEnquiry.addresses != null) {
        location = "${leadEnquiry.addresses?.firstOrNull?.subLocality ?? ''}, ${leadEnquiry.addresses?.firstOrNull?.locality ?? ''}";
        location = location
            .trim() // Remove surrounding spaces first
            .replaceAll(RegExp(r'^\s*,\s*'), '') // Remove leading ','
            .replaceAll(RegExp(r'\s*,\s*$'), ''); // Remove trailing ','
      }
      var description = "";
      if (leadEnquiry.propertyType?.childType != null) {
        description = "${leadEnquiry.propertyType?.childType?.displayName} - ${leadEnquiry.bHKs?.firstOrNull}";
      }

      if (event.leadEntity?.projects != null) {
        event.leadEntity?.projects?.forEach((project) => projects.add(SelectableItem(title: project.name ?? 'NA', value: project.id ?? '')));
      }

      final initProjectsIds = event.leadEntity?.projects?.map((e) => e.id);
      var response = await _projectRepository.getProjectsWithId();
      response.fold((failure) => {}, (result) {
        result?.forEach((project) {
          if (project.name.isNotNullOrEmpty()) {
            allProjects?.add(SelectableItem(title: project.name ?? 'NA', value: project.id ?? 'NA'));
          }
        });
      });

      duplicateProjects = allProjects;
      allProjects?.removeWhere((project) => initProjectsIds?.contains(project.value) ?? false);
      duplicateProjects?.forEach((project) {
        if (project.title == projects.firstOrNull?.title) {
          project.isSelected = true;
        }
      });
      var initialSelectedProjects = allProjects?.where((element) => (element.isSelected)).toList();
      final isProjectMandatory = (event.globalSettingModel?.leadProjectSetting?.isProjectMandatoryEnabled ?? false) && (event.leadEntity?.status?.displayName == 'Meeting Scheduled' ? (event.globalSettingModel?.leadProjectSetting?.isProjectMandatoryOnMeetingDone ?? false) : (event.globalSettingModel?.leadProjectSetting?.isProjectMandatoryOnSiteVisitDone ?? false));
      final isNotesMandatory = (event.globalSettingModel?.leadNotesSetting?.isNotesMandatoryEnabled ?? false) && (event.leadEntity?.status?.displayName == 'Meeting Scheduled' ? (event.globalSettingModel?.leadNotesSetting?.isNotesMandatoryOnMeetingDone ?? false) : (event.globalSettingModel?.leadNotesSetting?.isNotesMandatoryOnSiteVisitDone ?? false));
      emit(state.copyWith(
        pageState: AppointmentsPageState.initial,
        leadEntity: event.leadEntity,
        globalSettingModel: event.globalSettingModel,
        description: description,
        dimension: SelectableItem(title: (leadEnquiry.carpetArea == 0.0 || leadEnquiry.carpetArea == null) ? '--' : '${leadEnquiry.carpetArea?.doubleToWord()}', value: areaUnitId),
        enquiryDescription: enquiryDescription,
        leadName: event.leadEntity?.name ?? '--',
        location: location,
        price: price,
        leadProjects: projects,
        allProjects: allProjects,
        initialSelectedProjects: initialSelectedProjects,
        selectedProjects: event.leadEntity?.projects?.map((e) => ItemSimpleModel(title: e.name ?? '', description: e.id)).whereNotNull().toList(),
        duplicateAllProjects: duplicateProjects,
        selectedProject: projects.firstOrNull,
        isProjectMandatory: isProjectMandatory,
        isNotesMandatory: isNotesMandatory,
        isMeetingScheduled: event.leadEntity?.status?.displayName == "Meeting Scheduled",
      ));
      await _checkProjectsMeetingStatus(emit);
      initAssignUser(emit);
    } else {
      emit(state.copyWith(pageState: AppointmentsPageState.failure));
    }
  }

  Future<void> initAssignUser(Emitter<AppointmentsState> emit) async {
    if (_allUsers?.isNotEmpty ?? false) {
      final assignedUserId = state.leadEntity?.assignedUser?.id;
      emit(state.copyWith(assignToUsers: getAssignUsers(assignedUserId: assignedUserId)));
    }
  }

  List<SelectableItem<String>> getAssignUsers({String? assignedUserId}) {
    final currentUser = _usersDataRepository.getLoggedInUser();
    var assignToUsers = _allUsers!
        .whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true))
        .map((user) => SelectableItem<String>(
              title: user?.fullName ?? '',
              value: user?.id,
              isSelected: user?.id == assignedUserId,
            ))
        .toList();

    // Add disabled users
    _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUser) => assignToUsers.add(SelectableItem<String>(
          title: disabledUser?.fullName ?? '',
          value: disabledUser?.id,
          isSelected: disabledUser?.id == assignedUserId,
          isEnabled: false,
        )));
    final loggedInUserItem = SelectableItem<String>(title: "You", value: currentUser?.userId);
    assignToUsers.insert(0, loggedInUserItem);

    return assignToUsers;
  }

  FutureOr<void> _getAllProjects(GetAllProjectsEvent event, Emitter<AppointmentsState> emit) async {
    emit(state.copyWith(pageState: AppointmentsPageState.initial, allProjects: allProjects));
  }

  FutureOr<void> _updateLeadProjects(UpdateLeadProjectsEvent event, Emitter<AppointmentsState> emit) async {
    emit(state.copyWith(pageState: AppointmentsPageState.loading));
    List<SelectableItem<String?>> updatedProjects = [];
    List<ItemSimpleModel>? updatedSelectedProjects = [];
    if (event.selectedProjects != null) {
      event.selectedProjects?.forEach((selectedProject) => updatedProjects.add(SelectableItem(title: selectedProject.title, value: selectedProject.value ?? '')));
    }
    for (var selectedProject in state.selectedProjects) {
      updatedProjects.add(SelectableItem(title: selectedProject.title, value: selectedProject.description ?? ''));
    }
    var selectedProjects = updatedProjects.map((e) => e.title).toList();
    final addProjects = await _addProjectsInLeadUseCase(ProjectDto(leadId: state.leadEntity?.id, projects: selectedProjects));
    addProjects.fold(
      (failure) {
        DialogManager().hideTransparentProgressDialog();
      },
      (success) {
        if (success ?? false) {
          for (var element in updatedProjects) {
            final selectedProject = state.selectedProjects.firstWhereOrNull((item) => item.description == element.value);
            updatedSelectedProjects.add(ItemSimpleModel(title: element.title, description: element.value, isEnabled: selectedProject?.isEnabled));
          }
          allProjects?.removeWhere((element) => event.selectedProjects?.map((e) => e.value).contains(element.value) ?? false);
          emit(state.copyWith(pageState: AppointmentsPageState.initial, allProjects: allProjects, leadProjects: updatedProjects, showDialogProgress: false, selectedProjects: updatedSelectedProjects, selectedProject: updatedProjects.firstOrNull));
          if (state.leadEntity?.id?.isNotNullOrEmpty() ?? false) {
            getIt<LeadInfoBloc>().add(GetLeadInfoInitialEvent(state.leadEntity!.id!));
          }
        } else {
          emit(state.copyWith(pageState: AppointmentsPageState.initial));
        }
      },
    );
    emit(state.copyWith(allProjects: allProjects, leadProjects: updatedProjects));
  }

  FutureOr<void> _onProjectSelected(ProjectSelectedEvent event, Emitter<AppointmentsState> emit) {
    if (event.selectedProject != null) {
      if (event.projectEdited ?? false) {
        List<SelectableItem<String?>>? leadProjects = [];
        List<ItemSimpleModel<dynamic>>? selectedProjects = state.selectedProjects;
        state.leadProjects?.forEach((project) => leadProjects.add(project));
        leadProjects.removeAt(event.index ?? 0);
        selectedProjects.removeAt(event.index ?? 0);
        leadProjects.insert(event.index ?? 0, SelectableItem(title: event.selectedProject?.title ?? ''));
        selectedProjects.insert(event.index ?? 0, ItemSimpleModel(title: event.selectedProject?.title ?? '', description: event.selectedProject?.value));
        emit(state.copyWith(pageState: AppointmentsPageState.initial, leadProjects: leadProjects, selectedIndex: event.index, selectedProjects: selectedProjects, selectedProject: event.selectedProject));
      } else {
        duplicateProjects?.forEach((project) => project.isSelected = false);
        duplicateProjects?.firstWhereOrNull((project) => project.title == event.selectedProject?.title)?.isSelected = true;
        emit(state.copyWith(pageState: AppointmentsPageState.initial, duplicateAllProjects: duplicateProjects, selectedProject: event.selectedProject));
      }
    }
  }

  FutureOr<void> _addLeadDocuments(AddLeadDocumentsEvent event, Emitter<AppointmentsState> emit) async {
    var selectedFiles = await FilePickerUtil.pickFile(event.fileType ?? SelectFileEnum.gallery, fileType: FileType.pdf);
    if (selectedFiles?.any((element) => element != null) ?? false) {
      var uploadedUrl = await uploadImageToS3Bucket(selectedFiles?.first);
      leadDocuments?.add(SelectableItem(title: event.documentName ?? selectedFiles?.first?.name ?? '', value: uploadedUrl ?? ''));
      emit(state.copyWith(leadDocuments: leadDocuments));
    }
  }

  Future<String?> uploadImageToS3Bucket(XFile? capturedImage) async {
    if (capturedImage != null) {
      String? uploadedImage;
      final base64File = base64Encode(await capturedImage.readAsBytes());
      final uploadedImageToS3Bucket = await _uploadDocumentUseCase(UploadDocumentParams(BlobFolderNameEnum.leadDocuments.description, capturedImage.path.split('/').last != null ? capturedImage.path.split('/').last : await _nativeImplementationService.getImageExtension(capturedImage) ?? 'jpg', base64File));
      uploadedImageToS3Bucket.fold(
          (failure) => {
                // show toast
              },
          (res) => {
                uploadedImage = res?.isNotEmpty ?? false ? res : null,
              });
      return uploadedImage;
    } else {
      return null;
    }
  }

  FutureOr<void> _addLocation(AddLocationEvent event, Emitter<AppointmentsState> emit) {
    if (event.location != null) {
      emit(state.copyWith(pageState: AppointmentsPageState.initial, leadLocation: event.location));
    }
  }

  FutureOr<void> _appointmentDone(AppointmentDoneEvent event, Emitter<AppointmentsState> emit) async {
    if (state.isNotesMandatory && (event.item?.notes?.trim().isNullOrEmpty() ?? false)) {
      LeadratCustomSnackbar.show(message: "Please enter the notes field", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      DialogManager().hideTransparentProgressDialog();
      return;
    }
    emit(state.copyWith(pageState: AppointmentsPageState.updating));
    var isMeetingScheduled = false;
    Position? location;
    Placemark? currentPlacemark;
    List<ImagesWithNameModel?>? imagesWithNames;
    AddressModel? address;
    if ((state.leadEntity?.status?.shouldOpenAppointmentPage ?? false) && (state.globalSettingModel?.isCustomStatusEnabled ?? false)) {
      if (state.leadEntity?.status?.shouldUseForMeeting ?? false) {
        isMeetingScheduled = true;
      }
    } else {
      if (state.leadEntity?.status?.displayName == "Meeting Scheduled") {
        isMeetingScheduled = true;
      }
    }
    if (event.item?.leadLocation == null) {
      try {
        location = await _nativeImplementationService.getCurrentLocation();
        currentPlacemark = await _nativeImplementationService.getCurrentLocationPlaceMarks(location?.latitude ?? 0.0, location?.longitude ?? 0.0);
        address = AddressModel(
          latitude: location?.latitude.toString() ?? "0",
          longitude: location?.longitude.toString() ?? "0",
          locality: currentPlacemark?.subLocality,
          subLocality: currentPlacemark?.thoroughfare,
          postalCode: currentPlacemark?.postalCode,
          country: currentPlacemark?.country,
          isManual: false,
          city: currentPlacemark?.locality,
          state: currentPlacemark?.administrativeArea,
        );
      } catch (e) {
        DialogManager().hideTransparentProgressDialog();
        emit(state.copyWith(pageState: AppointmentsPageState.fetchingLocationError));
        return;
      }
    }

    if (event.item?.leadDocuments != null) {
      imagesWithNames = [];
      event.item?.leadDocuments?.forEach((document) => imagesWithNames?.add(ImagesWithNameModel(
            documentName: document.title,
            filePath: document.value,
            leadDocumentType: isMeetingScheduled ? LeadDocumentTypeEnum.meeting : LeadDocumentTypeEnum.siteVisit,
            uploadedOn: DateTime.now().toUserTimeZone().toUtcFormat(),
          )));
    }

    if (event.item?.leadLocation?.isManual ?? false) {
      address = AddressModel(
        subLocality: event.item?.leadLocation?.subLocality,
        locality: event.item?.leadLocation?.locality,
        city: event.item?.leadLocation?.city,
        state: event.item?.leadLocation?.state,
      );
    } else {
      if (event.item?.leadLocation?.locationId != null && event.item?.leadLocation?.locationId != '00000000-0000-0000-0000-000000000000') {
        address = AddressModel(
          locationId: event.item?.leadLocation?.locationId,
        );
      } else {
        if (event.item?.leadLocation?.placeId != null && event.item?.leadLocation?.placeId != '00000000-0000-0000-0000-000000000000') {
          address = AddressModel(
            placeId: event.item?.leadLocation?.placeId,
          );
        }
      }
    }

    var updateAppointmentsModel = UpdateAppointmentsModel(
      leadId: state.leadEntity?.id,
      meetingOrSiteVisit: isMeetingScheduled ? MeetingOrSiteVisitEnum.meeting : MeetingOrSiteVisitEnum.siteVisit,
      isDone: true,
      latitude: location?.latitude ?? 0.0,
      longitude: location?.longitude ?? 0.0,
      projectName: state.selectedProject?.title ?? state.selectedProjects.firstOrNull?.title,
      executiveName: event.item?.salesExecutiveName,
      executiveContactNo: event.item?.salesExecutiveNumber,
      image: null,
      imagesWithName: imagesWithNames,
      isManual: event.item?.leadLocation?.isManual,
      notes: event.item?.notes?.trim(),
      address: address,
      userId: state.selectedAssignedUser?.value,
    );
    var response = await _updateAppointmentsUseCase.call(updateAppointmentsModel);
    response.fold(
        (failure) => {
              emit(state.copyWith(pageState: AppointmentsPageState.failure)),
            }, (result) {
      final updatedSelectedProjects = state.selectedProjects.map((e) => e.title == updateAppointmentsModel.projectName ? e.copyWith(isEnabled: true) : e).whereNotNull().toList();
      emit(state.copyWith(pageState: AppointmentsPageState.success, leadProjects: state.leadProjects, selectedProjects: updatedSelectedProjects));
      if (state.leadEntity?.id?.isNotNullOrEmpty() ?? false) {
        getIt<LeadInfoBloc>().add(GetLeadInfoInitialEvent(state.leadEntity!.id!));
        getIt<ManageLeadsBloc>().add(ManageLeadsInitialEvent(leadFilter: getIt<ManageLeadsBloc>().leadFilter));
      }
    });
  }

  FutureOr<void> _appointmentNotDone(AppointmentNotDoneEvent event, Emitter<AppointmentsState> emit) async {
    var isMeetingScheduled = false;
    Position? location;
    Placemark? currentPlacemark;
    emit(state.copyWith(pageState: AppointmentsPageState.fetchingLocation));
    try {
      location = await _nativeImplementationService.getCurrentLocation();
      currentPlacemark = await _nativeImplementationService.getCurrentLocationPlaceMarks(location?.latitude ?? 0.0, location?.longitude ?? 0.0);
    } catch (e) {
      emit(state.copyWith(pageState: AppointmentsPageState.fetchingLocationError));
      return;
    }

    emit(state.copyWith(pageState: AppointmentsPageState.updating));
    if ((state.leadEntity?.status?.shouldOpenAppointmentPage ?? false) && (state.globalSettingModel?.isCustomStatusEnabled ?? false)) {
      if (state.leadEntity?.status?.shouldUseForMeeting ?? false) {
        isMeetingScheduled = true;
      }
    } else {
      if (state.leadEntity?.status?.displayName == "Meeting Scheduled") {
        isMeetingScheduled = true;
      }
    }

    var updateAppointmentsModel = UpdateAppointmentsModel(
      leadId: state.leadEntity?.id,
      meetingOrSiteVisit: isMeetingScheduled ? MeetingOrSiteVisitEnum.meeting : MeetingOrSiteVisitEnum.siteVisit,
      isDone: false,
      latitude: location?.latitude ?? 0.0,
      longitude: location?.longitude ?? 0.0,
      isManual: false,
      projectName: state.selectedProject?.title ?? state.selectedProjects.firstOrNull?.title,
    );

    var leadProjects = state.leadProjects;
    var response = await _updateAppointmentsUseCase.call(updateAppointmentsModel);
    response.fold(
        (failure) => {
              emit(state.copyWith(pageState: AppointmentsPageState.failure)),
            }, (result) {
      final updatedSelectedProjects = state.selectedProjects.map((e) => e.title == updateAppointmentsModel.projectName ? e.copyWith(isEnabled: false) : e).whereNotNull().toList();
      emit(state.copyWith(pageState: AppointmentsPageState.success, leadProjects: leadProjects, selectedProjects: updatedSelectedProjects));
      if (state.leadEntity?.id?.isNotNullOrEmpty() ?? false) {
        getIt<LeadInfoBloc>().add(GetLeadInfoInitialEvent(state.leadEntity!.id!));
        getIt<LeadInfoBloc>().add(InitializeHistoryEvent(true));
        getIt<LeadInfoBloc>().add(InitializeNotesEvent(true));
        getIt<ManageLeadsBloc>().add(ManageLeadsInitialEvent(leadFilter: getIt<ManageLeadsBloc>().leadFilter));
      }
    });
  }

  FutureOr<void> _clearState(ClearStateEvent event, Emitter<AppointmentsState> emit) {
    emit(state.clearState());
  }

  Future<void> _checkProjectsMeetingStatus(Emitter<AppointmentsState> emit) async {
    if (state.leadEntity?.projects?.isNotEmpty ?? false) {
      final response = await _getAppointmentsProjectsUseCase(ProjectDto(leadId: state.leadEntity?.id, projects: state.leadEntity?.projects?.map((e) => e.name).whereNotNull().toList() ?? []));
      response.fold(
        (failure) => null,
        (success) {
          if (success != null && success.isNotEmpty) {
            final initProjectsNames = success.map((e) => e.projectName).toList();
            final updatedSelectedProjects = state.selectedProjects.map((item) {
              if (initProjectsNames.contains(item.title)) {
                return item.copyWith(isEnabled: success.firstWhereOrNull((element) => element.projectName == item.title)?.isDone);
              }
              return item;
            }).toList();
            updatedSelectedProjects.sort((a, b) {
              if (a.isEnabled == b.isEnabled) return 0;
              if (a.isEnabled == true) return -1;
              if (a.isEnabled == false && b.isEnabled == null) return -1;
              return 1;
            });
            List<SelectableItem<String?>>? updatedLeadProjects = [];
            for (var element in updatedSelectedProjects) {
              updatedLeadProjects.add(SelectableItem<String>(title: element.title, value: element.description));
            }
            emit(state.copyWith(selectedProjects: updatedSelectedProjects, leadProjects: updatedLeadProjects, selectedProject: updatedLeadProjects.firstOrNull));
          }
        },
      );
    }
  }

  FutureOr<void> _onSelectAssignedUser(SelectAssignedUserEvent event, Emitter<AppointmentsState> emit) async {
    if (state.leadEntity?.assignedUser?.id == event.selectedUser.value) {
      emit(state.copyWith(selectedAssignedUser: event.selectedUser));
    } else {
      emit(state.copyWith(canUpdateSelectedAssignedUser: true, assignToUsers: getAssignUsers()));
      LeadratCustomSnackbar.show(message: "Only the primary owner can be selected. To choose a different user, reassign the lead to them first", navigatorKey: MyApp.navigatorKey, type: SnackbarType.warning);
    }
  }
}
