import 'dart:convert';

import 'package:leadrat/core_main/remote/leadrat_rest_service.dart';
import 'package:leadrat/core_main/remote/rest_response/rest_response_wrapper.dart';
import 'package:leadrat/core_main/resources/common/rest_resources.dart';
import 'package:leadrat/core_main/services/blob_storage_service/blob_storage_service.dart';

class BlobStorageServiceImpl extends LeadratRestService implements BlobStorageService {
  final String bucketName = RestResources.leadratBlobStorageBucketName;

  @override
  Future<List<String>?> uploadFileToBlobStorage({required String folderName, required String fileName, required List<String> filesBase64}) async {
    try {
      final restRequest = createPostRequest(BlobStorageRestService.uploadFileBase64(bucketName, folderName, fileName), body: jsonEncode(filesBase64));
      final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
        restRequest,
        (json) => ResponseWrapper<List<String>?>.fromJson(
          json,
          (data) => (data as List).map((item) => item as String).toList(),
        ),
      );

      return response.data;
    } catch (exception) {
      rethrow;
    }
  }
}
