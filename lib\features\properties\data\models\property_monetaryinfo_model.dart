import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/enums/property_enums/payment_frequency.dart';
import 'package:leadrat/features/properties/domain/entities/property_monetaryinfo_entity.dart';

part 'property_monetaryinfo_model.g.dart';

@JsonSerializable()
class PropertyMonetaryInfoModel {
  final String? id;
  final int? expectedPrice;
  final bool? isNegotiable;
  final double? brokerage;
  final int? brokerageUnit;
  final String? currency;
  final String? brokerageCurrency;
  final int? depositAmount;
  final int? maintenanceCost;
  final bool? isPriceVissible;
  final int? noOfChequesAllowed;
  final int? monthlyRentAmount;
  final int? escalationPercentage;
  @Json<PERSON>ey(unknownEnumValue: PaymentFrequency.none)
  final PaymentFrequency? paymentFrequency;
  final double? serviceChange;
  final String? depositAmountUnit;

  PropertyMonetaryInfoModel({
    this.id,
    this.monthlyRentAmount,
    this.expectedPrice,
    this.isNegotiable,
    this.brokerage,
    this.brokerageUnit,
    this.currency,
    this.brokerageCurrency,
    this.depositAmount,
    this.maintenanceCost,
    this.isPriceVissible,
    this.noOfChequesAllowed,
    this.escalationPercentage,
    this.paymentFrequency,
    this.serviceChange,
    this.depositAmountUnit,
  });

  factory PropertyMonetaryInfoModel.fromJson(Map<String, dynamic> json) => _$PropertyMonetaryInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$PropertyMonetaryInfoModelToJson(this);

  PropertyMonetaryInfoEntity toEntity() {
    return PropertyMonetaryInfoEntity(
      id: id,
      brokerageUnit: brokerageUnit,
      expectedPrice: expectedPrice,
      brokerage: brokerage,
      brokerageCurrency: brokerageCurrency,
      currency: currency,
      depositAmount: depositAmount,
      isNegotiable: isNegotiable,
      maintenanceCost: maintenanceCost,
      isPriceVissible: isPriceVissible,
      monthlyRentAmount: monthlyRentAmount,
      noOfChequesAllowed: noOfChequesAllowed,
      escalationPercentage: escalationPercentage,
      paymentFrequency: paymentFrequency,
      serviceChange: serviceChange,
      depositAmountUnit: depositAmountUnit,
    );
  }
}
