import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

class FilterCustomDatePicker extends LeadratStatefulWidget {
  final String labelText;
  final DateTime? selectedDate;
  final DateTime? minDate;
  final DateTime? maxDate;
  final Function(DateTime?) onDateSelected;

  const FilterCustomDatePicker({
    Key? key,
    required this.labelText,
    required this.selectedDate,
    required this.onDateSelected,
    this.minDate,
    this.maxDate,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _FilterCustomDatePickerState();
}

class _FilterCustomDatePickerState extends LeadratState<FilterCustomDatePicker> {
  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.selectedDate;
  }

  Future<void> _selectDate(BuildContext context) async {
    DateTime firstDate = widget.minDate ?? DateTime(2000);
    DateTime lastDate = widget.maxDate ?? DateTime(2101);

    DateTime initialDate = _selectedDate ?? DateTime.now().toUserTimeZone()!;

    if (initialDate.isBefore(firstDate)) {
      initialDate = firstDate;
    }
    if (initialDate.isAfter(lastDate)) {
      initialDate = lastDate;
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      widget.onDateSelected(picked);
    }
  }

  @override
  Widget buildContent(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          widget.labelText,
          style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.gray600),
        ),
        const SizedBox(width: 10),
        InkWell(
          onTap: () => _selectDate(context),
          child: Container(
            padding: const EdgeInsets.fromLTRB(10, 8, 8, 8),
            decoration: BoxDecoration(
              color: ColorPalette.primaryColor,
              border: Border.all(color: ColorPalette.primaryLightColor),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.max,
              children: [
                Text(
                  _selectedDate == null ? "select date" : "${_selectedDate?.toLocal().toString().split(' ')[0]}",
                  style: LexendTextStyles.lexend10Medium.copyWith(color: _selectedDate == null ? ColorPalette.gray600 : ColorPalette.white),
                ),
                const SizedBox(width: 20),
                Image.asset(ImageResources.icCalendarMark),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
