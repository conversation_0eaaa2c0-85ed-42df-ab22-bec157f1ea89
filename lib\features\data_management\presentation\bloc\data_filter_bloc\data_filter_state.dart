part of 'data_filter_bloc.dart';

@immutable
class DataFilterState {
  final PageState pageState;
  final String? errorMessage;
  final DataFilterModel? dataFilterModel;
  final List<DataFilterCategoryItem> dataFilterCategories;
  final List<DataFilterCategoryItem> searchFilteredCategories;
  final int selectedCategoryIndex;
  final DateTime? selectedFromDate;
  final DateTime? selectedToDate;
  final BudgetRangeModel customBudget;
  final List<SelectableItem<String>> currencies;
  final SelectableItem<String>? selectedCurrency;
  final List<SelectableItem<ProspectDateType>> dateTypes;
  final SelectableItem<ProspectDateType>? selectedDateType;
  final List<SelectableItem<MasterAreaUnitsModel>>? selectableAreaUnits;
  final SelectableItem<MasterAreaUnitsModel>? selectedCarpetAreaUnit;
  final SelectableItem<MasterAreaUnitsModel>? selectedBuildUpAreaUnit;
  final SelectableItem<MasterAreaUnitsModel>? selectedSalableAreaUnit;
  final SelectableItem<MasterAreaUnitsModel>? selectedNetAreaUnit;
  final SelectableItem<MasterAreaUnitsModel>? selectedPropertyAreaUnit;
  final RangeInputLead? saleableArea;
  final RangeInputLead? builtUpArea;
  final RangeInputLead? carpetArea;
  final RangeInputLead? propertyArea;
  final RangeInputLead? minBudget;
  final RangeInputLead? maxBudget;
  final RangeInputLead? netArea;

  const DataFilterState({
    this.pageState = PageState.initial,
    this.errorMessage,
    this.dataFilterModel,
    this.dataFilterCategories = const [],
    this.searchFilteredCategories = const [],
    this.selectedCategoryIndex = 0,
    this.selectedFromDate,
    this.selectedToDate,
    this.customBudget = const BudgetRangeModel(isCustomBudget: true),
    this.currencies = const [],
    this.selectedCurrency,
    this.dateTypes = const [],
    this.selectedDateType,
    this.selectableAreaUnits = const [],
    this.selectedCarpetAreaUnit,
    this.selectedBuildUpAreaUnit,
    this.selectedSalableAreaUnit,
    this.selectedNetAreaUnit,
    this.selectedPropertyAreaUnit,
    this.carpetArea,
    this.builtUpArea,
    this.saleableArea,
    this.propertyArea,
    this.minBudget,
    this.maxBudget,
    this.netArea,
  });

  DataFilterState copyWith({
    PageState? pageState,
    String? errorMessage,
    DataFilterModel? dataFilterModel,
    List<DataFilterCategoryItem>? dataFilterCategories,
    List<DataFilterCategoryItem>? searchFilteredCategories,
    int? selectedCategoryIndex,
    DateTime? selectedFromDate,
    DateTime? selectedToDate,
    BudgetRangeModel? customBudget,
    List<SelectableItem<String>>? currencies,
    SelectableItem<String>? selectedCurrency,
    List<SelectableItem<ProspectDateType>>? dateTypes,
    SelectableItem<ProspectDateType>? selectedDateType,
    List<SelectableItem<MasterAreaUnitsModel>>? selectableAreaUnits,
    SelectableItem<MasterAreaUnitsModel>? selectedCarpetAreaUnit,
    SelectableItem<MasterAreaUnitsModel>? selectedBuildUpAreaUnit,
    SelectableItem<MasterAreaUnitsModel>? selectedSalableAreaUnit,
    SelectableItem<MasterAreaUnitsModel>? selectedNetAreaUnit,
    SelectableItem<MasterAreaUnitsModel>? selectedPropertyAreaUnit,
    bool updateSelectCarpetAreaUnit = true,
    bool updateSelectBuildUpAreaUnit = true,
    bool updateSelectSalableAreaUnit = true,
    bool updateSelectNetAreaUnit = true,
    bool updateSelectPropertyAreaUnit = true,
    bool resetFilter = false,
    RangeInputLead? builtUpArea,
    RangeInputLead? carpetArea,
    RangeInputLead? saleableArea,
    RangeInputLead? propertyArea,
    bool updateSelectCarpetArea = true,
    bool updateSelectBuiltArea = true,
    bool updateSelectSaleableArea = true,
    bool updateSelectPropertyArea = true,
    bool updateSelectMinBudget = true,
    bool updateSelectMaxBudget = true,
    bool updateSelectNetArea = true,
    RangeInputLead? minBudget,
    RangeInputLead? maxBudget,
    RangeInputLead? netArea,
    bool updateSelectedDateType = true,
  }) {
    return DataFilterState(
      pageState: pageState ?? this.pageState,
      errorMessage: errorMessage ?? this.errorMessage,
      dataFilterModel: dataFilterModel ?? this.dataFilterModel,
      dataFilterCategories: dataFilterCategories ?? this.dataFilterCategories,
      searchFilteredCategories: searchFilteredCategories ?? this.searchFilteredCategories,
      selectedCategoryIndex: selectedCategoryIndex ?? this.selectedCategoryIndex,
      selectedFromDate: !resetFilter ? selectedFromDate ?? this.selectedFromDate : null,
      selectedToDate: !resetFilter ? selectedToDate ?? this.selectedToDate : null,
      customBudget: customBudget ?? this.customBudget,
      currencies: currencies ?? this.currencies,
      selectedCurrency: !resetFilter ? selectedCurrency ?? this.selectedCurrency : null,
      dateTypes: dateTypes ?? this.dateTypes,
      selectedDateType: updateSelectedDateType ? selectedDateType ?? this.selectedDateType : null,
      selectableAreaUnits: selectableAreaUnits ?? this.selectableAreaUnits,
      selectedCarpetAreaUnit: updateSelectCarpetAreaUnit ? selectedCarpetAreaUnit ?? this.selectedCarpetAreaUnit : null,
      selectedBuildUpAreaUnit: updateSelectBuildUpAreaUnit ? selectedBuildUpAreaUnit ?? this.selectedBuildUpAreaUnit : null,
      selectedSalableAreaUnit: updateSelectSalableAreaUnit ? selectedSalableAreaUnit ?? this.selectedSalableAreaUnit : null,
      selectedPropertyAreaUnit: updateSelectPropertyAreaUnit ? selectedPropertyAreaUnit ?? this.selectedPropertyAreaUnit : null,
      selectedNetAreaUnit: updateSelectNetAreaUnit ? selectedNetAreaUnit ?? this.selectedNetAreaUnit : null,
      saleableArea: updateSelectSaleableArea ? saleableArea ?? this.saleableArea : null,
      builtUpArea: updateSelectBuiltArea ? builtUpArea ?? this.builtUpArea : null,
      carpetArea: updateSelectCarpetArea ? carpetArea ?? this.carpetArea : null,
      propertyArea: updateSelectPropertyArea ? propertyArea ?? this.propertyArea : null,
      minBudget: updateSelectMinBudget ? minBudget ?? this.minBudget : null,
      maxBudget: updateSelectMaxBudget ? maxBudget ?? this.maxBudget : null,
      netArea: updateSelectNetArea ? netArea ?? this.netArea : null,    );
  }
}
