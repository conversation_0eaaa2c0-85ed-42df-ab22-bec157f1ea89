import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/models/address_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/taxation_mode.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/property_enums/completion_status.dart';
import 'package:leadrat/core_main/enums/property_enums/facing.dart';
import 'package:leadrat/core_main/enums/property_enums/furnish_status.dart';
import 'package:leadrat/core_main/enums/property_enums/listing_status.dart';
import 'package:leadrat/core_main/enums/property_enums/lockin_period_type.dart';
import 'package:leadrat/core_main/enums/property_enums/notice_period_type.dart';
import 'package:leadrat/core_main/enums/property_enums/property_status.dart';
import 'package:leadrat/core_main/enums/property_enums/sale_type.dart';
import 'package:leadrat/core_main/enums/property_enums/security_deposite_type.dart';
import 'package:leadrat/core_main/mapper/lead_mapper.dart';
import 'package:leadrat/core_main/mapper/property_mapper.dart';
import 'package:leadrat/features/data_management/data/models/property_type_model.dart';
import 'package:leadrat/features/properties/data/models/add_property_model.dart';
import 'package:leadrat/features/properties/data/models/property_attribute_model.dart';
import 'package:leadrat/features/properties/data/models/property_brochure_model.dart';
import 'package:leadrat/features/properties/data/models/property_dimension_model.dart';
import 'package:leadrat/features/properties/data/models/property_image_model.dart';
import 'package:leadrat/features/properties/data/models/property_monetaryinfo_model.dart';
import 'package:leadrat/features/properties/data/models/property_owner_details_model.dart';
import 'package:leadrat/features/properties/data/models/property_taginfo_model.dart';
import 'package:leadrat/features/properties/data/models/view_listing_source_address_model.dart';
import 'package:leadrat/features/properties/domain/entities/get_property_entity.dart';

part 'get_property_model.g.dart';

@JsonSerializable()
class GetPropertyModel {
  final String? id;
  final String? title;
  final SaleType? saleType;
  final EnquiryType? enquiredFor;
  final String? notes;
  final FurnishStatus? furnishStatus;
  final PropertyStatus? status;
  final String? rating;
  final int? shareCount;
  final DateTime? possessionDate;
  final bool? isGOListingEnabled;
  final Facing? facing;
  final String? goPropertyId;
  final double? noOfBHK;
  final BHKType? bhkType;
  final PropertyMonetaryInfoModel? monetaryInfo;
  final PropertyOwnerDetailsModel? ownerDetails;
  final PropertyDimensionModel? dimension;
  final PropertyTagInfoModel? tagInfo;
  final List<PropertyImageModel>? images;
  final Map<String, List<String>>? imageUrls;
  final List<PropertyAttributeModel>? attributes;
  final List<String>? amenities;
  final List<PropertyBrochureModel>? brochures;
  final int? whatsAppShareCount;
  final int? callShareCount;
  final int? emailShareCount;
  final int? smsShareCount;
  final String? aboutProperty;
  final AddressModel? address;
  final List<String>? links;
  final String? project;
  final List<String>? assignedTo;
  final DateTime? lastModifiedOn;
  final DateTime? createdOn;
  final String? createdBy;
  final String? lastModifiedBy;
  final PropertyTypeModel? propertyType;
  final String? micrositeURL;
  final String? serialNo;
  final String? coWorkingOperator;
  final String? coWorkingOperatorName;
  final String? coWorkingOperatorPhone;

  final LockInPeriodType? lockInPeriod;
  final NoticePeriodType? noticePeriod;
  final List<int>? noOfFloorsOccupied;
  final TenantContactInfo? tenantContactInfo;
  @JsonKey(unknownEnumValue: ListingStatus.none)
  final ListingStatus? listingStatus;
  final DateTime? listingExpireDate;
  final List<CustomListingSourceModel>? listingSources;
  final ListingLevel? listingLevel;
  final List<ViewListingSourceAddressModel>? listingSourceAddresses;
  final String? dldPermitNumber;
  final String? refrenceNo;
  final String? dtcmPermit;
  final OfferingType? offeringType;
  final CompletionStatus? completionStatus;
  final String? language;
  final String? titleWithLanguage;
  final String? aboutPropertyWithLanguage;
  final List<String>? view360Url;
  final TaxationMode? taxationMode;
  final bool? isWaterMarkEnabled;
  final PossessionType? possesionType;
  final double? securityDepositAmount;
  final String? securityDepositUnit;
  final List<String>? listingOnBehalf;
  final bool? shouldVisisbleOnListing;
  final List<PropertyOwnerDetailsModel>? propertyOwnerDetails;

  factory GetPropertyModel.fromJson(Map<String, dynamic> json) => _$GetPropertyModelFromJson(json);

  Map<String, dynamic> toJson() => _$GetPropertyModelToJson(this);

  GetPropertyEntity toEntity() {
    return GetPropertyEntity(
      isGOListingEnabled: isGOListingEnabled,
      id: id,
      title: title,
      createdOn: createdOn,
      createdBy: createdBy,
      lastModifiedBy: lastModifiedBy,
      lastModifiedOn: lastModifiedOn,
      bhkType: bhkType,
      whatsAppShareCount: whatsAppShareCount,
      status: status,
      smsShareCount: smsShareCount,
      shareCount: shareCount,
      serialNo: serialNo,
      saleType: saleType,
      propertyType: propertyType?.toEntity(),
      project: project,
      possessionDate: possessionDate,
      notes: notes,
      noOfBHK: noOfBHK,
      micrositeURL: micrositeURL,
      links: links,
      imageUrls: imageUrls,
      images: images
          ?.map(
            (image) => image.toEntity(),
          )
          .toList(),
      furnishStatus: furnishStatus,
      enquiredFor: enquiredFor,
      emailShareCount: emailShareCount,
      aboutProperty: aboutProperty,
      address: address?.toEntity(),
      amenities: amenities
          ?.map(
            (amenity) => getIt<PropertyEntityMapper>().getCustomAmenity(amenity),
          )
          .toList(),
      assignedTo: assignedTo?.map((userId) => getIt<LeadEntityMapper>().getUser(userId)).toList(),
      attributes: attributes
          ?.map(
            (attribute) => getIt<PropertyEntityMapper>().getCustomAttributeIdByName(attribute.attributeName,attribute?.value)
          )
          .toList(),
      callShareCount: callShareCount,
      brochures: brochures?.map((brochure) => brochure.toEntity()).toList(),
      dimension: dimension?.toEntity(),
      facing: facing,
      goPropertyId: goPropertyId,
      monetaryInfo: monetaryInfo?.toEntity(),
      ownerDetails: ownerDetails?.toEntity(),
      rating: rating,
      noOfFloorsOccupied: noOfFloorsOccupied,
      tenantContactInfoEntity: tenantContactInfo?.toEntity(),
      coWorkingOperator: coWorkingOperator,
      coWorkingOperatorName: coWorkingOperatorName,
      coWorkingOperatorPhone: coWorkingOperatorPhone,
      lockInPeriod: lockInPeriod,
      noticePeriod: noticePeriod,
      tagInfo: tagInfo?.toEntity(),
      offeringType: offeringType,
      completionStatus: completionStatus,
      listingSources: listingSources,
      dldPermitNumber: dldPermitNumber,
      aboutPropertyWithLanguage: aboutPropertyWithLanguage,
      dtcmPermit: dtcmPermit,
      language: language,
      listingExpireDate: listingExpireDate,
      listingLevel: listingLevel,
      listingSourceAddresses: listingSourceAddresses,
      listingStatus: listingStatus,
      refrenceNo: refrenceNo,
      titleWithLanguage: titleWithLanguage,
      view360Url: view360Url,
      taxationMode: taxationMode,
      isWaterMarkEnabled: isWaterMarkEnabled,
      possesionType: possesionType,
      securityDepositAmount: securityDepositAmount,
      securityDepositUnit: securityDepositUnit,
      listingOnBehalf: listingOnBehalf?.map((userId) => getIt<LeadEntityMapper>().getUser(userId)).toList(),
      shouldVisisbleOnListing: shouldVisisbleOnListing,
      propertyOwnerDetails: propertyOwnerDetails?.map((e) => e.toEntity()).toList(),
    );
  }

  const GetPropertyModel({
    this.id,
    this.title,
    this.saleType,
    this.enquiredFor,
    this.notes,
    this.furnishStatus,
    this.status,
    this.rating,
    this.shareCount,
    this.possessionDate,
    this.isGOListingEnabled,
    this.facing,
    this.goPropertyId,
    this.noOfBHK,
    this.bhkType,
    this.monetaryInfo,
    this.ownerDetails,
    this.dimension,
    this.tagInfo,
    this.images,
    this.imageUrls,
    this.attributes,
    this.amenities,
    this.brochures,
    this.whatsAppShareCount,
    this.callShareCount,
    this.emailShareCount,
    this.smsShareCount,
    this.aboutProperty,
    this.address,
    this.links,
    this.project,
    this.assignedTo,
    this.lastModifiedOn,
    this.createdOn,
    this.createdBy,
    this.lastModifiedBy,
    this.propertyType,
    this.micrositeURL,
    this.serialNo,
    this.coWorkingOperator,
    this.coWorkingOperatorName,
    this.coWorkingOperatorPhone,
    this.lockInPeriod,
    this.noticePeriod,
    this.noOfFloorsOccupied,
    this.tenantContactInfo,
    this.listingStatus,
    this.listingExpireDate,
    this.listingSources,
    this.dldPermitNumber,
    this.refrenceNo,
    this.dtcmPermit,
    this.offeringType,
    this.completionStatus,
    this.language,
    this.titleWithLanguage,
    this.aboutPropertyWithLanguage,
    this.view360Url,
    this.listingLevel,
    this.listingSourceAddresses,
    this.taxationMode,
    this.isWaterMarkEnabled,
    this.possesionType,
    this.securityDepositAmount,
    this.securityDepositUnit,
    this.listingOnBehalf,
    this.shouldVisisbleOnListing,
    this.propertyOwnerDetails,
  });
}

@JsonSerializable()
class PropertyWithDegreeMatchedModel {
  final int? totalNoOfFields;
  final int? noOfFieldsMatched;
  final String? percentageOfFieldsMatched;
  final GetPropertyModel? property;

  PropertyWithDegreeMatchedModel({this.totalNoOfFields, this.noOfFieldsMatched, this.percentageOfFieldsMatched, this.property});

  PropertyWithDegreeMatchedEntity toEntity() {
    return PropertyWithDegreeMatchedEntity(
      noOfFieldsMatched: noOfFieldsMatched,
      percentageOfFieldsMatched: percentageOfFieldsMatched,
      property: property?.toEntity(),
      totalNoOfFields: totalNoOfFields,
    );
  }

  factory PropertyWithDegreeMatchedModel.fromJson(Map<String, dynamic> json) => _$PropertyWithDegreeMatchedModelFromJson(json);

  Map<String, dynamic> toJson() => _$PropertyWithDegreeMatchedModelToJson(this);
}

@JsonSerializable()
class CustomListingSourceModel {
  final String? id;
  final String? displayName;
  final int? value;
  final int? orderRank;
  final String? imageURL;
  final String? progressColor;
  final String? backgroundColor;
  final bool? isDefault;

  CustomListingSourceModel({
    this.id,
    this.displayName,
    this.value,
    this.orderRank,
    this.imageURL,
    this.progressColor,
    this.backgroundColor,
    this.isDefault,
  });

  factory CustomListingSourceModel.fromJson(Map<String, dynamic> json) => _$CustomListingSourceModelFromJson(json);

  Map<String, dynamic> toJson() => _$CustomListingSourceModelToJson(this);
}
