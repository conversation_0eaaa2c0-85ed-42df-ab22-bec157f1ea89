part of 'custom_add_lead_bloc.dart';

@immutable
sealed class CustomAddLeadEvent {}

final class AddLeadInitialEvent extends CustomAddLeadEvent {
  final GlobalSettingModel? globalSettingModel;
  final GetLeadEntity? getLeadEntity;

  AddLeadInitialEvent({this.globalSettingModel, this.getLeadEntity});
}

final class ToggleEnquiredForEvent extends CustomAddLeadEvent {
  final ItemSimpleModel<EnquiryType>? selectedEnquiredFor;

  ToggleEnquiredForEvent(this.selectedEnquiredFor);
}

final class TogglePropertyTypeEvent extends CustomAddLeadEvent {
  final ItemSimpleModel<PropertyType> selectedPropertyType;

  TogglePropertyTypeEvent(this.selectedPropertyType);
}

final class ToggleFurnishStatusEvent extends CustomAddLeadEvent {
  final ItemSimpleModel<FurnishStatus> selectedFurnishStatus;

  ToggleFurnishStatusEvent(this.selectedFurnishStatus);
}

final class ToggleOfferingTypeEvent extends CustomAddLeadEvent {
  final ItemSimpleModel<OfferingType> selectedOfferingType;

  ToggleOfferingTypeEvent(this.selectedOfferingType);
}

final class TogglePropertySubTypeEvent extends CustomAddLeadEvent {
  final ItemSimpleModel<String> selectedPropertySubType;

  TogglePropertySubTypeEvent(this.selectedPropertySubType);
}

final class ToggleNoOfBhkEvent extends CustomAddLeadEvent {
  final ItemSimpleModel<double> selectedNoOfBhk;

  ToggleNoOfBhkEvent(this.selectedNoOfBhk);
}

final class ToggleBedsEvent extends CustomAddLeadEvent {
  final ItemSimpleModel<Beds> selectedBeds;

  ToggleBedsEvent(this.selectedBeds);
}

final class ToggleBathsEvent extends CustomAddLeadEvent {
  final ItemSimpleModel<int> selectedBaths;

  ToggleBathsEvent(this.selectedBaths);
}

final class ToggleProfessionEvent extends CustomAddLeadEvent {
  final ItemSimpleModel<Profession>? selectedProfession;

  ToggleProfessionEvent(this.selectedProfession);
}

final class SelectCarpetAreaEvent extends CustomAddLeadEvent {
  final SelectableItem<MasterAreaUnitsModel>? selectedCarpetArea;

  SelectCarpetAreaEvent(this.selectedCarpetArea);
}

final class SelectSaleableAreaEvent extends CustomAddLeadEvent {
  final SelectableItem<MasterAreaUnitsModel>? selectedSaleableArea;

  SelectSaleableAreaEvent(this.selectedSaleableArea);
}

final class SelectBuiltUpAreaEvent extends CustomAddLeadEvent {
  final SelectableItem<MasterAreaUnitsModel>? selectedBuiltUpArea;

  SelectBuiltUpAreaEvent(this.selectedBuiltUpArea);
}

final class SelectPropertyAreaEvent extends CustomAddLeadEvent {
  final SelectableItem<MasterAreaUnitsModel>? selectedPropertyArea;

  SelectPropertyAreaEvent(this.selectedPropertyArea);
}

final class SelectNetAreaEvent extends CustomAddLeadEvent {
  final SelectableItem<MasterAreaUnitsModel>? selectedNetArea;

  SelectNetAreaEvent(this.selectedNetArea);
}

final class SelectLeadSourceEvent extends CustomAddLeadEvent {
  final SelectableItem<int> selectedLeadSource;

  SelectLeadSourceEvent(this.selectedLeadSource);
}

final class SelectLeadNationalityEvent extends CustomAddLeadEvent {
  final SelectableItem<Country> selectedCountry;

  SelectLeadNationalityEvent(this.selectedCountry);
}

final class SelectLeadSubSourceEvent extends CustomAddLeadEvent {
  final SelectableItem<String> selectedLeadSubSource;

  SelectLeadSubSourceEvent(this.selectedLeadSubSource);
}

final class SelectAgencyNameEvent extends CustomAddLeadEvent {
  final List<SelectableItem<String>> selectedAgencyName;

  SelectAgencyNameEvent(this.selectedAgencyName);
}

final class RemoveCampaignNameEvent extends CustomAddLeadEvent {
  final SelectableItem<String> selectedCampaignName;

  RemoveCampaignNameEvent(this.selectedCampaignName);
}

final class SelectCampaignNameEvent extends CustomAddLeadEvent {
  final List<SelectableItem<String>> selectedCampaignName;

  SelectCampaignNameEvent(this.selectedCampaignName);
}

final class SelectFloorEvent extends CustomAddLeadEvent {
  final List<SelectableItem<String>> selectedFloor;

  SelectFloorEvent(this.selectedFloor);
}

final class RemoveAgencyNameEvent extends CustomAddLeadEvent {
  final SelectableItem<String> selectedAgencyName;

  RemoveAgencyNameEvent(this.selectedAgencyName);
}

final class RemoveChannelPartnerNameEvent extends CustomAddLeadEvent {
  final SelectableItem<String> selectedChannelPartner;

  RemoveChannelPartnerNameEvent(this.selectedChannelPartner);
}

final class SelectPropertiesEvent extends CustomAddLeadEvent {
  final List<SelectableItem<String>> selectedProperties;

  SelectPropertiesEvent(this.selectedProperties);
}

final class RemovePropertyEvent extends CustomAddLeadEvent {
  final SelectableItem<String> selectedProperty;

  RemovePropertyEvent(this.selectedProperty);
}

final class SelectProjectsEvent extends CustomAddLeadEvent {
  final List<SelectableItem<String>> selectedProjects;

  SelectProjectsEvent(this.selectedProjects);
}

final class RemoveProjectsEvent extends CustomAddLeadEvent {
  final SelectableItem<String> selectedProjects;

  RemoveProjectsEvent(this.selectedProjects);
}

final class SelectAssignedUserEvent extends CustomAddLeadEvent {
  final SelectableItem<String> selectedUser;

  SelectAssignedUserEvent(this.selectedUser);
}

final class SelectSourcingManagerEvent extends CustomAddLeadEvent {
  final SelectableItem<String> selectedSourcingManager;

  SelectSourcingManagerEvent(this.selectedSourcingManager);
}

final class SelectClosingManagerEvent extends CustomAddLeadEvent {
  final SelectableItem<String> selectedClosingManager;

  SelectClosingManagerEvent(this.selectedClosingManager);
}

final class SelectSecondaryUserEvent extends CustomAddLeadEvent {
  final SelectableItem<String> selectedUser;

  SelectSecondaryUserEvent(this.selectedUser);
}

final class ToggleSubTypesExpandedEvent extends CustomAddLeadEvent {}

final class ToggleNoOfBhkExpandedEvent extends CustomAddLeadEvent {}

final class ToggleEmailFieldEvent extends CustomAddLeadEvent {}

final class ToggleAltPhoneFieldEvent extends CustomAddLeadEvent {
  final bool? hideAltPhoneField;

  ToggleAltPhoneFieldEvent({this.hideAltPhoneField});
}

final class ToggleReferralFieldsEvent extends CustomAddLeadEvent {}

final class TogglePossessionDateEvent extends CustomAddLeadEvent {}

final class SelectPossessionDateEvent extends CustomAddLeadEvent {
  final DateTime selectedDate;

  SelectPossessionDateEvent(this.selectedDate);
}

final class AddLocationEvent extends CustomAddLeadEvent {
  final AddressModel? location;

  AddLocationEvent(this.location);
}

final class RemoveLocationEvent extends CustomAddLeadEvent {
  final ItemSimpleModel<AddressModel> selectedItem;

  RemoveLocationEvent(this.selectedItem);
}

final class CheckLeadContactAlreadyExistsEvent extends CustomAddLeadEvent {
  final String countryCode;
  final String contactNo;

  CheckLeadContactAlreadyExistsEvent(this.countryCode, this.contactNo);
}

final class CheckAltContactAlreadyExistsEvent extends CustomAddLeadEvent {
  final String countryCode;
  final String contactNo;

  CheckAltContactAlreadyExistsEvent(this.countryCode, this.contactNo);
}

final class CreateLeadEvent extends CustomAddLeadEvent {}

final class ResetStateEvent extends CustomAddLeadEvent {}

final class OnLeadContactChangedEvent extends CustomAddLeadEvent {
  final String countryCode;
  final String contactNumber;

  OnLeadContactChangedEvent(this.countryCode, this.contactNumber);
}

final class OnAltContactChangedEvent extends CustomAddLeadEvent {
  final String countryCode;
  final String contactNumber;

  OnAltContactChangedEvent(this.countryCode, this.contactNumber);
}

final class OnReferralContactChangedEvent extends CustomAddLeadEvent {
  final String countryCode;
  final String contactNumber;

  OnReferralContactChangedEvent(this.countryCode, this.contactNumber);
}

final class PickContactEvent extends CustomAddLeadEvent {}

final class SelectCurrency extends CustomAddLeadEvent {
  final SelectableItem<String> selectedCurrency;

  SelectCurrency(this.selectedCurrency);
}
final class SelectPossessionType extends CustomAddLeadEvent {
  final SelectableItem<PossessionType?>?selectPossessionType;

  SelectPossessionType(this.selectPossessionType);
}

final class SelectChannelPartnerEvent extends CustomAddLeadEvent {
  final List<SelectableItem<String>> selectedChannelPartners;

  SelectChannelPartnerEvent(this.selectedChannelPartners);
}

final class AddCustomerLocationEvent extends CustomAddLeadEvent {
  final AddressModel? customerLocation;

  AddCustomerLocationEvent(this.customerLocation);
}

final class RemoveCustomerLocationEvent extends CustomAddLeadEvent {
  final ItemSimpleModel<AddressModel>? selectedItem;

  RemoveCustomerLocationEvent(this.selectedItem);
}

final class SelectPurposeEvent extends CustomAddLeadEvent {
  final SelectableItem<PurposeEnum>? selectedPurpose;

  SelectPurposeEvent(this.selectedPurpose);
}

final class AssignedToLoggedInUser extends CustomAddLeadEvent {}

final class OnExecutiveContactChangedEvent extends CustomAddLeadEvent {
  final String countryCode;
  final String contactNumber;

  OnExecutiveContactChangedEvent(this.countryCode, this.contactNumber);
}
