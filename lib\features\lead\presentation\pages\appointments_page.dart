import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/models/address_model.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_text_form_field.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/preview_file.dart';
import 'package:leadrat/features/lead/presentation/bloc/appointments_bloc/appointments_bloc.dart';
import 'package:leadrat/features/lead/presentation/pages/update_lead_status_page.dart';
import 'package:leadrat/features/lead/presentation/widgets/appointment_details_screen_skeliton_view.dart';

import '../../../../core_main/common/widgets/select_file_bottom_modal.dart';
import '../../../../core_main/di/injection_container.dart';
import '../../../../core_main/utilities/dialog_manager.dart';
import '../../../../core_main/utilities/leadrat_custom_snackbar.dart';
import '../../../search_location/presentation/pages/search_location_page.dart';
import '../bloc/update_lead_status_bloc/update_lead_status_bloc.dart';
import '../item/appointment_done_item.dart';

class AppointmentsPage extends LeadratStatefulWidget {
  const AppointmentsPage({super.key});

  @override
  State<AppointmentsPage> createState() => _AppointmentsPageState();
}

class _AppointmentsPageState extends LeadratState<AppointmentsPage> {
  int? selectedIndex = 0; // -1 means no text is selected initially.
  bool? isDone;
  String? projectName;
  String? countryCode;
  TextEditingController salesExecutiveNameController = TextEditingController();
  TextEditingController salesExecutiveNumberController = TextEditingController();
  TextEditingController notesController = TextEditingController();
  AppointmentDoneItem appointmentDoneItem = AppointmentDoneItem();

  @override
  Widget buildContent(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Container(
          color: ColorPalette.white,
          child: Padding(
            padding: const EdgeInsets.only(right: 16, top: 16, left: 16),
            child: BlocConsumer<AppointmentsBloc, AppointmentsState>(
              listener: (context, state) async {
                if (state.pageState == AppointmentsPageState.loading) {
                  DialogManager().showTransparentProgressDialog(context, message: "loading");
                } else if (state.pageState == AppointmentsPageState.failure) {
                  selectedIndex = selectedIndex! - 1;
                  DialogManager().hideTransparentProgressDialog();
                  LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: "There was an error!! Please try again");
                } else if (state.pageState == AppointmentsPageState.success) {
                  DialogManager().hideTransparentProgressDialog();
                  LeadratCustomSnackbar.show(context: context, type: SnackbarType.success, message: "Updated the appointment");
                  setState(() {
                    isDone = false;
                    if (selectedIndex != null && state.selectedProjects.length > selectedIndex! - 1) {
                      selectedIndex = selectedIndex! + 1;
                      if (selectedIndex != state.selectedProjects.length && (state.selectedProjects.isNotEmpty)) {
                        context.read<AppointmentsBloc>().add(ProjectSelectedEvent(state.leadProjects?[selectedIndex!], false));
                      }
                    }
                  });
                  salesExecutiveNumberController.text = "";
                  salesExecutiveNameController.text = "";
                  if ((selectedIndex == state.selectedProjects.length && !(state.selectedProjects.any((element) => element.isEnabled == null)) || (!state.isProjectMandatory && state.selectedProjects.isEmpty))) {
                    Navigator.pop(context);
                    context.read<AppointmentsBloc>().add(ClearStateEvent());
                    if (state.leadEntity?.status?.displayName == "Booked") {
                      if (getIt<UsersDataRepository>().checkHasPermission(AppModule.lead, CommandType.updateBookedLead)) {
                        getIt<UpdateLeadStatusBloc>().add(InitStatusesEvent(state.globalSettingModel, state.leadEntity));
                        getIt<UpdateLeadStatusBloc>().add(GetUsersEvent());
                        Navigator.push(context, MaterialPageRoute(builder: (context) => const UpdateLeadStatusPage(), settings: const RouteSettings(name: 'UpdateLeadStatusPage')));
                      }
                    } else {
                      getIt<UpdateLeadStatusBloc>().add(InitStatusesEvent(state.globalSettingModel, state.leadEntity));
                      getIt<UpdateLeadStatusBloc>().add(GetUsersEvent());
                      Navigator.push(context, MaterialPageRoute(builder: (context) => const UpdateLeadStatusPage(), settings: const RouteSettings(name: 'UpdateLeadStatusPage')));
                    }
                  }
                } else if (state.pageState == AppointmentsPageState.fetchingLocation) {
                  DialogManager().showTransparentProgressDialog(context, message: "fetching location");
                } else if (state.pageState == AppointmentsPageState.updating) {
                  DialogManager().showTransparentProgressDialog(context, message: "updating the status");
                } else if (state.pageState == AppointmentsPageState.fetchingLocationError) {
                  DialogManager().hideTransparentProgressDialog();
                  LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: "Error in fetching the location");
                } else {
                  DialogManager().hideTransparentProgressDialog();
                }
              },
              builder: (context, state) {
                if (state.allProjects == null) {
                  return const Center(child: AppointmentDetailsScreenSkeletonView()); // or another loading widget
                }

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Project and Appointment Details Row
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "appointment details",
                              style: LexendTextStyles.lexend17SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                            ),
                            // Subtitle Text
                            Text(
                              "you can update the status of your essential lead",
                              style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.primaryDarkColor),
                            ),
                          ],
                        ),
                        // Text("project 1/3", style: LexendTextStyles.lexend14Regular.copyWith(color: ColorPalette.primaryDarkColor)),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Lead enquiry card
                    Container(
                      decoration: BoxDecoration(
                        color: ColorPalette.lightDarkBackground, // Dark Gray background
                        borderRadius: BorderRadius.circular(10),
                      ),
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Lead Name
                          Text(state.leadName ?? '--', style: LexendTextStyles.lexend14Medium.copyWith(color: ColorPalette.tertiaryTextColor)),
                          const SizedBox(height: 4),

                          // Property Details
                          Text(state.enquiryDescription ?? '--', style: LexendTextStyles.lexend14Medium.copyWith(color: ColorPalette.primaryGreen)),

                          const SizedBox(height: 10),

                          // Price, Area, and Locality Row
                          Row(
                            children: [
                              _infoItem("price : ", state.price ?? '--'),
                              const SizedBox(width: 10),
                              _verticalDivider(),
                              const SizedBox(width: 4),
                              _infoItem("area : ", "${state.dimension?.title ?? '--'} ${state.dimension?.value ?? '--'}"),
                              const SizedBox(width: 10),
                              _verticalDivider(),
                              const SizedBox(width: 4),
                              Flexible(
                                // This ensures the text is constrained within the row
                                child: _infoItem("locality : ", state.location == '' ? '--' : state.location ?? '--'),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Lead projects
                    Container(
                      color: ColorPalette.superSilver,
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            if (state.selectedProjects.isEmpty)
                              Text(
                                'no lead projects found!! please add a project',
                                style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.fadedRed.withOpacity(.8)),
                              ),
                            if (state.selectedProjects.isNotEmpty)
                              Expanded(
                                child: SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: List.generate(state.selectedProjects.length, (index) {
                                      var project = state.selectedProjects[index];
                                      return GestureDetector(
                                        onTap: () {
                                          context.read<AppointmentsBloc>().add(ProjectSelectedEvent(state.leadProjects?[index], false));
                                          setState(() {
                                            selectedIndex = index;
                                            isDone = false;
                                          });
                                          salesExecutiveNumberController.text = "";
                                          salesExecutiveNameController.text = "";
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 10.0),
                                          decoration: BoxDecoration(color: index == selectedIndex ? ColorPalette.gray200.withOpacity(.5) : ColorPalette.transparent, borderRadius: BorderRadius.circular(4), border: index == selectedIndex ? Border.all(color: ColorPalette.primaryDarkColor, width: .3) : null),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Text(
                                                    project.title,
                                                    style: LexendTextStyles.lexend14Medium.copyWith(
                                                        color: project.isEnabled == null
                                                            ? ColorPalette.primaryDarkColor
                                                            : (project.isEnabled ?? false)
                                                                ? ColorPalette.primaryGreen
                                                                : ColorPalette.fadedRed),
                                                  ),
                                                  const SizedBox(width: 10),
                                                  if (project.isEnabled ?? false) const Icon(Icons.check_circle, color: ColorPalette.primaryGreen, size: 18),
                                                  if (!(project.isEnabled ?? true)) const CircleAvatar(backgroundColor: ColorPalette.fadedRed, radius: 8, child: Icon(Icons.close, color: ColorPalette.white, size: 8)),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      );
                                    }),
                                  ),
                                ),
                              ),
                            SelectableItemBottomSheet<String?>(
                              key: const ValueKey('allProjects'),
                              title: 'select projects',
                              selectableItems: state.allProjects ?? [],
                              initialSelectedItems: null,
                              isMultipleSelection: true,
                              canSearchItems: true,
                              saveSelectedValues: false,
                              onItemsSelected: (value) => {
                                context.read<AppointmentsBloc>().add(UpdateLeadProjectsEvent(selectedProjects: value)),
                              },
                              child: const Icon(
                                Icons.add_circle_outline,
                                color: ColorPalette.primaryGreen,
                                size: 24,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Muso lottie
                    if (!(isDone ?? false)) Expanded(child: Center(child: Image.asset("assets/images/image_walkingrat.gif", gaplessPlayback: true, fit: BoxFit.fitHeight))),
                    if (isDone ?? false)
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'is appointment done?',
                                    style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.primaryDarkColor),
                                  ),
                                  GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          isDone = false;
                                        });
                                        salesExecutiveNumberController.text = "";
                                        salesExecutiveNameController.text = "";
                                      },
                                      child: Text(
                                        'Done✎',
                                        style: LexendTextStyles.lexend13SemiBold.copyWith(color: ColorPalette.primaryGreen),
                                      )),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text.rich(TextSpan(children: [
                                    TextSpan(
                                      text: 'edit selected project',
                                      style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.primaryDarkColor),
                                    ),
                                    if (state.isProjectMandatory)
                                      TextSpan(
                                        text: ' *',
                                        style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.fadedRed),
                                      )
                                  ])),
                                  const SizedBox(width: 20),
                                  Flexible(
                                    child: SelectableItemBottomSheet<String?>(
                                      key: const ValueKey('editProjects'),
                                      title: 'select projects',
                                      selectableItems: state.duplicateAllProjects ?? [],
                                      onItemSelected: (value) => {
                                        context.read<AppointmentsBloc>().add(ProjectSelectedEvent(value, true, index: selectedIndex)),
                                      },
                                      selectedItem: state.selectedProject,
                                      canSearchItems: true,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'documents',
                                    style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.primaryDarkColor),
                                  ),
                                  GestureDetector(
                                    onTap: () => {
                                      selectFileBottomModal(
                                        context,
                                        (selectedOption) {
                                          context.read<AppointmentsBloc>().add(AddLeadDocumentsEvent(selectedOption));
                                        },
                                      ),
                                    },
                                    child: Row(
                                      children: [
                                        const Icon(
                                          Icons.add_circle_outline_rounded,
                                          color: ColorPalette.primaryGreen,
                                        ),
                                        const SizedBox(
                                          width: 5,
                                        ),
                                        Text(
                                          'add document',
                                          style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.primaryGreen),
                                        )
                                      ],
                                    ),
                                  )
                                ],
                              ),
                              if (state.leadDocuments != null)
                                Align(
                                  alignment: Alignment.topLeft, // Ensure the chips start from the left
                                  child: Wrap(
                                    spacing: 8.0, // Space between chips
                                    runSpacing: 8.0, // Space between rows
                                    children: state.leadDocuments!.map((file) {
                                      return Chip(
                                        labelPadding: const EdgeInsets.symmetric(horizontal: 8.0),
                                        label: GestureDetector(
                                          onTap: () => PreviewFile.previewFile(file.value ?? '', context),
                                          child: Text(
                                            file.title,
                                            style: const TextStyle(
                                              color: Colors.black87, // Text color
                                              fontWeight: FontWeight.w500, // Slightly bold for file name
                                            ),
                                          ),
                                        ),
                                        backgroundColor: Colors.white,
                                        // Chip background color
                                        shape: StadiumBorder(
                                          side: BorderSide(
                                            color: Colors.grey.shade400, // Border color
                                            width: 1, // Border width
                                          ),
                                        ),
                                        deleteIcon: const Icon(
                                          Icons.cancel,
                                          color: Colors.grey, // Color of the cancel icon
                                          size: 18, // Icon size
                                        ),
                                        onDeleted: () {
                                          setState(() {
                                            state.leadDocuments!.remove(file); // Remove file from the list
                                          });
                                        },
                                      );
                                    }).toList(),
                                  ),
                                ),
                              const SizedBox(height: 16),
                              Padding(
                                padding: const EdgeInsets.fromLTRB(0, 0, 10, 0),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      (state.isMeetingScheduled ?? false) ? "meeting done by" : "site visit done by",
                                      style: LexendTextStyles.lexend12SemiBold.copyWith(
                                        color: ColorPalette.primaryDarkColor,
                                      ),
                                    ),
                                    SelectableItemBottomSheet(
                                      title: "select user ",
                                      canSearchItems: true,
                                      selectableItems: state.assignToUsers,
                                      selectedItem: state.selectedAssignedUser,
                                      onItemSelected: (selectedItem) {
                                        context.read<AppointmentsBloc>().add(SelectAssignedUserEvent(selectedItem));
                                      },
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 16),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'location',
                                    style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.primaryDarkColor),
                                  ),
                                  GestureDetector(
                                    onTap: () => {
                                      Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) => SearchLocationPage(
                                                    onLocationSelected: (value) => {
                                                      context.read<AppointmentsBloc>().add(AddLocationEvent(value)),
                                                    },
                                                  ))),
                                    },
                                    child: Row(
                                      children: [
                                        const Icon(
                                          Icons.search,
                                          color: ColorPalette.primaryGreen,
                                        ),
                                        Text(
                                          'seach location',
                                          style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.primaryGreen),
                                        )
                                      ],
                                    ),
                                  )
                                ],
                              ),
                              const SizedBox(height: 16),
                              if (state.leadLocation != null) _buildLocationWidget(state.leadLocation),
                              const SizedBox(height: 16),
                              LeadratTextFormField(
                                labelText: 'notes',
                                isRequired: state.isNotesMandatory,
                                hintText: 'ex: you can add additional inforamtion about your lead',
                                maxLines: 3,
                                controller: notesController,
                              ),
                            ],
                          ),
                        ),
                      ),

                    if ((!(isDone ?? false) && state.leadProjects!.isNotEmpty) || (!(state.isProjectMandatory) && !(isDone ?? false)))
                      Column(
                        children: [
                          Center(
                            child: Text(
                              'is ${state.leadEntity?.status?.displayName == 'Meeting Scheduled' ? 'meeting' : 'site visit'} done?',
                              style: LexendTextStyles.lexend13SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                            ),
                          ),
                          const SizedBox(
                            height: 14,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              GestureDetector(
                                onTap: () => {
                                  appointmentDoneItem = AppointmentDoneItem(
                                    isMeeting: true,
                                    selectedProject: (state.leadProjects?.isNotEmpty ?? false) ? (state.leadProjects?[selectedIndex ?? 0]) : null,
                                  ),
                                  setState(() {
                                    context.read<AppointmentsBloc>().add(AppointmentNotDoneEvent(appointmentDoneItem));
                                    // selectedIndex = selectedIndex! + 1;
                                  }),
                                },
                                child: Container(
                                  height: 30,
                                  width: 163,
                                  decoration: BoxDecoration(
                                    color: Colors.white, // Background color
                                    border: Border.all(
                                      color: ColorPalette.mediumRed, // Border color
                                      width: 1.0, // Border width
                                    ),
                                    borderRadius: BorderRadius.circular(20.0), // Optional: rounded corners
                                  ),
                                  child: Center(
                                    child: Text(
                                      'not done',
                                      style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.mediumRed),
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(
                                width: 20,
                              ),
                              GestureDetector(
                                onTap: () => {
                                  setState(() {
                                    if (selectedIndex != null && selectedIndex! < 0) {
                                      isDone = false;
                                    } else {
                                      isDone = true;
                                    }
                                  })
                                },
                                child: Container(
                                  height: 30,
                                  width: 163,
                                  decoration: BoxDecoration(
                                    color: Colors.white, // Background color
                                    border: Border.all(
                                      color: ColorPalette.primaryGreen, // Border color
                                      width: 1.0, // Border width
                                    ),
                                    borderRadius: BorderRadius.circular(20.0), // Optional: rounded corners
                                  ),
                                  child: Center(
                                    child: Text(
                                      'done',
                                      style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.primaryGreen),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(
                            height: 17,
                          ),
                          Container(
                            height: 2,
                            color: ColorPalette.gray100,
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          GestureDetector(
                            onTap: () => {
                              context.read<AppointmentsBloc>().add(ClearStateEvent()),
                              Navigator.pop(context),
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.more_time_rounded,
                                  color: ColorPalette.gray500, // Greenish color for icon
                                  size: 24,
                                ),
                                const SizedBox(
                                  width: 8,
                                ),
                                Text(
                                  'update later',
                                  style: LexendTextStyles.lexend13Regular.copyWith(color: ColorPalette.primaryDarkColor),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                    if (isDone ?? false)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          GestureDetector(
                            onTap: () => {
                              setState(() {
                                isDone = false;
                              })
                            },
                            child: Container(
                              constraints: BoxConstraints(minWidth: context.width(35)), // Minimum width constraint
                              decoration: BoxDecoration(
                                color: ColorPalette.lightDarkBackground,
                                border: Border.all(color: ColorPalette.white),
                                borderRadius: BorderRadius.circular(100),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Flexible(
                                    fit: FlexFit.loose,
                                    child: Padding(
                                      padding: const EdgeInsets.only(
                                        top: 17,
                                        bottom: 17,
                                        left: 20,
                                        right: 20,
                                      ),
                                      child: Text(
                                        'cancel',
                                        textAlign: TextAlign.center,
                                        style: LexendTextStyles.lexend14Regular.copyWith(
                                          color: ColorPalette.white,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(
                            width: 20,
                          ),
                          GestureDetector(
                            onTap: () => {
                              appointmentDoneItem = AppointmentDoneItem(
                                leadLocation: state.leadLocation,
                                leadDocuments: state.leadDocuments,
                                notes: notesController.text,
                                isMeeting: true,
                                salesExecutiveName: salesExecutiveNameController.text,
                                salesExecutiveNumber: salesExecutiveNumberController.text.isNotNullOrEmpty() ? '+$countryCode${salesExecutiveNumberController.text}' : null,
                                selectedProject: (state.leadProjects?.isNotEmpty ?? false) ? (state.leadProjects?[selectedIndex ?? 0]) : null,
                              ),
                              setState(() {
                                context.read<AppointmentsBloc>().add(AppointmentDoneEvent(appointmentDoneItem));
                                // selectedIndex = selectedIndex! + 1;
                              }),
                            },
                            child: Container(
                              constraints: BoxConstraints(minWidth: context.width(35)), // Minimum width constraint
                              decoration: BoxDecoration(
                                color: ColorPalette.lightDarkBackground,
                                border: Border.all(color: ColorPalette.white),
                                borderRadius: BorderRadius.circular(100),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Flexible(
                                    fit: FlexFit.loose,
                                    child: Padding(
                                      padding: const EdgeInsets.only(
                                        top: 17,
                                        bottom: 17,
                                        left: 20,
                                        right: 20,
                                      ),
                                      child: Text(
                                        'save & continue',
                                        textAlign: TextAlign.center,
                                        style: LexendTextStyles.lexend14Regular.copyWith(
                                          color: ColorPalette.white,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    const SizedBox(
                      height: 15,
                    )
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  // Widget for the vertical divider (|) between info items
  Widget _verticalDivider() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Container(
        height: 20,
        width: 1,
        color: ColorPalette.gravelFint, // Divider color (light gray)
      ),
    );
  }

  // Widget for each info item (e.g., price, area, etc.)
  Widget _infoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.tertiaryTextColor),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: LexendTextStyles.lexend13SemiBold.copyWith(color: ColorPalette.white),
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildLocationWidget(AddressModel? leadLocation) {
    var locality = leadLocation?.locality ?? leadLocation?.subLocality ?? '';
    var state_ = leadLocation?.state ?? '';
    var city = leadLocation?.city ?? '';
    var country = leadLocation?.country ?? '';
    String location = [locality, city, state_, country].where((element) => element.isNotEmpty).join(', ');
    return Container(
      width: context.width(100),
      color: ColorPalette.superSilver,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
        child: Center(
          child: Text(
            location,
            style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.primaryDarkColor),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }
}
