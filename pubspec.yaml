name: leadrat
description: CRM for Real Estate

version: 1.0.12+124
#version: 1.0.99+119 #IOS

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter


  cupertino_icons: ^1.0.2
  shared_preferences: ^2.0.15
  path_provider: ^2.0.5
  carousel_slider: ^5.0.0
  lottie: ^1.4.2
  image_picker: ^0.8.4+4
  permission_handler: ^11.4.0
  skeletonizer: ^1.1.0
  url_launcher: ^6.1.5
  share_plus: ^10.0.0
  flutter_svg: ^2.0.9
  fluttercontactpicker: ^4.7.0
  geolocator: ^11.0.0
  jumping_dot: ^0.0.6
  dart_jsonwebtoken: ^2.14.0
  package_info: ^2.0.2
  firebase_core: ^3.10.1
  firebase_messaging: ^15.2.1
  flutter_local_notifications: ^18.0.1
  uuid: ^4.4.0
  device_info_plus: ^11.2.0
  intl: ^0.19.0
  network_info_plus: ^5.0.3
  webview_flutter: ^4.8.0
  flutter_device_type: ^0.4.0
  path: ^1.9.0
  gradient_borders: ^1.0.1
  flutter_slidable: ^3.1.2
  shimmer: ^3.0.0
  pull_to_refresh: ^2.0.0
  just_audio: ^0.9.39
  flutter_image_compress: ^2.3.0
  dio: ^5.6.0
  get_it: ^7.7.0
  fpdart: ^1.1.0
  json_annotation: ^4.8.1
  flutter_bloc: ^8.1.6
  equatable: ^2.0.5
  collection: ^1.16.0
  package_info_plus: ^8.0.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  image: ^3.0.1
  flutter_html: ^3.0.0-beta.2
  connectivity_plus: ^6.1.0
  web_socket_channel: ^3.0.1
  video_player: ^2.9.2
  file_picker: ^8.1.7
  flutter_windowmanager: ^0.2.0
  aws_client: ^0.6.0
  flutter_dotenv: ^5.2.1
  flutter_phone_direct_caller: ^2.1.1
  cached_network_image: ^3.4.1
  sentry_flutter: ^8.13.2
  dlibphonenumber: ^1.1.32
  app_settings: ^5.2.0
  reorderables: ^0.6.0

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/image_launcher_icon.png"

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^2.0.0
  platform_device_id: ^1.0.1
  geocoding: ^2.0.5
  build_runner: ^2.4.9
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1


flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/prospect/
    - assets/icons/
    - assets/lottie/
    - assets/config/.env

  fonts:
    - family: LexendDecaThin
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-Thin.ttf
          weight: 100
          style: normal

    - family: LexendDecaRegular
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-Regular.ttf
          weight: 400
          style: normal

    - family: LexendDecaMedium
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-Medium.ttf
          weight: 500
          style: normal

    - family: LexendDecaSemiBold
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-SemiBold.ttf
          weight: 600
          style: normal

    - family: LexendDecaBold
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-Bold.ttf
          weight: 700
          style: normal

    - family: LexendDecaExtraBold
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-ExtraBold.ttf
          weight: 800
          style: normal

    - family: LexendDecaBlack
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-Black.ttf
          weight: 900
          style: normal

    - family: LexendDecaLight
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-Light.ttf
          weight: 300
          style: normal

    - family: LexendDecaExtraLight
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-ExtraLight.ttf
          weight: 200
          style: normal
