import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_phone_direct_caller/flutter_phone_direct_caller.dart';
import 'package:intl/intl.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/base/usecase/use_case.dart';
import 'package:leadrat/core_main/common/constants/app_analytics_constants.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/repository/global_setting_repository.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/models/ivr_config_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/contact_type_enum.dart';
import 'package:leadrat/core_main/enums/common/date_range.dart';
import 'package:leadrat/core_main/enums/common/no_of_beds.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/data_management/prospect_filter_key.dart';
import 'package:leadrat/core_main/enums/data_management/prospect_visibility.dart';
import 'package:leadrat/core_main/extensions/integer_extension.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/services/ivr_service/ivr_service.dart';
import 'package:leadrat/features/data_management/data/models/data_filter_model.dart';
import 'package:leadrat/features/data_management/domain/entities/prospect_entity.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_call_through_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_count_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_usecase.dart';
import 'package:leadrat/features/data_management/presentation/bloc/data_details_bloc/data_details_bloc.dart';
import 'package:leadrat/features/data_management/presentation/items/prospect_count_item.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

part 'manage_data_event.dart';
part 'manage_data_state.dart';

class ManageDataBloc extends Bloc<ManageDataEvent, ManageDataState> {
  final GetProspectUseCase _getProspectUseCase;
  final GetProspectCountUseCase _getProspectCountUseCase;
  final GetCallThroughUseCase _getCallThroughUseCase;
  final GlobalSettingRepository _globalSettingRepository;
  final AppAnalysisRepository _appAnalysisRepository;
  final IvrService _ivrService;
  final ScrollController itemListScrollController = ScrollController();
  final ScrollController filterTypeScrollController = ScrollController();
  List<ItemSimpleModel<ProspectFilterKey>> selectedFilters = [];
  late DataFilterModel dataFilter;
  int selectedFilterIndex = 0;
  List<ProspectCountItem>? filterItems = [];

  GlobalSettingModel? globalSettingModel;
  IvrConfigModel? ivrConfigModel;

  ManageDataBloc(
    this._getProspectUseCase,
    this._getProspectCountUseCase,
    this._getCallThroughUseCase,
    this._globalSettingRepository,
    this._ivrService,
    this._appAnalysisRepository,
  ) : super(const ManageDataState()) {
    on<ManageDataInitialEvent>(_onManageDataInitial);
    on<GetAllProspectEvent>(_onGetAllProspect);
    on<GetAllProspectCountEvent>(_onGetAllProspectCount);
    on<FilterOptionClickEvent>(_onFilterOptionClick);
    on<ScrolledEvent>(_onScrolled);
    on<LoadMoreEvent>(_onLoadMore);
    on<RefreshedEvent>(_onRefreshed);
    on<CommunicationActionToggleEvent>(_onCommunicationActionToggle);
    on<CallThroughIVREvent>(_onCallThroughIVR);
    on<CallThroughDialerEvent>(_onCallThroughDialer);
    on<RemoveFilterEvent>(_onRemoveFilter);
    on<LoadManageDataInitialEvent>(_onLoadManageDataInitial);
  }

  FutureOr<void> _onManageDataInitial(ManageDataInitialEvent event, Emitter<ManageDataState> emit) async {
    dataFilter = event.dataFilter ?? DataFilterModel(prospectVisiblity: ProspectVisibility.selfWithReportee);
    selectedFilters = _initSelectedFilters();
    selectedFilterIndex = 0;
    emit(
      state.copyWith(
        pageState: PageState.loading,
        prospectEntityList: [],
        pageNo: 1,
        prospectCounts: [],
        selectIndex: 0,
        errorMessage: null,
        loadMore: false,
        ivrState: IvrState.initial,
        selectedFilters: selectedFilters,
        isContactOption: false,
      ),
    );
    globalSettingModel = await _globalSettingRepository.getGlobalSettings();
    add(GetAllProspectCountEvent());
    add(GetAllProspectEvent(event.filterType ?? '22e90157-8c53-490e-bc5d-22c05d33b0ef'));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileManageDataPageManageDataView);
  }

  FutureOr<void> _onGetAllProspect(GetAllProspectEvent event, Emitter<ManageDataState> emit) async {
    emit(state.copyWith(prospectEntityList: null, isContactOption: false, ivrState: IvrState.initial));
    final prospectList = await _getProspectUseCase.call(GetProspectUseCaseParam(state.pageNo, event.filterType, dataFilter));
    List<ProspectEntity?>? models = state.prospectEntityList;
    prospectList.fold(
        (failure) => emit(
              state.copyWith(
                pageState: PageState.failure,
                errorMessage: failure.message,
                prospectEntityList: null,
                pageNo: state.pageNo,
                loadMore: false,
                isContactOption: false,
                ivrState: IvrState.initial,
              ),
            ), (result) {
      emit(state.copyWith(
        prospectEntityList: [...?models, ...?result?.items],
        totalCount: result?.totalCount,
        errorMessage: null,
        pageState: PageState.success,
        pageNo: state.pageNo + 1,
        loadMore: false,
        isContactOption: false,
        ivrState: IvrState.initial,
      ));
    });
  }

  FutureOr<void> _onGetAllProspectCount(GetAllProspectCountEvent event, Emitter<ManageDataState> emit) async {
    final prospectCountList = await _getProspectCountUseCase.call(UseCaseParam(dataFilter));
    filterItems = state.prospectCounts;

    prospectCountList.fold((failure) {
      emit(state.copyWith(pageState: PageState.failure, errorMessage: failure.message, prospectCounts: filterItems, isContactOption: false, ivrState: IvrState.initial));
    }, (result) {
      if (result != null) {
        filterItems = [];
        for (var filterModel in result) {
          filterItems?.add(ProspectCountItem(
            filterName: filterModel.name,
            count: filterModel.count,
            filterIndex: filterModel.orderRank,
            isSelected: filterModel.name == 'All',
            id: filterModel.id,
          ));
        }
        if (filterItems!.isEmpty) {
          filterItems = state.prospectCounts;
        }
      }
      emit(
        state.copyWith(prospectCounts: [...?filterItems], isContactOption: false, ivrState: IvrState.initial),
      );
    });
  }

  FutureOr<void> _onFilterOptionClick(FilterOptionClickEvent event, Emitter<ManageDataState> emit) async {
    if (itemListScrollController.hasClients) {
      itemListScrollController.jumpTo(0);
    }
    int pageNo = 1;
    emit(state.copyWith(pageState: PageState.loading, pageNo: pageNo, prospectEntityList: [], isContactOption: false, ivrState: IvrState.initial));
    List<ProspectCountItem>? filterTypeButton = state.prospectCounts;
    if (filterTypeButton == null) {
      return;
    }
    filterTypeButton = filterTypeButton.map((item) {
      return item.copyWith(isSelected: false);
    }).toList();

    filterTypeButton[event.index] = filterTypeButton[event.index].copyWith(isSelected: true);
    add(GetAllProspectEvent(filterTypeButton[event.index].id ?? '22e90157-8c53-490e-bc5d-22c05d33b0ef'));
    selectedFilterIndex = event.index;
    emit(state.copyWith(selectIndex: event.index, prospectCounts: [...filterTypeButton], isContactOption: false, ivrState: IvrState.initial));
  }

  FutureOr<void> _onScrolled(ScrolledEvent event, Emitter<ManageDataState> emit) {
    if (!state.loadMore) {
      if (itemListScrollController.position.pixels >= itemListScrollController.position.maxScrollExtent * 0.5) {
        emit(state.copyWith(loadMore: true, isContactOption: false, ivrState: IvrState.initial));
        add(LoadMoreEvent());
      }
    }
  }

  FutureOr<void> _onLoadMore(LoadMoreEvent event, Emitter<ManageDataState> emit) {
    add(GetAllProspectEvent(state.prospectCounts?[state.selectIndex].id ?? '22e90157-8c53-490e-bc5d-22c05d33b0ef'));
  }

  FutureOr<void> _onRefreshed(RefreshedEvent event, Emitter<ManageDataState> emit) {
    emit(state.copyWith(pageState: PageState.loading, pageNo: 1, prospectEntityList: [], isContactOption: false, ivrState: IvrState.initial));
    add(GetAllProspectEvent(state.prospectCounts?[state.selectIndex].id ?? '22e90157-8c53-490e-bc5d-22c05d33b0ef'));
  }

  @override
  Future<void> close() {
    filterTypeScrollController.dispose();
    itemListScrollController.dispose();
    return super.close();
  }

  String? getCurrency(ProspectEntity prospect) {
    return prospect.enquiry?.currency ?? globalSettingModel?.countries?.firstOrNull?.defaultCurrency ?? "INR";
  }

  String? getProject(ProspectEntity prospect) {
    return prospect.projects != null && prospect.projects!.isNotEmpty
        ? prospect.projects!.length == 1
            ? prospect.projects![0]?.name
            : prospect.projects![0]!.name!.length >= 4
                ? '${prospect.projects![0]?.name!.substring(0, 4)} ...+ ${prospect.projects!.length - 1}'
                : '${prospect.projects![0]?.name} + ${prospect.projects!.length - 1}'
        : null;
  }

  String? getBhk(ProspectEntity prospect) {
    final isCustomFormEnabled = globalSettingModel?.isCustomLeadFormEnabled ?? false;
    return prospect.enquiry != null && prospect.enquiry!.bhKs != null && prospect.enquiry!.bhKs!.isNotEmpty
        ? prospect.enquiry!.bhKs!.length == 1
            ? prospect.enquiry!.bhKs![0] == 0.5
                ? '1 RK'
                : "${convertNumber(prospect.enquiry!.bhKs![0] ?? 0.0)} ${isCustomFormEnabled ? 'BR' : 'BHK'}"
            : prospect.enquiry!.bhKs![0] == 0.5
                ? '1 RK + ${prospect.enquiry!.bhKs!.length - 1}'
                : "${convertNumber(prospect.enquiry!.bhKs![0] ?? 0.0)} ${isCustomFormEnabled ? 'BR' : 'BHK'} + ${prospect.enquiry!.bhKs!.length - 1}"
        : null;
  }

  String convertNumber(double number) {
    if (number % 1 == 0) {
      return number.toInt().toString();
    } else {
      return number.toStringAsFixed(1);
    }
  }

  String? getBudget(ProspectEntity prospect) {
    return prospect.enquiry?.upperBudget?.convertCurrencyFormat(isInternationalFormat: globalSettingModel?.isCustomLeadFormEnabled ?? false);
  }

  String? getLocation(ProspectEntity prospect) {
    return prospect.enquiry != null && prospect.enquiry?.addresses != null
        ? prospect.enquiry!.addresses!.isNotEmpty
            ? prospect.enquiry!.addresses!.length == 1
                ? prospect.enquiry!.addresses![0]!.subLocality
                : '${prospect.enquiry!.addresses![0]!.subLocality} + ${prospect.enquiry!.addresses!.length - 1}'
            : null
        : null;
  }

  FutureOr<void> _onCommunicationActionToggle(CommunicationActionToggleEvent event, Emitter<ManageDataState> emit) async {
    emit(state.copyWith(isContactOption: false, phoneNumber: event.contactNo, ivrState: IvrState.initial, prospectId: event.id));
    final callOption = await _getCallThroughUseCase.call(NoParams());
    callOption.fold(
      (failure) {},
      (success) {
        switch (success) {
          case 0:
            emit(state.copyWith(isContactOption: true));
            break;
          case 1:
            emit(state.copyWith(isContactOption: false));
            add(CallThroughIVREvent(event.id));
            break;
          case 2:
            emit(state.copyWith(isContactOption: false));
            add(CallThroughDialerEvent(event.id));
            break;
        }
      },
    );
  }

  FutureOr<void> _onCallThroughIVR(CallThroughIVREvent event, Emitter<ManageDataState> emit) async {
    final userDetails = await getIt<UsersDataRepository>().getUser();
    if (globalSettingModel?.isIVROutboundEnabled ?? false) {
      if (globalSettingModel?.isVirtualNumberRequiredForOutbound ?? false) {
        emit(state.copyWith(ivrState: IvrState.validatingAgent, isContactOption: false));
        ivrConfigModel = IvrConfigModel(
          destinationNumber: state.phoneNumber,
          agentNumber: userDetails?.phoneNumber,
          callerIdOrVirtualNumber: null,
          prospectId: event.id,
          userId: userDetails?.userId,
        );

        VirtualNumberModel? virtualNumberModel = await _ivrService.getVirtualNumberCheck(event.id);
        if (virtualNumberModel?.isVirtualNumberAssigned ?? false) {
          ivrConfigModel = IvrConfigModel(
            destinationNumber: state.phoneNumber,
            agentNumber: userDetails?.phoneNumber,
            callerIdOrVirtualNumber: virtualNumberModel?.virtualNumber,
            prospectId: event.id,
            userId: userDetails?.userId,
          );

          emit(state.copyWith(ivrState: IvrState.callingAgent, isContactOption: false));
          IvrResponseModel? ivrResponseModel = await _ivrService.postIvrConfiguration(ivrConfigModel ?? IvrConfigModel());
          if (ivrResponseModel?.success ?? false) {
            getIt<DataDetailsBloc>().add(UpdateActionButtonCountEvent(event.id, ContactType.call));
            emit(state.copyWith(ivrState: IvrState.success, isContactOption: false));
          } else {
            emit(state.copyWith(ivrState: IvrState.error, errorMessage: 'There was some problem with the IVR', isContactOption: false));
          }
        } else {
          if (virtualNumberModel?.shouldFetchVirtualNumbers ?? false) {
            final numbers = await _ivrService.getVirtualNumbers();
            final List<ItemSimpleModel>? virtualNumbers = numbers?.map((number) => ItemSimpleModel(title: number, isSelected: false)).toList();

            emit(state.copyWith(ivrState: IvrState.virtualNumber, isContactOption: false, selectableVirtualNumbers: virtualNumbers));
          } else {
            emit(state.copyWith(ivrState: IvrState.error, errorMessage: 'There are no virtual numbers', isContactOption: false));
          }
        }
      } else {
        ivrConfigModel = IvrConfigModel(
          destinationNumber: state.phoneNumber,
          agentNumber: userDetails?.phoneNumber,
          callerIdOrVirtualNumber: null,
          prospectId: event.id,
          userId: userDetails?.userId,
        );
        IvrResponseModel? ivrResponseModel = await _ivrService.postIvrConfiguration(ivrConfigModel ?? IvrConfigModel());
        if (ivrResponseModel?.success ?? false) {
          emit(state.copyWith(ivrState: IvrState.success, isContactOption: false));
        } else {
          emit(state.copyWith(ivrState: IvrState.error, errorMessage: (ivrResponseModel?.message?.isNotNullOrEmpty() ?? false) ? ivrResponseModel?.message : 'There was some problem with the IVR', isContactOption: false));
        }
      }
    } else {
      emit(state.copyWith(ivrState: IvrState.error, errorMessage: "Oops, No primary IVR account found in Integration, please integrate an 'Outbound' account with valid token and make it primary.", isContactOption: false));
    }
  }

  FutureOr<void> _onCallThroughDialer(CallThroughDialerEvent event, Emitter<ManageDataState> emit) async {
    try {
      // Check for CALL_PHONE permission
      final status = await Permission.phone.status;

      if (status.isGranted) {
        // Permission granted, make the call
        FlutterPhoneDirectCaller.callNumber(state.phoneNumber ?? '');
        emit(state.copyWith(ivrState: IvrState.success, isContactOption: false));
        getIt<DataDetailsBloc>().add(UpdateActionButtonCountEvent(event.id, ContactType.call));
      } else if (status.isDenied) {
        // Request permission if it's not permanently denied
        if (status.isDenied) {
          final result = await Permission.phone.request();
          if (result.isGranted) {
            // Retry making the call if the user grants permission
            FlutterPhoneDirectCaller.callNumber(state.phoneNumber ?? '');
            emit(state.copyWith(ivrState: IvrState.success, isContactOption: false));
            getIt<DataDetailsBloc>().add(UpdateActionButtonCountEvent(event.id, ContactType.call));
          } else {
            final call = Uri.parse('tel:${state.phoneNumber}');
            if (await canLaunchUrl(call)) {
              getIt<DataDetailsBloc>().add(UpdateActionButtonCountEvent(event.id, ContactType.call));

              emit(state.copyWith(ivrState: IvrState.success, isContactOption: false));
              launchUrl(call);
            } else {
              throw 'Could not launch $call';
            }
          }
        }
      } else {
        final call = Uri.parse('tel:${state.phoneNumber}');
        if (await canLaunchUrl(call)) {
          getIt<DataDetailsBloc>().add(UpdateActionButtonCountEvent(event.id, ContactType.call));

          emit(state.copyWith(ivrState: IvrState.success, isContactOption: false));
          launchUrl(call);
        } else {
          throw 'Could not launch $call';
        }
      }
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      final call = Uri.parse('tel:${state.phoneNumber}');
      if (await canLaunchUrl(call)) {
        getIt<DataDetailsBloc>().add(UpdateActionButtonCountEvent(event.id, ContactType.call));

        emit(state.copyWith(ivrState: IvrState.success, isContactOption: false));
        launchUrl(call);
      } else {
        throw 'Could not launch $call';
      }
    }
  }

  List<ItemSimpleModel<ProspectFilterKey>> _initSelectedFilters() {
    final selectedDataFilterModel = dataFilter;
    List<ItemSimpleModel<ProspectFilterKey>> selectedFilters = [];

    if (selectedDataFilterModel.prospectVisiblity != null && selectedDataFilterModel.prospectVisiblity != ProspectVisibility.selfWithReportee) {
      final selectedProspectVisibility = selectedDataFilterModel.prospectVisiblity?.description;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.prospectVisibility.description, description: ' - ${selectedProspectVisibility ?? ''}', value: ProspectFilterKey.prospectVisibility));
    }
    if (selectedDataFilterModel.sources?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.sources?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.source.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.source));
    }
    if (selectedDataFilterModel.subSources?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.subSources?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.subSource.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.subSource));
    }
    if (selectedDataFilterModel.enquiryTypes?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.enquiryTypes?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.enquiredFor.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.enquiredFor));
    }
    if (selectedDataFilterModel.agencyNames?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.agencyNames?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.agencyName.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.agencyName));
    }
    if (selectedDataFilterModel.campaignNames?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.campaignNames?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.campaigns.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.campaigns));
    }

    if (selectedDataFilterModel.dateRange != null || selectedDataFilterModel.possessionTypeDateRange != null) {
      if (selectedDataFilterModel.dateRange != null) {
        final isCustomDateRange = (selectedDataFilterModel.dateRange?.description == DateRange.customDate.description);
        var dateRange = isCustomDateRange ? "${_convertDateString(selectedDataFilterModel.fromDate ?? '')} - ${_convertDateString(selectedDataFilterModel.toDate ?? '')}" : selectedDataFilterModel.dateRange?.description ?? "";
        selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: dateRange, value: ProspectFilterKey.dateRange));
      } else if (selectedDataFilterModel.possessionTypeDateRange != null) {
        final isCustomDateRange = (selectedDataFilterModel.possessionTypeDateRange?.description == PossessionType.customDate.description);
        final dateFormat = DateFormat('MMM-yyyy');
        var dateRange = isCustomDateRange ? "Possession: from: ${dateFormat.format(DateTime.parse(selectedDataFilterModel.fromDate!).toLocal())} - to: ${dateFormat.format(DateTime.parse(selectedDataFilterModel.toDate!).toLocal())}" : 'Possession Type: ${selectedDataFilterModel.possessionTypeDateRange?.description ?? ""}';
        selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: dateRange, value: ProspectFilterKey.dateRange));
      }
    }
    if (selectedDataFilterModel.noOfBHKs?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.noOfBHKs?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.noOfBHK.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.noOfBHK));
    }
    if (selectedDataFilterModel.bhkTypes?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.bhkTypes?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.bHKTypes.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.bHKTypes));
    }
    if (selectedDataFilterModel.locations?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.locations?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.locations.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.locations));
    }
    if (selectedDataFilterModel.propertyTypes?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.propertyTypes?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.propertyType.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.propertyType));
    }
    if (selectedDataFilterModel.propertySubTypes?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.propertySubTypes?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.propertySubType.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.propertySubType));
    }
    if (selectedDataFilterModel.properties?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.properties?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.properties.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.properties));
    }
    if (selectedDataFilterModel.projects?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.projects?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.projects.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.projects));
    }
    if (selectedDataFilterModel.userIds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.userIds?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.assignedTo.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.assignedTo));
    }
    if (selectedDataFilterModel.assignedFromIds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.assignedFromIds?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.assignedFrom.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.assignedFrom));
    }
    if (selectedDataFilterModel.createdByIds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.createdByIds?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.createdBy.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.createdBy));
    }
    if (selectedDataFilterModel.qualifiedByIds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.qualifiedByIds?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.qualifiedBy.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.qualifiedBy));
    }
    if (selectedDataFilterModel.convertedByIds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.convertedByIds?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.convertedBy.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.convertedBy));
    }
    if (selectedDataFilterModel.statusIds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.statusIds?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.status.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.status));
    }

    if (selectedDataFilterModel.furnished?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.furnished?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.furnished.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.furnished));
    }
    if (selectedDataFilterModel.beds?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.beds?.length ?? 0;
      final desc = (selectedDataFilterModel.beds ?? []).map((b) => Beds.values.firstWhere((e) => e.value == b).description).join(', ');
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.beds.description, description: selectedFilterCount >= 1 ? ':$desc count(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.beds));
    }
    if (selectedDataFilterModel.baths?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.baths?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.baths.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.baths));
    }
    if (selectedDataFilterModel.floors?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.floors?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.floors.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.floors));
    }
    if (selectedDataFilterModel.offerTypes?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.offerTypes?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.offerTypes.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.offerTypes));
    }

    if (selectedDataFilterModel.communities?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.communities?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.community.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.community));
    }
    if (selectedDataFilterModel.subCommunities?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.subCommunities?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.subCommunity.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.subCommunity));
    }
    if (selectedDataFilterModel.towerNames?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.towerNames?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.towerName.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.towerName));
    }
    if (selectedDataFilterModel.countries?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.countries?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.country.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.country));
    }
    if (selectedDataFilterModel.pincode?.isNotEmpty ?? false) {
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.pinCode.description, description: selectedDataFilterModel.pincode, value: ProspectFilterKey.pinCode));
    }
    if (selectedDataFilterModel.carpetArea != null) {
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.carpetArea.description, description: selectedDataFilterModel.carpetArea.doubleToWord(), value: ProspectFilterKey.carpetArea));
    }
    if (selectedDataFilterModel.builtUpArea != null) {
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.buildUpArea.description, description: selectedDataFilterModel.builtUpArea.doubleToWord(), value: ProspectFilterKey.buildUpArea));
    }
    if (selectedDataFilterModel.saleableArea != null) {
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.salableArea.description, description: selectedDataFilterModel.saleableArea.doubleToWord(), value: ProspectFilterKey.salableArea));
    }
    if (selectedDataFilterModel.netArea != null) {
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.netArea.description, description: selectedDataFilterModel.netArea.doubleToWord(), value: ProspectFilterKey.netArea));
    }

    if ((selectedDataFilterModel.minPropertyArea != null && selectedDataFilterModel.minPropertyArea != 0) || (selectedDataFilterModel.maxPropertyArea != null && selectedDataFilterModel.maxPropertyArea != 0)) {
      String description = '';
      if (selectedDataFilterModel.minPropertyArea != null && selectedDataFilterModel.maxPropertyArea != null) {
        description = 'min:${selectedDataFilterModel.minPropertyArea}-max${selectedDataFilterModel.maxPropertyArea}';
      } else if (selectedDataFilterModel.minPropertyArea != null) {
        description = 'min:${selectedDataFilterModel.minPropertyArea}';
      } else if (selectedDataFilterModel.maxPropertyArea != null) {
        description = 'max${selectedDataFilterModel.maxPropertyArea}';
      }
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.propertyArea.description, description: description, value: ProspectFilterKey.propertyArea));
    }
    if (selectedDataFilterModel.nationality?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.nationality?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.nationality.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.nationality));
    }
    if (selectedDataFilterModel.clusterNames?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.clusterNames?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.clusterName.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.clusterName));
    }
    if (selectedDataFilterModel.unitNames?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.unitNames?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.unitNumberOrName.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.unitNumberOrName));
    }
    if (selectedDataFilterModel.purposes?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.purposes?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.purpose.description, description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.purpose));
    }
    if ((selectedDataFilterModel.minCarpetArea != null && selectedDataFilterModel.minCarpetArea != 0) || (selectedDataFilterModel.maxCarpetArea != null && selectedDataFilterModel.maxCarpetArea != 0)) {
      String description = '';
      if (selectedDataFilterModel.minCarpetArea != null && selectedDataFilterModel.maxCarpetArea != null) {
        description = 'min:${selectedDataFilterModel.minCarpetArea}-max${selectedDataFilterModel.maxCarpetArea}';
      } else if (selectedDataFilterModel.minCarpetArea != null) {
        description = 'min:${selectedDataFilterModel.minCarpetArea}';
      } else if (selectedDataFilterModel.maxCarpetArea != null) {
        description = 'max${selectedDataFilterModel.maxCarpetArea}';
      }
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.carpetArea.description, description: description, value: ProspectFilterKey.carpetArea));
    }

    if ((selectedDataFilterModel.minSaleAbleArea != null && selectedDataFilterModel.minSaleAbleArea != 0) || (selectedDataFilterModel.maxSaleableArea != null && selectedDataFilterModel.maxSaleableArea != 0)) {
      String description = '';
      if (selectedDataFilterModel.minSaleAbleArea != null && selectedDataFilterModel.maxSaleableArea != null) {
        description = 'min:${selectedDataFilterModel.minSaleAbleArea}-max${selectedDataFilterModel.maxSaleableArea}';
      } else if (selectedDataFilterModel.minSaleAbleArea != null) {
        description = 'min:${selectedDataFilterModel.minSaleAbleArea}';
      } else if (selectedDataFilterModel.maxSaleableArea != null) {
        description = 'max${selectedDataFilterModel.maxSaleableArea}';
      }
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.salableArea.description, description: description, value: ProspectFilterKey.salableArea));
    }

    if ((selectedDataFilterModel.minBuiltUpArea != null && selectedDataFilterModel.minBuiltUpArea != 0) || (selectedDataFilterModel.maxBuiltUpArea != null && selectedDataFilterModel.maxBuiltUpArea != 0)) {
      String description = '';
      if (selectedDataFilterModel.minBuiltUpArea != null && selectedDataFilterModel.maxBuiltUpArea != null) {
        description = 'min:${selectedDataFilterModel.minBuiltUpArea}-max${selectedDataFilterModel.maxBuiltUpArea}';
      } else if (selectedDataFilterModel.minBuiltUpArea != null) {
        description = 'min:${selectedDataFilterModel.minBuiltUpArea}';
      } else if (selectedDataFilterModel.maxBuiltUpArea != null) {
        description = 'max${selectedDataFilterModel.maxBuiltUpArea}';
      }
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.buildUpArea.description, description: description, value: ProspectFilterKey.buildUpArea));
    }

    if ((selectedDataFilterModel.minNetArea != null && selectedDataFilterModel.minNetArea != 0) || (selectedDataFilterModel.maxNetArea != null && selectedDataFilterModel.maxNetArea != 0)) {
      String description = '';
      if (selectedDataFilterModel.minNetArea != null && selectedDataFilterModel.maxNetArea != null) {
        description = 'min:${selectedDataFilterModel.minNetArea}-max${selectedDataFilterModel.maxNetArea}';
      } else if (selectedDataFilterModel.minNetArea != null) {
        description = 'min:${selectedDataFilterModel.minNetArea}';
      } else if (selectedDataFilterModel.maxNetArea != null) {
        description = 'max${selectedDataFilterModel.maxNetArea}';
      }
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.netArea.description, description: description, value: ProspectFilterKey.netArea));
    }

    if ((selectedDataFilterModel.toMinBudget != null && selectedDataFilterModel.toMinBudget != 0) || (selectedDataFilterModel.fromMinBudget != null && selectedDataFilterModel.fromMinBudget != 0)) {
      String description = '';
      if (selectedDataFilterModel.toMinBudget != null && selectedDataFilterModel.fromMinBudget != null) {
        description = 'min:${selectedDataFilterModel.fromMinBudget}-max${selectedDataFilterModel.toMinBudget} ${selectedDataFilterModel.currency ?? ''}';
      } else if (selectedDataFilterModel.toMinBudget != null) {
        description = 'max:${selectedDataFilterModel.toMinBudget} ${selectedDataFilterModel.currency ?? ''}';
      } else if (selectedDataFilterModel.fromMinBudget != null) {
        description = 'min:${selectedDataFilterModel.fromMinBudget} ${selectedDataFilterModel.currency ?? ''}';
      }

      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.minBudget.description, description: description, value: ProspectFilterKey.minBudget));
    }

    if ((selectedDataFilterModel.fromMaxBudget != null && selectedDataFilterModel.fromMaxBudget != 0) || (selectedDataFilterModel.toMaxBudget != null && selectedDataFilterModel.toMaxBudget != 0)) {
      String description = '';
      if (selectedDataFilterModel.toMaxBudget != null && selectedDataFilterModel.fromMaxBudget != null) {
        description = 'min:${selectedDataFilterModel.fromMaxBudget}-max${selectedDataFilterModel.toMaxBudget} ${selectedDataFilterModel.currency ?? ''}';
      } else if (selectedDataFilterModel.fromMaxBudget != null) {
        description = 'max:${selectedDataFilterModel.fromMaxBudget} ${selectedDataFilterModel.currency ?? ''}';
      } else if (selectedDataFilterModel.toMaxBudget != null) {
        description = 'min:${selectedDataFilterModel.toMaxBudget} ${selectedDataFilterModel.currency ?? ''}';
      }
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: ProspectFilterKey.maxBudget.description, description: description, value: ProspectFilterKey.maxBudget));
    }
    if (selectedDataFilterModel.excelSheets?.isNotEmpty ?? false) {
      int selectedFilterCount = selectedDataFilterModel.excelSheets?.length ?? 0;
      selectedFilters.add(ItemSimpleModel<ProspectFilterKey>(title: '${ProspectFilterKey.excelSheet.description}--${selectedDataFilterModel.excelSheets?.first ?? ''}', description: selectedFilterCount > 1 ? '(${selectedFilterCount.toString()})' : '', value: ProspectFilterKey.excelSheet));
    }
    return selectedFilters;
  }

  String _convertDateString(String date) {
    return DateTime.parse(date).toString().split(' ')[0].split('-').reversed.join('-');
  }

  FutureOr<void> _onRemoveFilter(RemoveFilterEvent event, Emitter<ManageDataState> emit) {
    if (event.prospectFilterKey == null) return null;
    switch (event.prospectFilterKey!) {
      case ProspectFilterKey.prospectVisibility:
        dataFilter = dataFilter.copyWith(prospectVisiblity: ProspectVisibility.selfWithReportee);
        break;
      case ProspectFilterKey.status:
        dataFilter = dataFilter.copyWith(statusIds: []);
        break;
      case ProspectFilterKey.source:
        dataFilter = dataFilter.copyWith(sources: []);
        break;
      case ProspectFilterKey.subSource:
        dataFilter = dataFilter.copyWith(subSources: []);
        break;
      case ProspectFilterKey.dateRange:
        dataFilter = dataFilter.copyWith(dateRangeSelected: true, updatePossessionDateRange: false);
        break;
      case ProspectFilterKey.enquiredFor:
        dataFilter = dataFilter.copyWith(enquiryTypes: []);
        break;
      case ProspectFilterKey.propertyType:
        dataFilter = dataFilter.copyWith(propertyTypes: []);
        break;
      case ProspectFilterKey.propertySubType:
        dataFilter = dataFilter.copyWith(propertySubTypes: []);
        break;
      case ProspectFilterKey.bHKTypes:
        dataFilter = dataFilter.copyWith(bhkTypes: []);
        break;
      case ProspectFilterKey.noOfBHK:
        dataFilter = dataFilter.copyWith(noOfBHKs: []);
        break;
      case ProspectFilterKey.agencyName:
        dataFilter = dataFilter.copyWith(agencyNames: []);
        break;
      case ProspectFilterKey.campaigns:
        dataFilter = dataFilter.copyWith(campaignNames: []);
        break;

      case ProspectFilterKey.assignedTo:
        dataFilter = dataFilter.copyWith(userIds: []);
        break;
      case ProspectFilterKey.assignedFrom:
        dataFilter = dataFilter.copyWith(assignedFromIds: []);
        break;
      case ProspectFilterKey.createdBy:
        dataFilter = dataFilter.copyWith(createdByIds: []);
        break;
      case ProspectFilterKey.qualifiedBy:
        dataFilter = dataFilter.copyWith(qualifiedByIds: []);
        break;
      case ProspectFilterKey.convertedBy:
        dataFilter = dataFilter.copyWith(convertedByIds: []);
        break;
      case ProspectFilterKey.properties:
        dataFilter = dataFilter.copyWith(properties: []);
        break;
      case ProspectFilterKey.projects:
        dataFilter = dataFilter.copyWith(projects: []);
        break;
      case ProspectFilterKey.locations:
        dataFilter = dataFilter.copyWith(locations: []);
        break;
      case ProspectFilterKey.furnished:
        dataFilter = dataFilter.copyWith(furnished: []);
        break;
      case ProspectFilterKey.beds:
        dataFilter = dataFilter.copyWith(beds: []);
        break;
      case ProspectFilterKey.baths:
        dataFilter = dataFilter.copyWith(baths: []);
        break;
      case ProspectFilterKey.floors:
        dataFilter = dataFilter.copyWith(floors: []);
        break;
      case ProspectFilterKey.offerTypes:
        dataFilter = dataFilter.copyWith(offerTypes: []);
        break;
      case ProspectFilterKey.community:
        dataFilter = dataFilter.copyWith(communities: []);
        break;
      case ProspectFilterKey.subCommunity:
        dataFilter = dataFilter.copyWith(subCommunities: []);
        break;
      case ProspectFilterKey.towerName:
        dataFilter = dataFilter.copyWith(towerNames: []);
        break;
      case ProspectFilterKey.country:
        dataFilter = dataFilter.copyWith(countries: []);
        break;
      case ProspectFilterKey.pinCode:
        dataFilter = dataFilter.copyWith(pincode: '');
        break;

      case ProspectFilterKey.carpetArea:
        dataFilter = dataFilter.copyWith(
          carpetAreaUnitId: '',
          updateCarpetArea: false,
        );
        break;
      case ProspectFilterKey.buildUpArea:
        dataFilter = dataFilter.copyWith(builtUpAreaUnitId: '', updateBuildUpArea: false);
        break;
      case ProspectFilterKey.salableArea:
        dataFilter = dataFilter.copyWith(builtUpAreaUnitId: '', updateSalableArea: false);
        break;
      case ProspectFilterKey.netArea:
        dataFilter = dataFilter.copyWith(netAreaUnitId: '', netArea: null, updateNetArea: false);
        break;
      case ProspectFilterKey.propertyArea:
        dataFilter = dataFilter.copyWith(propertyAreaUnitId: '', updatePropertyArea: false);
        break;
      case ProspectFilterKey.unitNumberOrName:
        dataFilter = dataFilter.copyWith(unitNames: []);
        break;
      case ProspectFilterKey.nationality:
        dataFilter = dataFilter.copyWith(nationality: []);
        break;
      case ProspectFilterKey.clusterName:
        dataFilter = dataFilter.copyWith(clusterNames: []);
        break;
      case ProspectFilterKey.purpose:
        dataFilter = dataFilter.copyWith(purposes: []);
        break;
      case ProspectFilterKey.minBudget:
        dataFilter = dataFilter.copyWith(updateMinBudget: false, currencySelected: true);

      case ProspectFilterKey.maxBudget:
        dataFilter = dataFilter.copyWith(updateMaxBudget: false, currencySelected: true);
      case ProspectFilterKey.excelSheet:
        dataFilter = dataFilter.copyWith(excelSheets: []);
        break;
      default:
        break;
    }
    selectedFilters.removeWhere((element) => element.value == event.prospectFilterKey);
    add(ManageDataInitialEvent(filterType: null, dataFilter: dataFilter));
  }

  FutureOr<void> _onLoadManageDataInitial(LoadManageDataInitialEvent event, Emitter<ManageDataState> emit) {
    add(FilterOptionClickEvent(event.selectedFilterIndex));
    add(GetAllProspectCountEvent());
  }
}
