// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'data_filter_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DataFilterModel _$DataFilterModelFromJson(Map<String, dynamic> json) =>
    DataFilterModel(
      prospectVisiblity: $enumDecodeNullable(
          _$ProspectVisibilityEnumMap, json['ProspectVisiblity']),
      sources: (json['SourceIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      subSources: (json['SubSources'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      statusIds: (json['StatusIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      filterTypes: (json['FilterTypes'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      userIds: (json['AssignTo'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      assignedFromIds: (json['AssignedFromIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      enquiryTypes: (json['EnquiryTypes'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$EnquiryTypeEnumMap, e))
          .toList(),
      projects: (json['Projects'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      properties: (json['Properties'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      dateType:
          $enumDecodeNullable(_$ProspectDateTypeEnumMap, json['DateType']),
      dateRange: $enumDecodeNullable(_$DateRangeEnumMap, json['DateRange']),
      fromDate: json['FromDate'] as String?,
      toDate: json['ToDate'] as String?,
      noOfBHKs: (json['NoOfBHKs'] as List<dynamic>?)
          ?.map((e) => (e as num).toDouble())
          .toList(),
      bhkTypes: (json['BhkTypes'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$BHKTypeEnumMap, e))
          .toList(),
      propertyTypes: (json['PropertyType'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      propertySubTypes: (json['PropertySubType'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      locations: (json['Locations'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      agencyNames: (json['AgencyNames'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      createdByIds: (json['CreatedByIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      qualifiedByIds: (json['QualifiedByIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      convertedByIds: (json['ConvertedByIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      currency: json['Currency'] as String?,
      furnished: (json['Furnished'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      baths: (json['Baths'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      floors:
          (json['Floors'] as List<dynamic>?)?.map((e) => e as String).toList(),
      beds: (json['Beds'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      offerTypes: (json['OfferTypes'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      brs: (json['Brs'] as List<dynamic>?)
          ?.map((e) => (e as num).toDouble())
          .toList(),
      communities: (json['Communities'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      subCommunities: (json['SubCommunities'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      towerNames: (json['TowerNames'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      countries: (json['Countries'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      pincode: json['Pincode'] as String?,
      builtUpAreaUnitId: json['BuiltUpAreaUnitId'] as String?,
      carpetAreaUnitId: json['CarpetAreaUnitId'] as String?,
      saleableAreaUnitId: json['SaleableAreaUnitId'] as String?,
      netAreaUnitId: json['NetAreaUnitId'] as String?,
      propertyAreaUnitId: json['PropertyAreaUnitId'] as String?,
      saleableArea: (json['SaleableArea'] as num?)?.toDouble(),
      carpetArea: (json['CarpetArea'] as num?)?.toDouble(),
      builtUpArea: (json['BuiltUpArea'] as num?)?.toDouble(),
      netArea: (json['NetArea'] as num?)?.toDouble(),
      propertyArea: (json['PropertyArea'] as num?)?.toDouble(),
      unitNames: (json['UnitNames'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      clusterNames: (json['ClusterName'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      nationality: (json['Nationality'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      campaignNames: (json['CampaignNames'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      purposes: (json['Purposes'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$PurposeEnumEnumMap, e))
          .toList(),
      maxBuiltUpArea: (json['MaxBuiltUpArea'] as num?)?.toDouble(),
      minBuiltUpArea: (json['MinBuiltUpArea'] as num?)?.toDouble(),
      maxSaleableArea: (json['MaxSaleableArea'] as num?)?.toDouble(),
      minSaleAbleArea: (json['MinSaleAbleArea'] as num?)?.toDouble(),
      maxCarpetArea: (json['MaxCarpetArea'] as num?)?.toDouble(),
      minCarpetArea: (json['MinCarpetArea'] as num?)?.toDouble(),
      minPropertyArea: (json['MinPropertyArea'] as num?)?.toDouble(),
      maxPropertyArea: (json['MaxPropertyArea'] as num?)?.toDouble(),
      fromMinBudget: (json['FromMinBudget'] as num?)?.toDouble(),
      toMinBudget: (json['ToMinBudget'] as num?)?.toDouble(),
      toMaxBudget: (json['ToMaxBudget'] as num?)?.toDouble(),
      fromMaxBudget: (json['FromMaxBudget'] as num?)?.toDouble(),
      maxNetArea: (json['MaxNetArea'] as num?)?.toDouble(),
      minNetArea: (json['MinNetArea'] as num?)?.toDouble(),
      excelSheets: (json['excelSheets'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      possessionTypeDateRange: $enumDecodeNullable(
          _$PossessionTypeEnumMap, json['PossessionTypeDateRange']),
    );

Map<String, dynamic> _$DataFilterModelToJson(DataFilterModel instance) =>
    <String, dynamic>{
      'ProspectVisiblity':
          _$ProspectVisibilityEnumMap[instance.prospectVisiblity],
      'SourceIds': instance.sources,
      'SubSources': instance.subSources,
      'StatusIds': instance.statusIds,
      'FilterTypes': instance.filterTypes,
      'AssignTo': instance.userIds,
      'AssignedFromIds': instance.assignedFromIds,
      'EnquiryTypes':
          instance.enquiryTypes?.map((e) => _$EnquiryTypeEnumMap[e]!).toList(),
      'Projects': instance.projects,
      'Properties': instance.properties,
      'DateType': _$ProspectDateTypeEnumMap[instance.dateType],
      'DateRange': _$DateRangeEnumMap[instance.dateRange],
      'FromDate': instance.fromDate,
      'ToDate': instance.toDate,
      'NoOfBHKs': instance.noOfBHKs,
      'BhkTypes': instance.bhkTypes?.map((e) => _$BHKTypeEnumMap[e]!).toList(),
      'PropertyType': instance.propertyTypes,
      'PropertySubType': instance.propertySubTypes,
      'Locations': instance.locations,
      'AgencyNames': instance.agencyNames,
      'CreatedByIds': instance.createdByIds,
      'QualifiedByIds': instance.qualifiedByIds,
      'ConvertedByIds': instance.convertedByIds,
      'Currency': instance.currency,
      'CampaignNames': instance.campaignNames,
      'Furnished': instance.furnished,
      'Beds': instance.beds,
      'Baths': instance.baths,
      'Floors': instance.floors,
      'OfferTypes': instance.offerTypes,
      'Brs': instance.brs,
      'Communities': instance.communities,
      'SubCommunities': instance.subCommunities,
      'TowerNames': instance.towerNames,
      'Countries': instance.countries,
      'Pincode': instance.pincode,
      'BuiltUpAreaUnitId': instance.builtUpAreaUnitId,
      'CarpetAreaUnitId': instance.carpetAreaUnitId,
      'SaleableAreaUnitId': instance.saleableAreaUnitId,
      'NetAreaUnitId': instance.netAreaUnitId,
      'PropertyAreaUnitId': instance.propertyAreaUnitId,
      'SaleableArea': instance.saleableArea,
      'CarpetArea': instance.carpetArea,
      'BuiltUpArea': instance.builtUpArea,
      'NetArea': instance.netArea,
      'PropertyArea': instance.propertyArea,
      'UnitNames': instance.unitNames,
      'ClusterName': instance.clusterNames,
      'Nationality': instance.nationality,
      'Purposes':
          instance.purposes?.map((e) => _$PurposeEnumEnumMap[e]!).toList(),
      'MaxBuiltUpArea': instance.maxBuiltUpArea,
      'MinBuiltUpArea': instance.minBuiltUpArea,
      'MaxSaleableArea': instance.maxSaleableArea,
      'MinSaleAbleArea': instance.minSaleAbleArea,
      'MaxCarpetArea': instance.maxCarpetArea,
      'MinCarpetArea': instance.minCarpetArea,
      'MaxPropertyArea': instance.maxPropertyArea,
      'MinPropertyArea': instance.minPropertyArea,
      'FromMinBudget': instance.fromMinBudget,
      'ToMinBudget': instance.toMinBudget,
      'FromMaxBudget': instance.fromMaxBudget,
      'ToMaxBudget': instance.toMaxBudget,
      'MinNetArea': instance.minNetArea,
      'MaxNetArea': instance.maxNetArea,
      'PossessionTypeDateRange':
          _$PossessionTypeEnumMap[instance.possessionTypeDateRange],
      'excelSheets': instance.excelSheets,
    };

const _$ProspectVisibilityEnumMap = {
  ProspectVisibility.selfWithReportee: 0,
  ProspectVisibility.self: 1,
  ProspectVisibility.reportee: 2,
  ProspectVisibility.convertedData: 3,
  ProspectVisibility.deletedData: 4,
  ProspectVisibility.unassignData: 5,
};

const _$EnquiryTypeEnumMap = {
  EnquiryType.none: 0,
  EnquiryType.buy: 1,
  EnquiryType.sale: 2,
  EnquiryType.rent: 3,
};

const _$ProspectDateTypeEnumMap = {
  ProspectDateType.all: 0,
  ProspectDateType.receivedDate: 1,
  ProspectDateType.modifiedDate: 2,
  ProspectDateType.scheduledDate: 3,
  ProspectDateType.qualifiedDate: 4,
  ProspectDateType.convertedDate: 5,
  ProspectDateType.possessionDate: 6,
};

const _$DateRangeEnumMap = {
  DateRange.today: 0,
  DateRange.yesterday: 1,
  DateRange.lastSevenDays: 2,
  DateRange.lastTwentyEightDays: 3,
  DateRange.customDate: 4,
};

const _$BHKTypeEnumMap = {
  BHKType.none: 0,
  BHKType.simplex: 1,
  BHKType.duplex: 2,
  BHKType.pentHouse: 3,
  BHKType.others: 4,
};

const _$PurposeEnumEnumMap = {
  PurposeEnum.none: 0,
  PurposeEnum.investment: 1,
  PurposeEnum.selfUse: 2,
};

const _$PossessionTypeEnumMap = {
  PossessionType.none: 0,
  PossessionType.underConstruction: 1,
  PossessionType.sixMonths: 2,
  PossessionType.oneYear: 3,
  PossessionType.twoYear: 4,
  PossessionType.customDate: 5,
};
