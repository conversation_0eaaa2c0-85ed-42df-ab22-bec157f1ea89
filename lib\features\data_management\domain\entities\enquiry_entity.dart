import 'package:leadrat/core_main/common/entites/address_entity.dart';
import 'package:leadrat/core_main/common/entites/area_unit_entity.dart';
import 'package:leadrat/core_main/common/entites/property_type_entity.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/purpose_enum.dart';
import 'package:leadrat/core_main/enums/data_management/enquiry_type_enum.dart';
import 'package:leadrat/features/data_management/domain/entities/prospect_source_entity.dart';

import '../../../../core_main/enums/common/no_of_beds.dart';

class EnquiryEntity {
  final String? id;
  final int? enquiryType;
  final List<EnquiryTypeEnum>? enquiryTypes;
  final int? bhKType;
  final double? noOfBhks;
  final List<BHKType>? bhkTypes;
  final List<double?>? bhKs;
  final String? subSource;
  final int? lowerBudget;
  final int? upperBudget;
  final int? carpetArea;
  final double? saleableArea;
  final double? builtUpArea;
  final double? netArea;
  final double? propertyArea;
  final String? conversionFactor;
  final String? carpetAreaUnit;
  final AreaUnitEntity? carpetAreaUnitId;
  final AddressEntity? address;
  final List<AddressEntity?>? addresses;
  final String? currency;
  final ProspectSourceEntity? prospectSource;
  final PropertyTypeEntity? propertyType;
  final List<PropertyTypeEntity>? propertyTypes;
  final int? furnished;
  final int? offerType;
  final AreaUnitEntity? saleableAreaUnitId;
  final AreaUnitEntity? builtUpAreaUnitId;
  final AreaUnitEntity? netAreaUnitId;
  final AreaUnitEntity? propertyAreaUnitId;
  final List<String>? floors;
  final List<Beds>? beds;
  final List<double>? bHKs;
  final List<int>? baths;
  final String? unitName;
  final String? clusterName;
  final PurposeEnum? purpose;
  final PossessionType? possesionType;

  EnquiryEntity({
    this.id,
    this.enquiryType,
    this.enquiryTypes,
    this.bhKType,
    this.noOfBhks,
    this.bhkTypes,
    this.bhKs,
    this.subSource,
    this.lowerBudget,
    this.upperBudget,
    this.carpetArea,
    this.builtUpArea,
    this.saleableArea,
    this.conversionFactor,
    this.carpetAreaUnit,
    this.carpetAreaUnitId,
    this.address,
    this.addresses,
    this.currency,
    this.prospectSource,
    this.propertyType,
    this.propertyTypes,
    this.furnished,
    this.offerType,
    this.saleableAreaUnitId,
    this.builtUpAreaUnitId,
    this.floors,
    this.beds,
    this.baths,
    this.bHKs,
    this.propertyAreaUnitId,
    this.netAreaUnitId,
    this.unitName,
    this.netArea,
    this.propertyArea,
    this.clusterName,
    this.purpose,
    this.possesionType,
  });

  EnquiryEntity copyWith({
    String? id,
    int? enquiryType,
    List<EnquiryTypeEnum>? enquiryTypes,
    int? bhKType,
    double? noOfBhks,
    List<BHKType>? bhkTypes,
    List<double?>? bhKs,
    String? subSource,
    int? lowerBudget,
    int? upperBudget,
    int? carpetArea,
    double? saleableArea,
    double? builtUpArea,
    String? conversionFactor,
    String? carpetAreaUnit,
    AreaUnitEntity? carpetAreaUnitId,
    AddressEntity? address,
    List<AddressEntity?>? addresses,
    String? currency,
    ProspectSourceEntity? prospectSource,
    PropertyTypeEntity? propertyType,
    List<PropertyTypeEntity>? propertyTypes,
    int? furnished,
    int? offerType,
    AreaUnitEntity? saleableAreaUnitId,
    AreaUnitEntity? builtUpAreaUnitId,
    AreaUnitEntity? propertyAreaUnitId,
    AreaUnitEntity? netAreaUnitId,
    List<String>? floors,
    List<Beds>? beds,
    List<double>? bHKs,
    List<int>? baths,
    String? unitName,
    double? netArea,
    double? propertyArea,
    String? clusterName,
    PurposeEnum? purpose,
    PossessionType? possesionType,
  }) {
    return EnquiryEntity(
      id: id ?? this.id,
      enquiryType: enquiryType ?? this.enquiryType,
      enquiryTypes: enquiryTypes ?? this.enquiryTypes,
      bhKType: bhKType ?? this.bhKType,
      noOfBhks: noOfBhks ?? this.noOfBhks,
      bhkTypes: bhkTypes ?? this.bhkTypes,
      bhKs: bhKs ?? this.bhKs,
      subSource: subSource ?? this.subSource,
      lowerBudget: lowerBudget ?? this.lowerBudget,
      upperBudget: upperBudget ?? this.upperBudget,
      carpetArea: carpetArea ?? this.carpetArea,
      builtUpArea: builtUpArea ?? this.builtUpArea,
      saleableArea: saleableArea ?? this.saleableArea,
      conversionFactor: conversionFactor ?? this.conversionFactor,
      carpetAreaUnit: carpetAreaUnit ?? this.carpetAreaUnit,
      carpetAreaUnitId: carpetAreaUnitId ?? this.carpetAreaUnitId,
      address: address ?? this.address,
      addresses: addresses ?? this.addresses,
      currency: currency ?? this.currency,
      prospectSource: prospectSource ?? this.prospectSource,
      propertyType: propertyType ?? this.propertyType,
      furnished: furnished ?? this.furnished,
      offerType: offerType ?? this.offerType,
      saleableAreaUnitId: saleableAreaUnitId ?? this.saleableAreaUnitId,
      builtUpAreaUnitId: builtUpAreaUnitId ?? this.builtUpAreaUnitId,
      propertyAreaUnitId: propertyAreaUnitId ?? this.propertyAreaUnitId,
      netAreaUnitId: netAreaUnitId ?? this.netAreaUnitId,
      floors: floors ?? this.floors,
      beds: beds ?? this.beds,
      bHKs: bHKs ?? this.bHKs,
      baths: baths ?? this.baths,
      unitName: unitName ?? this.unitName,
      netArea: netArea ?? this.netArea,
      propertyArea: propertyArea ?? this.propertyArea,
      clusterName: clusterName ?? this.clusterName,
      propertyTypes: propertyTypes ?? this.propertyTypes,
      purpose: purpose ?? this.purpose,
      possesionType: possesionType ?? this.possesionType,
    );
  }
}
