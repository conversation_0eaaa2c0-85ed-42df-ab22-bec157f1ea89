import 'dart:async';
import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:leadrat/core_main/common/data/master_data/repository/masterdata_repository.dart';
import 'package:leadrat/core_main/common/data/user/models/get_all_users_model.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_model.dart';
import 'package:leadrat/core_main/common/entites/address_entity.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/enums/app_enum/select_file_enum.dart';
import 'package:leadrat/core_main/enums/common/payment_mode_enum.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/features/lead/data/models/booked_lead_model.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';
import 'package:leadrat/features/lead/domain/entities/upload_booked_document_entity.dart';
import 'package:leadrat/features/lead/presentation/bloc/update_lead_status_bloc/update_lead_status_bloc.dart';
import 'package:leadrat/features/lead/presentation/item/booking_details_item.dart';

import '../../../../../core_main/common/data/global_settings/models/global_setting_model.dart';
import '../../../../../core_main/common/data/global_settings/repository/global_setting_repository.dart';
import '../../../../../core_main/common/data/user/repository/users_repository.dart';
import '../../../../../core_main/di/injection_container.dart';
import '../../../../../core_main/enums/app_enum/app_module.dart';
import '../../../../../core_main/enums/app_enum/blob_folder_names.dart';
import '../../../../../core_main/enums/app_enum/command_type.dart';
import '../../../../../core_main/enums/common/brokerage_type.dart';
import '../../../../../core_main/enums/common/discount_type.dart';
import '../../../../../core_main/enums/common/payment_type.dart';
import '../../../../../core_main/enums/user_profile/document_type_enum.dart';
import '../../../../../core_main/services/native_implementation_service/native_implementation_service.dart';
import '../../../../../core_main/utilities/file_picker_util.dart';
import '../../../../user_profile/domain/usecase/upload_document_usecase.dart';
import '../../../domain/usecase/booked_lead_use_case.dart';
import '../../item/booking_form_header_item.dart';
import '../lead_history_bloc/lead_history_bloc.dart';
import '../lead_info_bloc/lead_info_bloc.dart';
import '../manage_leads_bloc/manage_leads_bloc.dart';

part 'booking_form_event.dart';

part 'booking_form_state.dart';

class BookingFormBloc extends Bloc<BookingFormEvent, BookingFormState> {
  TextEditingController agreementValueController = TextEditingController();
  TextEditingController bookingDateController = TextEditingController();
  TextEditingController fileNameController = TextEditingController();
  TextEditingController addOnChargesController = TextEditingController();
  TextEditingController tokenAmountController = TextEditingController();
  TextEditingController carParkingChargesController = TextEditingController();
  TextEditingController totalSoldController = TextEditingController();
  TextEditingController discountController = TextEditingController();
  TextEditingController balanceAmountController = TextEditingController();
  TextEditingController notesController = TextEditingController();
  TextEditingController brokeragesChargesController = TextEditingController();
  TextEditingController netBrokeragesAmountController = TextEditingController();
  TextEditingController gstController = TextEditingController();
  TextEditingController totalBrokerageController = TextEditingController();
  TextEditingController referralNameController = TextEditingController();
  TextEditingController phoneNumberController = TextEditingController();
  TextEditingController referralCommissionController = TextEditingController();
  TextEditingController brokerageEarnedController = TextEditingController();
  TextEditingController agreementValueBrokerageInfo = TextEditingController();
  bool isCorrectImage = true;
  List<GetAllUsersModel?>? allUsers = [];

  final BookedLeadUseCase _bookedLeadUseCase;
  final UploadDocumentUseCase _uploadDocumentUseCase;
  final NativeImplementationService _nativeImplementationService;
  final UsersDataRepository _userDataRepository;
  final GlobalSettingRepository _globalSettingRepository;
  final MasterDataRepository _masterDataRepository;

  BookingFormBloc(this._bookedLeadUseCase, this._uploadDocumentUseCase, this._nativeImplementationService, this._userDataRepository, this._globalSettingRepository, this._masterDataRepository) : super(BookingFormState()) {
    on<BookingFormInitialEvent>(_onBookingFormInitial);
    on<ToggleDocument>(_onIsDocumentSelected);
    on<ToggleDiscountEvent>(_onIsDiscountSelected);
    on<ToggleReferralCommissionEvent>(_onIsReferralCommissionSelected);
    on<UploadFileEvent>(_onUploadFileEvent);
    on<DisposeBookingFormEvent>(_onDisposeBookingForm);
    on<DeleteFileEvent>(_onDeleteFileEvent);
    on<SelectPaymentModeEvent>(_onUpdatePaymentMode);
    on<DiscountEvent>(_onDiscountEvent);
    on<SelectTypeOfPaymentEvent>(_onTypeOfPaymentEvent);
    on<NetBrokerageAmountEvent>(_onNetBrokerageAmountEvent);
    on<SelectPrimaryOwnerEvent>(_onPrimaryOwnerEvent);
    on<SelectSecondaryOwnerEvent>(_onSecondaryOwnerEvent);
    on<BookingDetailsCalculationEvent>(_onBookingDetailsCalculation);
    on<ToggleNetBrokerageAmountEvent>(_onIsNetBrokerageAmountSelected);
    on<BrokerageInfoCalculationEvent>(_onBrokerageInfoCalculation);
    on<BookedLeadEvent>(_onBookedLead);
    on<PageChangedEvent>(_onPageChangedEvent);
    on<SelectCurrencyEvent>(_onSelectCurrencyEvent);
    on<CheckBookingDetailsConditionEvent>(_onCheckBookingDetailsCondition);
    on<ToBookingDetailsScreenEvent>(_onToBookingDetailsScreen);
  }

  FutureOr<void> _onBookingFormInitial(BookingFormInitialEvent event, Emitter<BookingFormState> emit) async {
    if (_userDataRepository.checkHasPermission(AppModule.invoice, CommandType.viewBrokerageInfo)) {
      emit(state.copyWith(hasPermission: true));
    }
    String? unit;
    if (event.bookingDetailsItem?.selectedUnit != null && event.bookingDetailsItem?.selectedUnit!.value != null && event.bookingDetailsItem?.selectedUnit!.value!.carpetAreaUnitId != null) {
      final initSelectedArea = event.bookingDetailsItem?.selectedUnit?.value?.carpetAreaUnitId ?? '';
      var masterAreaUnits = await _masterDataRepository.getAreaUnits();
      if (masterAreaUnits?.isNotEmpty ?? false) {
        masterAreaUnits?.forEach((action) {
          if (initSelectedArea == action?.id) {
            unit = action?.unit;
          }
        });
      }
    }
    emit(state.copyWith(selectedCurrency: event.updateLeadStatusState?.selectedCurrency, currencies: event.updateLeadStatusState?.currencies));
    BookingFormHeaderItem bookingFormHeaderItem = BookingFormHeaderItem(
        name: event.getLeadEntity?.name ?? '---',
        email: event.getLeadEntity?.email ?? '---',
        phoneNumber: event.getLeadEntity?.contactNo ?? '---',
        maxBudget: '${event.getLeadEntity?.enquiry?.currency} ${event.getLeadEntity?.enquiry?.upperBudget.toString().budgetToWord()}',
        address: (event.getLeadEntity?.enquiry?.addresses != null && event.getLeadEntity!.enquiry!.addresses!.isNotEmpty) ? getLeadAddress(event.getLeadEntity?.enquiry?.addresses) : '---',
        enquiryTypes: getEnquiryTypes(event.getLeadEntity?.enquiry?.enquiryTypes),
        propertyDisplayName: event.getLeadEntity?.enquiry?.propertyType?.displayName ?? '---',
        propertyChildDisplayName: event.getLeadEntity?.enquiry?.propertyType?.childType?.displayName ?? '---',
        bhks: getBhks(event.getLeadEntity?.enquiry?.bHKs) ?? '---',
        agreementvalue: event.bookingDetailsItem?.agreementvalue,
        bookedDate: event.bookingDetailsItem?.bookedDate,
        chosenProject: event.bookingDetailsItem?.chosenProject,
        chosenProperty: event.bookingDetailsItem?.chosenProperty,
        selectedUnit: event.bookingDetailsItem?.selectedUnit,
        carpetAreaUnit: unit);

    List<SelectableItem<GetAllUsersModel?>>? primaryUsers = [];
    List<SelectableItem<GetAllUsersModel?>>? secondaryUsers = [];
    List<SelectableItem<GetAllUsersModel?>>? allusers = [];
    UserDetailsModel? loggedInUser = _userDataRepository.getLoggedInUser();

    allUsers = await _userDataRepository.getAssignUser();

    allUsers?.forEach((user) {
      if (event.updateLeadStatusState?.selectedAssignToUser == null ? event.getLeadEntity?.assignedUser?.id == user?.id : event.updateLeadStatusState?.selectedAssignToUser?.value == user?.id) {
        if (loggedInUser?.userId == user?.id) {
          primaryUsers.insert(0, SelectableItem<GetAllUsersModel?>(title: 'You', isSelected: true, value: user));
          allusers.insert(0, SelectableItem<GetAllUsersModel?>(title: 'You', value: user));
        } else {
          primaryUsers.add(SelectableItem<GetAllUsersModel?>(title: '${user?.firstName ?? ''} ${user?.lastName ?? ''}', isSelected: true, value: user));
          allusers.add(SelectableItem<GetAllUsersModel?>(title: '${user?.firstName ?? ''} ${user?.lastName ?? ''}', value: user));
        }
      } else {
        if (loggedInUser?.userId == user?.id) {
          primaryUsers.insert(0, SelectableItem<GetAllUsersModel?>(title: 'You', isSelected: false, value: user));
          allusers.insert(0, SelectableItem<GetAllUsersModel?>(title: 'You', value: user));
        } else {
          primaryUsers.add(SelectableItem<GetAllUsersModel?>(title: '${user?.firstName ?? ''} ${user?.lastName ?? ''}', isSelected: false, value: user));
          allusers.add(SelectableItem<GetAllUsersModel?>(title: '${user?.firstName ?? ''} ${user?.lastName ?? ''}', value: user));
        }
      }
    });

    SelectableItem<GetAllUsersModel?>? primaryOwnerselectedItem;
    for (var action in primaryUsers) {
      if (action.isSelected) {
        primaryOwnerselectedItem = action;
      }
    }

    allUsers?.forEach((user) {
      if (primaryOwnerselectedItem?.value?.id != user?.id) {
        if (state.updateLeadStatusState?.selectedSecondaryAssignToUser == null ? event.getLeadEntity?.secondaryUser?.id == user?.id : state.updateLeadStatusState?.selectedSecondaryAssignToUser?.value == user?.id) {
          if (loggedInUser?.userId == user?.id) {
            secondaryUsers.insert(0, SelectableItem<GetAllUsersModel?>(title: 'You', isSelected: true, value: user));
          } else {
            secondaryUsers.add(SelectableItem<GetAllUsersModel?>(title: '${user?.firstName ?? ''} ${user?.lastName ?? ''}', isSelected: true, value: user));
          }
        } else {
          if (loggedInUser?.userId == user?.id) {
            secondaryUsers.insert(0, SelectableItem<GetAllUsersModel?>(title: 'You', isSelected: false, value: user));
          }
          secondaryUsers.add(SelectableItem<GetAllUsersModel?>(title: '${user?.firstName ?? ''} ${user?.lastName ?? ''}', isSelected: false, value: user));
        }
      }
    });
    SelectableItem<GetAllUsersModel?>? secondaryOwnerselectedItem;
    for (var action in secondaryUsers) {
      if (action.isSelected) {
        secondaryOwnerselectedItem = action;
      }
    }
    String inputDateString = event.bookingDetailsItem?.bookedDate ?? '';
    DateTime inputDate = DateFormat('yyyy-MM-dd HH:mm:ss.SSS').parse(inputDateString);

    String formattedDate = DateFormat('dd-MM-yyyy').format(inputDate);

    bookingDateController.text = formattedDate;
    agreementValueController.text = event.bookingDetailsItem?.agreementvalue ?? '';
    agreementValueBrokerageInfo.text = event.bookingDetailsItem?.agreementvalue ?? '';
    List<SelectableItem<PaymentModeEnum?>>? paymentModeSelectableItems = PaymentModeEnum.values
        .where((e) => e.description != 'None')
        .map(
          (e) => SelectableItem<PaymentModeEnum?>(title: e.description, value: e),
    )
        .toList();
    List<SelectableItem<DiscountType?>>? discountSelectableItems = DiscountType.values.where((element) => element != DiscountType.none).map((e) => e == DiscountType.cashback ? SelectableItem<DiscountType>(title: e.description, value: e, isSelected: true) : SelectableItem<DiscountType>(title: e.description, value: e)).toList();
    SelectableItem<DiscountType?>? discountSelected;
    for (var action in discountSelectableItems) {
      if (action.isSelected) {
        discountSelected = action;
      }
    }
    List<SelectableItem<PaymentType?>>? typeOfPaymentSelectableItem = PaymentType.values
        .where((e) => e.description != "None")
        .map(
          (toElement) => SelectableItem<PaymentType?>(title: toElement.description, value: toElement),
    )
        .toList();
    List<SelectableItem<BrokerageType?>>? netBrokerageSelectableItem = BrokerageType.values.where((test) => test.description != 'None').map((toElement) => toElement == BrokerageType.agreementValue ? SelectableItem<BrokerageType?>(title: toElement.description, value: toElement, isSelected: true) : SelectableItem<BrokerageType?>(title: toElement.description, value: toElement)).toList();
    SelectableItem<BrokerageType?>? netBrokerageSelected;
    for (var action in netBrokerageSelectableItem) {
      if (action.isSelected) {
        netBrokerageSelected = action;
      }
    }
    final globalSettings = await _globalSettingRepository.getGlobalSettings();
    emit(state.copyWith(globalSettingModel: globalSettings));

    notesController.text = event.bookingDetailsItem?.note ?? '';

    emit(state.copyWith(bookingFormHeaderItem: bookingFormHeaderItem,
        paymentModeSelectableItems: paymentModeSelectableItems,
        discountSelectableItems: discountSelectableItems,
        discountSelectedItem: discountSelected,
        typeOfPaymentSelectableItems: typeOfPaymentSelectableItem,
        netBrokerageSelectableItem: netBrokerageSelectableItem,
        netBrokerageSelectedItem: netBrokerageSelected,
        users: primaryUsers,
        primaryOwnerselectedItem: primaryOwnerselectedItem,
        secondaryOwnerSelectedItem: secondaryOwnerselectedItem,
        secondaryUsers: secondaryUsers,
        updateLeadStatusState: event.updateLeadStatusState,
        bookingItemDetails: event.bookingDetailsItem,
        allUsers: allusers));
  }

  FutureOr<void> _onIsDocumentSelected(ToggleDocument event, Emitter<BookingFormState> emit) {
    emit(state.copyWith(isDocumentSelected: event.isDocumentSelected, pageState: BookingFormPageState.initial));
  }

  FutureOr<void> _onIsDiscountSelected(ToggleDiscountEvent event, Emitter<BookingFormState> emit) {
    emit(state.copyWith(isDiscountSelected: event.isDiscountSelected));
    add(BookingDetailsCalculationEvent());
  }

  String? getLeadAddress(List<AddressEntity>? addresses) {
    String address = '';
    addresses?.forEach((action) {
      if (action.subLocality != null && action.city != null && action.state != null) {
        address = '$address${action.subLocality},${action.city},${action.state},';
      }
    });
    return address;
  }

  String? getEnquiryTypes(List<EnquiryType>? enquiryTypes) {
    String enquirytype = '';
    enquiryTypes?.forEach((action) {
      if (enquirytype == '') {
        enquirytype = '$enquirytype${action.description}';
      } else {
        enquirytype = '$enquirytype,${action.description}';
      }
    });
    return enquirytype;
  }

  String? getBhks(List<double>? bHKs) {
    String bhkString = '';
    bHKs?.forEach((action) {
      if (action != 0.5) {
        bhkString = '$bhkString${action.toString()} BHK,';
      } else {
        bhkString = '$bhkString 1 RK, ';
      }
    });
    return bhkString;
  }

  FutureOr<void> _onIsReferralCommissionSelected(ToggleReferralCommissionEvent event, Emitter<BookingFormState> emit) {
    emit(state.copyWith(isReferralCommissionSelected: event.isReferralCommissionSelected));
    add(BrokerageInfoCalculationEvent());
  }

  FutureOr<void> _onUploadFileEvent(UploadFileEvent event, Emitter<BookingFormState> emit) async {
    final files = await FilePickerUtil.pickFile(event.source);
    emit(state.copyWith(pageState: BookingFormPageState.initial));

    if (files?.any((element) => element != null) ?? false) {
      String extension = files?.first?.path
          .split('.')
          .last ?? '';
      if (extension.toLowerCase() == 'pdf' || extension.toLowerCase() == 'jpeg' || extension.toLowerCase() == 'jpg' || extension.toLowerCase() == 'png') {
        final base64File = base64Encode(await files!.first!.readAsBytes());
        UploadBookedDocumentEntity uploadBookingDocumentEntity = UploadBookedDocumentEntity(file: files.first, base64String: base64File, documentName: fileNameController.text);
        Map<String, UploadBookedDocumentEntity> documents = {};
        String key = '${fileNameController.text}.$extension';
        documents[key] = uploadBookingDocumentEntity;
        fileNameController.text = '';
        emit(state.copyWith(isDocumentSelected: false, selectedDocument: [...?state.selectedDocument, documents], pageState: BookingFormPageState.initial));
      } else {
        emit(state.copyWith(pageState: BookingFormPageState.isRightDocument));
      }
    }
  }

  FutureOr<void> _onDisposeBookingForm(DisposeBookingFormEvent event, Emitter<BookingFormState> emit) {
    agreementValueController.text = '';
    carParkingChargesController.text = '';
    addOnChargesController.text = '';
    totalSoldController.text = '';
    tokenAmountController.text = '';
    discountController.text = '';
    balanceAmountController.text = '';
    notesController.text = '';
    brokeragesChargesController.text = '';
    netBrokeragesAmountController.text = '';
    gstController.text = '';
    totalBrokerageController.text = '';
    referralNameController.text = '';
    phoneNumberController.text = '';
    referralCommissionController.text = '';
    brokerageEarnedController.text = '';
    fileNameController.text = '';

    emit(state.copyWith(
      selectedDocument: [],
      isDocumentSelected: false,
      isReferralCommissionSelected: false,
      isDiscountSelected: false,
      bookingFormHeaderItem: null,
      discountSelectedItem: null,
      discountSelectableItems: [],
      paymentModeSelectedItem: null,
      paymentModeSelectableItems: [],
      typeOfPaymentSelected: null,
      typeOfPaymentSelectableItems: [],
      secondaryOwnerSelectedItem: null,
      primaryOwnerselectedItem: null,
      users: [],
      netBrokerageSelectedItem: null,
      pageState: BookingFormPageState.initial,
      isNetBrokerageAmoutSelected: false,
      updateLeadStatusState: null,
      netBrokerageSelectableItem: null,
      pageViewIndex: 0,
      secondaryUsers: [],
    ));
  }

  FutureOr<void> _onDeleteFileEvent(DeleteFileEvent event, Emitter<BookingFormState> emit) {
    List<Map<String, UploadBookedDocumentEntity>>? item = state.selectedDocument;

    item?.remove(event.item);

    emit(state.copyWith(selectedDocument: item));
  }

  FutureOr<void> _onUpdatePaymentMode(SelectPaymentModeEvent event, Emitter<BookingFormState> emit) {
    emit(state.copyWith(paymentModeSelectedItem: event.paymentModeSelectedItem));
  }

  FutureOr<void> _onDiscountEvent(DiscountEvent event, Emitter<BookingFormState> emit) {
    emit(state.copyWith(discountSelectedItem: event.discountSelectedItem));
    add(BookingDetailsCalculationEvent());
  }

  FutureOr<void> _onTypeOfPaymentEvent(SelectTypeOfPaymentEvent event, Emitter<BookingFormState> emit) {
    emit(state.copyWith(typeOfPaymentSelected: event.typeOfPaymentSelected));
  }

  FutureOr<void> _onNetBrokerageAmountEvent(NetBrokerageAmountEvent event, Emitter<BookingFormState> emit) {
    emit(state.copyWith(netBrokerageSelectedItem: event.netBrokerageAmountSelected));
    add(BrokerageInfoCalculationEvent());
  }

  FutureOr<void> _onPrimaryOwnerEvent(SelectPrimaryOwnerEvent event, Emitter<BookingFormState> emit) {
    List<SelectableItem<GetAllUsersModel?>>? secondaryUsers = [];
    state.allUsers?.forEach((action) {
      if (event.selectedItem?.value?.id != action.value?.id) {
        secondaryUsers.add(action);
      }
    });

    emit(state.copyWith(primaryOwnerselectedItem: event.selectedItem, secondaryUsers: secondaryUsers));
  }

  FutureOr<void> _onSecondaryOwnerEvent(SelectSecondaryOwnerEvent event, Emitter<BookingFormState> emit) {
    List<SelectableItem<GetAllUsersModel?>>? primaryUsers = [];
    state.allUsers?.forEach((action) {
      if (event.selectedItem?.value?.id != action.value?.id) {
        primaryUsers.add(action);
      }
    });
    emit(state.copyWith(secondaryOwnerSelectedItem: event.selectedItem, users: primaryUsers));
  }

  FutureOr<void> _onBookingDetailsCalculation(BookingDetailsCalculationEvent event, Emitter<BookingFormState> emit) {
    emit(state.copyWith(pageState: BookingFormPageState.initial));
    double agreementValue = 0;
    double carParkingCharges = 0;
    double totalSoldPrice = 0;
    double addOnCharges = 0;
    double tokenAmountPaid = 0;
    double balanceAmount = 0;
    double discount = 0;
    if (state.discountSelectedItem?.value?.index == DiscountType.cashback.index) {
      if (agreementValueController.text.isNotEmpty) {
        agreementValue = double.parse(agreementValueController.text);
      }
      if (carParkingChargesController.text.isNotEmpty) {
        carParkingCharges = double.parse(carParkingChargesController.text);
        if (carParkingCharges < 0) {
          emit(state.copyWith(
            pageState: BookingFormPageState.isCarParkingChargesNegative,
          ));
        }
        if (carParkingCharges > agreementValue) {
          emit(state.copyWith(
            pageState: BookingFormPageState.isCarParkingChargesMore,
          ));
        }

        if (carParkingCharges > 0 && carParkingCharges < agreementValue) {
          emit(state.copyWith(
            pageState: BookingFormPageState.initial,
          ));
        }
      }
      if (addOnChargesController.text.isNotEmpty) {
        addOnCharges = double.parse(addOnChargesController.text);
        if (addOnCharges < 0) {
          emit(state.copyWith(pageState: BookingFormPageState.isAddOnChargesNegative));
        }
        if (addOnCharges > agreementValue) {
          emit(state.copyWith(
            pageState: BookingFormPageState.isAddOnChargesMore,
          ));
        }

        if (addOnCharges > 0 && addOnCharges < agreementValue) {
          emit(state.copyWith(
            pageState: BookingFormPageState.initial,
          ));
        }
      }
      totalSoldPrice = agreementValue + carParkingCharges + addOnCharges;
      totalSoldPrice = totalSoldPrice.roundToDouble();
      totalSoldController.text = totalSoldPrice.toString();
      balanceAmount = totalSoldPrice - tokenAmountPaid;
      balanceAmountController.text = balanceAmount.toString();

      if (tokenAmountController.text.isNotEmpty) {
        tokenAmountPaid = double.parse(tokenAmountController.text);
        if (tokenAmountPaid < 0) {
          emit(state.copyWith(pageState: BookingFormPageState.isTokenAmountNegative));
        }
        if (tokenAmountPaid > totalSoldPrice) {
          emit(state.copyWith(
            pageState: BookingFormPageState.isTokenAmountMore,
          ));
        }

        if (tokenAmountPaid > 0 && tokenAmountPaid < totalSoldPrice) {
          emit(state.copyWith(
            pageState: BookingFormPageState.initial,
          ));
        }
      }

      totalSoldPrice = agreementValue + carParkingCharges + addOnCharges;
      totalSoldPrice = totalSoldPrice.roundToDouble();
      totalSoldController.text = totalSoldPrice.toString();
      balanceAmount = totalSoldPrice - tokenAmountPaid;
      balanceAmountController.text = balanceAmount.toString();
      if (discountController.text.isNotEmpty) {
        discount = double.parse(discountController.text);
        if (discount > totalSoldPrice) {
          emit(state.copyWith(pageState: BookingFormPageState.isDisCountAmountMore));
        }
        if (discount < 0) {
          emit(state.copyWith(pageState: BookingFormPageState.isDisCountAmountNegative));
        }
        if (discount > 0 && discount < totalSoldPrice) {
          emit(state.copyWith(pageState: BookingFormPageState.initial));
        }
      }
    }

    if (state.isDiscountSelected && state.discountSelectedItem?.value?.index == DiscountType.directAdjustment.index) {
      if (agreementValueController.text.isNotEmpty) {
        agreementValue = double.parse(agreementValueController.text);
      }
      if (carParkingChargesController.text.isNotEmpty) {
        carParkingCharges = double.parse(carParkingChargesController.text);
        if (carParkingCharges < 0) {
          emit(state.copyWith(
            pageState: BookingFormPageState.isCarParkingChargesNegative,
          ));
        }
        if (carParkingCharges > agreementValue) {
          emit(state.copyWith(
            pageState: BookingFormPageState.isCarParkingChargesMore,
          ));
        }
        if (carParkingCharges < agreementValue) {
          emit(state.copyWith(
            pageState: BookingFormPageState.initial,
          ));
        }
        if (carParkingCharges > 0 && carParkingCharges < agreementValue) {
          emit(state.copyWith(
            pageState: BookingFormPageState.initial,
          ));
        }
      }
      if (addOnChargesController.text.isNotEmpty) {
        addOnCharges = double.parse(addOnChargesController.text);
        if (addOnCharges < 0) {
          emit(state.copyWith(pageState: BookingFormPageState.isAddOnChargesNegative));
        }
        if (addOnCharges > agreementValue) {
          emit(state.copyWith(
            pageState: BookingFormPageState.isAddOnChargesMore,
          ));
        }
        if (addOnCharges < agreementValue) {
          emit(state.copyWith(
            pageState: BookingFormPageState.initial,
          ));
        }
        if (addOnCharges > 0 && addOnCharges < agreementValue) {
          emit(state.copyWith(
            pageState: BookingFormPageState.initial,
          ));
        }
      }
      totalSoldPrice = agreementValue + carParkingCharges + addOnCharges;
      totalSoldPrice = totalSoldPrice.roundToDouble();
      totalSoldController.text = totalSoldPrice.toString();
      balanceAmount = totalSoldPrice - tokenAmountPaid;
      balanceAmountController.text = balanceAmount.toString();

      if (tokenAmountController.text.isNotEmpty) {
        tokenAmountPaid = double.parse(tokenAmountController.text);
        if (tokenAmountPaid < 0) {
          emit(state.copyWith(pageState: BookingFormPageState.isTokenAmountNegative));
        }
        if (tokenAmountPaid > totalSoldPrice) {
          emit(state.copyWith(
            pageState: BookingFormPageState.isTokenAmountMore,
          ));
        }
        if (tokenAmountPaid < totalSoldPrice) {
          emit(state.copyWith(
            pageState: BookingFormPageState.initial,
          ));
        }
        if (tokenAmountPaid > 0 && tokenAmountPaid < totalSoldPrice) {
          emit(state.copyWith(
            pageState: BookingFormPageState.initial,
          ));
        }
      }
      totalSoldPrice = agreementValue + carParkingCharges + addOnCharges;
      totalSoldPrice = totalSoldPrice.roundToDouble();
      totalSoldController.text = totalSoldPrice.toString();
      balanceAmount = totalSoldPrice - tokenAmountPaid;
      balanceAmountController.text = balanceAmount.toString();
      if (discountController.text.isNotEmpty && double.parse(discountController.text) >= 100) {
        emit(state.copyWith(pageState: BookingFormPageState.isDisCountValueMore));
        return null;
      }
      if (discountController.text.isNotEmpty && double.parse(discountController.text) < 0) {
        emit(state.copyWith(pageState: BookingFormPageState.isDisCountValueNegative));
        return null;
      }
      if (discountController.text.isNotEmpty && double.parse(discountController.text) < 100 && double.parse(discountController.text) > 0) {
        emit(state.copyWith(pageState: BookingFormPageState.initial));
      }
      if (discountController.text.isNotEmpty) {
        discount = double.parse(discountController.text);
        double value = agreementValue / 100;
        double discountAmount = value * discount;
        agreementValue = agreementValue - discountAmount;
        totalSoldPrice = agreementValue + carParkingCharges + addOnCharges;
        totalSoldController.text = totalSoldPrice.roundToDouble().toString();
        balanceAmount = totalSoldPrice - tokenAmountPaid;
        balanceAmountController.text = balanceAmount.toString();
        agreementValueBrokerageInfo.text = agreementValue.toString();
      } else {
        discount = 0;
        double value = agreementValue / 100;
        double discountAmount = value * discount;
        agreementValue = agreementValue - discountAmount;
        totalSoldPrice = agreementValue + carParkingCharges + addOnCharges;
        totalSoldController.text = totalSoldPrice.roundToDouble().toString();
        balanceAmount = totalSoldPrice - tokenAmountPaid;
        balanceAmountController.text = balanceAmount.toString();
        agreementValueBrokerageInfo.text = agreementValue.toString();
      }
    }
    if (!state.isDiscountSelected && state.discountSelectedItem?.value?.index == DiscountType.directAdjustment.index) {
      if (agreementValueController.text.isNotEmpty) {
        agreementValue = double.parse(agreementValueController.text);
      }
      if (carParkingChargesController.text.isNotEmpty) {
        carParkingCharges = double.parse(carParkingChargesController.text);
        if (carParkingCharges < 0) {
          emit(state.copyWith(
            pageState: BookingFormPageState.isCarParkingChargesNegative,
          ));
        }
        if (carParkingCharges > agreementValue) {
          emit(state.copyWith(
            pageState: BookingFormPageState.isCarParkingChargesMore,
          ));
        }
        if (carParkingCharges < agreementValue) {
          emit(state.copyWith(
            pageState: BookingFormPageState.initial,
          ));
        }
        if (carParkingCharges > 0 && carParkingCharges < agreementValue) {
          emit(state.copyWith(
            pageState: BookingFormPageState.initial,
          ));
        }
      }
      if (addOnChargesController.text.isNotEmpty) {
        addOnCharges = double.parse(addOnChargesController.text);
        if (addOnCharges < 0) {
          emit(state.copyWith(pageState: BookingFormPageState.isAddOnChargesNegative));
        }
        if (addOnCharges > agreementValue) {
          emit(state.copyWith(
            pageState: BookingFormPageState.isAddOnChargesMore,
          ));
        }
        if (addOnCharges < agreementValue) {
          emit(state.copyWith(
            pageState: BookingFormPageState.initial,
          ));
        }
        if (addOnCharges > 0 && addOnCharges < agreementValue) {
          emit(state.copyWith(
            pageState: BookingFormPageState.initial,
          ));
        }
      }
      totalSoldPrice = agreementValue + carParkingCharges + addOnCharges;
      totalSoldPrice = totalSoldPrice.roundToDouble();
      totalSoldController.text = totalSoldPrice.toString();
      balanceAmount = totalSoldPrice - tokenAmountPaid;
      balanceAmountController.text = balanceAmount.toString();

      if (tokenAmountController.text.isNotEmpty) {
        tokenAmountPaid = double.parse(tokenAmountController.text);
        if (tokenAmountPaid < 0) {
          emit(state.copyWith(pageState: BookingFormPageState.isTokenAmountNegative));
        }
        if (tokenAmountPaid > totalSoldPrice) {
          emit(state.copyWith(
            pageState: BookingFormPageState.isTokenAmountMore,
          ));
        }
        if (tokenAmountPaid < totalSoldPrice) {
          emit(state.copyWith(
            pageState: BookingFormPageState.initial,
          ));
        }
        if (tokenAmountPaid > 0 && tokenAmountPaid < totalSoldPrice) {
          emit(state.copyWith(
            pageState: BookingFormPageState.initial,
          ));
        }
      }
      totalSoldPrice = agreementValue + carParkingCharges + addOnCharges;
      totalSoldPrice = totalSoldPrice.roundToDouble();
      totalSoldController.text = totalSoldPrice.toString();
      balanceAmount = totalSoldPrice - tokenAmountPaid;
      balanceAmountController.text = balanceAmount.toString();
      if (discountController.text.isNotEmpty) {
        discount = double.parse(discountController.text);
        if (discount > totalSoldPrice) {
          emit(state.copyWith(pageState: BookingFormPageState.isDisCountAmountMore));
        }
        if (discount < 0) {
          emit(state.copyWith(pageState: BookingFormPageState.isDisCountAmountNegative));
        }
        if (discount > 0 && discount < totalSoldPrice) {
          emit(state.copyWith(pageState: BookingFormPageState.initial));
        }

        agreementValue = agreementValue - discount;
        totalSoldPrice = agreementValue + carParkingCharges + addOnCharges;
        totalSoldPrice = totalSoldPrice.roundToDouble();
        totalSoldController.text = totalSoldPrice.toString();
        balanceAmount = totalSoldPrice - tokenAmountPaid;
        balanceAmountController.text = balanceAmount.toString();
        agreementValueBrokerageInfo.text = agreementValue.toString();
      } else {
        discount = 0;

        agreementValue = agreementValue - discount;
        totalSoldPrice = agreementValue + carParkingCharges + addOnCharges;
        totalSoldPrice = totalSoldPrice.roundToDouble();
        totalSoldController.text = totalSoldPrice.toString();
        balanceAmount = totalSoldPrice - tokenAmountPaid;
        balanceAmountController.text = balanceAmount.toString();
        agreementValueBrokerageInfo.text = agreementValue.toString();
      }
    }
    add(BrokerageInfoCalculationEvent());
  }

  FutureOr<void> _onIsNetBrokerageAmountSelected(ToggleNetBrokerageAmountEvent event, Emitter<BookingFormState> emit) {
    emit(state.copyWith(isNetBrokerageAmoutSelected: event.isNetBrokerageAmountSelected));
    add(BrokerageInfoCalculationEvent());
  }

  FutureOr<void> _onBrokerageInfoCalculation(BrokerageInfoCalculationEvent event, Emitter<BookingFormState> emit) {
    double brokerageInfoAgreementValue = 0;
    double soldPrice = 0;
    double brokerageCharges = 0;
    double gst = 0;
    double referralCommission = 0;
    if (brokeragesChargesController.text.isEmpty) {
      emit(state.copyWith(pageState: BookingFormPageState.initial));
    }

    if (agreementValueBrokerageInfo.text.isNotEmpty) {
      brokerageInfoAgreementValue = double.parse(agreementValueBrokerageInfo.text);
    }
    if (totalSoldController.text.isNotEmpty) {
      soldPrice = double.parse(totalSoldController.text);
    }
    if (brokeragesChargesController.text.isNotEmpty && state.isNetBrokerageAmoutSelected) {
      brokerageCharges = double.parse(brokeragesChargesController.text);
      if (brokerageCharges >= 100) {
        emit(state.copyWith(pageState: BookingFormPageState.isBrokerageChargeMore));
        return null;
      } else if (brokerageCharges < 0) {
        emit(state.copyWith(pageState: BookingFormPageState.isBrokerageChargeNegative));
        return null;
      } else if (brokerageCharges <= 100 && brokerageCharges > 0) {
        emit(state.copyWith(pageState: BookingFormPageState.initial));
      }
    }
    if (brokeragesChargesController.text.isNotEmpty && !state.isNetBrokerageAmoutSelected) {
      brokerageCharges = double.parse(brokeragesChargesController.text);
      if (state.netBrokerageSelectedItem?.value?.index == BrokerageType.agreementValue.index) {
        if (brokerageCharges < 0) {
          emit(state.copyWith(pageState: BookingFormPageState.isBrokerageChargeNegative));
          return null;
        }
        if (brokerageCharges > brokerageInfoAgreementValue) {
          emit(state.copyWith(pageState: BookingFormPageState.isBrokerageChargesMoreAgreement));
          return null;
        }
        if (brokerageCharges > 0 && brokerageCharges < brokerageInfoAgreementValue) {
          emit(state.copyWith(pageState: BookingFormPageState.initial));
        }
      }
      if (state.netBrokerageSelectedItem?.value?.index == BrokerageType.soldPrice.index) {
        if (brokerageCharges < 0) {
          emit(state.copyWith(pageState: BookingFormPageState.isBrokerageChargeNegative));
          return null;
        }
        if (brokerageCharges > soldPrice) {
          emit(state.copyWith(pageState: BookingFormPageState.isBrokerageChargesMoreSold));
          return null;
        }
        if (brokerageCharges > 0 && brokerageCharges < soldPrice) {
          emit(state.copyWith(pageState: BookingFormPageState.initial));
        }
      }
    }
    if (gstController.text.isNotEmpty) {
      gst = double.parse(gstController.text);
      if (gst >= 100) {
        emit(state.copyWith(pageState: BookingFormPageState.isGstMore));
        return null;
      }
      if (gst < 0) {
        emit(state.copyWith(pageState: BookingFormPageState.isGstNegative));
        return null;
      }
      if (gst > 0 && gst < 100) {
        emit(state.copyWith(pageState: BookingFormPageState.initial));
      }
    }
    if (referralCommissionController.text.isNotEmpty && state.isReferralCommissionSelected) {
      referralCommission = double.parse(referralCommissionController.text);
      if (referralCommission < 0) {
        emit(state.copyWith(pageState: BookingFormPageState.isReferralCommissionNegative));
        return null;
      }
      if (referralCommission >= 100) {
        emit(state.copyWith(pageState: BookingFormPageState.isReferralCommissionMore));
        return null;
      }
      if (referralCommission > 0 && referralCommission < 100) {
        emit(state.copyWith(pageState: BookingFormPageState.initial));
      }
    }
    if (referralCommissionController.text.isNotEmpty && !state.isReferralCommissionSelected) {
      referralCommission = double.parse(referralCommissionController.text);
      if (referralCommission < 0) {
        emit(state.copyWith(pageState: BookingFormPageState.isReferralCommissionNegative));
        return null;
      }

      if (referralCommission > 0) {
        emit(state.copyWith(pageState: BookingFormPageState.initial));
      }
    }

    if (state.isNetBrokerageAmoutSelected && state.netBrokerageSelectedItem?.value?.index == BrokerageType.agreementValue.index) {
      double value = brokerageCharges * brokerageInfoAgreementValue;
      double value2 = value / 100;
      double netBrokerageCharges = value2;
      netBrokeragesAmountController.text = netBrokerageCharges.toString();
    }
    if (!state.isNetBrokerageAmoutSelected) {
      netBrokeragesAmountController.text = brokerageCharges.toString();
    }
    if (state.isNetBrokerageAmoutSelected && state.netBrokerageSelectedItem?.value?.index == BrokerageType.soldPrice.index) {
      double value = brokerageCharges * soldPrice;
      double value2 = value / 100;
      double netBrokerageCharges = value2;
      netBrokeragesAmountController.text = netBrokerageCharges.toString();
    }
    if (gst == 0) {
      totalBrokerageController.text = double.parse(netBrokeragesAmountController.text).toString();
    }
    if (gstController.text.isNotEmpty) {
      if (netBrokeragesAmountController.text.isNotEmpty) {
        double netBrokerageAmount = double.parse(netBrokeragesAmountController.text);
        double value = netBrokerageAmount * gst;
        double value2 = value / 100;
        double totalBrokerage = netBrokerageAmount + value2;
        totalBrokerageController.text = totalBrokerage.toString();
      }
    }
    if (referralCommissionController.text.isNotEmpty) {
      if (state.isReferralCommissionSelected) {
        double totalBrokerage = double.parse(totalBrokerageController.text);
        double value = totalBrokerage * referralCommission;
        double value2 = value / 100;
        double brokerageEarned = totalBrokerage - value2;
        brokerageEarnedController.text = brokerageEarned.toString();
      } else {
        double totalBrokerage = double.parse(totalBrokerageController.text);
        if (referralCommission > totalBrokerage) {
          emit(state.copyWith(pageState: BookingFormPageState.isReferralCommissionMoreBrokerage));
        } else {
          emit(state.copyWith(pageState: BookingFormPageState.initial));
        }
        double value = totalBrokerage - referralCommission;
        brokerageEarnedController.text = value.toString();
      }
    }
    if (referralCommission == 0) {
      if (totalBrokerageController.text.isNotEmpty) {
        double totalBrokerage = double.parse(totalBrokerageController.text);
        double value = totalBrokerage - referralCommission;
        brokerageEarnedController.text = value.toString();
      }
    }
  }

  Future<String?> uploadImageToS3Bucket(XFile? capturedImage) async {
    if (capturedImage != null) {
      String? uploadedImage;
      final base64File = base64Encode(await capturedImage.readAsBytes());
      final uploadedImageToS3Bucket = await _uploadDocumentUseCase(UploadDocumentParams(BlobFolderNameEnum.bookingFormDocument.description, capturedImage.path
          .split('/')
          .last != null ? capturedImage.path
          .split('/')
          .last : await _nativeImplementationService.getImageExtension(capturedImage) ?? 'jpg', base64File,));
      uploadedImageToS3Bucket.fold(
              (failure) =>
          {
            // show toast
          },
              (res) =>
          {
            uploadedImage = res?.isNotEmpty ?? false ? res : null,
          });

      return uploadedImage;
    } else {
      return null;
    }
  }

  FutureOr<void> _onBookedLead(BookedLeadEvent event, Emitter<BookingFormState> emit) async {
    emit(state.copyWith(pageState: BookingFormPageState.loading));

    List<Document?> documentList = [];

    if (state.selectedDocument != null && state.selectedDocument!.isNotEmpty) {
      for (int i = 0; i < state.selectedDocument!.length; i++) {
        final data = state.selectedDocument?[i];
        String? s3Data = await uploadImageToS3Bucket(data?.values.firstOrNull?.file);

        DocumentType? type;
        for (var documentType in DocumentType.values) {
          if (documentType.description.toLowerCase() == data?.values.firstOrNull?.file?.path
              .split('.')
              .last) {
            type = documentType;
            continue;
          }
        }

        Document document = Document(id: null,
            type: type,
            documentName: data?.values.firstOrNull?.documentName,
            bookedDocumentType: null,
            filePath: s3Data);
        documentList.add(document);
      }
    }
    UnitType? unittype = UnitType(
      id: state.updateLeadStatusState?.selectedUnit?.value?.id,
      carpetArea: state.updateLeadStatusState?.selectedUnit?.value?.carpetArea,
      carpetAreaUnitId: state.updateLeadStatusState?.selectedUnit?.value?.carpetAreaUnitId,
      name: state.updateLeadStatusState?.selectedUnit?.value?.name,
      superBuildUpArea: state.updateLeadStatusState?.selectedUnit?.value?.superBuildUpArea,
      superBuildUpAreaUnit: state.updateLeadStatusState?.selectedUnit?.value?.superBuildUpAreaUnit,
    );

    BrokerageInfo brokerageInfo = BrokerageInfo(
      soldPrice: double.parse(totalSoldController.text.isNotEmpty ? totalSoldController.text : '0'),
      agreementValue: double.parse(agreementValueBrokerageInfo.text.isNotEmpty ? agreementValueBrokerageInfo.text : '0'),
      brokerageCharges: double.parse(brokeragesChargesController.text.isNotEmpty ? brokeragesChargesController.text : '0'),
      brokerageType: state.netBrokerageSelectedItem?.value,
      brokerageUnit: state.isNetBrokerageAmoutSelected ? '%' : state.selectedCurrency?.value,
      gst: double.parse(gstController.text.isNotEmpty ? gstController.text : '0'),
      gstUnit: '%',
      totalBrokerage: double.parse(totalBrokerageController.text.isNotEmpty ? totalBrokerageController.text : '0'),
      earnedBrokerage: double.parse(brokerageEarnedController.text.isNotEmpty ? brokerageEarnedController.text : '0'),
      netBrokerageAmount: double.parse(netBrokeragesAmountController.text.isNotEmpty ? netBrokeragesAmountController.text : '0'),
      commission: double.parse(referralCommissionController.text.isNotEmpty ? referralCommissionController.text : '0'),
      commissionUnit: state.isReferralCommissionSelected ? '%' : state.selectedCurrency?.value,
      referralName: referralNameController.text,
      referralNumber: phoneNumberController.text,
    );
    DateFormat format = DateFormat("dd-MM-yyyy"); // Adjust the format as needed
    DateTime? dateTime = format.parse(bookingDateController.text.isNotEmpty ? bookingDateController.text : state.updateLeadStatusState!.bookingItemDetails!.bookedDate!).getBasedOnTimeZone()?.toUtcFormat();

    BookedLeadModel bookedLeadModel = BookedLeadModel(
        leadId: state.updateLeadStatusState?.leadEntity?.id ?? '00000000-0000-0000-0000-000000000000',
        agreementValue: double.parse(agreementValueController.text.isNotEmpty ? agreementValueController.text : '0'),
        carParkingCharges: double.parse(carParkingChargesController.text.isNotEmpty ? carParkingChargesController.text : '0'),
        additionalCharges: double.parse(addOnChargesController.text.isNotEmpty ? addOnChargesController.text : '0'),
        soldPrice: totalSoldController.text,
        discount: double.parse(discountController.text.isNotEmpty ? discountController.text : '0'),
        discountUnit: state.isDiscountSelected ? '%' : state.selectedCurrency?.value,
        paymentType: state.typeOfPaymentSelected?.value,
        paymentMode: state.paymentModeSelectedItem?.value,
        discountMode: state.discountSelectedItem?.value,
        tokenAmount: double.parse(tokenAmountController.text.isNotEmpty ? tokenAmountController.text : '0'),
        notes: notesController.text,
        brokerageInfo: brokerageInfo,
        documents: documentList,
        currency: state.selectedCurrency?.value,
        projectsList: state.bookingItemDetails?.chosenProject != null ? [state.bookingItemDetails?.chosenProject?.title ?? ''] : null,
        projectIds: state.bookingItemDetails?.chosenProject != null ? [state.bookingItemDetails?.chosenProject?.value ?? ''] : null,
        propertiesList: state.bookingItemDetails?.chosenProperty != null ? [state.bookingItemDetails?.chosenProperty?.title ?? ''] : null,
        propertyIds: state.bookingItemDetails?.chosenProperty != null ? [state.bookingItemDetails?.chosenProperty?.value ?? ''] : null,
        bookedUnderName: state.bookingItemDetails?.bookedUnderName,
        unitType: unittype,
        remainingAmount: balanceAmountController.text.isNotNullOrEmpty() ? double.parse(balanceAmountController.text) : null,
        bookedDate: dateTime);

    final response = await _bookedLeadUseCase(bookedLeadModel);

    response.fold((failure) => {emit(state.copyWith(pageState: BookingFormPageState.failure))}, (result) {
      emit(state.copyWith(pageState: BookingFormPageState.success));
      if (state.updateLeadStatusState?.leadEntity?.id?.isNotNullOrEmpty() ?? false) {
        getIt<LeadInfoBloc>().add(GetLeadInfoInitialEvent((state.updateLeadStatusState!.leadEntity!.id!)));
        getIt<LeadInfoBloc>().add(InitializeHistoryEvent(true));
        getIt<LeadInfoBloc>().add(InitializeNotesEvent(true));
        getIt<ManageLeadsBloc>().add(ManageLeadsInitialEvent(leadFilter: getIt<ManageLeadsBloc>().leadFilter));
      }
    });
  }

  FutureOr<void> _onPageChangedEvent(PageChangedEvent event, Emitter<BookingFormState> emit) {
    emit(state.copyWith(pageViewIndex: event.index));
  }

  FutureOr<void> _onSelectCurrencyEvent(SelectCurrencyEvent event, Emitter<BookingFormState> emit) {
    emit(state.copyWith(selectedCurrency: event.selectedCurrency));
  }

  FutureOr<void> _onCheckBookingDetailsCondition(CheckBookingDetailsConditionEvent event, Emitter<BookingFormState> emit) {
    emit(state.copyWith(pageState: BookingFormPageState.initial));
    if (carParkingChargesController.text.isNotEmpty && double.parse(carParkingChargesController.text) > double.parse(agreementValueController.text)) {
      emit(state.copyWith(pageState: BookingFormPageState.isCarParkingChargesMore));
      return null;
    } else if (addOnChargesController.text.isNotEmpty && double.parse(addOnChargesController.text) > double.parse(agreementValueController.text)) {
      emit(state.copyWith(pageState: BookingFormPageState.isAddOnChargesMore));
      return null;
    } else if (tokenAmountController.text.isNotEmpty && double.parse(tokenAmountController.text) > double.parse(totalSoldController.text)) {
      emit(state.copyWith(pageState: BookingFormPageState.isTokenAmountMore));
    } else if (carParkingChargesController.text.isNotEmpty && double.parse(carParkingChargesController.text) < 0) {
      emit(state.copyWith(pageState: BookingFormPageState.isCarParkingChargesNegative));
      return null;
    } else if (addOnChargesController.text.isNotEmpty && double.parse(addOnChargesController.text) < 0) {
      emit(state.copyWith(pageState: BookingFormPageState.isAddOnChargesNegative));
      return null;
    } else if (tokenAmountController.text.isNotEmpty && double.parse(tokenAmountController.text) < 0) {
      emit(state.copyWith(pageState: BookingFormPageState.isTokenAmountNegative));
      return null;
    } else if (discountController.text.isNotEmpty && state.isDiscountSelected && double.parse(discountController.text) >= 100) {
      emit(state.copyWith(pageState: BookingFormPageState.isDisCountValueMore));
      return null;
    } else if (discountController.text.isNotEmpty && double.parse(discountController.text) < 0) {
      emit(state.copyWith(pageState: BookingFormPageState.isDisCountValueNegative));
      return null;
    } else if (discountController.text.isNotEmpty && !state.isDiscountSelected && double.parse(discountController.text) > double.parse(totalSoldController.text)) {
      emit(state.copyWith(pageState: BookingFormPageState.isDisCountAmountMore));
      return null;
    } else {
      event.pageController.nextPage(
        duration: const Duration(milliseconds: 100),
        curve: Curves.easeIn,
      );
    }
  }

  bool isDocumentNamePresent(String name) {
    if (state.selectedDocument?.isNotEmpty ?? false) {
      for (int i = 0; i < state.selectedDocument!.length; i++) {
        if (name.toLowerCase() == state.selectedDocument?[i].values.firstOrNull?.documentName?.toLowerCase()) {
          return true;
        }
      }
    }
    return false;
  }

  FutureOr<void> _onToBookingDetailsScreen(ToBookingDetailsScreenEvent event, Emitter<BookingFormState> emit) {
    event.pageController.previousPage(
      duration: const Duration(milliseconds: 100),
      curve: Curves.easeIn,
    );
  }
}
