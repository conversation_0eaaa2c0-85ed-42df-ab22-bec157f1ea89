part of 'listing_management_filter_bloc.dart';

@immutable
class ListingManagementFilterState {
  final PageState pageState;
  final String? errorMessage;
  final ListingManagementFilterModel? listingManagementFilterModel;
  final List<ItemListingPropertyFilterCategoryModel> listingManagementFilterCategories;
  final List<ItemListingPropertyFilterCategoryModel> searchFilteredCategories;
  final int selectedCategoryIndex;
  final DateTime? selectedFromDate;
  final DateTime? selectedToDate;
  final BudgetRangeModel customBudget;
  final List<SelectableItem<String>> currencies;
  final SelectableItem<String>? selectedCurrency;
  final List<SelectableItem<PropertyDateType>> dateTypes;
  final SelectableItem<PropertyDateType>? selectedDateType;
  final List<SelectableItem<MasterAreaUnitsModel>> areaUnits;
  final SelectableItem<MasterAreaUnitsModel>? selectedSaleableAreaUnit;
  final SelectableItem<MasterAreaUnitsModel>? selectedBuiltUpAreaUnit;
  final SelectableItem<MasterAreaUnitsModel>? selectedCarpetAreaUnit;
  final SelectableItem<MasterAreaUnitsModel>? selectedNetAreaUnit;
  final SelectableItem<MasterAreaUnitsModel>? selectedPropertySizeAreaUnit;
  final RangeInput? saleableArea;
  final RangeInput? carpetArea;
  final RangeInput? netArea;
  final RangeInput? builtUpArea;
  final RangeInput? propertySize;
  final RangeInput? minBudget;
  final RangeInput? maxBudget;

  const ListingManagementFilterState({
    this.pageState = PageState.initial,
    this.errorMessage,
    this.selectedCategoryIndex = 0,
    this.listingManagementFilterCategories = const [],
    this.searchFilteredCategories = const [],
    this.listingManagementFilterModel,
    this.selectedFromDate,
    this.selectedToDate,
    this.customBudget = const BudgetRangeModel(isCustomBudget: true),
    this.currencies = const [],
    this.selectedCurrency,
    this.dateTypes = const [],
    this.selectedDateType,
    this.areaUnits = const [],
    this.selectedSaleableAreaUnit,
    this.selectedBuiltUpAreaUnit,
    this.selectedCarpetAreaUnit,
    this.selectedNetAreaUnit,
    this.selectedPropertySizeAreaUnit,
    this.saleableArea,
    this.carpetArea,
    this.netArea,
    this.propertySize,
    this.builtUpArea,
    this.minBudget,
    this.maxBudget,
  });

  ListingManagementFilterState copyWith({
    PageState? pageState,
    String? errorMessage,
    ListingManagementFilterModel? listingManagementFilterModel,
    List<ItemListingPropertyFilterCategoryModel>? listingManagementFilterCategories,
    List<ItemListingPropertyFilterCategoryModel>? searchFilteredCategories,
    int? selectedCategoryIndex,
    DateTime? selectedFromDate,
    DateTime? selectedToDate,
    BudgetRangeModel? customBudget,
    List<SelectableItem<String>>? currencies,
    SelectableItem<String>? selectedCurrency,
    List<SelectableItem<PropertyDateType>>? dateTypes,
    SelectableItem<PropertyDateType>? selectedDateType,
    bool updateSelectedDateType = false,
    bool updateSelectedFromDate = true,
    bool updateSelectedToDate = true,
    bool updateCustomBudget = true,
    List<SelectableItem<MasterAreaUnitsModel>>? areaUnits,
    SelectableItem<MasterAreaUnitsModel>? selectedSaleableAreaUnit,
    SelectableItem<MasterAreaUnitsModel>? selectedBuiltUpAreaUnit,
    SelectableItem<MasterAreaUnitsModel>? selectedCarpetAreaUnit,
    SelectableItem<MasterAreaUnitsModel>? selectedNetAreaUnit,
    SelectableItem<MasterAreaUnitsModel>? selectedPropertySizeAreaUnit,
    RangeInput? saleableArea,
    bool removeSaleableArea = false,
    RangeInput? carpetArea,
    RangeInput? netArea,
    bool removeCarpetArea = false,
    bool removeNetArea = false,
    RangeInput? builtUpArea,
    bool removeBuiltUpArea = false,
    RangeInput? propertySize,
    bool removePropertySize = false,
    resetValues = false,
    bool updateSelectMinBudget = true,
    bool updateSelectMaxBudget = true,
    RangeInput? propertyArea,
    RangeInput? minBudget,
    RangeInput? maxBudget,
    bool updateCarpetAreaUnit=false,
    bool updateSaleableAreaUnit=false,
    bool updateBuiltUpAreaUnit=false,
    bool updatePropertyAreaUnit=false,
    bool updateNetAreaUnit=false,

  }) {
    return ListingManagementFilterState(
      pageState: pageState ?? this.pageState,
      errorMessage: errorMessage,
      listingManagementFilterModel: listingManagementFilterModel ?? this.listingManagementFilterModel,
      listingManagementFilterCategories: listingManagementFilterCategories ?? this.listingManagementFilterCategories,
      searchFilteredCategories: searchFilteredCategories ?? this.searchFilteredCategories,
      selectedCategoryIndex: selectedCategoryIndex ?? this.selectedCategoryIndex,
      selectedFromDate: updateSelectedFromDate ? selectedFromDate ?? this.selectedFromDate : null,
      selectedToDate: updateSelectedToDate ? selectedToDate ?? this.selectedToDate : null,
      customBudget: updateCustomBudget ? customBudget ?? this.customBudget : const BudgetRangeModel(maxBudget: 1000, minBudget: 0, isCustomBudget: false),
      currencies: currencies ?? this.currencies,
      selectedCurrency: resetValues ? null : (selectedCurrency ?? this.selectedCurrency),
      dateTypes: dateTypes ?? this.dateTypes,
      selectedDateType: (resetValues||updateSelectedDateType) ? null : (selectedDateType ?? this.selectedDateType),
      areaUnits: areaUnits ?? this.areaUnits,
      selectedSaleableAreaUnit: (resetValues || removeSaleableArea||updateSaleableAreaUnit) ? null : (selectedSaleableAreaUnit ?? this.selectedSaleableAreaUnit),
      selectedBuiltUpAreaUnit: (resetValues || removeBuiltUpArea||updateBuiltUpAreaUnit) ? null : (selectedBuiltUpAreaUnit ?? this.selectedBuiltUpAreaUnit),
      selectedCarpetAreaUnit: (resetValues || removeCarpetArea||updateCarpetAreaUnit) ? null : (selectedCarpetAreaUnit ?? this.selectedCarpetAreaUnit),
      selectedNetAreaUnit: (resetValues || removeNetArea||updateNetAreaUnit) ? null : (selectedNetAreaUnit ?? this.selectedNetAreaUnit),
      selectedPropertySizeAreaUnit: (resetValues || removePropertySize||updatePropertyAreaUnit) ? null : (selectedPropertySizeAreaUnit ?? this.selectedPropertySizeAreaUnit),
      saleableArea: (resetValues || removeSaleableArea) ? null : (saleableArea ?? this.saleableArea),
      carpetArea: (resetValues || removeCarpetArea) ? null : (carpetArea ?? this.carpetArea),
      netArea: (resetValues || removeNetArea) ? null : (netArea ?? this.netArea),
      builtUpArea: (resetValues || removeBuiltUpArea) ? null : (builtUpArea ?? this.builtUpArea),
      propertySize: (resetValues || removePropertySize) ? null : (propertySize ?? this.propertySize),
      minBudget: updateSelectMinBudget ? minBudget ?? this.minBudget : null,
      maxBudget: updateSelectMaxBudget ? maxBudget ?? this.maxBudget : null,
    );
  }
}
