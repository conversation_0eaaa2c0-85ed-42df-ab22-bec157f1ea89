import 'package:carousel_slider/carousel_controller.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/common/constants/app_analytics_constants.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/common/shapes/sharp_divider_painter.dart';
import 'package:leadrat/core_main/common/widgets/readmore_text_widget.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/listing_management_bloc/listing_management_bloc.dart';
import 'package:leadrat/features/properties/presentation/bloc/manage_property_bloc/manage_property_bloc.dart';
import 'package:leadrat/features/properties/presentation/bloc/property_info_bloc/property_info_bloc.dart';
import 'package:leadrat/features/properties/presentation/widgets/about_property_widget.dart';
import 'package:leadrat/features/properties/presentation/widgets/assign_to_widget.dart';
import 'package:leadrat/features/properties/presentation/widgets/listingOnbehalfWidget.dart';
import 'package:leadrat/features/properties/presentation/widgets/matching_leads_item_widget.dart';
import 'package:leadrat/features/properties/presentation/widgets/property_amenity_widget.dart';
import 'package:leadrat/features/properties/presentation/widgets/property_features_widget.dart';
import 'package:leadrat/features/properties/presentation/widgets/property_info_appbar.dart';
import 'package:leadrat/features/properties/presentation/widgets/property_info_footer.dart';
import 'package:leadrat/features/properties/presentation/widgets/property_owner_details_widget.dart';

import '../../../../core_main/common/data/user/repository/users_repository.dart';
import '../../../../core_main/common/widgets/leadrat_blur_effect_widget.dart';
import '../../../../core_main/common/widgets/text_underline.dart';
import '../../../../core_main/enums/app_enum/app_module.dart';
import '../../../../core_main/enums/app_enum/command_type.dart';
import '../../../../core_main/enums/property_enums/property_status.dart';
import '../../../../core_main/utilities/dialog_manager.dart';
import '../../../../core_main/utilities/leadrat_custom_snackbar.dart';
import '../widgets/properties_images_carousel_view.dart';
import '../widgets/property_info_skeleton_view.dart';

class PropertyInfoPage extends LeadratStatefulWidget {
  const PropertyInfoPage({required this.propertyId, super.key});

  final String propertyId;

  @override
  State<PropertyInfoPage> createState() => _PropertyInfoPageState();
}

class _PropertyInfoPageState extends LeadratState<PropertyInfoPage> {
  @override
  void initState() {
    getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobilePropertyInfoView);
    propertyInfoBloc.add(PropertyInfoInitialEvent(propertyId: widget.propertyId));
    super.initState();
  }

  CarouselSliderController carouselSliderController = CarouselSliderController();
  PropertyInfoBloc propertyInfoBloc = getIt<PropertyInfoBloc>();

  @override
  Widget buildContent(BuildContext context) {
    return BlocConsumer<PropertyInfoBloc, PropertyInfoState>(
      listener: (context, state) {
        if (state.pageState == PropertyInfoPageState.loading) {
          DialogManager().showTransparentProgressDialog(context, message: state.statusMessage);
        } else if (state.pageState == PropertyInfoPageState.failure) {
          DialogManager().hideTransparentProgressDialog();
          LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: state.statusMessage);
        } else if (state.pageState == PropertyInfoPageState.reAssignSuccessful) {
          DialogManager().hideTransparentProgressDialog();
          LeadratCustomSnackbar.show(context: context, type: SnackbarType.success, message: state.statusMessage);
        } else if (state.pageState == PropertyInfoPageState.deleteSuccessful) {
          DialogManager().hideTransparentProgressDialog();
          LeadratCustomSnackbar.show(context: context, message: state.statusMessage);
          if (propertyInfoBloc.isPropertyListingEnabled) {
            getIt<ListingManagementBloc>().add(ListingManagementInitialEvent());
          } else {
            getIt<ManagePropertyBloc>().add(ManagePropertyInitialEvent());
          }
          Navigator.pop(context);
        } else if (state.pageState == PropertyPageState.archivePropertySuccessfull) {
          LeadratCustomSnackbar.show(context: context, type: SnackbarType.success, message: 'property archived successfully');
          DialogManager().hideTransparentProgressDialog();
        } else if (state.pageState == PropertyPageState.archivePropertyFailure) {
          LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: 'oops...can\'t able to archived');
          DialogManager().hideTransparentProgressDialog();
        } else if (state.pageState == PropertyPageState.archivePropertyLoading) {
          DialogManager().showTransparentProgressDialog(context, message: "archiving the property");
        }
      },
      builder: (context, state) {
        final isSoldOut = propertyInfoBloc.isPropertyListingEnabled && state.itemPropertyInfoModel?.propertyByIdEntity?.status?.value == PropertyStatus.sold.value;
        return state.pageState == PropertyInfoPageState.success || state.pageState == PropertyInfoPageState.reAssignSuccessful || (state.pageState == PropertyInfoPageState.normal && state.itemPropertyInfoModel != null)
            ? Scaffold(
                appBar: propertyInfoAppBar(
                  context: context,
                  name: state.itemPropertyInfoModel?.propertyByIdEntity?.title ?? '',
                  id: state.itemPropertyInfoModel?.propertyByIdEntity?.id ?? '',
                  projectSerialNumber: state.itemPropertyInfoModel?.propertyByIdEntity?.serialNo ?? '',
                  isPropertyListingEnabled: propertyInfoBloc.isPropertyListingEnabled,
                ),
                body: RefreshIndicator(
                  onRefresh: () async => propertyInfoBloc.add(PropertyInfoInitialEvent(propertyId: propertyInfoBloc.propertyId)),
                  child: SafeArea(
                      child: SingleChildScrollView(
                    child: Stack(children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 24, right: 14, top: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Stack(children: [
                              PropertiesCarouselViewWidget(
                                images: state.itemPropertyInfoModel?.images ?? [],
                                carouselSliderController: carouselSliderController,
                                propertyImageIndex: state.selectedPropertyImageIndex,
                                function: (value, reason) => propertyInfoBloc.add(PropertyImageToggleEvent(index: value)),
                                isGrayScale: isSoldOut,
                                isSoldOut: isSoldOut,
                              ),
                              Positioned(
                                  top: context.height(18.5),
                                  left: 0,
                                  child: isSoldOut
                                      ? ColorFiltered(
                                          colorFilter: const ColorFilter.matrix(<double>[
                                            0.2126, 0.7152, 0.0722, 0, 0, // red
                                            0.2126, 0.7152, 0.0722, 0, 0, // green
                                            0.2126, 0.7152, 0.0722, 0, 0, // blue
                                            0, 0, 0, 1, 0, // alpha
                                          ]),
                                          child: Container(
                                            decoration: const BoxDecoration(borderRadius: BorderRadius.only(bottomLeft: Radius.circular(6))),
                                            padding: const EdgeInsets.fromLTRB(5, 5, 5, 5),
                                            child: RichText(
                                              text: TextSpan(
                                                text: 'Posted on ',
                                                style: LexendTextStyles.lexend9Regular.copyWith(
                                                  color: ColorPalette.primary,
                                                ),
                                                children: [
                                                  TextSpan(
                                                      text: state.itemPropertyInfoModel?.postedDate ?? "--",
                                                      style: LexendTextStyles.lexend10SemiBold.copyWith(
                                                        color: ColorPalette.white,
                                                      ))
                                                ],
                                              ),
                                            ),
                                          ),
                                        )
                                      : Container(
                                          decoration: const BoxDecoration(color: ColorPalette.leadInfoCardBg, borderRadius: BorderRadius.only(bottomLeft: Radius.circular(6))),
                                          padding: const EdgeInsets.fromLTRB(5, 5, 5, 5),
                                          child: RichText(
                                            text: TextSpan(
                                              text: 'Posted on ',
                                              style: LexendTextStyles.lexend9Regular.copyWith(
                                                color: ColorPalette.primary,
                                              ),
                                              children: [
                                                TextSpan(
                                                    text: state.itemPropertyInfoModel?.postedDate ?? "--",
                                                    style: LexendTextStyles.lexend10SemiBold.copyWith(
                                                      color: ColorPalette.white,
                                                    ))
                                              ],
                                            ),
                                          ),
                                        )),
                              state.isHighlighted! ? Positioned(top: 0, left: 0, child: SvgPicture.asset(ImageResources.highlightedProperty)) : const SizedBox.shrink()
                            ]),
                            isSoldOut
                                ? Padding(
                                    padding: const EdgeInsets.fromLTRB(0, 5, 0, 0),
                                    child: LeadratBlurEffectWidget(child: _buildPropertyInfoWidget(state)),
                                  )
                                : _buildPropertyInfoWidget(state)
                          ],
                        ),
                      ),
                    ]),
                  )),
                ),
              )
            : state.pageState == PropertyInfoPageState.error
                ? SafeArea(
                    child: Scaffold(
                        body: Center(
                      child: RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: "Oops, Something went wrong\n",
                              style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.secondaryTextColor),
                            ),
                            TextSpan(
                              text: "Click here to refresh\n",
                              style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.primaryTextColor),
                              recognizer: TapGestureRecognizer()..onTap = () => propertyInfoBloc.add(PropertyInfoInitialEvent(propertyId: propertyInfoBloc.propertyId)),
                            ),
                          ],
                        ),
                      ),
                    )),
                  )
                : const PropertyInfoSkeletonView();
      },
    );
  }

  Widget _buildPropertyInfoWidget(PropertyInfoState state) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      AboutPropertyWidget(itemPropertyInfoModel: state.itemPropertyInfoModel),
      PropertyFeaturesWidget(propertyFeatures: state.itemPropertyInfoModel?.features ?? []),
      if (getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.viewAssigned)) ...[
        AssignToWidget(state),
        if (state.itemPropertyInfoModel?.isPropertyListingEnabled ?? false) ListingOnBehalfWidget(state),
      ],
      MatchingLeadsItemWidget(
        title: 'Matching Leads',
        matchingLeadsCount: state.matchingLeadsCount,
        getPropertyEntity: state.itemPropertyInfoModel?.propertyByIdEntity,
        isMatchingLeads: true,
      ),
      MatchingLeadsItemWidget(title: 'Leads Associated', isMatchingLeads: false, matchingLeadsCount: state.associateLeadsCount, getPropertyEntity: state.itemPropertyInfoModel?.propertyByIdEntity),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(
            height: 4,
          ),
          const UnderlineTextWidget(
            title: 'Description',
          ),
          Row(
            children: [
              const Icon(Icons.description_outlined, color: ColorPalette.white, size: 16),
              ReadMoreTextWidget(
                notes: state.itemPropertyInfoModel?.aboutProperty ?? '--',
              ),
            ],
          ),
        ],
      ),
      if (getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.viewOwnerInfo)) PropertyOwnerDetailsWidget(state: state),
      if (propertyInfoBloc.isPropertyListingEnabled) ..._buildRegulatoryInformationWidget(state),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const UnderlineTextWidget(
            title: 'Notes',
          ),
          Row(
            children: [
              SvgPicture.asset(ImageResources.iconNotes, height: 18, colorFilter: const ColorFilter.mode(ColorPalette.white, BlendMode.srcIn)),
              ReadMoreTextWidget(
                canBuildLinkTextSpan: true,
                notes: state.itemPropertyInfoModel?.notes ?? '--',
              ),
            ],
          ),
        ],
      ),
      if (state.itemPropertyInfoModel != null && state.itemPropertyInfoModel!.areasList.isNotEmpty)
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const UnderlineTextWidget(
              title: 'Area',
            ),
            Wrap(
              crossAxisAlignment: WrapCrossAlignment.center,
              runAlignment: WrapAlignment.center,
              children: List.generate(state.itemPropertyInfoModel?.areasList.length ?? 0, (index) {
                var area = state.itemPropertyInfoModel?.areasList[index];
                return Container(
                    width: (MediaQuery.of(context).size.width / 2) - 20, // Width for 3 items
                    padding: const EdgeInsets.symmetric(vertical: 8.5),
                    child: _getHeadTailWidget(area?.first, area?.last));
              }),
            ),
          ],
        ),
      if (state.itemPropertyInfoModel != null && state.itemPropertyInfoModel!.additionalInfoList.isNotEmpty)
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const UnderlineTextWidget(
              title: 'Additional info',
            ),
            Wrap(
              crossAxisAlignment: WrapCrossAlignment.center,
              runAlignment: WrapAlignment.center,
              children: List.generate(state.itemPropertyInfoModel?.additionalInfoList.length ?? 0, (index) {
                var additionalInfo = state.itemPropertyInfoModel?.additionalInfoList[index];
                return Container(
                    width: (MediaQuery.of(context).size.width / 2) - 20, // Width for 3 items
                    padding: const EdgeInsets.symmetric(vertical: 8.5),
                    child: _getHeadTailWidget(additionalInfo?.first, additionalInfo?.last));
              }),
            ),
          ],
        ),
      const SizedBox(
        height: 20,
      ),
      Container(
        height: 58,
        width: 245,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: ColorPalette.gray300.withOpacity(0.3),
          borderRadius: const BorderRadius.all(Radius.circular(25)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            GestureDetector(
              onTap: () => propertyInfoBloc.add(AmenitiesToggleEvent(isAmenitiesToggled: true)),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 19, vertical: 9.5),
                decoration: BoxDecoration(color: ColorPalette.white.withOpacity(state.isAmenitiesSelected ? 1 : 0), borderRadius: const BorderRadius.all(Radius.circular(17))),
                child: Text(
                  'Amenities',
                  style: LexendTextStyles.lexend13SemiBold.copyWith(
                    color: state.isAmenitiesSelected ? ColorPalette.black : ColorPalette.white,
                  ),
                ),
              ),
            ),
            GestureDetector(
                onTap: () => propertyInfoBloc.add(AmenitiesToggleEvent(isAmenitiesToggled: false)),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 19, vertical: 9.5),
                  decoration: BoxDecoration(color: ColorPalette.white.withOpacity(state.isAmenitiesSelected ? 0 : 1), borderRadius: const BorderRadius.all(Radius.circular(17))),
                  child: Text(
                    'Attributes',
                    style: LexendTextStyles.lexend13SemiBold.copyWith(
                      color: state.isAmenitiesSelected ? ColorPalette.white : ColorPalette.black,
                    ),
                  ),
                ))
          ],
        ),
      ),
      if (state.isAmenitiesSelected)
        Column(
            children: (state.itemPropertyInfoModel?.isAmenityFound ?? false)
                ? List.generate(
                    state.itemPropertyInfoModel?.propertyAmenities.length ?? 0,
                    (index) => state.itemPropertyInfoModel!.propertyAmenities.values.elementAt(index).isNotEmpty
                        ? PropertyAmenityWidget(
                            amenity: state.itemPropertyInfoModel?.propertyAmenities.keys.elementAt(index),
                            amenityDetails: state.itemPropertyInfoModel?.propertyAmenities.values.elementAt(index),
                          )
                        : const SizedBox(),
                  )
                : [emptyWidget('No Amenities Found')])
      else
        _propertyAttributeWidget(state),
      CustomPaint(
        size: const Size(double.infinity, 4),
        painter: SharpDividerPainter(width: 1),
      ),
      const PropertyInfoTailWidget(),
    ]);
  }

  Widget emptyWidget(String text) {
    return SizedBox(
      height: context.height(40),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(ImageResources.iconNotFound),
            Text(
              text,
              style: LexendTextStyles.lexend15Bold.copyWith(color: ColorPalette.white),
            ),
          ],
        ),
      ),
    );
  }

  Widget _getHeadTailWidget(String? text1, String? text2) {
    return RichText(
        textAlign: TextAlign.start,
        text: TextSpan(
            text: '$text1\n',
            style: LexendTextStyles.lexend11Bold.copyWith(
              color: ColorPalette.primaryWhite300TextColor,
            ),
            children: [
              TextSpan(
                text: '$text2',
                style: LexendTextStyles.lexend12Medium.copyWith(
                  color: ColorPalette.white,
                ),
              )
            ]));
  }

  Widget _propertyAttributeWidget(PropertyInfoState state) {
    final customAttributes = (state.itemPropertyInfoModel?.propertyByIdEntity?.attributes ?? []).where((element) => (element?.isActive ?? false) && element?.attributeType == "Additional").toList();
    if (customAttributes.isNotEmpty) customAttributes.sort((a, b) => (a?.attributeDisplayName?.toLowerCase() ?? '').compareTo(b?.attributeDisplayName?.toLowerCase() ?? ''));

    return customAttributes.isNotEmpty
        ? Padding(
            padding: const EdgeInsets.symmetric(vertical: 13),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Wrap(
                  runAlignment: WrapAlignment.spaceBetween,
                  runSpacing: 8,
                  spacing: 33,
                  children: List.generate(customAttributes.length, (index) {
                    var attribute = customAttributes[index];
                    return Container(
                      width: (MediaQuery.of(context).size.width / 3) - 36, // Width for 3 items
                      padding: const EdgeInsets.symmetric(vertical: 13), alignment: Alignment.center,
                      child: Column(
                        children: [
                          SvgPicture.network(
                            (attribute?.activeImageURL ?? ''),
                            colorFilter: const ColorFilter.mode(
                              ColorPalette.primaryGreen,
                              BlendMode.srcIn,
                            ),
                            height: 30,
                          ),
                          Container(
                            margin: const EdgeInsets.only(top: 9),
                            height: 20,
                            width: 80,
                            child: Text(
                              attribute?.attributeDisplayName ?? "--",
                              style: LexendTextStyles.lexend10SemiBold.copyWith(
                                color: ColorPalette.primary,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                            ),
                          )
                        ],
                      ),
                    );
                  }),
                )
              ],
            ))
        : emptyWidget('No Attributes Found');
  }

  _buildRegulatoryInformationWidget(PropertyInfoState state) {
    return [
      const UnderlineTextWidget(
        title: 'Regulatory information',
      ),
      const SizedBox(height: 10),
      Text.rich(TextSpan(children: [
        TextSpan(text: "reference", style: LexendTextStyles.lexend10Light.copyWith(color: ColorPalette.primary)),
        TextSpan(text: " - ", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
        TextSpan(text: state.itemPropertyInfoModel?.propertyByIdEntity?.serialNo ?? '--', style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
      ])),
      Text.rich(TextSpan(children: [
        TextSpan(text: "listed", style: LexendTextStyles.lexend10Light.copyWith(color: ColorPalette.primary)),
        TextSpan(text: " - ", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
        if (state.itemPropertyInfoModel?.propertyByIdEntity?.listingExpireDate != null) TextSpan(text: DateFormat('dd-MM-yy').format(state.itemPropertyInfoModel!.propertyByIdEntity!.listingExpireDate!.toUserTimeZone()!), style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)) else TextSpan(text: " -- ", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
      ])),
      if (state.itemPropertyInfoModel?.propertyByIdEntity?.dldPermitNumber?.isNotNullOrEmpty() ?? false)
        Text.rich(TextSpan(children: [
          TextSpan(text: "dld permit number", style: LexendTextStyles.lexend10Light.copyWith(color: ColorPalette.primary)),
          TextSpan(text: " - ", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
          TextSpan(text: state.itemPropertyInfoModel?.propertyByIdEntity?.dldPermitNumber ?? '--', style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
        ]))
      else if ((state.itemPropertyInfoModel?.propertyByIdEntity?.dtcmPermit?.isNotNullOrEmpty() ?? false))
        Text.rich(TextSpan(children: [
          TextSpan(text: "dtcm permit number", style: LexendTextStyles.lexend10Light.copyWith(color: ColorPalette.primary)),
          TextSpan(text: " - ", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
          TextSpan(text: state.itemPropertyInfoModel?.propertyByIdEntity?.dtcmPermit ?? '--', style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
        ])),
      Text.rich(TextSpan(children: [
        TextSpan(text: "offering type", style: LexendTextStyles.lexend10Light.copyWith(color: ColorPalette.primary)),
        TextSpan(text: " - ", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
        TextSpan(text: state.itemPropertyInfoModel?.propertyByIdEntity?.offeringType?.description ?? '--', style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
      ])),
      Text.rich(TextSpan(children: [
        TextSpan(text: "completion status", style: LexendTextStyles.lexend10Light.copyWith(color: ColorPalette.primary)),
        TextSpan(text: " - ", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
        TextSpan(text: state.itemPropertyInfoModel?.propertyByIdEntity?.completionStatus?.description ?? '--', style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
      ])),
      Text.rich(TextSpan(children: [
        TextSpan(text: "service changes", style: LexendTextStyles.lexend10Light.copyWith(color: ColorPalette.primary)),
        TextSpan(text: " - ", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
        TextSpan(text: state.itemPropertyInfoModel?.propertyByIdEntity?.monetaryInfo?.serviceChange?.toString() ?? '--', style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
      ])),
      Text.rich(TextSpan(children: [
        TextSpan(text: "broker name", style: LexendTextStyles.lexend10Light.copyWith(color: ColorPalette.primary)),
        TextSpan(text: " - ", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
        TextSpan(text: state.itemPropertyInfoModel?.propertyByIdEntity?.assignedTo?.firstOrNull?.fullName ?? '--', style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
      ])),
      Text.rich(TextSpan(children: [
        TextSpan(text: "agent license", style: LexendTextStyles.lexend10Light.copyWith(color: ColorPalette.primary)),
        TextSpan(text: " - ", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
        TextSpan(text: state.itemPropertyInfoModel?.propertyByIdEntity?.assignedTo?.firstOrNull?.licenseNo ?? '--', style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
      ])),
    ];
  }
}
