import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_stateless_widget.dart';
import 'package:leadrat/core_main/common/widgets/gray_scale_image_widget.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/features/properties/presentation/bloc/property_info_bloc/property_info_bloc.dart';
import 'package:lottie/lottie.dart';

import '../../../../core_main/resources/common/image_resources.dart';
import '../../../../core_main/resources/theme/color_palette.dart';

class PropertiesCarouselViewWidget extends LeadratStatelessWidget {
  PropertiesCarouselViewWidget({required this.images, required this.carouselSliderController, super.key, required this.propertyImageIndex, required this.function, this.isGrayScale = false, this.isSoldOut = false});

  final List<String?> images;
  final CarouselSliderController carouselSliderController;
  final int propertyImageIndex;
  final bool isGrayScale;
  final bool isSoldOut;
  final Function(int value, CarouselPageChangedReason reason) function;

  final propertyInfoBloc = getIt<PropertyInfoBloc>();

  @override
  Widget buildContent(BuildContext context) {
    return (images.isEmpty)
        ? SizedBox(
            height: 200,
            width: double.infinity,
            child: Stack(children: [
              isSoldOut ?  const GrayScaleImageWidget(lottieAsset: LottieResources.buildAnimation,) : Lottie.asset(LottieResources.buildAnimation, fit: BoxFit.fill, height: 200),
              if (isSoldOut)
                Center(
                  child: Image.asset(
                    ImageResources.imageSoldOut,
                    fit: BoxFit.cover,
                  ),
                ),
            ]),
          )
        : Column(
            children: [
              Stack(children: [
                CarouselSlider(
                  items: List.generate(
                    images.length ?? 0,
                    (index) => Container(
                      height: 200,
                      width: double.infinity,
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(6)),
                      ),
                      child: ClipRRect(
                        borderRadius: const BorderRadius.all(Radius.circular(6)),
                        child: isGrayScale
                            ? GrayScaleImageWidget(imageUrl: images[index]?.appendWithImageBaseUrl() ?? "")
                            : CachedNetworkImage(
                                imageUrl: images[index]?.appendWithImageBaseUrl() ?? '',
                                fit: BoxFit.cover,
                              ),
                      ),
                    ),
                  ),
                  carouselController: carouselSliderController,
                  options: CarouselOptions(
                    onPageChanged: function,
                    scrollDirection: Axis.horizontal,
                    autoPlay: images.length > 1 ? true : false,
                    enlargeCenterPage: true,
                    pageSnapping: true,
                    viewportFraction: 1,
                    aspectRatio: 2.0,
                    initialPage: 0, // Change to 0 or any valid initial page
                  ),
                ),
                if (isSoldOut)
                  Center(
                    child: Image.asset(
                      ImageResources.imageSoldOut,
                      fit: BoxFit.cover,
                    ),
                  ),
              ]),
              if (images.length > 1)
                Container(
                  margin: const EdgeInsets.only(top: 10),
                  height: 35,
                  child: Stack(children: [
                    ListView.builder(
                      itemCount: images.length ?? 0,
                      scrollDirection: Axis.horizontal,
                      itemBuilder: (context, index) {
                        return GestureDetector(
                          onTap: () => carouselSliderController.jumpToPage(index),
                          child: Container(
                            height: 35,
                            width: 55,
                            margin: const EdgeInsets.only(right: 8),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: propertyImageIndex == index ? ColorPalette.primaryGreen : ColorPalette.transparent,
                                width: propertyImageIndex == index ? 1.4 : 0,
                              ),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(4),
                              child: isGrayScale
                                  ? GrayScaleImageWidget(imageUrl: images[index]?.appendWithImageBaseUrl() ?? "")
                                  : CachedNetworkImage(
                                      imageUrl: images[index]?.appendWithImageBaseUrl() ?? "",
                                      fit: BoxFit.cover,
                                    ),
                            ),
                          ),
                        );
                      },
                    ),
                    Positioned(
                      top: -8,
                      left: -4,
                      child: GestureDetector(onTap: () => carouselSliderController.previousPage(), child: shadowScrollButton(const Icon(Icons.chevron_left, color: Colors.white))),
                    ),
                    Positioned(
                      top: -8,
                      right: -4,
                      child: GestureDetector(
                          onTap: () => carouselSliderController.nextPage(),
                          child: shadowScrollButton(const Icon(
                            Icons.chevron_right,
                            color: Colors.white,
                          ))),
                    ),
                  ]),
                ),
            ],
          );
  }

  shadowScrollButton(Widget icon) {
    return Container(
      height: 50,
      width: 25,
      decoration: BoxDecoration(boxShadow: [
        BoxShadow(
          color: ColorPalette.black.withOpacity(0.8),
          blurRadius: 2.0,
        ),
      ]),
      child: icon,
    );
  }
}
