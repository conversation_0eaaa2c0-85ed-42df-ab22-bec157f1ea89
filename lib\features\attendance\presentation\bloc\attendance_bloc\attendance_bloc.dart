import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:leadrat/core_main/common/base/usecase/use_case.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/utilities/dialog_manager.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/attendance/data/models/attendance_model.dart';
import 'package:leadrat/features/attendance/data/models/attendance_post_model.dart';
import 'package:leadrat/features/attendance/domain/entities/attendance_settings_entity.dart';
import 'package:leadrat/features/attendance/domain/usecase/get_attendance_logs_by_user_usecase.dart';
import 'package:leadrat/features/attendance/domain/usecase/get_attendance_settings_usecase.dart';
import 'package:leadrat/features/attendance/domain/usecase/post_clockin_attendance_usecase.dart';
import 'package:leadrat/features/attendance/domain/usecase/post_clockout_attendance_usecase.dart';
import 'package:leadrat/features/attendance/presentation/items/clock_in_out_item.dart';
import 'package:leadrat/main.dart';

import '../../../../../core_main/enums/app_enum/blob_folder_names.dart';
import '../../../../../core_main/services/native_implementation_service/native_implementation_service.dart';
import '../../../../user_profile/domain/usecase/upload_document_usecase.dart';

part 'attendance_event.dart';
part 'attendance_state.dart';

class AttendanceBloc extends Bloc<AttendanceEvent, AttendanceState> {
  final GetAttendanceLogsByUserUseCase _getAttendanceLogsByUserUseCase;
  final GetAttendanceLogsByUserTimeZoneUseCase _getAttendanceLogsByUserTimeZoneUseCase;
  final GetAttendanceSettingsUseCase _getAttendanceSettingsUseCase;
  final PostClockInAttendanceUseCase _postClockInAttendanceUseCase;
  final PostClockOutAttendanceUseCase _postClockOutAttendanceUseCase;
  final UploadDocumentUseCase _uploadDocumentUseCase;
  final NativeImplementationService _nativeImplementationService;

  static AttendanceSettingsEntity? attendanceSettingsEntity;
  static List<AttendanceModel>? todayAttendanceLogs;

  AttendanceBloc(
    this._getAttendanceLogsByUserUseCase,
    this._getAttendanceSettingsUseCase,
    this._postClockInAttendanceUseCase,
    this._postClockOutAttendanceUseCase,
    this._uploadDocumentUseCase,
    this._nativeImplementationService,
    this._getAttendanceLogsByUserTimeZoneUseCase,
  ) : super(const AttendanceState()) {
    on<ToggleClockInEvent>(_onToggleClockin);
    on<GetAttendanceSettingsEvent>(_getAttendanceSettings);
    on<GetTodayAttendanceLogEvent>(_getTodaysAttendanceLog);
  }

  FutureOr<void> _getAttendanceSettings(GetAttendanceSettingsEvent event, Emitter<AttendanceState> emit) async {
    var result = await _getAttendanceSettingsUseCase.call(NoParams());
    result.fold(
        (failure) => {
              // emit(state.copyWith(clockedInState: AttendanceLogInState.errorInLoadingAttendance)),
            },
        (res) => {
              attendanceSettingsEntity = res,
            });
  }

  FutureOr<void> _getTodaysAttendanceLog(GetTodayAttendanceLogEvent event, Emitter<AttendanceState> emit) async {
    emit(state.copyWith(clockedInState: AttendanceLogInState.loading));
    await storeTheAttendanceLogsLocally();
    final clockInOutItem = ClockInOutItem(
      clockInTimeStamp: todayAttendanceLogs?.firstOrNull?.clockInTime != null && todayAttendanceLogs?.firstOrNull?.clockOutTime == null ? DateFormat('hh:mm a').format(todayAttendanceLogs?.firstOrNull?.clockInTime ?? DateTime(DateTime.now().year)) : "--",
      clockOutTimeStamp: "--",
      isClockedIn: todayAttendanceLogs!.isNotEmpty ? todayAttendanceLogs?.firstOrNull?.clockOutTime == null : false,
    );

    List<ClockInOutItem> attendanceLogs = [];
    todayAttendanceLogs?.forEach((attendanceLog) {
      attendanceLogs.add(ClockInOutItem(
        clockInTimeStamp: attendanceLog.clockInTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockInTime ?? DateTime(DateTime.now().year)) : "--",
        clockOutTimeStamp: attendanceLog.clockOutTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockOutTime ?? DateTime(DateTime.now().year)) : "--",
        clockInLocation: attendanceLog.clockInLocation != null ? attendanceLog.clockInLocation ?? "--" : "--",
        clockOutLocation: attendanceLog.clockOutLocation != null ? attendanceLog.clockOutLocation ?? "--" : "--",
        logInDuration: attendanceLog.clockOutTime != null ? "${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inHours}h : ${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inMinutes.remainder(60)}m" : "--",
        isClockedIn: attendanceLog.clockOutTime != null ? false : true,
      ));
    });
    emit(state.copyWith(clockedInState: AttendanceLogInState.success, clockInOutItem: clockInOutItem, attendanceList: attendanceLogs));
  }

  FutureOr<void> _onToggleClockin(ToggleClockInEvent event, Emitter<AttendanceState> emit) async {
    try {
      if (attendanceSettingsEntity == null) {
        var attendanceSettingsResult = await _getAttendanceSettingsUseCase.call(NoParams());
        attendanceSettingsResult.fold(
            (failure) => {
                  emit(state.copyWith(clockedInState: AttendanceLogInState.errorInLoadingAttendance)),
                },
            (res) => {
                  attendanceSettingsEntity = res,
                });
      }

      if (event.isClockedIn ?? false) {
        emit(state.copyWith(clockedInState: AttendanceLogInState.clockingOut));
        var location = await _nativeImplementationService.getCurrentLocation();
        if (location != null) {
          var currentPlacemark = await _nativeImplementationService.getCurrentLocationPlaceMarks(location.latitude, location.longitude);
          if (attendanceSettingsEntity != null && (attendanceSettingsEntity?.isSelfieMandatoryForClockOut ?? false)) {
            var capturedImage = await _nativeImplementationService.openCamera();
            if (capturedImage != null) {
              var params = AttendancePostModel(
                latitude: location.latitude,
                longitude: location.longitude,
                locationName: "${currentPlacemark?.subLocality ?? ""}, ${currentPlacemark?.locality ?? ""}, ${currentPlacemark?.administrativeArea ?? ""}",
                clockInImageUrl: null,
                clockOutImageUrl: await uploadImageToS3Bucket(capturedImage),
              );
              var result = await _postClockOutAttendanceUseCase.call(params);
              result.fold(
                  (failure) => {
                        emit(state.copyWith(clockedInState: AttendanceLogInState.errorInClockingOut)),
                      },
                  (res) => {});
              if (result.isRight()) {
                final ClockInOutItem clockInOutItem = ClockInOutItem(
                  clockOutTimeStamp: "--",
                  clockInTimeStamp: "--",
                  isClockedIn: false,
                );

                List<ClockInOutItem>? attendanceLogs = [];
                await storeTheAttendanceLogsLocally();
                todayAttendanceLogs?.forEach((attendanceLog) {
                  attendanceLogs.add(ClockInOutItem(
                    clockInTimeStamp: attendanceLog.clockInTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockInTime ?? DateTime(DateTime.now().year)) : "--",
                    clockOutTimeStamp: attendanceLog.clockOutTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockOutTime ?? DateTime(DateTime.now().year)) : "--",
                    clockInLocation: attendanceLog.clockInLocation != null ? attendanceLog.clockInLocation ?? "--" : "--",
                    clockOutLocation: attendanceLog.clockOutLocation != null ? attendanceLog.clockOutLocation ?? "--" : "--",
                    logInDuration: attendanceLog.clockOutTime != null ? "${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inHours}h : ${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inMinutes.remainder(60)}m" : "--",
                    isClockedIn: attendanceLog.clockOutTime != null ? false : true,
                  ));
                });
                emit(state.copyWith(clockedInState: AttendanceLogInState.successfullyClockedOut, clockInOutItem: clockInOutItem, attendanceList: attendanceLogs));
              }
            } else {
              emit(state.copyWith(clockedInState: AttendanceLogInState.selfieIsMandatory));
            }
          } else {
            var params = AttendancePostModel(
              latitude: location.latitude,
              longitude: location.longitude,
              locationName: "${currentPlacemark?.subLocality ?? ""}, ${currentPlacemark?.locality ?? ""}, ${currentPlacemark?.administrativeArea ?? ""}",
              clockInImageUrl: null,
              clockOutImageUrl: null,
            );
            var result = await _postClockOutAttendanceUseCase.call(params);
            result.fold(
                (failure) => {
                      emit(state.copyWith(clockedInState: AttendanceLogInState.errorInClockingOut)),
                    },
                (res) => {});
            if (result.isRight()) {
              final ClockInOutItem clockInOutItem = ClockInOutItem(
                clockOutTimeStamp: "--",
                clockInTimeStamp: "--",
                isClockedIn: false,
              );

              List<ClockInOutItem>? attendanceLogs = [];
              await storeTheAttendanceLogsLocally();
              todayAttendanceLogs?.forEach((attendanceLog) {
                attendanceLogs.add(ClockInOutItem(
                  clockInTimeStamp: attendanceLog.clockInTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockInTime ?? DateTime(DateTime.now().year)) : "--",
                  clockOutTimeStamp: attendanceLog.clockOutTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockOutTime ?? DateTime(DateTime.now().year)) : "--",
                  clockInLocation: attendanceLog.clockInLocation != null ? attendanceLog.clockInLocation ?? "--" : "--",
                  clockOutLocation: attendanceLog.clockOutLocation != null ? attendanceLog.clockOutLocation ?? "--" : "--",
                  logInDuration: attendanceLog.clockOutTime != null ? "${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inHours}h : ${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inMinutes.remainder(60)}m" : "--",
                  isClockedIn: attendanceLog.clockOutTime != null ? false : true,
                ));
              });
              emit(state.copyWith(clockedInState: AttendanceLogInState.successfullyClockedOut, clockInOutItem: clockInOutItem, attendanceList: attendanceLogs));
            }
          }
        } else {
          emit(state.copyWith(clockedInState: AttendanceLogInState.locationMandatory));
        }
      } else {
        emit(state.copyWith(clockedInState: AttendanceLogInState.clockingIn));
        var location = await _nativeImplementationService.getCurrentLocation();
        if (location != null) {
          var currentPlacemark = await _nativeImplementationService.getCurrentLocationPlaceMarks(location.latitude, location.longitude);
          if (attendanceSettingsEntity != null && (attendanceSettingsEntity?.isSelfieMandatoryForClockIn ?? false)) {
            var capturedImage = await _nativeImplementationService.openCamera();
            if (capturedImage != null) {
              var params = AttendancePostModel(
                latitude: location.latitude,
                longitude: location.longitude,
                locationName: "${currentPlacemark?.subLocality ?? ""}, ${currentPlacemark?.locality ?? ""}, ${currentPlacemark?.administrativeArea ?? ""}",
                clockInImageUrl: await uploadImageToS3Bucket(capturedImage),
                clockOutImageUrl: null,
              );
              var result = await _postClockInAttendanceUseCase.call(params);
              result.fold(
                  (failure) => {
                        emit(state.copyWith(clockedInState: AttendanceLogInState.errorInClockingIn)),
                      },
                  (res) => {});
              if (result.isRight()) {
                final ClockInOutItem clockInOutItem = ClockInOutItem(
                  clockInTimeStamp: DateFormat('hh:mm a').format(DateTime.now().toUserTimeZone()!),
                  clockOutTimeStamp: "--",
                  isClockedIn: true,
                );

                List<ClockInOutItem>? attendanceLogs = [];

                todayAttendanceLogs?.insert(
                    0,
                    AttendanceModel(
                      clockInTime: DateTime.now().toUserTimeZone()!,
                      clockOutTime: null,
                      clockInLocation: params.locationName ?? "",
                      clockOutLocation: null,
                    ));

                todayAttendanceLogs?.forEach((attendanceLog) {
                  attendanceLogs.add(ClockInOutItem(
                    clockInTimeStamp: attendanceLog.clockInTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockInTime ?? DateTime(DateTime.now().year)) : "--",
                    clockOutTimeStamp: attendanceLog.clockOutTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockOutTime ?? DateTime(DateTime.now().year)) : "--",
                    clockInLocation: attendanceLog.clockInLocation != null ? attendanceLog.clockInLocation ?? "--" : "--",
                    clockOutLocation: attendanceLog.clockOutLocation != null ? attendanceLog.clockOutLocation ?? "--" : "--",
                    logInDuration: attendanceLog.clockOutTime != null ? "${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inHours}h : ${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inMinutes.remainder(60)}m" : "--",
                    isClockedIn: attendanceLog.clockOutTime != null ? false : true,
                  ));
                });
                emit(state.copyWith(clockedInState: AttendanceLogInState.successfullyClockedIn, clockInOutItem: clockInOutItem, attendanceList: attendanceLogs));
              }
            } else {
              emit(state.copyWith(clockedInState: AttendanceLogInState.selfieIsMandatory));
            }
          } else {
            var params = AttendancePostModel(
              latitude: location.latitude,
              longitude: location.longitude,
              locationName: "${currentPlacemark?.subLocality ?? ""}, ${currentPlacemark?.locality ?? ""}, ${currentPlacemark?.administrativeArea ?? ""}",
              clockInImageUrl: null,
              clockOutImageUrl: null,
            );
            var result = await _postClockInAttendanceUseCase.call(params);
            result.fold(
                (failure) => {
                      emit(state.copyWith(clockedInState: AttendanceLogInState.errorInClockingIn)),
                    },
                (res) => {});
            if (result.isRight()) {
              final ClockInOutItem clockInOutItem = ClockInOutItem(
                clockInTimeStamp: DateFormat('hh:mm a').format(DateTime.now().toUserTimeZone()!),
                clockOutTimeStamp: "--",
                isClockedIn: true,
              );

              List<ClockInOutItem>? attendanceLogs = [];

              todayAttendanceLogs?.insert(
                  0,
                  AttendanceModel(
                    clockInTime: DateTime.now().toUserTimeZone(),
                    clockOutTime: null,
                    clockInLocation: params.locationName ?? "",
                    clockOutLocation: null,
                  ));

              todayAttendanceLogs?.forEach((attendanceLog) {
                attendanceLogs.add(ClockInOutItem(
                  clockInTimeStamp: attendanceLog.clockInTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockInTime ?? DateTime(DateTime.now().year)) : "--",
                  clockOutTimeStamp: attendanceLog.clockOutTime != null ? DateFormat('hh:mm a').format(attendanceLog.clockOutTime ?? DateTime(DateTime.now().year)) : "--",
                  clockInLocation: attendanceLog.clockInLocation != null ? attendanceLog.clockInLocation ?? "--" : "--",
                  clockOutLocation: attendanceLog.clockOutLocation != null ? attendanceLog.clockOutLocation ?? "--" : "--",
                  logInDuration: attendanceLog.clockOutTime != null ? "${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inHours}h : ${attendanceLog.clockOutTime?.toLocal().difference(attendanceLog.clockInTime?.toLocal() ?? DateTime(1)).inMinutes.remainder(60)}m" : "--",
                  isClockedIn: attendanceLog.clockOutTime != null ? false : true,
                ));
              });
              emit(state.copyWith(clockedInState: AttendanceLogInState.successfullyClockedIn, clockInOutItem: clockInOutItem, attendanceList: attendanceLogs));
            }
          }
        } else {
          emit(state.copyWith(clockedInState: AttendanceLogInState.locationMandatory));
        }
      }
    } on TimeoutException catch (e) {
      LeadratCustomSnackbar.show(message: e.message ?? "Request timed out. Please try again.",type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
      DialogManager().hideTransparentProgressDialog();
    } catch (ex) {
      LeadratCustomSnackbar.show(message: "Some thing went wrong. Please try again.", type: SnackbarType.error,navigatorKey: MyApp.navigatorKey);
      DialogManager().hideTransparentProgressDialog();
    }
  }

  Future<String?> uploadImageToS3Bucket(XFile? capturedImage) async {
    if (capturedImage != null) {
      String? uploadedImage;
      final base64File = base64Encode(await capturedImage.readAsBytes());
      final uploadedImageToS3Bucket = await _uploadDocumentUseCase(UploadDocumentParams(BlobFolderNameEnum.attendanceImages.description, await _nativeImplementationService.getImageExtension(capturedImage) ?? 'jpg', base64File));
      uploadedImageToS3Bucket.fold(
          (failure) => {
                // show toast
              },
          (res) => {
                uploadedImage = res?.isNotEmpty ?? false ? res : null,
              });
      return uploadedImage;
    } else {
      return null;
    }
  }

  Future<void> storeTheAttendanceLogsLocally() async {
    final userDetails = getIt<UsersDataRepository>().getLoggedInUser();
    final userTimeZoneInfo = userDetails?.timeZoneInfo;
    if (userTimeZoneInfo?.baseUTcOffset != null && userTimeZoneInfo?.timeZoneDisplay != null && userTimeZoneInfo?.timeZoneId != null && userTimeZoneInfo?.timeZoneName != null) {
      final usecaseParams = GetAllAttendanceLogsUsecaseParams(userId: userDetails?.userId ?? "", timeZone: userTimeZoneInfo?.timeZoneId, baseUtcOffset: userTimeZoneInfo?.baseUTcOffset, startTime: DateTime.now().toUniversalTimeStartOfDay().toString());
      var latestAttendanceLog = await _getAttendanceLogsByUserTimeZoneUseCase.call(usecaseParams);
      latestAttendanceLog.fold(
          (failure) => {
                // Show toast for the error message
              }, (res) {
        todayAttendanceLogs = res;
      });
    } else {
      final timeZoneId = DateTime.now().timeZoneName;
      final usecaseParams = GetAllAttendanceLogsUsecaseParams(userId: userDetails?.userId ?? "", timeZone: timeZoneId);
      var latestAttendanceLog = await _getAttendanceLogsByUserUseCase.call(usecaseParams);
      latestAttendanceLog.fold(
          (failure) => {
                // Show toast for the error message
              }, (res) {
        todayAttendanceLogs = res;
      });
    }
  }
}
