import 'package:collection/collection.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_amenity_model.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_attributes_model.dart';
import 'package:leadrat/core_main/common/entites/address_entity.dart';
import 'package:leadrat/core_main/common/entites/property_type_entity.dart';
import 'package:leadrat/core_main/common/models/address_model.dart';
import 'package:leadrat/core_main/common/models/base_user_model.dart';
import 'package:leadrat/core_main/common/models/booked_details_model.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/common/brokerage_unit.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/taxation_mode.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/property_enums/completion_status.dart';
import 'package:leadrat/core_main/enums/property_enums/facing.dart';
import 'package:leadrat/core_main/enums/property_enums/furnish_status.dart';
import 'package:leadrat/core_main/enums/property_enums/listing_status.dart';
import 'package:leadrat/core_main/enums/property_enums/lockin_period_type.dart';
import 'package:leadrat/core_main/enums/property_enums/notice_period_type.dart';
import 'package:leadrat/core_main/enums/property_enums/property_status.dart';
import 'package:leadrat/core_main/enums/property_enums/sale_type.dart';
import 'package:leadrat/core_main/enums/property_enums/security_deposite_type.dart';
import 'package:leadrat/features/properties/data/models/add_property_model.dart';
import 'package:leadrat/features/properties/data/models/get_property_model.dart';
import 'package:leadrat/features/properties/data/models/property_attribute_model.dart';
import 'package:leadrat/features/properties/data/models/property_brochure_model.dart';
import 'package:leadrat/features/properties/data/models/property_dimension_model.dart';
import 'package:leadrat/features/properties/data/models/property_image_model.dart';
import 'package:leadrat/features/properties/data/models/property_taginfo_model.dart';
import 'package:leadrat/features/properties/data/models/view_listing_source_address_model.dart';
import 'package:leadrat/features/properties/domain/entities/property_attribute_entity.dart';
import 'package:leadrat/features/properties/domain/entities/property_brochure_entity.dart';
import 'package:leadrat/features/properties/domain/entities/property_dimension_entity.dart';
import 'package:leadrat/features/properties/domain/entities/property_images_type_entity.dart';
import 'package:leadrat/features/properties/domain/entities/property_monetaryinfo_entity.dart';
import 'package:leadrat/features/properties/domain/entities/property_owner_details_entity.dart';
import 'package:leadrat/features/properties/domain/entities/property_taginfo_entity.dart';

class GetPropertyEntity {
  final String? id;
  final String? title;
  final SaleType? saleType;
  final EnquiryType? enquiredFor;
  final String? notes;
  final FurnishStatus? furnishStatus;
  final PropertyStatus? status;
  final String? rating;
  final int? shareCount;
  final DateTime? possessionDate;
  final bool? isGOListingEnabled;
  final Facing? facing;
  final String? goPropertyId;
  final double? noOfBHK;
  final BHKType? bhkType;
  final PropertyMonetaryInfoEntity? monetaryInfo;
  final PropertyOwnerDetailsEntity? ownerDetails;
  final PropertyDimensionEntity? dimension;
  final PropertyTagInfoEntity? tagInfo;
  final List<PropertyImageEntity>? images;
  final Map<String, List<String>>? imageUrls;
  final List<CustomAttributesModel?>? attributes;
  final List<CustomAmenityModel?>? amenities;
  final List<PropertyBrochureEntity>? brochures;
  final int? whatsAppShareCount;
  final int? callShareCount;
  final int? emailShareCount;
  final int? smsShareCount;
  final String? aboutProperty;
  final AddressEntity? address;
  final List<String>? links;
  final String? project;
  List<BaseUserModel?>? assignedTo;
  List<BaseUserModel?>? listingOnBehalf;
  final DateTime? lastModifiedOn;
  final DateTime? createdOn;
  final String? createdBy;
  final String? lastModifiedBy;
  final PropertyTypeEntity? propertyType;
  final String? micrositeURL;
  final String? serialNo;
  final String? coWorkingOperator;
  final String? coWorkingOperatorName;
  final String? coWorkingOperatorPhone;

  final LockInPeriodType? lockInPeriod;
  final NoticePeriodType? noticePeriod;
  final List<int>? noOfFloorsOccupied;
  final TenantContactInfoEntity? tenantContactInfoEntity;
  final ListingStatus? listingStatus;
  final DateTime? listingExpireDate;
  final List<CustomListingSourceModel>? listingSources;
  final ListingLevel? listingLevel;
  final List<ViewListingSourceAddressModel>? listingSourceAddresses;
  final String? dldPermitNumber;
  final String? refrenceNo;
  final String? dtcmPermit;
  final OfferingType? offeringType;
  final CompletionStatus? completionStatus;
  final String? language;
  final String? titleWithLanguage;
  final String? aboutPropertyWithLanguage;
  final List<String>? view360Url;
  final TaxationMode? taxationMode;
  final bool? isWaterMarkEnabled;
  final PossessionType? possesionType;
  final double? securityDepositAmount;
  final String? securityDepositUnit;
  final bool? shouldVisisbleOnListing;
  final List<PropertyOwnerDetailsEntity>? propertyOwnerDetails;

  GetPropertyEntity(
      {this.id,
      this.title,
      this.saleType,
      this.enquiredFor,
      this.notes,
      this.furnishStatus,
      this.status,
      this.rating,
      this.shareCount,
      this.possessionDate,
      this.isGOListingEnabled,
      this.facing,
      this.goPropertyId,
      this.noOfBHK,
      this.bhkType,
      this.monetaryInfo,
      this.ownerDetails,
      this.dimension,
      this.tagInfo,
      this.images,
      this.imageUrls,
      this.attributes,
      this.amenities,
      this.brochures,
      this.whatsAppShareCount,
      this.callShareCount,
      this.emailShareCount,
      this.smsShareCount,
      this.aboutProperty,
      this.address,
      this.links,
      this.project,
      this.assignedTo,
      this.lastModifiedOn,
      this.createdOn,
      this.createdBy,
      this.lastModifiedBy,
      this.propertyType,
      this.micrositeURL,
      this.serialNo,
      this.coWorkingOperator,
      this.coWorkingOperatorName,
      this.coWorkingOperatorPhone,
      this.lockInPeriod,
      this.noticePeriod,
      this.noOfFloorsOccupied,
      this.tenantContactInfoEntity,
      this.listingStatus,
      this.listingExpireDate,
      this.listingSources,
      this.listingLevel,
      this.listingSourceAddresses,
      this.dldPermitNumber,
      this.refrenceNo,
      this.dtcmPermit,
      this.offeringType,
      this.completionStatus,
      this.language,
      this.titleWithLanguage,
      this.aboutPropertyWithLanguage,
      this.view360Url,
      this.taxationMode,
      this.isWaterMarkEnabled,
      this.possesionType,
      this.securityDepositAmount,
      this.securityDepositUnit,
        this.shouldVisisbleOnListing,
        this.propertyOwnerDetails,
        this.listingOnBehalf,
      });

  GetPropertyEntity copyWith({
    String? id,
    String? title,
    SaleType? saleType,
    EnquiryType? enquiredFor,
    String? notes,
    FurnishStatus? furnishStatus,
    PropertyStatus? status,
    String? rating,
    int? shareCount,
    DateTime? possessionDate,
    bool? isGOListingEnabled,
    Facing? facing,
    String? goPropertyId,
    double? noOfBHK,
    BHKType? bhkType,
    PropertyMonetaryInfoEntity? monetaryInfo,
    PropertyOwnerDetailsEntity? ownerDetails,
    PropertyDimensionEntity? dimension,
    PropertyTagInfoEntity? tagInfo,
    List<PropertyImageEntity>? images,
    Map<String, List<String>>? imageUrls,
    List<CustomAttributesModel>? attributes,
    List<CustomAmenityModel?>? amenities,
    List<PropertyBrochureEntity>? brochures,
    int? whatsAppShareCount,
    int? callShareCount,
    int? emailShareCount,
    int? smsShareCount,
    String? aboutProperty,
    AddressEntity? address,
    List<String>? links,
    String? project,
    List<BaseUserModel?>? assignedTo,
    List<BaseUserModel?>? listingOnBehalf,
    DateTime? lastModifiedOn,
    DateTime? createdOn,
    String? createdBy,
    String? lastModifiedBy,
    PropertyTypeEntity? propertyType,
    String? micrositeURL,
    String? serialNo,
    String? coWorkingOperator,
    String? coWorkingOperatorName,
    String? coWorkingOperatorPhone,
    LockInPeriodType? lockInPeriod,
    NoticePeriodType? noticePeriod,
    List<int>? noOfFloorsOccupied,
    TenantContactInfoEntity? tenantContactInfo,
    TenantContactInfoEntity? tenantContactInfoEntity,
    ListingStatus? listingStatus,
    DateTime? listingExpireDate,
    List<CustomListingSourceModel>? listingSources,
    ListingLevel? listingLevel,
    List<ViewListingSourceAddressModel>? listingSourceAddresses,
    String? dldPermitNumber,
    String? refrenceNo,
    String? dtcmPermit,
    OfferingType? offeringType,
    CompletionStatus? completionStatus,
    String? language,
    String? titleWithLanguage,
    String? aboutPropertyWithLanguage,
    List<String>? view360Url,
    TaxationMode? taxationMode,
    bool? isWaterMarkEnabled,
    List<PropertyOwnerDetailsEntity>? propertyOwnerDetails,
    PossessionType? possesionType,
     double? securityDepositAmount,
     String? securityDepositUnit,
  }) {
    return GetPropertyEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      saleType: saleType ?? this.saleType,
      enquiredFor: enquiredFor ?? this.enquiredFor,
      notes: notes ?? this.notes,
      furnishStatus: furnishStatus ?? this.furnishStatus,
      status: status ?? this.status,
      rating: rating ?? this.rating,
      shareCount: shareCount ?? this.shareCount,
      possessionDate: possessionDate ?? this.possessionDate,
      isGOListingEnabled: isGOListingEnabled ?? this.isGOListingEnabled,
      facing: facing ?? this.facing,
      goPropertyId: goPropertyId ?? this.goPropertyId,
      noOfBHK: noOfBHK ?? this.noOfBHK,
      bhkType: bhkType ?? this.bhkType,
      monetaryInfo: monetaryInfo ?? this.monetaryInfo,
      ownerDetails: ownerDetails ?? this.ownerDetails,
      dimension: dimension ?? this.dimension,
      tagInfo: tagInfo ?? this.tagInfo,
      images: images ?? this.images,
      imageUrls: imageUrls ?? this.imageUrls,
      attributes: attributes ?? this.attributes,
      amenities: amenities ?? this.amenities,
      brochures: brochures ?? this.brochures,
      whatsAppShareCount: whatsAppShareCount ?? this.whatsAppShareCount,
      callShareCount: callShareCount ?? this.callShareCount,
      emailShareCount: emailShareCount ?? this.emailShareCount,
      smsShareCount: smsShareCount ?? this.smsShareCount,
      aboutProperty: aboutProperty ?? this.aboutProperty,
      address: address ?? this.address,
      links: links ?? this.links,
      project: project ?? this.project,
      assignedTo: assignedTo ?? this.assignedTo,
      listingOnBehalf: listingOnBehalf ?? this.listingOnBehalf,
      lastModifiedOn: lastModifiedOn ?? this.lastModifiedOn,
      createdOn: createdOn ?? this.createdOn,
      createdBy: createdBy ?? this.createdBy,
      lastModifiedBy: lastModifiedBy ?? this.lastModifiedBy,
      propertyType: propertyType ?? this.propertyType,
      micrositeURL: micrositeURL ?? this.micrositeURL,
      serialNo: serialNo ?? this.serialNo,
      coWorkingOperatorName: coWorkingOperatorName ?? this.coWorkingOperatorName,
      coWorkingOperatorPhone: coWorkingOperatorPhone ?? this.coWorkingOperatorPhone,
      lockInPeriod: lockInPeriod ?? this.lockInPeriod,
      noticePeriod: noticePeriod ?? this.noticePeriod,
      noOfFloorsOccupied: noOfFloorsOccupied ?? this.noOfFloorsOccupied,
      tenantContactInfoEntity: tenantContactInfo ?? this.tenantContactInfoEntity,
      listingStatus: listingStatus ?? this.listingStatus,
      listingExpireDate: listingExpireDate ?? this.listingExpireDate,
      listingSources: listingSources ?? this.listingSources,
      listingLevel: listingLevel ?? this.listingLevel,
      listingSourceAddresses: listingSourceAddresses ?? this.listingSourceAddresses,
      dldPermitNumber: dldPermitNumber ?? this.dldPermitNumber,
      refrenceNo: refrenceNo ?? this.refrenceNo,
      dtcmPermit: dtcmPermit ?? this.dtcmPermit,
      offeringType: offeringType ?? this.offeringType,
      completionStatus: completionStatus ?? this.completionStatus,
      language: language ?? this.language,
      titleWithLanguage: titleWithLanguage ?? this.titleWithLanguage,
      aboutPropertyWithLanguage: aboutPropertyWithLanguage ?? this.aboutPropertyWithLanguage,
      view360Url: view360Url ?? this.view360Url,
      taxationMode: taxationMode ?? this.taxationMode,
      isWaterMarkEnabled: isWaterMarkEnabled ?? this.isWaterMarkEnabled,
      possesionType: possesionType ?? this.possesionType,
      securityDepositUnit: securityDepositUnit??this.securityDepositUnit,
      securityDepositAmount: securityDepositAmount??this.securityDepositAmount,
      propertyOwnerDetails: propertyOwnerDetails ?? this.propertyOwnerDetails,
    );
  }

  AddPropertyModel toAddPropertyModel(GetPropertyEntity getPropertyEntity) {
    var monetoryInfo = PropertyMonetaryInfoModel(
      id: getPropertyEntity.monetaryInfo?.id,
      brokerageCurrency: getPropertyEntity.monetaryInfo?.brokerageCurrency,
      brokerageUnit: BrokerageUnit.values.firstWhereOrNull((brokerageUnit) => brokerageUnit.value == getPropertyEntity.monetaryInfo?.brokerageUnit),
      currency: getPropertyEntity.monetaryInfo?.currency,
      isNegotiable: getPropertyEntity.monetaryInfo?.isNegotiable,
      depositAmount: getPropertyEntity.monetaryInfo?.depositAmount,
      brokerage: getPropertyEntity.monetaryInfo?.brokerage,
      maintenanceCost: getPropertyEntity.monetaryInfo?.maintenanceCost,
      expectedPrice: getPropertyEntity.monetaryInfo?.expectedPrice,
      escalationPercentage: getPropertyEntity.monetaryInfo?.escalationPercentage,
      isPriceVissible: getPropertyEntity.monetaryInfo?.isPriceVissible,
      paymentFrequency: getPropertyEntity.monetaryInfo?.paymentFrequency,
      serviceChange: getPropertyEntity.monetaryInfo?.serviceChange,
      noOfChequesAllowed: getPropertyEntity.monetaryInfo?.noOfChequesAllowed,
      monthlyRentAmount: getPropertyEntity.monetaryInfo?.monthlyRentAmount,

    );

    var ownerDetails = PropertyOwnerDetailsModel(
      id: getPropertyEntity.ownerDetails?.id,
      name: getPropertyEntity.ownerDetails?.name,
      email: getPropertyEntity.ownerDetails?.email,
      phone: getPropertyEntity.ownerDetails?.phone,
    );
    var propertyOwnerDetails = getPropertyEntity.propertyOwnerDetails
        ?.map((details) => PropertyOwnerDetailsModel(
              id: details.id,
              name: details.name,
              email: details.email,
              phone: details.phone,
              alternateContactNo: details.alternateContactNo,
            ))
        .toList();

    var dimension = PropertyDimensionModel(
      id: getPropertyEntity.dimension?.id,
      saleableArea: getPropertyEntity.dimension?.saleableArea,
      buildUpArea: getPropertyEntity.dimension?.buildUpArea,
      carpetArea: getPropertyEntity.dimension?.carpetArea,
      area: getPropertyEntity.dimension?.area,
      saleableAreaConversionFactor: getPropertyEntity.dimension?.saleableAreaConversionFactor,
      saleableAreaId: getPropertyEntity.dimension?.saleableAreaId,
      buildUpAreaId: getPropertyEntity.dimension?.buildUpAreaId,
      areaUnitId: getPropertyEntity.dimension?.areaUnit,
      breadth: getPropertyEntity.dimension?.breadth,
      buildUpConversionFactor: getPropertyEntity.dimension?.buildUpConversionFactor,
      carpetAreaConversionFactor: getPropertyEntity.dimension?.carpetAreaConversionFactor,
      carpetAreaId: getPropertyEntity.dimension?.carpetAreaId,
      conversionFactor: getPropertyEntity.dimension?.conversionFactor,
      length: getPropertyEntity.dimension?.length,
      unit: getPropertyEntity.dimension?.unit,
      commonAreaCharges: getPropertyEntity.dimension?.commonAreaCharges,
      commonAreaChargesId: getPropertyEntity.dimension?.commonAreaChargesId,
      netAreaConversionFactor: getPropertyEntity.dimension?.netAreaConversionFactor,
      netArea: getPropertyEntity.dimension?.netArea,
      netAreaUnitId: getPropertyEntity.dimension?.netAreaUnitId,
    );

    var tagInfo = PropertyTagInfoModel(
      // id: getPropertyEntity.tagInfo?.id,
      isGOListingEnabled: getPropertyEntity.tagInfo?.isGOListingEnabled,
      isFeatured: getPropertyEntity.tagInfo?.isFeatured,
      isValidated: getPropertyEntity.tagInfo?.isValidated,
    );

    List<PropertyImageModel> propertyImage = [];

    getPropertyEntity.images?.forEach((image) {
      propertyImage.add(
        PropertyImageModel(
          name: image.name,
          isCoverImage: image.isCoverImage,
          imageFilePath: image.imageFilePath,
          imageSegregationType: image.imageSegregationType,
          galleryType: image.galleryType,
          imageKey: image.imageKey,
          orderRank: image.orderRank,
        ),
      );
    });

    List<PropertyAttributeModel> attributes = [];

    getPropertyEntity.attributes?.forEach((attribute) {
      attributes.add(PropertyAttributeModel(
        value: attribute?.defaultValue,
        attributeDisplayName: attribute?.attributeDisplayName,
        attributeName: attribute?.attributeName,
        masterPropertyAttributeId: attribute?.id,
      ));
    });

    List<String> amenities = [];

    getPropertyEntity.amenities?.forEach((amenity) {
      amenities.add(amenity?.id ?? '');
    });

    List<PropertyBrochureModel> brochures = [];

    getPropertyEntity.brochures?.forEach((brochure) {
      brochures.add(PropertyBrochureModel(
        name: brochure.name,
        url: brochure.url,
      ));
    });

    AddressModel address = AddressModel(
      subCommunity: getPropertyEntity.address?.subCommunity,
      community: getPropertyEntity.address?.community,
      placeId: getPropertyEntity.address?.placeId,
      locationId: getPropertyEntity.address?.locationId,
      state: getPropertyEntity.address?.state,
      city: getPropertyEntity.address?.city,
      locality: getPropertyEntity.address?.locality,
      subLocality: getPropertyEntity.address?.subLocality,
      isManual: getPropertyEntity.address?.isManual ?? false,
      isGoogleMapLocation: getPropertyEntity.address?.isGoogleMapLocation ?? false,
      longitude: getPropertyEntity.address?.longitude,
      latitude: getPropertyEntity.address?.latitude,
      lastModifiedOn: getPropertyEntity.address?.lastModifiedOn,
      lastModifiedBy: getPropertyEntity.address?.lastModifiedBy,
      createdOn: getPropertyEntity.address?.createdOn,
      createdBy: getPropertyEntity.address?.createdBy,
      country: getPropertyEntity.address?.country,
      district: getPropertyEntity.address?.district,
      postalCode: getPropertyEntity.address?.postalCode,
    );

    List<String> assignTo = [];

    getPropertyEntity.assignedTo?.forEach((user) {
      assignTo.add(user?.id ?? '');
    });
    List<String> listingOnBehalf = [];

    getPropertyEntity.listingOnBehalf?.forEach((user) {
      listingOnBehalf.add(user?.id ?? '');
    });

    return AddPropertyModel(
        id: getPropertyEntity.id,
        title: getPropertyEntity.title,
        saleType: getPropertyEntity.saleType?.value,
        enquiredFor: getPropertyEntity.enquiredFor?.value,
        notes: getPropertyEntity.notes,
        furnishStatus: getPropertyEntity.furnishStatus?.value,
        status: getPropertyEntity.status?.value,
        rating: getPropertyEntity.rating,
        shareCount: getPropertyEntity.shareCount,
        possessionDate: getPropertyEntity.possessionDate,
        isGOListingEnabled: false,
        facing: getPropertyEntity.facing?.value,
        goPropertyId: getPropertyEntity.goPropertyId,
        noOfBHK: getPropertyEntity.noOfBHK,
        bhkType: getPropertyEntity.bhkType?.value,
        monetaryInfo: monetoryInfo,
        ownerDetails: ownerDetails,
        dimension: dimension,
        tagInfo: tagInfo,
        images: propertyImage,
        attributes: attributes,
        amenities: amenities,
        brochures: brochures,
        whatsAppShareCount: getPropertyEntity.whatsAppShareCount,
        callShareCount: getPropertyEntity.callShareCount,
        emailShareCount: getPropertyEntity.emailShareCount,
        smsShareCount: getPropertyEntity.smsShareCount,
        aboutProperty: getPropertyEntity.aboutProperty,
        address: address,
        placeId: address.placeId,
        project: getPropertyEntity.project,
        assignedTo: assignTo,
        links: getPropertyEntity.links,
        propertyTypeId: getPropertyEntity.propertyType?.childType?.id ?? getPropertyEntity.propertyType?.id,
        shouldVisisbleOnListing: true,
        coWorkingOperator: getPropertyEntity.coWorkingOperator,
        coWorkingOperatorPhone: getPropertyEntity.coWorkingOperatorPhone,
        noticePeriod: getPropertyEntity.noticePeriod,
        lockInPeriod: getPropertyEntity.lockInPeriod,
        coWorkingOperatorName: getPropertyEntity.coWorkingOperatorName,
        noOfFloorsOccupied: getPropertyEntity.noOfFloorsOccupied,
        serialNo: getPropertyEntity.serialNo,
        imageUrls: getPropertyEntity.imageUrls,
        tenantContactInfo: getPropertyEntity.tenantContactInfoEntity?.toModel(),
        listingAddresses: listingSourceAddresses?.map((e) => CreateListingAddressModel(listingSourceId: e.listingSource?.id, locationId: e.id)).toList(),
        dldPermitNumber: dldPermitNumber,
        refrenceNo: refrenceNo,
        dtcmPermit: dtcmPermit,
        offeringType: offeringType,
        completionStatus: completionStatus,
        language: language,
        titleWithLanguage: titleWithLanguage,
        aboutPropertyWithLanguage: aboutPropertyWithLanguage,
        view360Url: view360Url,
        taxationMode: taxationMode,
        isWaterMarkEnabled: isWaterMarkEnabled,
        possesionType: possesionType,
      listingOnBehalf: listingOnBehalf,
      securityDepositAmount:securityDepositAmount ,securityDepositUnit: securityDepositUnit,
      propertyOwnerDetails: propertyOwnerDetails,
    );
  }
}

class TenantContactInfoEntity {
  String? id;
  String? name;
  String? phone;
  String? designation;

  TenantContactInfoEntity({
    this.id,
    this.name,
    this.phone,
    this.designation,
  });

  TenantContactInfo toModel() {
    return TenantContactInfo(id: id, phone: phone, designation: designation, name: name);
  }
}

class PropertyWithDegreeMatchedEntity {
  final int? totalNoOfFields;
  final int? noOfFieldsMatched;
  final String? percentageOfFieldsMatched;
  final GetPropertyEntity? property;

  PropertyWithDegreeMatchedEntity({
    this.property,
    this.percentageOfFieldsMatched,
    this.noOfFieldsMatched,
    this.totalNoOfFields,
  });

  PropertyWithDegreeMatchedEntity copyWith({
    int? totalNoOfFields,
    int? noOfFieldsMatched,
    String? percentageOfFieldsMatched,
    GetPropertyEntity? property,
  }) {
    return PropertyWithDegreeMatchedEntity(
      totalNoOfFields: totalNoOfFields ?? this.totalNoOfFields,
      noOfFieldsMatched: noOfFieldsMatched ?? this.noOfFieldsMatched,
      percentageOfFieldsMatched: percentageOfFieldsMatched ?? this.percentageOfFieldsMatched,
      property: property ?? this.property,
    );
  }
}
