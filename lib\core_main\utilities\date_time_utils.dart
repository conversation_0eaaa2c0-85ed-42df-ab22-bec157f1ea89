import 'package:leadrat/core_main/enums/common/possession_type.dart';

class DateTimeUtils {
  static DateTime? getPossessionDate(PossessionType possessionType) {
    if (possessionType.month == 0) {
      return null;
    }

    final now = DateTime.now();
    final monthsToAdd = possessionType.month;

    int newYear = now.year;
    int newMonth = now.month + monthsToAdd;

    if (monthsToAdd >= 12) {
      newYear = now.year + (monthsToAdd ~/ 12);
      newMonth = now.month;
    }

    if (newMonth > 12) {
      newYear += (newMonth - 1) ~/ 12;
      newMonth = ((newMonth - 1) % 12) + 1;
    }

    final lastDay = DateTime(newYear, newMonth + 1, 0).day;

    return DateTime(newYear, newMonth, lastDay);
  }
}
