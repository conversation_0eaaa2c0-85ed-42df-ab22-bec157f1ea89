import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:intl/intl.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/models/appointment_model.dart';
import 'package:leadrat/core_main/common/models/booked_details_model.dart';
import 'package:leadrat/core_main/common/models/view_whatsapp_communication_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/common/booked_document_type.dart';
import 'package:leadrat/core_main/enums/common/brokerage_type.dart';
import 'package:leadrat/core_main/enums/common/contact_type_enum.dart';
import 'package:leadrat/core_main/enums/common/discount_type.dart';
import 'package:leadrat/core_main/enums/common/no_of_beds.dart';
import 'package:leadrat/core_main/enums/common/payment_type.dart';
import 'package:leadrat/core_main/enums/common/token_type.dart';
import 'package:leadrat/core_main/enums/leads/lead_history_filter_key.dart';
import 'package:leadrat/core_main/enums/user_profile/document_type_enum.dart';
import 'package:leadrat/core_main/extensions/double_extension.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/utilities/string_utils.dart';
import 'package:leadrat/features/lead/data/models/lead_communication_model.dart';
import 'package:leadrat/features/lead/domain/entities/lead_history_entity.dart';
import 'package:leadrat/features/properties/presentation/items/item_property_model.dart';

sealed class ItemLeadHistory {}

class ItemLeadHistoryModel extends ItemLeadHistory {
  String? notes;
  String? assignTo;
  String? status;
  LeadHistoryFilterKey? filterKey;
  DateTime timeStamp;
  String? updatedBy;
  List<String?>? subitems = [];
  bool isLeadSourceVisible;
  List<LeadHistoryEntity> itemsHistory;
  final bool isCustomFormEnabled;
  String? audioUrl;
  bool get hasHistoryItems => notes.isNotNullOrEmpty() || assignTo.isNotNullOrEmpty() || (subitems?.isNotEmpty ?? false) || (audioUrl.isNotNullOrEmpty()) || (status.isNotNullOrEmpty());

  ItemLeadHistoryModel({
    this.notes,
    this.assignTo,
    this.status,
    this.filterKey,
    this.updatedBy,
    required this.itemsHistory,
    required this.timeStamp,
    this.subitems,
    required this.isLeadSourceVisible,
    this.isCustomFormEnabled = false,
    this.audioUrl,
  }) {
    subitems = [];
    timeStamp = timeStamp.toLocal();
    updatedBy = itemsHistory.firstOrNull?.updatedBy;

    for (var item in itemsHistory) {
      switch (item.filterKey) {
        case LeadHistoryFilterKey.none:
          if (item.oldValue == null && item.newValue == null) {
            status = item.fieldName;
          } else {
            // Adding bullet points
            var formattedString = _formatString(item, isLeadSourceVisible);
            if (formattedString.trim().isNotEmpty) {
              subitems?.add(formattedString);
            }
          }
          break;

        case LeadHistoryFilterKey.notes:
          notes = item.newValue;
          break;

        case LeadHistoryFilterKey.status:
          status = (item.oldValue?.isNotEmpty ?? false) ? "Status changed from <b><u>'${item.oldValue}'</u></b> to <b><u>'${item.newValue}'</u></b>" : "Status changed to <b><u>'${item.newValue}'</u></b>";
          assignTo = "assigned to <b>${item.newValue}</b>";
          break;

        default:
          break;
      }
    }
  }

  String _formatString(LeadHistoryEntity leadHistoryItem, bool isLeadSourceVisible) {
    try {
      LeadHistoryEntity item = leadHistoryItem;
      String formattedString = "";
      String? newValue;
      String? oldValue;

      var leadTags = ["Is Highlighted", "Is Escalated", "Is About To Convert", "Is Hot Lead", "Is Integration Lead", "Is Warm Lead", "Is Cold Lead"];

      if (leadHistoryItem.fieldName == null) return "";

      if (leadHistoryItem.fieldName == "Lead Source" || leadHistoryItem.fieldName == "Sub Source") {
        item = leadHistoryItem.copyWith(newValue: isLeadSourceVisible ? leadHistoryItem.newValue : "--");
      }

      if (leadHistoryItem.fieldName!.toLowerCase().contains("lead got added")) {
        return "Lead got added";
      }

      if (leadTags.contains(leadHistoryItem.fieldName)) {
        bool isFlagged = leadHistoryItem.newValue?.toLowerCase() == 'true';
        return "Lead ${isFlagged ? 'Tagged' : 'Untagged'} from ${leadHistoryItem.fieldName?.replaceAll('as ', '')}";
      }

      if (leadTags.contains(item.fieldName)) {
        bool isFlagged = item.newValue?.toLowerCase() == "true";
        return "Lead ${isFlagged ? "Tagged" : "Untagged"} from ${item.fieldName?.replaceAll("as ", "")}";
      }
      if (item.fieldName?.toLowerCase().contains("rating") ?? false) {
        oldValue = (item.oldValue?.isEmpty ?? true) ? '' : '${item.oldValue ?? ""} stars';
        newValue = '${item.newValue ?? ""} stars';
      } else if (item.fieldName?.toLowerCase().contains("date") ?? false) {
        final formats = [
          "yyyy-MM-ddTHH:mm:ss.fffffffZ",
          "yyyy-MM-ddTHH:mm:ssZ",
        ];
        DateTime? oldDate = _tryParseExact(item.oldValue, formats);
        oldValue = oldDate != null ? _formatDate(oldDate.toUserTimeZone() ?? oldDate.toLocal(), "dd/MM/yyyy hh:mm a") : '';

        DateTime? newDate = _tryParseExact(item.newValue, formats);
        newValue = newDate != null ? _formatDate(newDate.toUserTimeZone() ?? newDate.toLocal(), "dd/MM/yyyy hh:mm a") : '';
      } else {
        oldValue = item.oldValue;
        newValue = item.newValue;
      }

      if (item.fieldName!.toLowerCase().contains("assigned to user")) {
        if (oldValue.isNotNullOrEmpty()) {
          formattedString = "• Lead assigned updated <b>'$oldValue'</b> to <b>'$newValue'</b>";
        } else {
          formattedString = "• Lead assigned added <b>'$newValue'</b>";
        }
      } else if (item.fieldName!.toLowerCase().contains("contact records")) {
        formattedString = "• <b>$newValue</b> had been initiated";
      } else if (item.fieldName!.toLowerCase().contains("lead comunication")) {
        try {
          LeadCommunicationModel? commDto;
          try {
            final data = StringUtils.pascalToCamelCaseJson(item.newValue ?? "");
            commDto = LeadCommunicationModel.fromJson(jsonDecode(data));
          } catch (exception, stackTrace) {
            exception.logException(stackTrace);
          }
          if (commDto?.contactType != ContactType.call && (commDto?.message?.isNotEmpty ?? false)) {
            formattedString = "• <b>Message</b><br>${commDto?.message ?? ''}";
          }
        } catch (e, stackTrace) {
          e.logException(stackTrace);
        }
      } else if (leadTags.contains(item.fieldName)) {
        bool isFlagged = item.newValue?.toLowerCase() == 'true';
        formattedString = "Lead has been ${isFlagged ? 'Tagged' : 'Untagged'} - ${item.fieldName?.replaceAll('Is ', '')}";
      } else if (item.fieldName!.toLowerCase() == "documents") {
        List<String> oldDocuments = (item.oldValue?.split(',') ?? []).where((i) => i.isNotEmpty).toList();
        List<String> newDocuments = (item.newValue?.split(',') ?? []).where((i) => i.isNotEmpty).toList();

        List<String> deletedDocument = oldDocuments.where((i) => !newDocuments.contains(i)).toList();
        List<String> uploadedDocument = newDocuments.where((i) => !oldDocuments.contains(i)).toList();

        if (uploadedDocument.isNotEmpty) {
          for (var document in uploadedDocument) {
            String newLine = uploadedDocument.length > 1 ? "<br>" : '';
            formattedString += "• Document uploaded - <b>$document</b> $newLine";
          }
        }

        if (deletedDocument.isNotEmpty) {
          for (var document in deletedDocument) {
            String newLine = deletedDocument.length > 1 ? "<br>" : '';
            formattedString += "• Document deleted - <b>$document</b> $newLine";
          }
        }
      } else if (item.fieldName!.toLowerCase() == "is meeting done") {
        bool isDone = item.newValue?.toLowerCase() == 'true';
        formattedString = "• Meeting ${isDone ? 'Done' : 'Not Done'}";
      } else if (item.fieldName!.toLowerCase() == "is site visit done") {
        bool isDone = item.newValue?.toLowerCase() == 'true';
        formattedString = "• Site Visit ${isDone ? 'Done' : 'Not Done'}";
      } else if (item.fieldName == "Meeting Location" || item.fieldName == "Site Location") {
        // TODO: Need to add location details
      } else if (item.fieldName == "Company Name") {
        formattedString = oldValue.isNotNullOrEmpty() ? "• ${item.fieldName} updated <b>'$oldValue'</b> to <b>'$newValue'</b>" : "• ${item.fieldName} added - <b>$newValue</b>";
      } else if (item.fieldName == "Agency Name") {
        formattedString = oldValue.isNotNullOrEmpty() ? "• ${item.fieldName} updated <b>'$oldValue'</b> to <b>'$newValue'</b>" : "• ${item.fieldName} added <b>$newValue</b>";
      } else if (item.fieldName!.toLowerCase() == "leadcalllog") {
        String? displayText = newValue;

        if (newValue?.contains("CallRecordingUrl") ?? false) {
          final urlPattern = RegExp(r'CallRecordingUrl -> (https?:\/\/[^\s,]+)');
          final match = urlPattern.firstMatch(newValue ?? '');
          if (match != null) {
            audioUrl = match.group(1)!;
            displayText = newValue!.replaceAll(', CallRecordingUrl -> $audioUrl', '');
            formattedString = "•$displayText";
          } else {
            displayText = newValue!.replaceAll(', CallRecordingUrl -> ', '');
            formattedString = "•$displayText";
          }
        }

        formattedString = "•$displayText";
      } else if (item.fieldName!.toLowerCase() == "confidential notes") {
        try {
          var userRepository = getIt<UsersDataRepository>();
          if (userRepository.checkHasPermission(AppModule.lead, CommandType.viewConfidentialNotes)) {
            formattedString = "• ${item.fieldName} updated <b>'${oldValue ?? ''}'</b> to <b>'${newValue ?? ''}'</b>";
          }
        } catch (e, stackTrace) {
          e.logException(stackTrace);
        }
      } else if (item.fieldName!.toLowerCase() == "lead appointment") {
        final data = StringUtils.pascalToCamelCaseJson(item.newValue ?? "");
        AppointmentModel? appointmentDto;
        try {
          appointmentDto = AppointmentModel.fromJson(jsonDecode(data));
        } catch (exception, stackTrace) {
          exception.logException(stackTrace);
        }
        var appointmentDetails = '';
        if (appointmentDto?.projectName != null && (appointmentDto?.projectName?.isNotEmpty ?? false)) {
          appointmentDetails += " Project name - ${appointmentDto?.projectName ?? ""}";
        }

        if (appointmentDto?.executiveName != null && (appointmentDto?.executiveName?.isNotEmpty ?? false)) {
          appointmentDetails += "<br> <b> Sales Executive name - ${appointmentDto?.executiveName ?? ""} </b>";
        }

        if (appointmentDto?.executiveContactNo != null && (appointmentDto?.executiveContactNo?.isNotEmpty ?? false)) {
          appointmentDetails += "<br> <b> Sales Executive contact no - ${appointmentDto?.executiveContactNo ?? ""} </b>";
        }

        if (appointmentDto?.notes != null && (appointmentDto?.notes?.isNotEmpty ?? false)) {
          appointmentDetails += "<br> <b> Notes - ${appointmentDto?.notes ?? ""} </b>";
        }

        if (appointmentDto?.imagesWithName != null && (appointmentDto?.imagesWithName!.isNotEmpty ?? false)) {
          var documents = appointmentDto?.imagesWithName!.map((i) => i.documentName ?? '').join(', ');
          appointmentDetails += "<br> <b> Documents - $documents </b>";
        }

        if (appointmentDto?.location != null) {
          var location = "${appointmentDto?.location?.locality ?? ''}, ${appointmentDto?.location?.subLocality ?? ''}";
          appointmentDetails += "<br> <b> Location - $location </b>";
        }

        formattedString = "• Lead appointment updated - <br> <b> $appointmentDetails </b>";
      } else if (item.fieldName!.toLowerCase() == "whatsapp communication") {
        try {
          final data = StringUtils.pascalToCamelCaseJson(item.newValue ?? "");
          ViewWhatsAppCommunicationModel? commDto;
          try {
            commDto = ViewWhatsAppCommunicationModel.fromJson(jsonDecode(data));
          } catch (exception, stackTrace) {
            exception.logException(stackTrace);
          }
          if (commDto?.templateName?.isNotEmpty ?? false) {
            formattedString = "• <b> WhatsApp Business </b> <br> Template name - ${commDto?.templateName ?? ''} ";
          }
        } catch (e, stackTrace) {
          e.logException(stackTrace);
        }
      } else if (item.fieldName!.toLowerCase().contains('bhks')) {
        String tempOldValue = getBhks(oldValue ?? '');
        String tempNewValue = getBhks(newValue ?? '');

        formattedString = oldValue.isNotNullOrEmpty()
            ? "• ${item.fieldName} updated <b>'$tempOldValue'</b> to <b>'${tempNewValue.isNullOrEmpty() ? 'has been removed' : tempNewValue}'</b>"
            : "• ${item.fieldName} added - <b>${tempNewValue.isNullOrEmpty() ? 'has been removed' : tempNewValue}</b>";
      }
      else if (item.fieldName!.toLowerCase().contains('bhks')) {
        String tempOldValue = getBhks(oldValue ?? '');
        String tempNewValue = getBhks(newValue ?? '');
        final updatedFieldName = isCustomFormEnabled ? "BRs" : "BHKs";
        formattedString = oldValue.isNotNullOrEmpty() ? "• $updatedFieldName updated <b>'$tempOldValue'</b> to <b>'$tempNewValue'</b>" : "• $updatedFieldName added - <b>$tempNewValue</b>";
      } else if (item.fieldName == "BHKType") {
        if (newValue != "None") {
          formattedString = oldValue.isNotNullOrEmpty() ? "• ${item.fieldName} updated <b>'$oldValue'</b> to <b>'$newValue'</b>" : "• ${item.fieldName} added - <b>$newValue</b>";
        }
      } else if (item.fieldName!.toLowerCase() == "booked details information") {
        final data = StringUtils.pascalToCamelCaseJson(item.newValue ?? "");
        BookedDetailsModel? bookingDetailsDto;
        try {
          bookingDetailsDto = BookedDetailsModel.fromJson(jsonDecode(data));
        } catch (exception, stackTrace) {
          exception.logException(stackTrace);
        }
        var bookingDetails = '';
        if (bookingDetailsDto?.bookedDate != null) {
          bookingDetails += "booked date - ${bookingDetailsDto?.bookedDate.toUserTimeZone() ?? ""} <br>";
        }

        if (bookingDetailsDto?.bookedByName != null) {
          bookingDetails += "booked by name - ${bookingDetailsDto?.bookedByName ?? ""} <br>";
        }

        if (bookingDetailsDto?.secondaryOwnerName != null) {
          bookingDetails += "secondary owner name - ${bookingDetailsDto?.secondaryOwnerName ?? ""} <br>";
        }

        if (bookingDetailsDto?.bookedUnderName != null) {
          bookingDetails += "booked under name - ${bookingDetailsDto?.bookedUnderName ?? ""} <br>";
        }

        if (bookingDetailsDto?.soldPrice != null) {
          bookingDetails += "sold price - ${bookingDetailsDto?.soldPrice ?? ""} <br>";
        }

        if (bookingDetailsDto?.projects?.name.isNotNullOrEmpty() ?? false) {
          bookingDetails += "project name - ${bookingDetailsDto?.projects?.name ?? ""} <br>";
        }

        if (bookingDetailsDto?.unitType != null) {
          bookingDetails += "unit name - ${bookingDetailsDto?.unitType?.name ?? ""} <br>";
        }

        if (bookingDetailsDto?.property != null) {
          bookingDetails += "property name - ${bookingDetailsDto?.property?.title ?? ""} <br>";
        }

        if (bookingDetailsDto?.teamHeadName != null && (bookingDetailsDto?.teamHeadName?.isNotEmpty ?? false)) {
          bookingDetails += "team head name - ${bookingDetailsDto?.teamHeadName ?? ""} <br>";
        }

        if (bookingDetailsDto?.agreementValue != null && bookingDetailsDto?.agreementValue != 0) {
          bookingDetails += "agreement value - ${bookingDetailsDto?.agreementValue?.toDoubleFormattedString() ?? ""} <br>";
        }

        if (bookingDetailsDto?.carParkingCharges != null && bookingDetailsDto?.carParkingCharges != 0) {
          bookingDetails += "car parking charges - ${bookingDetailsDto?.carParkingCharges ?? ""} <br>";
        }

        if (bookingDetailsDto?.additionalCharges != null && (bookingDetailsDto?.additionalCharges ?? 0) != 0) {
          bookingDetails += "additional charges - ${bookingDetailsDto?.additionalCharges ?? ""} <br>";
        }

        if (bookingDetailsDto?.tokenAmount != null && (bookingDetailsDto?.tokenAmount ?? 0) != 0) {
          bookingDetails += "token amount - ${bookingDetailsDto?.tokenAmount ?? ""} <br>";
        }

        if (bookingDetailsDto?.paymentMode != TokenType.none) {
          bookingDetails += "payment mode - ${bookingDetailsDto?.paymentMode?.description ?? ""} <br>";
        }

        if (bookingDetailsDto?.discount != null && (bookingDetailsDto?.discount ?? 0) != 0) {
          bookingDetails += "discount - ${bookingDetailsDto?.discount ?? ""} <br>";
        }

        if (bookingDetailsDto?.discountUnit != null && (bookingDetailsDto?.discountUnit?.isNotEmpty ?? false)) {
          bookingDetails += "discount Unit - ${bookingDetailsDto?.discountUnit ?? ""} <br>";
        }

        if ((bookingDetailsDto?.discountMode ?? DiscountType.none) != DiscountType.none) {
          bookingDetails += "discount mode - ${bookingDetailsDto?.discountMode?.description ?? ""} <br>";
        }

        if (bookingDetailsDto?.remainingAmount != null && bookingDetailsDto?.remainingAmount != 0) {
          bookingDetails += "remaining amount - ${bookingDetailsDto?.remainingAmount ?? ""} <br>";
        }

        if ((bookingDetailsDto?.paymentType ?? PaymentType.none) != PaymentType.none) {
          bookingDetails += "payment type - ${bookingDetailsDto?.paymentType?.description ?? ""} <br>";
        }

        formattedString = "• booked details information updated<br><b>$bookingDetails</b>";
      } else if (item.fieldName!.toLowerCase() == "documents details information") {
        final data = StringUtils.pascalToCamelCaseJson(item.newValue ?? "");
        DocumentsModel? documents;
        try {
          documents = DocumentsModel.fromJson(jsonDecode(data));
        } catch (exception, stackTrace) {
          exception.logException(stackTrace);
        }
        var documentDetails = '';

        documentDetails += "document name - ${documents?.documentName ?? ""}";

        if ((documents?.type ?? DocumentType.defaultType) != DocumentType.defaultType) {
          documentDetails += ".${documents?.type?.description ?? ""}";
        }

        if ((documents?.bookedDocumentType ?? BookedDocumentType.none) != BookedDocumentType.none) {
          documentDetails += "<br> document type - ${documents?.bookedDocumentType?.description ?? ""}";
        }

        formattedString = "• documents details information - <br> <b> $documentDetails </b>";
      } else if (item.fieldName!.toLowerCase() == "lead brokerage details information") {
        final data = StringUtils.pascalToCamelCaseJson(item.newValue ?? "");
        LeadBrokerageInfoModel? brokerageInfoDto;
        try {
          brokerageInfoDto = LeadBrokerageInfoModel.fromJson(jsonDecode(data));
        } catch (exception, stackTrace) {
          exception.logException(stackTrace);
        }
        var brokerageInfo = '';

        if (brokerageInfoDto != null) {
          if (brokerageInfoDto.soldPrice != null && brokerageInfoDto.soldPrice != 0) {
            brokerageInfo += "sold price - ${brokerageInfoDto.soldPrice ?? ""} <br>";
          }

          if (brokerageInfoDto.agreementValue != null && brokerageInfoDto.agreementValue != 0) {
            brokerageInfo += "agreement value - ${brokerageInfoDto.agreementValue ?? ""} <br>";
          }

          if (brokerageInfoDto.brokerageCharges != null && brokerageInfoDto.brokerageCharges != 0) {
            brokerageInfo += "brokerage charges - ${brokerageInfoDto.brokerageCharges ?? ""} <br>";
          }

          if (brokerageInfoDto.netBrokerageAmount != null && brokerageInfoDto.netBrokerageAmount != 0) {
            brokerageInfo += "net brokerage amount - ${brokerageInfoDto.netBrokerageAmount ?? ""} <br>";
          }

          if (brokerageInfoDto.gst != null && brokerageInfoDto.gst != 0) {
            brokerageInfo += "gst - ${brokerageInfoDto.gst ?? ""} <br>";
          }

          if (brokerageInfoDto.totalBrokerage != null && brokerageInfoDto.totalBrokerage != 0) {
            brokerageInfo += "total brokerage - ${brokerageInfoDto.totalBrokerage ?? ""} <br>";
          }

          if (brokerageInfoDto.referralNumber != null && brokerageInfoDto.referralNumber!.isNotEmpty) {
            brokerageInfo += "referral number - ${brokerageInfoDto.referralNumber ?? ""} <br>";
          }

          if (brokerageInfoDto.referralName != null && brokerageInfoDto.referralName!.isNotEmpty) {
            brokerageInfo += "referral name - ${brokerageInfoDto.referralName ?? ""} <br>";
          }

          if (brokerageInfoDto.commission != null && brokerageInfoDto.commission != 0) {
            brokerageInfo += "commission - ${brokerageInfoDto.commission ?? ""} <br>";
          }

          if (brokerageInfoDto.commissionUnit != null && brokerageInfoDto.commissionUnit!.isNotEmpty) {
            brokerageInfo += "commission unit - ${brokerageInfoDto.commissionUnit ?? ""} <br>";
          }

          if (brokerageInfoDto.brokerageType != null && brokerageInfoDto.brokerageType != BrokerageType.none) {
            brokerageInfo += "brokerage type - ${brokerageInfoDto.brokerageType?.description ?? ""} <br>";
          }

          if (brokerageInfoDto.earnedBrokerage != null && brokerageInfoDto.earnedBrokerage != 0) {
            brokerageInfo += "earned brokerage - ${brokerageInfoDto.earnedBrokerage ?? ""} <br>";
          }

          if (brokerageInfoDto.gSTUnit != null && brokerageInfoDto.gSTUnit!.isNotEmpty) {
            brokerageInfo += "GST unit - ${brokerageInfoDto.gSTUnit ?? ""} <br>";
          }

          if (brokerageInfoDto.brokerageUnit != null && brokerageInfoDto.brokerageUnit!.isNotEmpty) {
            brokerageInfo += "brokerage unit - ${brokerageInfoDto.brokerageUnit ?? ""}";
          }
        }

        formattedString = "• lead brokerage details information - <br> <b> $brokerageInfo </b>";
      } else if (item.fieldName?.toLowerCase() == "custom flags") {
        try {
          var oldItems = item.oldValue?.split(',').toList() ?? [];
          var newItems = item.newValue?.split(',').toList() ?? [];

          if ((oldItems.any((i) => i.isEmpty)) && newItems.isNotEmpty) {
            formattedString = "• lead is Tagged as <b>'${newItems.firstOrNull ?? ''}'</b>";
          } else {
            for (var i in newItems) {
              i = i.trimLeft();
              if (!oldItems.any((j) => j.trimLeft() == i)) {
                formattedString = "• lead is Tagged as <b>'$i'</b>";
              }
            }

            for (var i in oldItems) {
              i = i.trimLeft();
              if (!newItems.any((j) => j.trimLeft() == i)) {
                formattedString = "• lead is Untagged as <b>'$i'</b>";
              }
            }
          }
        } catch (ex, stackTrace) {
          ex.logException(stackTrace);
        }
      } else if (oldValue.isNullOrEmpty() || oldValue == ' ' || ['None', 'Unknown'].contains(oldValue)) {
        formattedString = "• ${item.fieldName} added <b>'$newValue'</b>";
      } else if (item.fieldName?.toLowerCase() == "is archived") {
        bool isDeleted = item.newValue?.toLowerCase() == "true";
        formattedString = "• Lead was ${isDeleted ? "Deleted" : "Restored"}";
      } else if (item.fieldName?.toLowerCase() == "lower budget") {
        formattedString = oldValue.isNotNullOrEmpty() ? "• ${item.fieldName} updated <b>'$oldValue'</b> to <b>'$newValue'</b>" : "• ${item.fieldName} added <b>$newValue /-</b>";
      } else if (item.fieldName?.toLowerCase() == "upper budget") {
        formattedString = oldValue.isNotNullOrEmpty() ? "• ${item.fieldName} updated <b>'$oldValue'</b> to <b>'$newValue'</b>" : "• ${item.fieldName} added <b>$newValue</b>";
      } else if (!["scheduled date", "picked date"].contains(item.fieldName?.toLowerCase()) && newValue.isNullOrEmpty()) {
        formattedString = oldValue.isNotNullOrEmpty() ? "• ${item.fieldName} <b>'$oldValue'</b> has been removed" : '';
      } else {
        if (!(item.fieldName?.toLowerCase().contains("assigned from user") ?? false)) {
          final isUnknownFurnishedStatus = (item.fieldName?.toLowerCase() == "furnished") && (item.newValue?.toLowerCase() == "unknown") && (item.oldValue?.toLowerCase() == "unknown");
          if (newValue.isNotNullOrEmpty() && oldValue.isNotNullOrEmpty() && !isUnknownFurnishedStatus) {
            formattedString = "• ${item.fieldName} updated <b>'$oldValue'</b> to <b>'$newValue'</b>";
          }
        }
      }
      return formattedString;
    } catch (exception, stackTrace) {
      exception.logException(stackTrace);
      return '';
    }
  }

  DateTime? _tryParseExact(String? value, List<String> formats) {
    for (var format in formats) {
      try {
        return value != null ? DateFormat(format).parse(value, true).toUtc() : null;
      } catch (_) {}
    }
    return null;
  }

  String _formatDate(DateTime date, String format) {
    return DateFormat(format).format(date);
  }

  String getBhks(String oldValue) {
    List<String>? bhksList = oldValue.split(',');
    String formattedBhks = '';
    if (bhksList.isNotEmpty) {
      for (var value in bhksList) {
        try {
          if (value.isNotEmpty) {
            double convertedValue = double.parse(value);
            formattedBhks = '$formattedBhks${convertedValue == 0.5 ? '1 RK, ' : "${convertNumber(convertedValue)} ${isCustomFormEnabled ? 'BR' : 'BHK'} ,"}';
          }
        } catch (ex, stackTrace) {
          ex.logException(stackTrace);
        }
      }
    }
    if (formattedBhks.length > 2) formattedBhks = formattedBhks.substring(0, formattedBhks.length - 1);
    return formattedBhks;
  }

  String getBeds(String value) {
    try {
      var beds = value.split(',');
      var noOfBeds = beds.map((e) => int.tryParse(e)).toList().nonNulls;
      String formattedBeds = '';
      if (beds.isNotEmpty) {
        formattedBeds = noOfBeds.map((e) => getEnumFromBeds(Beds.values, e)).map((element) => element?.description).nonNulls.join(", ");
      }
      return formattedBeds;
    } catch (_) {
      return '';
    }
  }
}

class ItemLeadHistoryHeaderModel extends ItemLeadHistory {
  final DateTime? timeStamp;

  ItemLeadHistoryHeaderModel(this.timeStamp);
}
