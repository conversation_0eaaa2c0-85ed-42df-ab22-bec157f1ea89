import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/base/usecase/use_case.dart';
import 'package:leadrat/core_main/common/constants/app_analytics_constants.dart';
import 'package:leadrat/core_main/common/constants/constants.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/repository/global_setting_repository.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_type_model.dart';
import 'package:leadrat/core_main/common/data/master_data/repository/masterdata_repository.dart';
import 'package:leadrat/core_main/common/data/user/models/get_all_users_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/models/address_model.dart';
import 'package:leadrat/core_main/common/models/dto_with_name_model.dart';
import 'package:leadrat/core_main/common/widgets/lrb_phone_field/countries.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/lead_source_enum.dart';
import 'package:leadrat/core_main/enums/common/no_of_baths.dart';
import 'package:leadrat/core_main/enums/common/no_of_beds.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/property_type_enum.dart';
import 'package:leadrat/core_main/enums/common/purpose_enum.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/leads/profession.dart';
import 'package:leadrat/core_main/enums/property_enums/furnish_status.dart';
import 'package:leadrat/core_main/enums/property_enums/offering_type.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:leadrat/core_main/extensions/enum_extension.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/utilities/date_time_utils.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/core_main/utilities/phone_utils.dart';
import 'package:leadrat/features/lead/data/models/add_lead_model.dart';
import 'package:leadrat/features/lead/data/models/create_enquiry_model.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';
import 'package:leadrat/features/lead/domain/entities/lead_sub_source_entity.dart';
import 'package:leadrat/features/lead/domain/usecase/add_lead_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/check_lead_assigned_by_leadId_usecase.dart';
import 'package:leadrat/features/lead/domain/usecase/get_channel_partner_names_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_lead_by_contact_no_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_lead_sub_source_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_project_name_with_id_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_property_name_with_id_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/update_lead_use_case.dart';
import 'package:leadrat/features/lead/presentation/bloc/lead_info_bloc/lead_info_bloc.dart';
import 'package:leadrat/features/lead/presentation/bloc/manage_leads_bloc/manage_leads_bloc.dart';
import 'package:leadrat/main.dart';

import '../../../../../core_main/enums/common/no_of_br.dart';
import '../../../domain/repository/leads_repository.dart';

part 'custom_add_lead_event.dart';
part 'custom_add_lead_state.dart';

class CustomAddLeadBloc extends Bloc<CustomAddLeadEvent, CustomAddLeadState> {
  //TextEditing Controller
  TextEditingController? leadNameController;
  TextEditingController? phoneController;
  TextEditingController? altPhoneController;
  TextEditingController? emailController;
  TextEditingController? minBudgetController;
  TextEditingController? maxBudgetController;
  TextEditingController? companyNameController;
  TextEditingController? carpetAreaController;
  TextEditingController? saleableAreaAreaController;
  TextEditingController? builtUpAreaController;
  TextEditingController? notesController;
  TextEditingController? referralNameController;
  TextEditingController? referralPhoneController;
  TextEditingController? designationController;
  TextEditingController? propertyAreaController;
  TextEditingController? netAreaController;
  TextEditingController? unitNumberOrNameController;
  TextEditingController? clusterNameController;
  TextEditingController? executiveNameController;
  TextEditingController? executivePhoneController;
  TextEditingController? referralEmailController;

  //private fields
  List<MasterPropertyTypeModel>? _masterPropertyTypes;
  AddLeadModel _addLeadModel = AddLeadModel(enquiry: CreateLeadEnquiryModel());
  List<LeadSubSourceEntity> _allLeadSource = [];
  GetLeadEntity? getLeadEntity;
  bool isEditing = false;
  List<GetAllUsersModel?>? _allUsers = [];
  GlobalSettingModel? globalSettings;

  //DI
  final MasterDataRepository _masterDataRepository;
  final UsersDataRepository _usersDataRepository;
  final AddLeadUseCase _addLeadUseCase;
  final LeadsRepository _leadsRepository;
  final UpdateLeadUseCase _updateLeadUseCase;
  final GetPropertyNameWithIdUseCase _getPropertyNameWithIdUseCase;
  final GetProjectNameWithIdUseCase _getProjectNameWithIdUseCase;
  final GetLeadSubSourceUseCase _getLeadSubSourceUseCase;
  final GetLeadByContactNoUseCase _getLeadByContactNoUseCase;
  final GlobalSettingRepository _globalSettingRepository;
  final GetChannelPartnerNamesUseCase _getChannelPartnerNamesUseCase;
  final AppAnalysisRepository _appAnalysisRepository;
  final CheckLeadAssignedByLeadIdUseCase _checkLeadAssignedByLeadIdUseCase;

  CustomAddLeadBloc(
    this._masterDataRepository,
    this._usersDataRepository,
    this._addLeadUseCase,
    this._leadsRepository,
    this._getLeadSubSourceUseCase,
    this._getProjectNameWithIdUseCase,
    this._getPropertyNameWithIdUseCase,
    this._updateLeadUseCase,
    this._getLeadByContactNoUseCase,
    this._globalSettingRepository,
    this._getChannelPartnerNamesUseCase,
    this._appAnalysisRepository,
    this._checkLeadAssignedByLeadIdUseCase,
  ) : super(const CustomAddLeadState()) {
    on<AddLeadInitialEvent>(_onAddLeadInitial);
    on<ToggleEnquiredForEvent>(_onToggleEnquiredFor);
    on<TogglePropertyTypeEvent>(_onTogglePropertyType);
    on<ToggleFurnishStatusEvent>(_onToggleFurnishStatus);
    on<ToggleOfferingTypeEvent>(_onToggleOfferingType);
    on<TogglePropertySubTypeEvent>(_onTogglePropertySubType);
    on<ToggleSubTypesExpandedEvent>(_onToggleSubTypesExpanded);
    on<ToggleProfessionEvent>(_onToggleProfession);
    on<ToggleNoOfBhkExpandedEvent>(_onToggleNoOfBhkExpanded);
    on<ToggleNoOfBhkEvent>(_onToggleNoOfBhk);
    on<ToggleBedsEvent>(_onToggleBeds);
    on<ToggleBathsEvent>(_onToggleBaths);
    on<CreateLeadEvent>(_onCreateLead);
    on<SelectLeadSourceEvent>(_onSelectLeadSource);
    on<SelectLeadSubSourceEvent>(_onSelectLeadSubSource);
    on<SelectAgencyNameEvent>(_onSelectAgencyName);
    on<SelectFloorEvent>(_onSelectFloor);
    on<SelectCarpetAreaEvent>(_onSelectCarpetArea);
    on<SelectProjectsEvent>(_onSelectProjects);
    on<SelectPropertiesEvent>(_onSelectProperties);
    on<RemoveProjectsEvent>(_onRemoveProjects);
    on<RemovePropertyEvent>(_onRemoveProperty);
    on<RemoveAgencyNameEvent>(_onRemoveAgencyName);
    on<ResetStateEvent>(_onResetState);
    on<ToggleReferralFieldsEvent>(_onToggleReferralFields);
    on<ToggleAltPhoneFieldEvent>(_onToggleAltPhoneField);
    on<ToggleEmailFieldEvent>(_onToggleEmailField);
    on<TogglePossessionDateEvent>(_onTogglePossessionDate);
    on<SelectPossessionDateEvent>(_onSelectPossessionDate);
    on<SelectAssignedUserEvent>(_onSelectAssignedUser);
    on<SelectSecondaryUserEvent>(_onSelectSecondaryUser);
    on<SelectSourcingManagerEvent>(_onSelectSourcingManager);
    on<SelectClosingManagerEvent>(_onSelectClosingManager);
    on<AddLocationEvent>(_onAddLocation);
    on<RemoveLocationEvent>(_onRemoveLocation);
    on<RemoveCustomerLocationEvent>(_onRemoveCustomerLocation);
    on<CheckLeadContactAlreadyExistsEvent>(_onCheckLeadContactAlreadyExists);
    on<CheckAltContactAlreadyExistsEvent>(_onCheckAltContactAlreadyExists);
    on<OnLeadContactChangedEvent>(_onOnLeadContactChanged);
    on<OnAltContactChangedEvent>(_onOnAltContactChanged);
    on<OnReferralContactChangedEvent>(_onOnReferralContactChanged);
    on<PickContactEvent>(_onPickContact);
    on<SelectCurrency>(_onCurrencySelect);
    on<SelectChannelPartnerEvent>(_onSelectedChannelPartners);
    on<RemoveChannelPartnerNameEvent>(_onRemoveChannelPartnerName);
    on<AddCustomerLocationEvent>(_onAddCustomerLocation);
    on<SelectCampaignNameEvent>(_onSelectCampaignName);
    on<RemoveCampaignNameEvent>(_onRemoveCampaignName);
    on<SelectLeadNationalityEvent>(_onSelectLeadNationalityEvent);
    on<SelectSaleableAreaEvent>(_onSelectSaleableAreaEvent);
    on<SelectBuiltUpAreaEvent>(_onSelectBuiltUpAreaEvent);
    on<SelectPropertyAreaEvent>(_onSelectPropertyAreaEvent);
    on<SelectNetAreaEvent>(_onSelectNetAreaEvent);
    on<SelectPurposeEvent>(_onSelectPurpose);
    on<AssignedToLoggedInUser>(_onAssignedToLoggedInUser);
    on<OnExecutiveContactChangedEvent>(_onExecutiveContactChanged);
    on<SelectPossessionType>(_onSelectPossessionType);
  }

  void initTextController() {
    leadNameController = TextEditingController();
    phoneController = TextEditingController();
    altPhoneController = TextEditingController();
    emailController = TextEditingController();
    minBudgetController = TextEditingController();
    maxBudgetController = TextEditingController();
    companyNameController = TextEditingController();
    carpetAreaController = TextEditingController();
    saleableAreaAreaController = TextEditingController();
    builtUpAreaController = TextEditingController();
    notesController = TextEditingController();
    referralNameController = TextEditingController();
    referralPhoneController = TextEditingController();
    designationController = TextEditingController();
    unitNumberOrNameController = TextEditingController();
    netAreaController = TextEditingController();
    propertyAreaController = TextEditingController();
    clusterNameController = TextEditingController();
    executivePhoneController = TextEditingController();
    executiveNameController = TextEditingController();
    referralEmailController = TextEditingController();
  }

  void disposeTextController() {
    leadNameController?.dispose();
    phoneController?.dispose();
    altPhoneController?.dispose();
    emailController?.dispose();
    minBudgetController?.dispose();
    maxBudgetController?.dispose();
    companyNameController?.dispose();
    carpetAreaController?.dispose();
    saleableAreaAreaController?.dispose();
    builtUpAreaController?.dispose();
    notesController?.dispose();
    referralNameController?.dispose();
    referralPhoneController?.dispose();
    designationController?.dispose();
    unitNumberOrNameController?.dispose();
    netAreaController?.dispose();
    propertyAreaController?.dispose();
    clusterNameController?.dispose();
    executivePhoneController?.dispose();
    executiveNameController?.dispose();
    referralEmailController?.dispose();
  }

  FutureOr<void> _onAddLeadInitial(AddLeadInitialEvent event, Emitter<CustomAddLeadState> emit) async {
    getLeadEntity = event.getLeadEntity;
    isEditing = getLeadEntity != null && (getLeadEntity?.id.isNotNullOrEmpty() ?? false);
    _addLeadModel = AddLeadModel(enquiry: CreateLeadEnquiryModel(), scheduledDate: getLeadEntity?.scheduledDate, assignedFrom: getLeadEntity?.assignedFromUser?.id);
    globalSettings = await _globalSettingRepository.getGlobalSettings();
    emit(state.copyWith(showDialogProgress: isEditing, defaultCountryCode: (globalSettings?.hasInternationalSupport ?? false) ? globalSettings!.countries?.firstOrNull?.defaultCallingCode : "+971", globalSettingModel: globalSettings));
    _allUsers = await _usersDataRepository.getAssignUser();
    await _initRemoteData(emit);
    _initEnquiredFor(emit);
    _initPropertyTypes(emit);
    _initProfessions(emit);
    _initNationality(emit);
    _initOfferingType(emit);
    _initPurpose(emit);
    _initPossessionType(emit);
    if (isEditing) await _initInitialData(emit);
    emit(state.copyWith(
      pageState: PageState.initial,
      globalSettingModel: globalSettings,
      isAltPhoneFieldVisible: isEditing ? altPhoneController?.text.isNotEmpty : state.isAltPhoneFieldVisible,
      isEmailFieldVisible: isEditing ? emailController?.text.isNotEmpty : state.isEmailFieldVisible,
      isReferralDetailsVisible: (referralPhoneController?.text.isNotEmpty ?? false) || (referralNameController?.text.isNotEmpty ?? false),
      isPossessionDateVisible: getLeadEntity?.enquiry?.possessionDate != null,
      possessionDate: getLeadEntity?.enquiry?.possessionDate?.toUserTimeZone(),
    ));
    emit(state.copyWith(showDialogProgress: false));
  }

  void _initEnquiredFor(Emitter<CustomAddLeadState> emit) {
    final selectedEnquiredTypes = getLeadEntity?.enquiry?.enquiryTypes?.whereNotNull();
    final enquiryTypes = EnquiryType.values.where((type) => type != EnquiryType.none).map((type) => ItemSimpleModel<EnquiryType>(title: type.description, value: type)).toList();
    emit(state.copyWith(enquiredFor: enquiryTypes));
    if (selectedEnquiredTypes != null) {
      for (var element in selectedEnquiredTypes) {
        final selectedEnquiry = enquiryTypes.firstWhereOrNull((enquiry) => enquiry.value == element);
        add(ToggleEnquiredForEvent(selectedEnquiry));
      }
    }
  }

  void _initPropertyTypes(Emitter<CustomAddLeadState> emit) {
    final initSelectedPropertyType = getLeadEntity?.enquiry?.propertyType;
    final propertyTypes = PropertyType.values.map((type) => ItemSimpleModel<PropertyType>(title: type.description, value: type, description: type.baseId)).toList();
    emit(state.copyWith(propertyTypes: propertyTypes));
    if (initSelectedPropertyType != null) {
      final selectedPropertyType = propertyTypes.firstWhereOrNull((element) => element.description == initSelectedPropertyType.id);
      if (selectedPropertyType != null) add(TogglePropertyTypeEvent(selectedPropertyType));
    }
  }

  void _initFurnishStatus(Emitter<CustomAddLeadState> emit) {
    final initSelectedFurnishStatus = getLeadEntity?.enquiry?.furnished;
    final furnishStatuses = FurnishStatus.values.where((type) => type != FurnishStatus.none).map((type) => ItemSimpleModel<FurnishStatus>(title: type.description, value: type, description: type.value.toString())).toList();
    emit(state.copyWith(furnished: furnishStatuses));
    if (initSelectedFurnishStatus != null) {
      final selectedFurnishStatus = furnishStatuses.firstWhereOrNull((element) => element.value?.value == initSelectedFurnishStatus);
      if (selectedFurnishStatus != null) add(ToggleFurnishStatusEvent(selectedFurnishStatus));
    }
  }

  void _initOfferingType(Emitter<CustomAddLeadState> emit) {
    final initSelectedOfferingType = getLeadEntity?.enquiry?.offerType;
    final offeringTypes = OfferingType.values.where((type) => type != OfferingType.none).map((type) => ItemSimpleModel<OfferingType>(title: type.description, value: type, description: type.value.toString())).toList();
    emit(state.copyWith(offerType: offeringTypes));
    if (initSelectedOfferingType != null) {
      final selectedOfferingType = offeringTypes.firstWhereOrNull((element) => element.value?.value == initSelectedOfferingType);
      if (selectedOfferingType != null) add(ToggleOfferingTypeEvent(selectedOfferingType));
    }
  }

  void _initProfessions(Emitter<CustomAddLeadState> emit) {
    final initSelectedProfession = getLeadEntity?.profession;
    final professions = Profession.values.where((profession) => profession != Profession.none).map((profession) => ItemSimpleModel<Profession>(title: profession.description, value: profession)).toList();
    emit(state.copyWith(professions: professions));
    if (initSelectedProfession != null) add(ToggleProfessionEvent(professions.firstWhereOrNull((element) => element.value == initSelectedProfession)));
  }

  Future<void> _initRemoteData(Emitter<CustomAddLeadState> emit) async {
    try {
      _masterPropertyTypes = await _masterDataRepository.getCustomPropertyTypes(isCustomFormEnabled: true);
      await Future.wait([
        _initMasterAreaUnits(emit),
        _initMasterLeadSource(emit),
        _initProperties(emit),
        _initProjects(emit),
        _initAgencyNames(emit),
        _initCampaignNames(emit),
        _initAssignToUsers(emit),
        _initCurrencies(emit),
        _initChannelPartnerNames(emit),
        _initClosingManager(emit),
        _initSourcingManager(emit),
      ]);
    } catch (exception, stackTrace) {
      exception.logException(stackTrace);
    }
  }

  Future<void> _initChannelPartnerNames(Emitter<CustomAddLeadState> emit) async {
    try {
      final initSelectedChannelPartner = getLeadEntity?.channelPartners;
      final channelPartnersResponse = await _getChannelPartnerNamesUseCase(NoParams());
      channelPartnersResponse.fold(
        (failure) => null,
        (success) {
          if (success != null && success.isNotEmpty) {
            final selectedAgencyNames = initSelectedChannelPartner?.whereNotNull().map((e) => e.firmName).toList();
            final channelPartners = success.map((name) => SelectableItem<String>(title: name, isSelected: selectedAgencyNames?.contains(name) ?? false, value: name)).toList();
            final selectedChannelPartners = channelPartners.where((element) => element.isSelected).toList();
            if (selectedChannelPartners.isNotEmpty) add(SelectChannelPartnerEvent(selectedChannelPartners));
            emit(state.copyWith(channelPartners: channelPartners, selectedChannelPartners: selectedChannelPartners));
          }
        },
      );
    } catch (ex, stackTrace) {
      ex.logException(stackTrace);
    }
  }

  Future<void> _initCurrencies(Emitter<CustomAddLeadState> emit) async {
    try {
      final countries = globalSettings?.countries;
      List<SelectableItem<String>> allCurrencies = [];
      SelectableItem<String>? selectedCurrency;
      countries?.firstOrNull?.currencies?.forEach((item) => allCurrencies.add(SelectableItem<String>(title: item.currency ?? '', value: item.currency)));
      if (countries != null) {
        var defaultSymbol = getLeadEntity?.enquiry?.currency ?? globalSettings?.countries?.firstOrNull?.defaultCurrency ?? "AED";
        selectedCurrency = allCurrencies.firstWhereOrNull((element) => element.value == defaultSymbol);
        _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(currency: selectedCurrency?.value ?? 'AED'));
      }
      emit(state.copyWith(currencies: allCurrencies, selectedCurrency: selectedCurrency ?? SelectableItem<String>(title: 'AED', value: 'AED')));
    } catch (ex, stackTrace) {
      ex.logException(stackTrace);
    }
  }

  Future<void> _initMasterAreaUnits(Emitter<CustomAddLeadState> emit) async {
    final initSelectedCarpetArea = (getLeadEntity?.enquiry?.carpetAreaUnitId.isNullOrEmptyGuid() ?? true) ? (globalSettings?.defaultValues?["masterAreaUnit"]) : getLeadEntity?.enquiry?.carpetAreaUnitId;
    final initSelectedSaleableArea = (getLeadEntity?.enquiry?.saleableAreaUnitId.isNullOrEmptyGuid() ?? true) ? (globalSettings?.defaultValues?["masterAreaUnit"]) : getLeadEntity?.enquiry?.saleableAreaUnitId;
    final initSelectedBuiltUpArea = (getLeadEntity?.enquiry?.builtUpAreaUnitId.isNullOrEmptyGuid() ?? true) ? (globalSettings?.defaultValues?["masterAreaUnit"]) : getLeadEntity?.enquiry?.builtUpAreaUnitId;
    final initSelectedNetArea = (getLeadEntity?.enquiry?.netAreaUnitId?.isNullOrEmptyGuid() ?? true) ? (globalSettings?.defaultValues?["masterAreaUnit"]) : getLeadEntity?.enquiry?.netAreaUnitId;
    final initSelectedPropertyArea = (getLeadEntity?.enquiry?.propertyAreaUnitId.isNullOrEmptyGuid() ?? true) ? (globalSettings?.defaultValues?["masterAreaUnit"]) : getLeadEntity?.enquiry?.propertyAreaUnitId;
    var masterAreaUnits = await _masterDataRepository.getAreaUnits();
    if (masterAreaUnits?.isNotEmpty ?? false) {
      final carpetAreaUnits = masterAreaUnits!.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit, isSelected: areaUnit?.id == initSelectedCarpetArea)).toList();
      final saleableAreaUnits = masterAreaUnits!.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit, isSelected: areaUnit?.id == initSelectedSaleableArea)).toList();
      final builtUpAreaUnits = masterAreaUnits!.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit, isSelected: areaUnit?.id == initSelectedBuiltUpArea)).toList();
      final netAreaUnits = masterAreaUnits.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit, isSelected: areaUnit?.id == initSelectedNetArea)).toList();
      final propertyAreaUnits = masterAreaUnits.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit, isSelected: areaUnit?.id == initSelectedPropertyArea)).toList();
      emit(state.copyWith(carpetAreas: carpetAreaUnits, saleableAreas: saleableAreaUnits, builtUpAreas: builtUpAreaUnits, errorMessage: '', netAreas: netAreaUnits, propertyAreas: propertyAreaUnits));
      final selectedCarpetAea = carpetAreaUnits.firstWhereOrNull((element) => element.isSelected);
      final selectedSaleableArea = saleableAreaUnits.firstWhereOrNull((element) => element.isSelected);
      final selectedBuiltUpArea = builtUpAreaUnits.firstWhereOrNull((element) => element.isSelected);
      final selectedNetArea = netAreaUnits.firstWhereOrNull((element) => element.isSelected);
      final selectedPropertyArea = propertyAreaUnits.firstWhereOrNull((element) => element.isSelected);
      if (selectedSaleableArea != null) {
        add(SelectSaleableAreaEvent(selectedSaleableArea));
      }
      if (selectedBuiltUpArea != null) {
        add(SelectBuiltUpAreaEvent(selectedBuiltUpArea));
      }
      if (selectedNetArea != null) {
        add(SelectNetAreaEvent(selectedNetArea));
      }
      if (selectedPropertyArea != null) {
        add(SelectPropertyAreaEvent(selectedPropertyArea));
      }
      if (selectedCarpetAea != null) {
        add(SelectCarpetAreaEvent(selectedCarpetAea));
      }
    }
  }

  void _initNationality(Emitter<CustomAddLeadState> emit) {
    try {
      final initialSelectedCountry = getLeadEntity?.nationality;

      final allCountries = countryCodes
          .map(
            (e) => SelectableItem<Country>(title: e.name, value: e, isSelected: e.name == initialSelectedCountry),
          )
          .toList();
      emit(state.copyWith(nationality: allCountries));
      final selectedCountry = allCountries.firstWhereOrNull(
        (element) => element.isSelected,
      );
      if (selectedCountry != null) add(SelectLeadNationalityEvent(selectedCountry));
    } catch (ex, stackTrace) {
      ex.logException(stackTrace);
    }
  }

  Future<void> _initMasterLeadSource(Emitter<CustomAddLeadState> emit) async {
    final initialSelectedLeadSource = getLeadEntity?.enquiry?.leadSource;
    final leadSource = await _getLeadSubSourceUseCase(NoParams());
    leadSource.fold((failure) => null, (success) {
      if (success != null && success.isNotEmpty) {
        final allLeadSource = success
            .where((element) => element.leadSourceEntity?.isEnabled ?? false)
            .map((e) => SelectableItem<int>(
                  title: e.leadSourceEntity?.displayName ?? "",
                  value: e.leadSourceEntity?.value,
                  isSelected: (e.leadSourceEntity?.value != null) ? initialSelectedLeadSource?.value == e.leadSourceEntity?.value : false,
                ))
            .toList();
        _allLeadSource = success;
        emit(state.copyWith(
          leadSource: allLeadSource,
          errorMessage: '',
        ));
        final selectedLeadSource = allLeadSource.firstWhereOrNull((element) => element.isSelected);
        if (selectedLeadSource != null) add(SelectLeadSourceEvent(selectedLeadSource));
      }
    });
  }

  Future<void> _initAgencyNames(Emitter<CustomAddLeadState> emit) async {
    final initialSelectedAgencyName = getLeadEntity?.agencies?.map((e) => e.name).whereNotNull();
    var agencyNames = await _masterDataRepository.getAgencyNames();
    if (agencyNames?.isNotEmpty ?? false) {
      final agenciesNames = agencyNames!.map((name) => SelectableItem<String>(title: name, value: name, isSelected: initialSelectedAgencyName?.contains(name) ?? false)).toList();
      final selectedAgencyNames = agenciesNames.where((element) => element.isSelected).toList();
      emit(state.copyWith(agencyNames: agenciesNames, errorMessage: '', selectedAgencyNames: selectedAgencyNames));
      _addLeadModel = _addLeadModel.copyWith(agencies: selectedAgencyNames.map((e) => DTOWithNameModel(name: e.title)).toList());
    }
  }

  Future<void> _initFloors(Emitter<CustomAddLeadState> emit) async {
    List<SelectableItem<String>> floors = [
      SelectableItem(title: 'upper basement', value: 'upper basement'),
      SelectableItem(title: 'lower basement', value: 'lower basement'),
      SelectableItem(title: 'ground floor', value: 'ground floor'),
    ];

    for (int i = 1; i <= 200; i++) {
      floors.add(SelectableItem(title: '$i', value: '$i'));
    }
    final initialSelectedFloors = getLeadEntity?.enquiry?.floors?.map((e) => e).whereNotNull();
    if (initialSelectedFloors != null) {
      floors.forEach((floor) {
        if (initialSelectedFloors.contains(floor.title)) {
          floor.isSelected = true;
        }
      });
    }
    final selectedFloors = floors.where((element) => element.isSelected).toList();
    final floorsNumber = floors.where((element) => element.isSelected).map((e) => e.title).toList();
    emit(state.copyWith(floors: floors, errorMessage: '', selectedFloors: selectedFloors));
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(floors: floorsNumber));
  }

  Future<void> _initAssignToUsers(Emitter<CustomAddLeadState> emit) async {
    final initSelectedPrimaryUserId = getLeadEntity?.assignedUser?.id;
    final initSelectedSecondaryUserId = getLeadEntity?.secondaryUser?.id;
    if (_allUsers?.isNotEmpty ?? false) {
      final currentUser = _usersDataRepository.getLoggedInUser();
      var assignToUsers = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id, isSelected: initSelectedPrimaryUserId == user?.id)).toList();
      //adding disabled users
      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => assignToUsers.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isSelected: initSelectedPrimaryUserId == disabledUsers?.id, isEnabled: false)));
      final assignSecondaryUsers = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id, isSelected: initSelectedSecondaryUserId == user?.id)).toList();
      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => assignSecondaryUsers.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isSelected: initSelectedPrimaryUserId == disabledUsers?.id, isEnabled: false)));
      assignToUsers.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId, isSelected: currentUser?.userId == initSelectedPrimaryUserId));
      assignSecondaryUsers.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId, isSelected: currentUser?.userId == initSelectedSecondaryUserId));
      emit(state.copyWith(assignToUsers: assignToUsers, secondaryUsers: assignSecondaryUsers, errorMessage: ''));
      final selectedPrimaryUser = assignToUsers.firstWhereOrNull((element) => element.isSelected);
      final selectedSecondaryUser = assignSecondaryUsers.firstWhereOrNull((element) => element.isSelected);
      if (selectedPrimaryUser != null) add(SelectAssignedUserEvent(selectedPrimaryUser));
      if (selectedSecondaryUser != null) add(SelectSecondaryUserEvent(selectedSecondaryUser));
    }
  }

  Future<void> _initSourcingManager(Emitter<CustomAddLeadState> emit) async {
    final initSourcingManager = getLeadEntity?.sourcingManager?.id;
    if (_allUsers?.isNotEmpty ?? false) {
      final currentUser = _usersDataRepository.getLoggedInUser();
      var sourcingManagerUsers = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id, isSelected: initSourcingManager == user?.id)).toList();
      //adding disabled users
      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => sourcingManagerUsers.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isSelected: initSourcingManager == disabledUsers?.id, isEnabled: false)));
      sourcingManagerUsers.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId, isSelected: currentUser?.userId == initSourcingManager));
      emit(state.copyWith(sourcingManager: sourcingManagerUsers, errorMessage: ''));
      final selectedSourcingManager = sourcingManagerUsers.firstWhereOrNull((element) => element.isSelected);
      if (selectedSourcingManager != null) add(SelectSourcingManagerEvent(selectedSourcingManager));
    }
  }

  Future<void> _initClosingManager(Emitter<CustomAddLeadState> emit) async {
    final initClosingManager = getLeadEntity?.closingManagerUser?.id;
    if (_allUsers?.isNotEmpty ?? false) {
      final currentUser = _usersDataRepository.getLoggedInUser();
      var closingManagerUsers = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id, isSelected: initClosingManager == user?.id)).toList();

      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => closingManagerUsers.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isSelected: initClosingManager == disabledUsers?.id, isEnabled: false)));
      closingManagerUsers.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId, isSelected: currentUser?.userId == initClosingManager));
      emit(state.copyWith(closingManager: closingManagerUsers, errorMessage: ''));
      final selectedClosingManager = closingManagerUsers.firstWhereOrNull((element) => element.isSelected);
      if (selectedClosingManager != null) add(SelectClosingManagerEvent(selectedClosingManager));
    }
  }

  Future<void> _initCampaignNames(Emitter<CustomAddLeadState> emit) async {
    final initialSelectedCampaignNames = getLeadEntity?.campaigns?.map((e) => e.name).nonNulls;
    var campaignNames = await _leadsRepository.getCampaignNames();
    if (campaignNames?.isNotEmpty ?? false) {
      final campaignsNames = campaignNames!.map((name) => SelectableItem<String>(title: name, value: name, isSelected: initialSelectedCampaignNames?.contains(name) ?? false)).toList();
      final selectedCamapaignNames = campaignsNames.where((element) => element.isSelected).toList();
      emit(state.copyWith(campaignNames: campaignsNames, errorMessage: '', selectedCampaignNames: selectedCamapaignNames));
      _addLeadModel = _addLeadModel.copyWith(campaigns: selectedCamapaignNames.map((e) => DTOWithNameModel(name: e.title)).toList());
    }
  }

  Future<void> _initProperties(Emitter<CustomAddLeadState> emit) async {
    final initialSelectedPropertiesId = getLeadEntity?.properties?.map((e) => e.id).nonNulls.toSet() ?? {};
    final properties = await _getPropertyNameWithIdUseCase(NoParams());
    properties.fold((failure) => null, (success) {
      if (success != null && success.isNotEmpty) {
        final allProperties = success.map((e) => SelectableItem<String>(title: e.title ?? "", value: e.id, isSelected: initialSelectedPropertiesId.contains(e.id))).toList();
        final selectedProperties = allProperties.where((element) => element.isSelected).toList();
        emit(state.copyWith(properties: allProperties, errorMessage: '', selectedProperties: selectedProperties));
        _addLeadModel = _addLeadModel.copyWith(propertiesList: selectedProperties.map((e) => e.title).toList());
      }
    });
  }

  Future<void> _initProjects(Emitter<CustomAddLeadState> emit) async {
    final initialSelectedProjectsId = getLeadEntity?.projects?.map((e) => e.id).whereNotNull();
    final projects = await _getProjectNameWithIdUseCase(NoParams());
    projects.fold((failure) => null, (success) {
      if (success != null && success.isNotEmpty) {
        final allProjects = success.map((e) => SelectableItem<String>(title: e.name ?? "", value: e.id, isSelected: initialSelectedProjectsId?.contains(e.id) ?? false)).toList();
        final selectedProjects = allProjects.where((element) => element.isSelected).toList();
        emit(state.copyWith(projects: allProjects, errorMessage: '', selectedProjects: selectedProjects));
        _addLeadModel = _addLeadModel.copyWith(projectsList: selectedProjects.map((e) => e.title).toList());
      }
    });
  }

  FutureOr<void> _onToggleEnquiredFor(ToggleEnquiredForEvent event, Emitter<CustomAddLeadState> emit) async {
    if (event.selectedEnquiredFor == null) return;
    final updatedEnquiredFor = state.enquiredFor.map((e) => e.value == event.selectedEnquiredFor?.value ? e.copyWith(isSelected: !e.isSelected) : e).toList();
    emit(state.copyWith(enquiredFor: updatedEnquiredFor, errorMessage: ''));
    final selectedEnquiryTypes = state.enquiredFor.where((element) => element.isSelected).map((e) => e.value).whereNotNull().toList();
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(enquiryTypes: selectedEnquiryTypes));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonEnquiredForClick);
  }

  FutureOr<void> _onTogglePropertyType(TogglePropertyTypeEvent event, Emitter<CustomAddLeadState> emit) async {
    final updatedPropertyTypes = state.propertyTypes.map((e) => e.description == event.selectedPropertyType.description ? e.copyWith(isSelected: !e.isSelected) : e.copyWith(isSelected: false)).toList();
    if (event.selectedPropertyType.isSelected) {
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(propertyTypeId: StringConstants.emptyGuidId, propertyTypeIds: [], bhks: [], bhkTypes: [], beds: []));
      emit(state.copyWith(propertyTypes: updatedPropertyTypes, propertySubTypes: [], errorMessage: '', bhkTypes: [], beds: [], noOfBhk: [], baths: [], furnished: []));
    } else {
      final propertySubTypes = initPropertySubTypes(event.selectedPropertyType.description ?? '', emit);
      emit(state.copyWith(propertyTypes: updatedPropertyTypes, propertySubTypes: propertySubTypes ?? [], floors: [], errorMessage: ''));
      _initBhkAndBhkTypes(emit);
    }
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonPropertyTypeClick);
  }

  FutureOr<void> _onToggleFurnishStatus(ToggleFurnishStatusEvent event, Emitter<CustomAddLeadState> emit) async {
    final updatedFurnishStatus = state.furnished.map((e) => e.description == event.selectedFurnishStatus.description ? e.copyWith(isSelected: !e.isSelected) : e.copyWith(isSelected: false)).toList();
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(furnished: updatedFurnishStatus.firstOrNull?.value?.value ?? 0));
    emit(state.copyWith(
      furnished: updatedFurnishStatus,
      errorMessage: '',
    ));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonFurnishedClick);
  }

  FutureOr<void> _onToggleOfferingType(ToggleOfferingTypeEvent event, Emitter<CustomAddLeadState> emit) async {
    final updatedOfferingType = state.offerType.map((e) => e.description == event.selectedOfferingType.description ? e.copyWith(isSelected: !e.isSelected) : e.copyWith(isSelected: false)).toList();
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(offerType: event.selectedOfferingType.value?.value ?? 0));
    emit(state.copyWith(
      offerType: updatedOfferingType,
      errorMessage: '',
    ));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonOfferingTypeClick);
  }

  List<ItemSimpleModel<String>> initPropertySubTypes(String propertyTypeId, [Emitter<CustomAddLeadState>? emit]) {
    if (_masterPropertyTypes == null) return [];
    final initSelectedPropertySubTypesId = getLeadEntity?.enquiry?.propertyTypes?.map((p) => p.childType?.id).toList();
    final propertySubTypes = _masterPropertyTypes!
        .where((type) => type.id == propertyTypeId && type.childTypes != null)
        .expand((type) => type.childTypes!)
        .map(
          (subType) => ItemSimpleModel<String>(title: subType.displayName ?? "", value: subType.id, description: subType.baseId),
        )
        .toList();
    if (initSelectedPropertySubTypesId?.isNotEmpty ?? false) {
      final bool shouldExpand = propertySubTypes.any((element) {
        final isSelected = initSelectedPropertySubTypesId!.contains(element.value);
        final index = propertySubTypes.indexOf(element);
        return isSelected && index > 3;
      });

      if (emit != null && shouldExpand) {
        emit(state.copyWith(isSubTypesExpanded: true));
      }

      final selectedPropertySubTypes = propertySubTypes.where((element) => initSelectedPropertySubTypesId?.contains(element.value) ?? false).toList();
      for (var selectedPropertySubType in selectedPropertySubTypes) {
        add(TogglePropertySubTypeEvent(selectedPropertySubType));
      }
    }
    return propertySubTypes;
  }

  FutureOr<void> _onSelectLeadSource(SelectLeadSourceEvent event, Emitter<CustomAddLeadState> emit) async {
    emit(state.copyWith(selectedLeadSource: event.selectedLeadSource, errorMessage: ''));
    _initLeadSubSource(event.selectedLeadSource.value, emit);
    final selectedSource = event.selectedLeadSource.isSelected ? event.selectedLeadSource.value : null;
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(leadSource: getEnumFromValue(LeadSource.values, selectedSource)));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonLeadSourceClick);
  }

  void _initLeadSubSource(int? value, Emitter<CustomAddLeadState> emit) {
    final initSelectedLeadSubSource = getLeadEntity?.enquiry?.subSource;
    final subSource = _allLeadSource.where((element) => element.leadSourceEntity?.isEnabled ?? false).firstWhereOrNull((e) => e.leadSourceEntity?.value == value)?.subSources?.map((e) => SelectableItem<String>(title: e, value: e, isSelected: initSelectedLeadSubSource == e)).toList();
    emit(state.copyWith(leadSubSource: subSource ?? [], selectedLeadSubSource: null, updateSubSource: false, errorMessage: ''));
    final selectedSubSource = subSource?.firstWhereOrNull((element) => element.isSelected);
    if (selectedSubSource != null) add(SelectLeadSubSourceEvent(selectedSubSource));
  }

  FutureOr<void> _onSelectLeadSubSource(SelectLeadSubSourceEvent event, Emitter<CustomAddLeadState> emit) {
    emit(state.copyWith(selectedLeadSubSource: event.selectedLeadSubSource, errorMessage: ''));
    final selectedSubSource = event.selectedLeadSubSource.isSelected ? event.selectedLeadSubSource.value : null;
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(subSource: selectedSubSource));
  }

  FutureOr<void> _onTogglePropertySubType(TogglePropertySubTypeEvent event, Emitter<CustomAddLeadState> emit) async {
    final updatedPropertySubTypes = state.propertySubTypes.map((e) => e.value == event.selectedPropertySubType.value ? e.copyWith(isSelected: !e.isSelected) : e).toList();
    emit(state.copyWith(propertySubTypes: updatedPropertySubTypes, errorMessage: ''));
    final propertyTypeIds = state.propertySubTypes.where((element) => element.isSelected).map((e) => e.value).nonNulls.toList();
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(propertyTypeIds: propertyTypeIds));
    _initBhkAndBhkTypes(emit);
    _initFurnishStatus(emit);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonPropertySubTypeClick);
  }

  FutureOr<void> _onToggleNoOfBhk(ToggleNoOfBhkEvent event, Emitter<CustomAddLeadState> emit) async {
    final updatedNoOfBhk = state.noOfBhk.map((e) => e.value == event.selectedNoOfBhk.value ? e.copyWith(isSelected: !e.isSelected) : e).toList();
    emit(state.copyWith(noOfBhk: updatedNoOfBhk, errorMessage: ''));
    final selectedBhks = state.noOfBhk.where((element) => element.isSelected).map((bhk) => bhk.value).whereNotNull().toList();
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(bhks: selectedBhks));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonBRClick);
  }

  FutureOr<void> _onToggleBeds(ToggleBedsEvent event, Emitter<CustomAddLeadState> emit) {
    final updatedNoOfBeds = state.beds.map((e) => e.value == event.selectedBeds.value ? e.copyWith(isSelected: !e.isSelected) : e).toList();
    emit(state.copyWith(beds: updatedNoOfBeds, errorMessage: ''));
    final selectedBeds = state.beds.where((element) => element.isSelected).map((e) => e.value).whereNotNull().toList();
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(beds: selectedBeds));
  }

  FutureOr<void> _onToggleBaths(ToggleBathsEvent event, Emitter<CustomAddLeadState> emit) async {
    final updatedBaths = state.baths.map((e) => e.value == event.selectedBaths.value ? e.copyWith(isSelected: !e.isSelected) : e).toList();
    emit(state.copyWith(baths: updatedBaths, errorMessage: ''));
    final selectedBaths = state.baths.where((element) => element.isSelected).map((bath) => bath.value).whereNotNull().toList();
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(baths: selectedBaths));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonBathsClick);
  }

  FutureOr<void> _onToggleProfession(ToggleProfessionEvent event, Emitter<CustomAddLeadState> emit) async {
    if (event.selectedProfession == null) return;
    final updatedProfession = state.professions.map((e) => e.value == event.selectedProfession!.value ? e.copyWith(isSelected: !e.isSelected) : e.copyWith(isSelected: false)).toList();
    emit(state.copyWith(professions: updatedProfession, errorMessage: ''));
    final selectedProfession = event.selectedProfession!.isSelected ? null : event.selectedProfession!.value;
    _addLeadModel = _addLeadModel.copyWith(profession: selectedProfession);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonProfessionClick);
  }

  FutureOr<void> _onSelectAgencyName(SelectAgencyNameEvent event, Emitter<CustomAddLeadState> emit) {
    emit(state.copyWith(selectedAgencyNames: event.selectedAgencyName, errorMessage: ''));
    final selectedAgencyNames = event.selectedAgencyName.map((e) => DTOWithNameModel(name: e.title)).toList();
    _addLeadModel = _addLeadModel.copyWith(agencies: selectedAgencyNames);
  }

  FutureOr<void> _onSelectFloor(SelectFloorEvent event, Emitter<CustomAddLeadState> emit) {
    emit(state.copyWith(selectedFloors: event.selectedFloor, errorMessage: ''));
    final selectedFloors = event.selectedFloor.map((e) => e.title).toList();
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(floors: selectedFloors));
  }

  FutureOr<void> _onSelectCampaignName(SelectCampaignNameEvent event, Emitter<CustomAddLeadState> emit) {
    emit(state.copyWith(selectedCampaignNames: event.selectedCampaignName, errorMessage: ''));
    final selectedCampaignNames = event.selectedCampaignName.map((e) => DTOWithNameModel(name: e.title)).toList();
    _addLeadModel = _addLeadModel.copyWith(campaigns: selectedCampaignNames);
  }

  FutureOr<void> _onRemoveCampaignName(RemoveCampaignNameEvent event, Emitter<CustomAddLeadState> emit) {
    final updatedSelectedCampaign = state.selectedCampaignNames?.whereNot((element) => element.title == event.selectedCampaignName.title).toList();
    emit(state.copyWith(selectedCampaignNames: updatedSelectedCampaign, errorMessage: ''));
    _addLeadModel.campaigns?.removeWhere((element) => element.name == event.selectedCampaignName.title);
  }

  FutureOr<void> _onRemoveAgencyName(RemoveAgencyNameEvent event, Emitter<CustomAddLeadState> emit) {
    final updatedSelectedAgency = state.selectedAgencyNames?.whereNot((element) => element.title == event.selectedAgencyName.title).toList();
    emit(state.copyWith(selectedAgencyNames: updatedSelectedAgency, errorMessage: ''));
    _addLeadModel.agencies?.removeWhere((element) => element.name == event.selectedAgencyName.title);
  }

  FutureOr<void> _onSelectCarpetArea(SelectCarpetAreaEvent event, Emitter<CustomAddLeadState> emit) async {
    if (event.selectedCarpetArea == null) return;
    emit(state.copyWith(selectedCarpetArea: event.selectedCarpetArea, errorMessage: ''));
    final selectedCarpetAreaValue = event.selectedCarpetArea!.isSelected ? event.selectedCarpetArea : null;
    _addLeadModel = _addLeadModel.copyWith(
        enquiry: _addLeadModel.enquiry?.copyWith(
      carpetAreaUnitId: selectedCarpetAreaValue?.value?.id,
      conversionFactor: selectedCarpetAreaValue?.value?.conversionFactor,
    ));
  }

  FutureOr<void> _onSelectSaleableAreaEvent(SelectSaleableAreaEvent event, Emitter<CustomAddLeadState> emit) async {
    if (event.selectedSaleableArea == null) return;
    emit(state.copyWith(selectedSaleableArea: event.selectedSaleableArea, errorMessage: ''));
    final selectedSaleableAreaValue = event.selectedSaleableArea!.isSelected ? event.selectedSaleableArea : null;
    _addLeadModel = _addLeadModel.copyWith(
        enquiry: _addLeadModel.enquiry?.copyWith(
      saleableAreaUnitId: selectedSaleableAreaValue?.value?.id,
      saleableAreaConversionFactor: selectedSaleableAreaValue?.value?.conversionFactor,
    ));
  }

  FutureOr<void> _onSelectBuiltUpAreaEvent(SelectBuiltUpAreaEvent event, Emitter<CustomAddLeadState> emit) async {
    if (event.selectedBuiltUpArea == null) return;
    emit(state.copyWith(selectedBuiltUpArea: event.selectedBuiltUpArea, errorMessage: ''));
    final selectedBuiltUpAreaValue = event.selectedBuiltUpArea!.isSelected ? event.selectedBuiltUpArea : null;
    _addLeadModel = _addLeadModel.copyWith(
        enquiry: _addLeadModel.enquiry?.copyWith(
      builtUpAreaUnitId: selectedBuiltUpAreaValue?.value?.id,
      builtUpAreaConversionFactor: selectedBuiltUpAreaValue?.value?.conversionFactor,
    ));
  }

  FutureOr<void> _onSelectPropertyAreaEvent(SelectPropertyAreaEvent event, Emitter<CustomAddLeadState> emit) async {
    if (event.selectedPropertyArea == null) return;
    emit(state.copyWith(selectedPropertyArea: event.selectedPropertyArea, errorMessage: ''));
    final selectedPropertyAreaValue = event.selectedPropertyArea!.isSelected ? event.selectedPropertyArea : null;
    _addLeadModel = _addLeadModel.copyWith(
        enquiry: _addLeadModel.enquiry?.copyWith(
      propertyAreaUnitId: selectedPropertyAreaValue?.value?.id,
      propertyAreaConversionFactor: selectedPropertyAreaValue?.value?.conversionFactor,
    ));
  }

  FutureOr<void> _onSelectNetAreaEvent(SelectNetAreaEvent event, Emitter<CustomAddLeadState> emit) async {
    if (event.selectedNetArea == null) return;
    emit(state.copyWith(selectedNetArea: event.selectedNetArea, errorMessage: ''));

    final selectedNetAreaValue = event.selectedNetArea!.isSelected ? event.selectedNetArea : null;
    _addLeadModel = _addLeadModel.copyWith(
        enquiry: _addLeadModel.enquiry?.copyWith(
      netAreaUnitId: selectedNetAreaValue?.value?.id,
      netAreaConversionFactor: selectedNetAreaValue?.value?.conversionFactor,
    ));
  }

  void _initBhkAndBhkTypes(Emitter<CustomAddLeadState> emit) async {
    final initSelectedNoOfBhk = getLeadEntity?.enquiry?.bHKs?.whereNotNull();
    final initSelectedBhkTypes = getLeadEntity?.enquiry?.beds?.whereNotNull();
    final initSelectedBaths = getLeadEntity?.enquiry?.baths?.whereNotNull();

    final selectedPropertyType = state.propertyTypes.firstWhereOrNull((element) => element.isSelected);
    final isOnlyPlotSelected = state.propertySubTypes.firstWhereOrNull((element) => element.title.toLowerCase() == "plot")?.isSelected ?? false;
    final isAnyOtherSelected = state.propertySubTypes.any((element) => element.title.toLowerCase() != "plot" && element.isSelected);

    final isOnlyPlotSelectedAndNoOthers = isOnlyPlotSelected && !isAnyOtherSelected;
    final isPropertySubTypesSelected = state.propertySubTypes.any((element) => element.isSelected);
    if (!isPropertySubTypesSelected || selectedPropertyType == null) {
      emit(state.copyWith(noOfBhk: [], bhkTypes: [], beds: [], baths: [], furnished: [], errorMessage: ''));
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(beds: [], bhks: []));
    } else if (selectedPropertyType.value == PropertyType.residential && isPropertySubTypesSelected && !isOnlyPlotSelectedAndNoOthers) {
      _initFloors(emit);
      if (!state.noOfBhk.any((element) => element.isSelected)) {
        final noOfBr = NoOfBr.values.map((br) => ItemSimpleModel<double>(title: br.description, value: br.noOfBr, isSelected: initSelectedNoOfBhk?.contains(br.noOfBr) ?? false)).toList();
        final noOfBaths = NoOfBaths.values.map((bath) => ItemSimpleModel<int>(title: bath.description, value: bath.noOfBaths, isSelected: initSelectedBaths?.contains(bath.noOfBaths) ?? false)).toList();
        final noOfBeds = Beds.values.map((type) => ItemSimpleModel<Beds>(title: type.description, value: type, isSelected: initSelectedBhkTypes?.contains(type) ?? false)).toList();
        final selectedBhkIndex = noOfBr.lastIndexWhere((element) => element.isSelected);

        emit(state.copyWith(noOfBhk: noOfBr, baths: noOfBaths, beds: noOfBeds, floors: [], isNoOfBhkExpanded: selectedBhkIndex > 3));
        final selectedBhks = state.noOfBhk.where((element) => element.isSelected).map((bhk) => bhk.value).whereNotNull().toList();
        final selectedBaths = state.baths.where((element) => element.isSelected).map((bath) => bath.value).whereNotNull().toList();
        final selectedBhkTypes = state.beds.where((element) => element.isSelected).map((e) => e.value).whereNotNull().toList();
        _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(beds: selectedBhkTypes, bhks: selectedBhks, baths: selectedBaths));
      }
    } else {
      _initFloors(emit);
      emit(state.copyWith(noOfBhk: [], baths: [], bhkTypes: [], beds: [], furnished: [], errorMessage: ''));
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(beds: [], bhks: [], baths: [], furnished: 0));
    }
  }

  FutureOr<void> _onSelectProperties(SelectPropertiesEvent event, Emitter<CustomAddLeadState> emit) async {
    emit(state.copyWith(selectedProperties: event.selectedProperties, errorMessage: ''));
    final selectedProperties = event.selectedProperties.map((e) => e.title).toList();
    _addLeadModel = _addLeadModel.copyWith(propertiesList: selectedProperties);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonSelectPropertiesClick);
  }

  FutureOr<void> _onRemoveProperty(RemovePropertyEvent event, Emitter<CustomAddLeadState> emit) {
    final updatedSelectedProperties = state.selectedProperties?.whereNot((element) => element.title == event.selectedProperty.title).toList();
    emit(state.copyWith(selectedProperties: updatedSelectedProperties, errorMessage: ''));
    _addLeadModel.propertiesList?.removeWhere((title) => title == event.selectedProperty.title);
  }

  FutureOr<void> _onSelectProjects(SelectProjectsEvent event, Emitter<CustomAddLeadState> emit) async {
    emit(state.copyWith(selectedProjects: event.selectedProjects, errorMessage: ''));
    final selectedProjects = event.selectedProjects.map((e) => e.title).toList();
    _addLeadModel = _addLeadModel.copyWith(projectsList: selectedProjects);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonSelectProjectsClick);
  }

  FutureOr<void> _onRemoveProjects(RemoveProjectsEvent event, Emitter<CustomAddLeadState> emit) {
    final updatedSelectedProjects = state.selectedProjects?.whereNot((element) => element.title == event.selectedProjects.title).toList();
    emit(state.copyWith(selectedProjects: updatedSelectedProjects, errorMessage: ''));
    _addLeadModel.projectsList?.removeWhere((title) => title == event.selectedProjects.title);
  }

  FutureOr<void> _onSelectAssignedUser(SelectAssignedUserEvent event, Emitter<CustomAddLeadState> emit) async {
    List<SelectableItem<String>>? updatedSecondaryUsers;
    if (globalSettings?.isDualOwnershipEnabled ?? false) {
      final currentUser = _usersDataRepository.getLoggedInUser();
      updatedSecondaryUsers = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id)).toList();
      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => updatedSecondaryUsers?.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isEnabled: false)));
      updatedSecondaryUsers.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId));
      updatedSecondaryUsers.removeWhere((element) => element.value == event.selectedUser.value);
    }
    emit(state.copyWith(selectedAssignedUser: event.selectedUser, errorMessage: '', secondaryUsers: updatedSecondaryUsers));
    _addLeadModel = _addLeadModel.copyWith(assignTo: event.selectedUser.value);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonAssignLeadToClick);
  }

  FutureOr<void> _onSelectSourcingManager(SelectSourcingManagerEvent event, Emitter<CustomAddLeadState> emit) async {
    List<SelectableItem<String>>? updatedSourcingManager;
    final currentUser = _usersDataRepository.getLoggedInUser();
    updatedSourcingManager = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id)).toList();
    _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => updatedSourcingManager?.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isEnabled: false)));
    updatedSourcingManager.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId));
    updatedSourcingManager.removeWhere((element) => element.value == event.selectedSourcingManager.value);
    emit(state.copyWith(selectedSourcingManager: event.selectedSourcingManager, errorMessage: ''));
    _addLeadModel = _addLeadModel.copyWith(sourcingManager: event.selectedSourcingManager.value);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonSourcingManagerClick);
  }

  FutureOr<void> _onSelectClosingManager(SelectClosingManagerEvent event, Emitter<CustomAddLeadState> emit) async {
    List<SelectableItem<String>>? updatedClosingManager;
    final currentUser = _usersDataRepository.getLoggedInUser();
    updatedClosingManager = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id)).toList();
    _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => updatedClosingManager?.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isEnabled: false)));
    updatedClosingManager.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId));
    updatedClosingManager.removeWhere((element) => element.value == event.selectedClosingManager.value);
    emit(state.copyWith(selectedClosingManager: event.selectedClosingManager, errorMessage: ''));
    _addLeadModel = _addLeadModel.copyWith(closingManager: event.selectedClosingManager.value);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonClosingManagerClick);
  }

  FutureOr<void> _onSelectSecondaryUser(SelectSecondaryUserEvent event, Emitter<CustomAddLeadState> emit) async {
    if (globalSettings?.isDualOwnershipEnabled ?? false) {
      if (state.selectedAssignedUser == null) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: "selected assign to user first"));
        return;
      }
      final currentUser = _usersDataRepository.getLoggedInUser();
      final updatedAssignedUser = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id)).toList();
      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => updatedAssignedUser.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isEnabled: false)));
      updatedAssignedUser.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId));
      updatedAssignedUser.removeWhere((element) => element.value == event.selectedUser.value);
      emit(state.copyWith(selectedSecondaryAssignedUser: event.selectedUser, errorMessage: '', assignToUsers: updatedAssignedUser));
      _addLeadModel = _addLeadModel.copyWith(secondaryUserId: event.selectedUser.value);
      await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonSecondaryAssignToClick);
    }
  }

  FutureOr<void> _onToggleSubTypesExpanded(ToggleSubTypesExpandedEvent event, Emitter<CustomAddLeadState> emit) {
    emit(state.copyWith(isSubTypesExpanded: !state.isSubTypesExpanded, errorMessage: ''));
  }

  FutureOr<void> _onToggleNoOfBhkExpanded(ToggleNoOfBhkExpandedEvent event, Emitter<CustomAddLeadState> emit) {
    emit(state.copyWith(isNoOfBhkExpanded: !state.isNoOfBhkExpanded, errorMessage: ''));
  }

  void _emitFailureState(Emitter<CustomAddLeadState> emit, String errorMessage) => emit(state.copyWith(
        errorMessage: errorMessage,
        pageState: PageState.failure,
      ));

  FutureOr<void> _onResetState(ResetStateEvent event, Emitter<CustomAddLeadState> emit) {
    emit(state.initialState());
  }

  FutureOr<void> _onToggleReferralFields(ToggleReferralFieldsEvent event, Emitter<CustomAddLeadState> emit) async {
    emit(state.copyWith(isReferralDetailsVisible: !state.isReferralDetailsVisible, errorMessage: ''));
    if (!state.isReferralDetailsVisible) referralNameController?.text = (referralPhoneController?.text = "") ?? "";
    await _appAnalysisRepository.sendAppAnalysis(name: state.isEmailFieldVisible ? AppAnalyticsConstants.mobileAddLeadButtonAddReferralDetailsClick : AppAnalyticsConstants.mobileAddLeadButtonRemoveReferralDetailsClick);
  }

  FutureOr<void> _onToggleAltPhoneField(ToggleAltPhoneFieldEvent event, Emitter<CustomAddLeadState> emit) async {
    emit(state.copyWith(isAltPhoneFieldVisible: event.hideAltPhoneField ?? !state.isAltPhoneFieldVisible, errorMessage: '', altContactNumber: ''));
    if (!state.isAltPhoneFieldVisible) altPhoneController?.text = "";
    await _appAnalysisRepository.sendAppAnalysis(name: state.isAltPhoneFieldVisible ? AppAnalyticsConstants.mobileAddLeadButtonRemoveAlternateNumberClick : AppAnalyticsConstants.mobileAddLeadButtonAddAlternateNumberClick);
  }

  FutureOr<void> _onToggleEmailField(ToggleEmailFieldEvent event, Emitter<CustomAddLeadState> emit) async {
    emit(state.copyWith(isEmailFieldVisible: !state.isEmailFieldVisible, errorMessage: ''));
    if (!state.isEmailFieldVisible) emailController?.text = "";
    await _appAnalysisRepository.sendAppAnalysis(name: state.isEmailFieldVisible ? AppAnalyticsConstants.mobileAddLeadButtonAddEmailClick : AppAnalyticsConstants.mobileAddLeadButtonRemoveEmailClick);
  }

  FutureOr<void> _onTogglePossessionDate(TogglePossessionDateEvent event, Emitter<CustomAddLeadState> emit) {
    emit(state.copyWith(isPossessionDateVisible: !state.isPossessionDateVisible, errorMessage: '', updatePossessionDate: state.isPossessionDateVisible));
  }

  FutureOr<void> _onSelectPossessionDate(SelectPossessionDateEvent event, Emitter<CustomAddLeadState> emit) {
    emit(state.copyWith(possessionDate: event.selectedDate));
  }

  FutureOr<void> _onCreateLead(CreateLeadEvent event, Emitter<CustomAddLeadState> emit) async {
    if ((state.globalSettingModel?.leadNotesSetting?.isNotesMandatoryEnabled ?? false) && (state.globalSettingModel?.leadNotesSetting?.isNotesMandatoryOnAddLead ?? false)) {
      if (notesController?.text.isEmpty ?? true) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'notes are mandatory field '));
        return;
      }
    }
    if (leadNameController?.text.trim().isEmpty ?? false) {
      _emitFailureState(emit, "Please enter a lead name");
      return;
    }
    if (state.contactNumber?.isEmpty ?? false) {
      _emitFailureState(emit, "Please enter a contact number");
      return;
    }
    if (minBudgetController?.text.isNotEmpty ?? false) {
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(lowerBudget: int.tryParse(minBudgetController?.text ?? '0')));
    }
    if (maxBudgetController?.text.isNotEmpty ?? false) {
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(upperBudget: int.tryParse(maxBudgetController?.text ?? '0')));
    }
    final isPropertyTypeSelected = state.propertyTypes.any((element) => element.isSelected);
    final isPropertySubTypeSelected = state.propertySubTypes.any((element) => element.isSelected);
    if (isPropertyTypeSelected && !isPropertySubTypeSelected) {
      _emitFailureState(emit, "Please select property sub-type");
      return;
    }
    _addLeadModel = _addLeadModel.copyWith(
      alternateContactNo: state.altContactNumber.isNotNullOrEmpty() ? state.altContactNumber?.trim() : null,
      email: (emailController?.text.isEmpty ?? true) ? null : emailController?.text.trim(),
      notes: (notesController?.text.isEmpty ?? true) ? null : notesController?.text.trim(),
      companyName: (companyNameController?.text.isEmpty ?? true) ? null : companyNameController?.text.trim(),
      referralName: (referralNameController?.text.isEmpty ?? true) ? null : referralNameController?.text.trim(),
      referralContactNo: state.referralContact?.trim(),
      designation: (designationController?.text.isEmpty ?? true) ? null : designationController?.text.trim(),
      enquiry: _addLeadModel.enquiry?.copyWith(
        possessionDate: state.possessionDate?.getBasedOnTimeZone()?.toUtcFormat(),
        unitName: (unitNumberOrNameController?.text.isEmpty ?? true) ? null : unitNumberOrNameController?.text.trim(),
        clusterName: (clusterNameController?.text.isEmpty ?? true) ? null : clusterNameController?.text.trim(),
      ),
      confidentialNotes: getLeadEntity?.confidentialNotes,
      duplicateLeadVersion: getLeadEntity?.duplicateLeadVersion,
      additionalProperties: getLeadEntity?.additionalProperties,
      referralEmail: (referralEmailController?.text.isNotEmpty ?? false) ? referralEmailController?.text.trim() : null,
      channelPartnerExecutiveName: (executiveNameController?.text.isEmpty ?? true) ? null : executiveNameController?.text.trim(),
      channelPartnerContactNo: state.executiveContact?.trim(),
    );

    if (carpetAreaController?.text.isNotEmpty ?? false) {
      var carpetValue = double.parse(carpetAreaController!.text);
      if (state.selectedCarpetArea == null) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'please select the carpet unit'));
        return;
      }
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(carpetArea: carpetValue));
    } else {
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(resetCarpetAreaUnit: true));
    }

    if (saleableAreaAreaController?.text.isNotEmpty ?? false) {
      var saleableValue = double.parse(saleableAreaAreaController!.text);
      if (state.selectedSaleableArea == null) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'please select the saleable unit'));
        return;
      }
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(saleableArea: saleableValue));
    } else {
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(resetSaleableAreaUnit: true));
    }

    if (builtUpAreaController?.text.isNotEmpty ?? false) {
      var builtUpValue = double.parse(builtUpAreaController!.text);
      if (state.selectedBuiltUpArea == null) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'please select the builtUp unit'));
        return;
      }
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(builtUpArea: builtUpValue));
    } else {
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(resetBuiltUpAreaAreaUnit: true));
    }
    if (netAreaController?.text.isNotEmpty ?? false) {
      var netAreaValue = double.parse(netAreaController!.text);
      if (state.selectedNetArea == null) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'please select the net unit'));
        return;
      }
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(netArea: netAreaValue));
    } else {
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(resetNetAreaUnit: true));
    }
    if (propertyAreaController?.text.isNotEmpty ?? false) {
      var propertyAreaValue = double.parse(propertyAreaController!.text);
      if (state.selectedPropertyArea == null) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'please select the property area unit'));
        return;
      }
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(propertyArea: propertyAreaValue));
    } else {
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(resetPropertyAreaUnit: true));
    }
    _addLeadModel = _addLeadModel.copyWith(name: leadNameController?.text.trim() ?? "", contactNo: state.contactNumber?.trim() ?? "");

    if (state.possessionTypeSelectedItem != null) {
      _addLeadModel = _addLeadModel.copyWith(possesionType: state.possessionTypeSelectedItem?.value);

      _addLeadModel = _addLeadModel.copyWith(possesionType: state.possessionTypeSelectedItem?.value);
    }
    if (isEditing) {
      emit(state.copyWith(pageState: PageState.loading, dialogMessage: "updating lead", showDialogProgress: true));
      var statusId = getLeadEntity != null && getLeadEntity?.status?.childType != null ? getLeadEntity?.status?.childType?.id : getLeadEntity?.status?.id;
      _addLeadModel = _addLeadModel.copyWith(leadStatusId: statusId, id: getLeadEntity?.id, serialNumber: getLeadEntity?.serialNumber);
      final updateLead = await _updateLeadUseCase(_addLeadModel.toUpdateLeadModel());
      updateLead.fold((failure) => _emitFailureState(emit, failure.message), (success) {
        if (success != null) {
          getIt<LeadInfoBloc>().add(GetLeadInfoInitialEvent(success));
          getIt<LeadInfoBloc>().add(InitializeHistoryEvent(true));
          getIt<LeadInfoBloc>().add(InitializeNotesEvent(true));
          emit(state.copyWith(pageState: PageState.success, errorMessage: '', successMessage: "Lead edited successfully", showDialogProgress: false));
          getIt<ManageLeadsBloc>().add(ManageLeadsInitialEvent(leadFilter: getIt<ManageLeadsBloc>().leadFilter));
        }
      });
    } else {
      emit(state.copyWith(pageState: PageState.loading, dialogMessage: "saving lead", showDialogProgress: true));
      final addLead = await _addLeadUseCase(_addLeadModel);
      addLead.fold((failure) => _emitFailureState(emit, failure.message), (success) {
        emit(state.copyWith(pageState: PageState.success, errorMessage: '', successMessage: "Lead added successfully", showDialogProgress: false));
        getIt<LeadInfoBloc>().add(LeadInfoInitialEvent(getLeadEntity ?? GetLeadEntity(id: success), null));
        getIt<ManageLeadsBloc>().add(ManageLeadsInitialEvent(leadFilter: getIt<ManageLeadsBloc>().leadFilter));
      });
    }
  }

  Future<void> _initInitialData(Emitter<CustomAddLeadState> emit) async {
    leadNameController?.text = getLeadEntity?.name ?? '';
    phoneController?.text = getLeadEntity?.contactNo ?? '';
    emailController?.text = getLeadEntity?.email ?? '';
    altPhoneController?.text = getLeadEntity?.alternateContactNo ?? '';
    referralNameController?.text = getLeadEntity?.referralName ?? '';
    referralPhoneController?.text = getLeadEntity?.referralContactNo ?? '';
    designationController?.text = getLeadEntity?.designation ?? '';
    notesController?.text = getLeadEntity?.notes ?? '';
    companyNameController?.text = getLeadEntity?.companyName ?? '';
    unitNumberOrNameController?.text = getLeadEntity?.enquiry?.unitName ?? '';
    clusterNameController?.text = getLeadEntity?.enquiry?.clusterName ?? '';
    referralEmailController?.text = getLeadEntity?.referralEmail ?? '';
    executiveNameController?.text = getLeadEntity?.channelPartnerExecutiveName ?? '';
    executivePhoneController?.text = getLeadEntity?.channelPartnerContactNo ?? '';
    if (getLeadEntity?.enquiry?.upperBudget != null && getLeadEntity!.enquiry!.upperBudget! > 0) {
      maxBudgetController?.text = getLeadEntity?.enquiry?.upperBudget?.toString() ?? '';
    }
    if (getLeadEntity?.enquiry?.lowerBudget != null && getLeadEntity!.enquiry!.lowerBudget! > 0) {
      minBudgetController?.text = getLeadEntity?.enquiry?.lowerBudget?.toString() ?? '';
    }
    if (getLeadEntity?.enquiry?.carpetArea != null && getLeadEntity!.enquiry!.carpetArea! > 0.0) {
      carpetAreaController?.text = getLeadEntity?.enquiry?.carpetArea?.toString().replaceAll('.0', '') ?? '';
    }
    if (getLeadEntity?.enquiry?.saleableArea != null && getLeadEntity!.enquiry!.saleableArea! > 0.0) {
      saleableAreaAreaController?.text = getLeadEntity?.enquiry?.saleableArea?.toString().replaceAll('.0', '') ?? '';
    }
    if (getLeadEntity?.enquiry?.builtUpArea != null && getLeadEntity!.enquiry!.builtUpArea! > 0.0) {
      builtUpAreaController?.text = getLeadEntity?.enquiry?.builtUpArea?.toString().replaceAll('.0', '') ?? '';
    }
    if (getLeadEntity?.enquiry?.netArea != null && getLeadEntity!.enquiry!.netArea! > 0.0) {
      netAreaController?.text = getLeadEntity?.enquiry?.netArea?.toString().replaceAll('.0', '') ?? '';
    }
    if (getLeadEntity?.enquiry?.propertyArea != null && getLeadEntity!.enquiry!.propertyArea! > 0.0) {
      propertyAreaController?.text = getLeadEntity?.enquiry?.propertyArea?.toString().replaceAll('.0', '') ?? '';
    }
    final initSelectedLocations = getLeadEntity?.enquiry?.addresses;
    if (initSelectedLocations != null) {
      final locations = initSelectedLocations
          .map((e) {
            final title = (e.subLocality?.isNotEmpty == true) ? e.subLocality : [e.locality, e.state].where((s) => s?.isNotEmpty == true).join(', ');
            if (title.isNotNullOrEmpty()) {
              return ItemSimpleModel<AddressModel>(
                title: title ?? '',
                value: e.toModel(),
              );
            }
          })
          .nonNulls
          .toList();
      emit(state.copyWith(locations: locations));
      _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(addresses: locations.map((e) => e.value).whereNotNull().toList()));
    }

    final initSelectedCustomerLocation = getLeadEntity?.address;
    if (initSelectedCustomerLocation != null) {
      final customerLocation = ItemSimpleModel<AddressModel>(title: '${initSelectedCustomerLocation.subLocality ?? ''}, ${initSelectedCustomerLocation.locality ?? ''}, ${initSelectedCustomerLocation.state ?? ''}, ${initSelectedCustomerLocation.city ?? ''}, ${initSelectedCustomerLocation.country ?? ''}', value: initSelectedCustomerLocation);
      emit(state.copyWith(customerLocations: customerLocation));
      _addLeadModel = _addLeadModel.copyWith(address: customerLocation.value);
    }

    emit(state);
  }

  FutureOr<void> _onAddLocation(AddLocationEvent event, Emitter<CustomAddLeadState> emit) async {
    if (event.location == null) return;
    List<ItemSimpleModel<AddressModel>> locations = [];
    var locality = event.location?.locality ?? event.location?.subLocality ?? '';
    var subCommunity = event.location?.subCommunity ?? '';
    var community = event.location?.community ?? '';
    var towerName = event.location?.towerName ?? '';
    var city = event.location?.city ?? '';
    var state_ = event.location?.state ?? '';
    var country = event.location?.country ?? '';
    var postalCode = event.location?.postalCode ?? '';
    String title = [locality, subCommunity, community, towerName, city, state_, country, postalCode].where((element) => element.isNotEmpty).join(', ');
    locations.add(ItemSimpleModel<AddressModel>(title: title, value: event.location));
    emit(state.copyWith(locations: [...locations, ...state.locations]));
    final selectedAddresses = state.locations.map((e) => e.value).whereNotNull().toList();
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(addresses: selectedAddresses));
  }

  FutureOr<void> _onAddCustomerLocation(AddCustomerLocationEvent event, Emitter<CustomAddLeadState> emit) async {
    if (event.customerLocation == null) return;
    ItemSimpleModel<AddressModel> location;
    var locality = event.customerLocation?.locality ?? event.customerLocation?.subLocality ?? '';
    var subCommunity = event.customerLocation?.subCommunity ?? '';
    var community = event.customerLocation?.community ?? '';
    var towerName = event.customerLocation?.towerName ?? '';
    var city = event.customerLocation?.city ?? '';
    var state_ = event.customerLocation?.state ?? '';
    var country = event.customerLocation?.country ?? '';
    var postalCode = event.customerLocation?.postalCode ?? '';
    String title = [locality, subCommunity, community, towerName, city, state_, country, postalCode].where((element) => element.isNotEmpty).join(', ');
    location = ItemSimpleModel<AddressModel>(title: title, value: event.customerLocation);
    emit(state.copyWith(customerLocations: location));
    _addLeadModel = _addLeadModel.copyWith(address: state.customerLocations?.value);
  }

  FutureOr<void> _onRemoveLocation(RemoveLocationEvent event, Emitter<CustomAddLeadState> emit) {
    final updatedLocations = state.locations.whereNot((element) => element.value == event.selectedItem.value).toList();
    emit(state.copyWith(locations: updatedLocations));
    final selectedAddresses = updatedLocations.map((e) => e.value).whereNotNull().toList();
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(addresses: selectedAddresses));
  }

  FutureOr<void> _onRemoveCustomerLocation(RemoveCustomerLocationEvent event, Emitter<CustomAddLeadState> emit) {
    emit(state.copyWith(customerLocations: ItemSimpleModel<AddressModel>(title: 'None')));
    _addLeadModel = _addLeadModel.copyWith(address: state.customerLocations?.value);
  }

  FutureOr<void> _onCheckLeadContactAlreadyExists(CheckLeadContactAlreadyExistsEvent event, Emitter<CustomAddLeadState> emit) async {
    final contactWithCountryCode = "${event.countryCode}${event.contactNo}";
    if (isEditing && contactWithCountryCode == getLeadEntity?.contactNo) return;

    final checkIfLeadContactExists = await _getLeadByContactNoUseCase(GetLeadByContactNoUseCaseParams(event.countryCode.trim(), event.contactNo.trim()));
    checkIfLeadContactExists.fold(
      (failure) => null,
      (leadContact) {
        if (leadContact != null && _usersDataRepository.checkHasPermission(AppModule.lead, CommandType.createDuplicateLeads) && (globalSettings?.duplicateLeadFeatureInfo?.isFeatureAdded ?? false) && (globalSettings?.duplicateLeadFeatureInfo?.allowAllDuplicates ?? false)) {
          if (!(state.isDuplicateLeadBottomSheetVisible)) {
            emit(state.copyWith(isDuplicateLeadBottomSheetVisible: (leadContact.canNavigate ?? false) || leadContact.id != null, existingLeadId: leadContact.id, primaryOrSecondary: 'primary'));
          }
        } else if (leadContact != null && !(state.isLeadAlreadyExits)) {
          LeadratCustomSnackbar.show(message: "Lead already exists with this number.", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
          emit(state.copyWith(isLeadAlreadyExits: (leadContact.canNavigate ?? false) || leadContact.id != null, existingLeadId: leadContact.id));
        } else if (leadContact == null) {
          emit(state.copyWith(isLeadAlreadyExits: false, contactNumber: contactWithCountryCode, isDuplicateLeadBottomSheetVisible: false, existingLeadId: ''));
        }
        if (!(_usersDataRepository.checkHasPermission(AppModule.lead, CommandType.viewAllLeads))) {
          if (state.existingLeadId.isNotNullOrEmpty()) {
            add(AssignedToLoggedInUser());
          }
        }
      },
    );
  }

  FutureOr<void> _onCheckAltContactAlreadyExists(CheckAltContactAlreadyExistsEvent event, Emitter<CustomAddLeadState> emit) async {
    final contactWithCountryCode = "${event.countryCode}${event.contactNo}";
    if (isEditing && contactWithCountryCode == getLeadEntity?.alternateContactNo || contactWithCountryCode == getLeadEntity?.contactNo) return;

    final checkIfLeadContactExists = await _getLeadByContactNoUseCase(GetLeadByContactNoUseCaseParams(event.countryCode.trim(), event.contactNo.trim()));
    checkIfLeadContactExists.fold(
      (failure) => null,
      (leadContact) {
        if (leadContact != null && _usersDataRepository.checkHasPermission(AppModule.lead, CommandType.createDuplicateLeads) && (globalSettings?.duplicateLeadFeatureInfo?.isFeatureAdded ?? false) && (globalSettings?.duplicateLeadFeatureInfo?.allowAllDuplicates ?? false)) {
          if (!(state.isDuplicateLeadBottomSheetVisibleForAltNumber)) {
            emit(state.copyWith(isDuplicateLeadBottomSheetVisibleForAltNumber: (leadContact.canNavigate ?? false) || leadContact.id != null, existingLeadId: leadContact.id, primaryOrSecondary: 'alternative'));
          }
        } else if (leadContact != null && !(state.isLeadAlreadyExitsOnAltNumber)) {
          LeadratCustomSnackbar.show(message: "Lead already exists with this number.", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
          emit(state.copyWith(isLeadAlreadyExitsOnAltNumber: (leadContact.canNavigate ?? false) || leadContact.id != null, existingLeadId: leadContact.id));
        }
        if (!(_usersDataRepository.checkHasPermission(AppModule.lead, CommandType.viewAllLeads))) {
          if (state.existingLeadId.isNotNullOrEmpty()) {
            add(AssignedToLoggedInUser());
          }
        }
      },
    );
  }

  FutureOr<void> _onOnLeadContactChanged(OnLeadContactChangedEvent event, Emitter<CustomAddLeadState> emit) {
    final leadContact = "+${event.countryCode}${event.contactNumber}";
    if (event.countryCode.isNotEmpty && event.contactNumber.isNotEmpty) emit(state.copyWith(contactNumber: leadContact, isLeadAlreadyExits: false, isDuplicateLeadBottomSheetVisible: false, primaryOrSecondary: ''));
  }

  FutureOr<void> _onOnAltContactChanged(OnAltContactChangedEvent event, Emitter<CustomAddLeadState> emit) {
    final altContact = "+${event.countryCode}${event.contactNumber}";
    emit(state.copyWith(altContactNumber: altContact, isLeadAlreadyExitsOnAltNumber: false, isDuplicateLeadBottomSheetVisibleForAltNumber: false, primaryOrSecondary: ''));
  }

  FutureOr<void> _onOnReferralContactChanged(OnReferralContactChangedEvent event, Emitter<CustomAddLeadState> emit) {
    final referralContact = "+${event.countryCode}${event.contactNumber}";
    if (event.countryCode.isNotEmpty && event.contactNumber.isNotEmpty) emit(state.copyWith(referralContact: referralContact));
  }

  FutureOr<void> _onPickContact(PickContactEvent event, Emitter<CustomAddLeadState> emit) async {
    final contact = await PhoneUtils.pickContact();
    if (contact != null && contact.fullName != null && contact.phoneNumber != null) {
      leadNameController?.text = contact.fullName ?? '';
      final contactNumber = contact.phoneNumber?.number.toString().replaceAll(' ', '').replaceAll('-', '').trim() ?? '';
      phoneController?.text = contactNumber;
    }
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonImportContactsClick);
  }

  FutureOr<void> _onCurrencySelect(SelectCurrency event, Emitter<CustomAddLeadState> emit) async {
    emit(state.copyWith(selectedCurrency: event.selectedCurrency));
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(currency: event.selectedCurrency.value));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonBudgetCurrencyClick);
  }

  FutureOr<void> _onSelectedChannelPartners(SelectChannelPartnerEvent event, Emitter<CustomAddLeadState> emit) async {
    final selectedChannelPartners = event.selectedChannelPartners.map((e) => e.title).toList();
    emit(state.copyWith(selectedChannelPartners: event.selectedChannelPartners));
    _addLeadModel = _addLeadModel.copyWith(channelPartnerList: selectedChannelPartners);
  }

  FutureOr<void> _onRemoveChannelPartnerName(RemoveChannelPartnerNameEvent event, Emitter<CustomAddLeadState> emit) {
    final updatedSelectedChannelPartners = state.selectedChannelPartners.whereNot((element) => element.title == event.selectedChannelPartner.title).toList();
    emit(state.copyWith(selectedChannelPartners: updatedSelectedChannelPartners, errorMessage: ''));
    _addLeadModel.channelPartners?.removeWhere((element) => element.firmName == event.selectedChannelPartner.title);
    _addLeadModel.channelPartnerList?.removeWhere((element) => element == event.selectedChannelPartner.title);
  }

  FutureOr<void> _onExecutiveContactChanged(OnExecutiveContactChangedEvent event, Emitter<CustomAddLeadState> emit) {
    final executiveContact = "+${event.countryCode}${event.contactNumber}";
    if (event.countryCode.isNotEmpty && event.contactNumber.isNotEmpty) emit(state.copyWith(executiveContact: executiveContact));
  }

  bool _validatePropertyAreas() {
    double? propertyArea = (propertyAreaController?.text.isNotNullOrEmpty() ?? false) ? double.parse(propertyAreaController!.text) : null;
    double? saleableArea = (saleableAreaAreaController?.text.isNotNullOrEmpty() ?? false) ? double.parse(saleableAreaAreaController!.text) : null;
    double? builtUpArea = (builtUpAreaController?.text.isNotNullOrEmpty() ?? false) ? double.parse(builtUpAreaController!.text) : null;
    double? carpetArea = (carpetAreaController?.text.isNotNullOrEmpty() ?? false) ? double.parse(carpetAreaController!.text) : null;
    double? netArea = (netAreaController?.text.isNotNullOrEmpty() ?? false) ? double.parse(netAreaController!.text) : null;
    if (propertyArea != null && saleableArea != null && propertyArea <= saleableArea) {
      LeadratCustomSnackbar.show(message: "Saleable Area should be less than Property Area.", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (propertyArea != null && builtUpArea != null && propertyArea <= builtUpArea) {
      LeadratCustomSnackbar.show(message: 'Built-up Area should be less than Property Area.', navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (propertyArea != null && carpetArea != null && propertyArea <= carpetArea) {
      LeadratCustomSnackbar.show(message: 'Carpet Area should be less than Property Area.', navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);

      return false;
    }
    if (propertyArea != null && netArea != null && propertyArea <= netArea) {
      LeadratCustomSnackbar.show(message: 'Net Area should be less than Property Area.', navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (saleableArea != null && builtUpArea != null && saleableArea <= builtUpArea) {
      LeadratCustomSnackbar.show(message: 'Built-up Area should be less than Saleable Area.', navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (saleableArea != null && carpetArea != null && saleableArea <= carpetArea) {
      LeadratCustomSnackbar.show(message: 'Carpet Area should be less than Saleable Area.', navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (saleableArea != null && netArea != null && saleableArea <= netArea) {
      LeadratCustomSnackbar.show(message: 'Net Area should be less than Saleable Area.', navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (builtUpArea != null && carpetArea != null && builtUpArea <= carpetArea) {
      LeadratCustomSnackbar.show(message: 'Carpet Area should be less than Built-up Area.', navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (builtUpArea != null && netArea != null && builtUpArea <= netArea) {
      LeadratCustomSnackbar.show(message: 'Net Area should be less than Built-up Area.', navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (carpetArea != null && netArea != null && carpetArea <= netArea) {
      LeadratCustomSnackbar.show(message: "Net Area should be less than Carpet Area.", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    return true;
  }

  FutureOr<void> _onSelectLeadNationalityEvent(SelectLeadNationalityEvent event, Emitter<CustomAddLeadState> emit) {
    emit(state.copyWith(selectedCountry: event.selectedCountry));
    _addLeadModel = _addLeadModel.copyWith(nationality: state.selectedCountry?.title);
  }

  FutureOr<void> _onSelectPurpose(SelectPurposeEvent event, Emitter<CustomAddLeadState> emit) async {
    if (event.selectedPurpose == null) return;
    emit(state.copyWith(selectedPurpose: event.selectedPurpose));
    _addLeadModel = _addLeadModel.copyWith(enquiry: _addLeadModel.enquiry?.copyWith(purpose: state.selectedPurpose?.value));
  }

  void _initPurpose(Emitter<CustomAddLeadState> emit) {
    final initialSelectedPurpose = getLeadEntity?.enquiry?.purpose;
    final purposes = PurposeEnum.values.where((element) => element != PurposeEnum.none).map((e) => SelectableItem<PurposeEnum>(title: e.description, value: e));
    emit(state.copyWith(purposes: purposes.toList()));
    if (purposes.isNotEmpty) add(SelectPurposeEvent(purposes.firstWhereOrNull((element) => element.value == initialSelectedPurpose)));
  }

  void _initPossessionType(Emitter<CustomAddLeadState> emit) {
    PossessionType? initialPossessionType = getLeadEntity?.possesionType;
    initialPossessionType ??= getLeadEntity?.enquiry?.possesionType;
    if (getLeadEntity?.enquiry?.possessionDate != null) initialPossessionType ??= PossessionType.customDate;
    List<SelectableItem<PossessionType?>> possessionTypeItem = PossessionType.values.where((e) => e.description != 'None').map((e) => SelectableItem<PossessionType?>(title: e.description, value: e)).toList();
    emit(state.copyWith(possessionTypeSelectableItems: possessionTypeItem, possessionDate: getLeadEntity?.enquiry?.possessionDate));
    if (possessionTypeItem.isNotEmpty) add(SelectPossessionType(possessionTypeItem.firstWhereOrNull((element) => element.value == initialPossessionType)));
  }

  FutureOr<void> _onAssignedToLoggedInUser(AssignedToLoggedInUser event, Emitter<CustomAddLeadState> emit) async {
    final response = await _checkLeadAssignedByLeadIdUseCase.call(state.existingLeadId ?? '');
    response.fold((failure) => {}, (result) {
      emit(state.copyWith(isAssignedLoggedInUser: result));
    });
  }

  FutureOr<void> _onSelectPossessionType(SelectPossessionType event, Emitter<CustomAddLeadState> emit) async {
    emit(
      state.copyWith(possessionTypeSelectedItem: event.selectPossessionType, isPossessionDateCustomSelected: event.selectPossessionType?.value == PossessionType.customDate, possessionDate: DateTimeUtils.getPossessionDate(event.selectPossessionType?.value ?? PossessionType.none), updatePossessionDate: PossessionType.underConstruction == event.selectPossessionType?.value),
    );
  }
}
