import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:jumping_dot/jumping_dot.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/common/constants/app_analytics_constants.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/common/shapes/sharp_divider_painter.dart';
import 'package:leadrat/core_main/common/widgets/leading_icon_with_text.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/module_name.dart';
import 'package:leadrat/core_main/extensions/integer_extension.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';
import 'package:leadrat/features/lead/presentation/bloc/matching_properties_bloc/matching_properties_bloc.dart';
import 'package:leadrat/features/lead/presentation/widgets/matching_property_item.dart';
import 'package:leadrat/features/properties/presentation/pages/property_share_page.dart';

class MatchingPropertiesPage extends LeadratStatefulWidget {
  final GetLeadEntity? leadEntity;

  const MatchingPropertiesPage({super.key,this.leadEntity});

  @override
  State<MatchingPropertiesPage> createState() => _MatchingPropertiesState();
}

class _MatchingPropertiesState extends LeadratState<MatchingPropertiesPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileMatchingPropertyPageMatchingPropertyView);
  }

  @override
  Widget buildContent(BuildContext context) {
    return BlocBuilder<MatchingPropertiesBloc, MatchingPropertiesState>(
      builder: (context, state) {
        return Scaffold(
          body: SafeArea(
            child: Column(
              children: [
                _buildHeader(context, state),
                Expanded(
                  child: Stack(
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.fromLTRB(24, 10, 14, 0),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Expanded(
                                  child: SizedBox(
                                    height: 40,
                                    child: TextFormField(
                                      style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primary),
                                      onChanged: (value) => context.read<MatchingPropertiesBloc>().add(OnSearchPropertiesEvent(value)),
                                      decoration: InputDecoration(
                                          contentPadding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                                          hintText: "search property name",
                                          border: const OutlineInputBorder(borderSide: BorderSide(color: ColorPalette.tertiaryTextColor)),
                                          enabledBorder: const OutlineInputBorder(borderSide: BorderSide(color: ColorPalette.tertiaryTextColor)),
                                          focusedBorder: const OutlineInputBorder(borderSide: BorderSide(color: ColorPalette.tertiaryTextColor)),
                                          hintStyle: LexendTextStyles.lexend10Medium.copyWith(
                                            color: ColorPalette.tertiaryTextColor,
                                          )),
                                    ),
                                  ),
                                ),
                                if (state.selectedMatchingProperties.isNotEmpty)
                                  GestureDetector(
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => PropertySharePage(
                                            title: state.selectedPropertyTitle,
                                            propertyIds: state.selectedPropertyIds ?? [],
                                            moduleName: ModuleName.property,
                                            contactNumber: widget.leadEntity?.contactNo,
                                            emailAddress: widget.leadEntity?.email,
                                          ),
                                        ),
                                      );
                                      getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileMatchingPropertyButtonMultipleShareClick);
                                    },
                                    child: Container(
                                      margin: const EdgeInsets.only(left: 10),
                                      padding: const EdgeInsets.all(12),
                                      decoration: const BoxDecoration(color: ColorPalette.gray900, border: Border.fromBorderSide(BorderSide(color: ColorPalette.primaryLightColor, width: 1)), borderRadius: BorderRadius.all(Radius.circular(8))),
                                      child: SvgPicture.asset(ImageResources.iconFilledShare),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.fromLTRB(24, 10, 0, 0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                RichText(
                                    text: TextSpan(children: [
                                  TextSpan(text: state.totalMatchingProperties.toString(), style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primary)),
                                  TextSpan(text: " properties matches found", style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.primary)),
                                ])),
                                const Spacer(),
                                if (state.selectedRadius != null) LeadingIconWithText(textStyle: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.fadedRed), text: "clear", onTap: () => context.read<MatchingPropertiesBloc>().add(ClearMatchingRadiusEvent()), iconWidget: const Icon(Icons.remove_circle_outline_rounded, color: ColorPalette.fadedRed, size: 15)),
                                SelectableItemBottomSheet(
                                  title: "select radius",
                                  selectableItems: state.selectableRadiusInKms,
                                  onItemSelected: (selectedValue) {
                                    context.read<MatchingPropertiesBloc>().add(SelectRadiusEvent(selectedValue));
                                    getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileMatchingPropertyButtonSelectRadiusClick);
                                  },
                                  selectedItem: state.selectedRadius,
                                  child: Padding(
                                    padding: const EdgeInsets.only(right: 10, left: 14),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(state.selectedRadius?.title.toString() ?? "select radius", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
                                        const Icon(Icons.keyboard_arrow_down_sharp, color: ColorPalette.primary),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          if (state.selectedMatchingProperties.isNotEmpty) ...[
                            ...[
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Expanded(
                                    child: ListTile(
                                      contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 14),
                                      visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
                                      onTap: () => getIt<MatchingPropertiesBloc>().add(SelectAllMatchingPropertiesEvent(!state.isAllPropertiesSelected)),
                                      title: Row(
                                        children: [
                                          Checkbox(
                                            value: state.isAllPropertiesSelected,
                                            activeColor: ColorPalette.primaryGreen,
                                            onChanged: (value) {
                                              getIt<MatchingPropertiesBloc>().add(SelectAllMatchingPropertiesEvent(value ?? false));
                                              getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileMatchingPropertyButtonSelectAllClick);
                                            },
                                            visualDensity: const VisualDensity(horizontal: -4, vertical: -3),
                                          ),
                                          Text("Select all", style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.primary)),
                                        ],
                                      ),
                                    ),
                                  ),
                                  Flexible(
                                    child: Padding(
                                      padding: const EdgeInsets.fromLTRB(24, 10, 0, 0),
                                      child: RichText(
                                          text: TextSpan(children: [
                                        TextSpan(text: "selected ", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
                                        TextSpan(text: state.selectedMatchingProperties.length.toString(), style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.primary)),
                                        TextSpan(text: " of ", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
                                        TextSpan(text: state.totalMatchingProperties.toString(), style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.primary)),
                                        TextSpan(text: " matching properties ", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.primary)),
                                      ])),
                                    ),
                                  ),
                                ],
                              ),
                            ]
                          ],
                          if (state.pageState == PageState.loading) const Expanded(child: Center(child: CircularProgressIndicator(color: ColorPalette.primaryGreen))),
                          if (state.pageState == PageState.success)
                            state.matchingProperties.isNotEmpty
                                ? Expanded(
                                    child: ListView.builder(
                                      controller: _scrollController,
                                      padding: const EdgeInsets.fromLTRB(24, 10, 14, 0),
                                      itemCount: state.matchingProperties.length + (state.isLoadingMore ? 1 : 0),
                                      itemBuilder: (context, index) {
                                        if (index < state.matchingProperties.length) {
                                          return MatchingPropertyItem(
                                            propertyItem: state.matchingProperties[index],
                                            emailAddress: state.leadEntity?.email,
                                            contactNumber: state.leadEntity?.contactNo,
                                          );
                                        } else {
                                          return Padding(
                                            padding: const EdgeInsets.all(10.0),
                                            child: JumpingDots(color: ColorPalette.leadratGreen, radius: 4, numberOfDots: 5, animationDuration: const Duration(milliseconds: 200)),
                                          );
                                        }
                                        // return MatchingPropertyItem(propertyItem: state.matchingProperties[index], emailAddress: state.leadEntity?.email, contactNumber: state.leadEntity?.contactNo);
                                      },
                                    ),
                                  )
                                : Expanded(child: Center(child: Text('No properties found.', style: LexendTextStyles.lexend14Bold.copyWith(color: ColorPalette.white))))
                        ],
                      ),
                      if (state.isLeadDetailsVisible)
                        Column(
                          children: [
                            Container(
                              color: ColorPalette.darkToneInk,
                              child: Column(
                                children: [
                                  ...buildLeadDetails(context, state),
                                ],
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, MatchingPropertiesState state) {
    return Container(
      color: ColorPalette.darkToneInk,
      padding: const EdgeInsets.only(bottom: 10),
      child: Stack(
        children: [
          Positioned(
            right: 1,
            child: SvgPicture.asset(ImageResources.imageProfileBackgroundPattern),
          ),
          Column(
            children: [
              Stack(
                alignment: Alignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 24),
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: SvgPicture.asset(ImageResources.iconBack),
                      ),
                    ),
                  ),
                  Center(
                    child: Text(
                      "Matching Properties",
                      style: LexendTextStyles.lexend16SemiBold.copyWith(
                        color: ColorPalette.tertiaryTextColor,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Container(
                    width: context.width(44),
                    margin: const EdgeInsets.only(left: 24),
                    child: Text.rich(
                      TextSpan(children: [
                        TextSpan(text: "lead name:\n", style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.lightBackground)),
                        TextSpan(text: state.leadEntity?.name ?? "", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.tertiaryTextColor)),
                      ]),
                    ),
                  ),
                  Container(width: 1, height: 24, color: ColorPalette.tertiaryTextColor, margin: const EdgeInsets.only(right: 10)),
                  Container(
                    width: context.width(43),
                    margin: const EdgeInsets.only(right: 14),
                    alignment: Alignment.center,
                    child: Text.rich(
                      TextSpan(children: [
                        TextSpan(text: "lead number:\n", style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.lightBackground)),
                        TextSpan(text: state.leadEntity?.contactNo ?? "--", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.tertiaryTextColor)),
                        if (state.leadEntity?.alternateContactNo?.isNotNullOrEmpty() ?? false) TextSpan(text: "\n${state.leadEntity?.alternateContactNo}", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.tertiaryTextColor)),
                      ]),
                    ),
                  )
                ],
              ),
              if (!state.isLeadDetailsVisible) ...[
                const SizedBox(height: 10),
                GestureDetector(
                    onTap: () => context.read<MatchingPropertiesBloc>().add(ToggleLeadDetailsEvent()),
                    child: Center(
                        child: Text(
                      "see more",
                      style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryGreen),
                    ))),
              ]
            ],
          ),
        ],
      ),
    );
  }

  buildLeadDetails(BuildContext context, MatchingPropertiesState state) {
    return [
      const SizedBox(height: 20),
      CustomPaint(size: const Size(double.infinity, 4), painter: SharpDividerPainter(width: .4)),
      const SizedBox(height: 20),
      Padding(
        padding: const EdgeInsets.only(left: 24, right: 14),
        child: Row(
          children: [
            Expanded(
              child: Text.rich(
                TextSpan(children: [
                  TextSpan(text: "enquired for:\n", style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.lightBackground)),
                  TextSpan(text: state.leadInfoState?.enquiredFor ?? "--", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.tertiaryTextColor)),
                ]),
              ),
            ),
            Expanded(
              child: Text.rich(
                TextSpan(children: [
                  TextSpan(text: "property type:\n", style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.lightBackground)),
                  TextSpan(text: state.leadEntity?.enquiry?.propertyType?.displayName ?? "--", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.tertiaryTextColor)),
                ]),
              ),
            ),
            Expanded(
              child: Text.rich(
                TextSpan(children: [
                  TextSpan(text: "enquired location:\n", style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.lightBackground)),
                  TextSpan(text: state.leadEntity?.enquiry?.addresses?.firstOrNull?.subLocality ?? "--", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.tertiaryTextColor)),
                ]),
              ),
            ),
          ],
        ),
      ),
      const SizedBox(height: 10),
      Padding(
        padding: const EdgeInsets.only(left: 24, right: 14),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text.rich(
                TextSpan(children: [
                  TextSpan(text: "source:\n", style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.lightBackground)),
                  TextSpan(text: state.leadEntity?.enquiry?.leadSource?.description ?? "", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.tertiaryTextColor)),
                ]),
              ),
            ),
            Expanded(
              child: Text.rich(
                TextSpan(children: [
                  TextSpan(text: "sub-property type:\n", style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.lightBackground)),
                  TextSpan(text: state.leadEntity?.enquiry?.propertyType?.childType?.displayName ?? "--", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.tertiaryTextColor)),
                ]),
              ),
            ),
            Expanded(
              child: Text.rich(
                TextSpan(children: [
                  TextSpan(text: "min. budget:\n", style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.lightBackground)),
                  TextSpan(text: state.leadEntity?.enquiry?.lowerBudget?.convertCurrencyFormat(currency: state.leadEntity?.enquiry?.currency) ?? "", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.tertiaryTextColor)),
                ]),
              ),
            ),
          ],
        ),
      ),
      const SizedBox(height: 10),
      Padding(
        padding: const EdgeInsets.only(left: 24, right: 14),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if(!(state.globalSettingModel?.isCustomLeadFormEnabled??true))
            Expanded(
              child: Text.rich(
                TextSpan(children: [
                  TextSpan(text: "BHK:\n", style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.lightBackground)),
                  TextSpan(text: state.leadInfoState?.noOfBHK ?? "--", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.tertiaryTextColor)),
                ]),
              ),
            ),
            if((state.globalSettingModel?.isCustomLeadFormEnabled??false))
            Expanded(
              child: Text.rich(
                TextSpan(children: [
                  TextSpan(text: "br:\n", style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.lightBackground)),
                  TextSpan(text: state.leadInfoState?.noOfBHK ?? "--", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.tertiaryTextColor)),
                ]),
              ),
            ),

            Expanded(
              child: Text.rich(
                TextSpan(children: [
                  TextSpan(text: "max. budget:\n", style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.lightBackground)),
                  TextSpan(text: state.leadEntity?.enquiry?.upperBudget?.convertCurrencyFormat(currency: state.leadEntity?.enquiry?.currency) ?? "--", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.tertiaryTextColor)),
                ]),
              ),
            ),
            if(!(state.globalSettingModel?.isCustomLeadFormEnabled??true))
            Expanded(
              child: Text.rich(
                TextSpan(children: [
                  TextSpan(text: "BHK Type:\n", style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.lightBackground)),
                  TextSpan(text: state.leadInfoState?.bhkType ?? "--", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.tertiaryTextColor)),
                ]),
              ),
            ),
          ],
        ),
      ),
      const SizedBox(height: 10),
      GestureDetector(onTap: () => context.read<MatchingPropertiesBloc>().add(ToggleLeadDetailsEvent()), child: Center(child: Text("see less", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryGreen)))),
      const SizedBox(height: 10),
    ];
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      getIt<MatchingPropertiesBloc>().add(LoadMoreMatchingPropertiesEvent());
    }
  }
}
