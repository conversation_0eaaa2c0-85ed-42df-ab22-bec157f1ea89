

import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'index')
enum PossessionType {
  none(0, 'None',0),
  underConstruction(1, 'Under Construction',0),
  sixMonths(2,'6 Months',6),
  oneYear(3, 'One Year',12),
  twoYear(4, 'Two Year',24),
  customDate(5, 'Custom Date',0);

  final int value;
  final String description;
  final int month;

  const PossessionType(this.value, this.description,this.month);
}
