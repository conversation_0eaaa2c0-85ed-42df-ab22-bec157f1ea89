import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/common/bloc/saved_filter_bloc/saved_filter_bloc.dart';
import 'package:leadrat/core_main/common/constants/app_analytics_constants.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/saved_filter/models/get_saved_filter_model.dart';
import 'package:leadrat/core_main/common/widgets/filter_last_date_month_picker.dart';
import 'package:leadrat/core_main/common/widgets/filter_first_date_month_picker.dart';
import 'package:leadrat/core_main/common/widgets/leadrat_form_button.dart';
import 'package:leadrat/core_main/common/widgets/saved_filter/saved_filter_input_dialog_widget.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/common/widgets/selectable_reorderable_list_widget.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/date_type.dart';
import 'package:leadrat/core_main/enums/leads/lead_filter_keys.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/widget_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/lead/data/models/lead_filter_model.dart';
import 'package:leadrat/features/lead/presentation/bloc/lead_filter_bloc/lead_filter_bloc.dart';
import 'package:leadrat/features/lead/presentation/item/item_lead_filter_model.dart';
import 'package:leadrat/features/lead/presentation/widgets/budget_range_slider_widget.dart';
import 'package:leadrat/features/lead/presentation/widgets/filter_custom_date_picker_widget.dart';
import 'package:leadrat/features/lead/presentation/widgets/range_input_widget.dart';

class LeadFilterPage extends LeadratStatefulWidget {
  final Function(LeadFilterModel?) onApplyFilter;
  final bool updateSavedFilter;
  final ItemSimpleModel<GetSavedFilterModel>? savedFilter;

  const LeadFilterPage({
    super.key,
    required this.onApplyFilter,
    required this.savedFilter,
    required this.updateSavedFilter
  });

  @override
  State<LeadFilterPage> createState() => _LeadFilterPageState();
}

class _LeadFilterPageState extends LeadratState<LeadFilterPage> {
  @override
  void initState() {
    getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileLeadFilterPageLeadFilterView);
    super.initState();
  }

  @override
  Widget buildContent(BuildContext context) {
    return BlocConsumer<LeadFilterBloc, LeadFilterState>(
      listener: (context, state) {
        if (state.pageState == PageState.success) {
          if (widget.updateSavedFilter) {
            showDialog(
              context: Navigator.of(context, rootNavigator: true).context,
              barrierDismissible: true,
              builder: (context) => SavedFilterInputDialogWidget(
                filterId: widget.savedFilter?.description,
                filterCriteria: state.leadFilterModel?.toJson(),
                module: SavedFilterModule.leads,
                filterName: widget.savedFilter?.title,
                onSavedOrCanceled: () {
                  widget.onApplyFilter(state.leadFilterModel);
                  Navigator.of(context).pop();
                },
              ),
            );
          } else {
            widget.onApplyFilter(state.leadFilterModel);
            Navigator.of(context).pop();
          }
        } else if (state.pageState == PageState.failure && state.errorMessage.isNotNullOrEmpty()) {
          LeadratCustomSnackbar.show(context: context, message: state.errorMessage ?? "Something went wrong", type: SnackbarType.error);
        }
      },
      builder: (context, state) {
        final selectedCategoryItem = state.leadFilterCategories[state.selectedCategoryIndex];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            Container(
              decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(ImageResources.imageAppBarPattern), alignment: Alignment.bottomRight)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Text("filter", style: LexendTextStyles.lexend18Bold.copyWith(color: ColorPalette.tertiaryTextColor)),
                  ),
                  GestureDetector(
                      onTap: () => context.read<LeadFilterBloc>().add(ResetFilterEvent()),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Text("reset", style: LexendTextStyles.lexend14Bold.copyWith(color: ColorPalette.tertiaryTextColor)),
                      )),
                ],
              ),
            ),
            Expanded(
              child: Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  // Filter categories
                  Container(
                    color: ColorPalette.primaryDarkColor,
                    width: context.width(35),
                    child: ListView.builder(
                      itemCount: state.leadFilterCategories.length,
                      itemBuilder: (context, index) {
                        final filterCategory = state.leadFilterCategories[index];
                        return GestureDetector(
                          onTap: () => context.read<LeadFilterBloc>().add(FilterCategorySelectEvent(itemLeadFilterCategoryModel: filterCategory, selectedCategoryIndex: index)),
                          child: Container(
                            key: ValueKey(index),
                            padding: const EdgeInsets.all(22),
                            decoration: BoxDecoration(
                                color: filterCategory.isSelected ? ColorPalette.primaryDarkColor : ColorPalette.darkToneInk,
                                border: Border(
                                  top: BorderSide(color: index == 0 ? ColorPalette.transparent : ColorPalette.lightBackground, width: .35),
                                  bottom: BorderSide(color: filterCategory.isSelected ? ColorPalette.lightBackground : ColorPalette.lightBackground, width: .35),
                                  right: BorderSide(color: filterCategory.isSelected ? ColorPalette.transparent : ColorPalette.lightBackground, width: .7),
                                )),
                            child: Text(filterCategory.displayName ?? '', style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.tertiaryTextColor, fontWeight: filterCategory.isSelected ? FontWeight.w600 : FontWeight.w400)),
                          ),
                        );
                      },
                    ),
                  ),
                  // Filters
                  Container(
                    width: context.width(65),
                    padding: const EdgeInsets.only(right: 14, left: 8),
                    color: ColorPalette.primaryDarkColor,
                    child: (selectedCategoryItem.isAreaRelated || selectedCategoryItem.filterKey == LeadFilterKey.minBudget || selectedCategoryItem.filterKey == LeadFilterKey.maxBudget)
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.max,
                            children: [_buildCustomFilterWidget(context, state, selectedCategoryItem)],
                          )
                        : Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              if (selectedCategoryItem.hasCustomHeaderView) ..._buildCustomHeaderView(context, selectedCategoryItem, state),
                              if (selectedCategoryItem.hasSearch) ..._buildSearchWidget(context, selectedCategoryItem, state),
                              if (selectedCategoryItem.hasSelectAll || selectedCategoryItem.hasMultiSelect) ..._buildSelectAllItem(context, selectedCategoryItem),
                              if (selectedCategoryItem.pinCodeController != null) ..._buildPinCodeTextField(context, selectedCategoryItem, state),
                              Expanded(
                                child: state.leadFilterCategories[state.selectedCategoryIndex].isInitialized
                                    ? (selectedCategoryItem.filters == null || selectedCategoryItem.filters!.isEmpty) && selectedCategoryItem.pinCodeController == null
                                        ? Center(child: Text('no item found', style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.primary, overflow: TextOverflow.ellipsis)))
                                        : _buildFilterList(context, state, selectedCategoryItem)
                                    : const Center(
                                        child: CircularProgressIndicator(
                                          color: ColorPalette.primaryGreen,
                                        ),
                                      ),
                              ),
                            ],
                          ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.only(top: 15, right: 24, left: 14, bottom: 15),
              decoration: const BoxDecoration(color: ColorPalette.primaryDarkColor, border: Border(top: BorderSide(color: ColorPalette.tertiaryTextColor, width: .8))),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                      child: LeadratFormButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileLeadFilterButtonCloseClick);
                          },
                          buttonText: "Close")),
                  const SizedBox(width: 14),
                  Expanded(
                      child: LeadratFormButton(
                    onPressed: () {
                      context.read<LeadFilterBloc>().add(ApplyLeadFilterEvent());
                      getIt<AppAnalysisRepository>().sendAppAnalysis(name: AppAnalyticsConstants.mobileLeadFilterButtonApplyClick);
                    },
                    buttonText: "Apply",
                  )),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  // Lead Filter ListView With checkbox and radio tile
  Widget _buildFilterList(BuildContext context, LeadFilterState state, ItemLeadFilterCategoryModel category) {
    final selectedCategoryItem = (category.searchController?.text.isEmpty ?? false) ? state.leadFilterCategories[state.selectedCategoryIndex] : state.searchFilteredCategories[state.selectedCategoryIndex];
    final leadFilters = selectedCategoryItem.filters;
    return (selectedCategoryItem.isReorderAbleList && category.isReorderAbleList)
        ? _buildReorderAbleListCategory(state, category)
        : ListView.builder(
            itemCount: leadFilters?.length ?? 0,
            itemBuilder: (context, index) {
              final categoryFilters = leadFilters!;
              final filter = categoryFilters[index];
              return Column(
                children: [
                  if (selectedCategoryItem.hasMultiSelect)
                    CheckboxListTile(
                      key: ValueKey(index),
                      value: filter.isSelected,
                      onChanged: (value) {
                        context.read<LeadFilterBloc>().add(SelectFilterEvent(itemLeadFilterModel: filter, selectedFilterCategory: selectedCategoryItem));
                      },
                      visualDensity: const VisualDensity(horizontal: -4, vertical: -2),
                      side: const BorderSide(color: ColorPalette.gray500, width: 1),
                      checkboxShape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
                      title: Row(
                        children: [
                          Expanded(child: Text(filter.displayName, style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.primary, overflow: TextOverflow.ellipsis))),
                          if (!filter.isActive) Text("(disabled)", style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.fadedRed, overflow: TextOverflow.ellipsis)),
                        ],
                      ),
                      controlAffinity: ListTileControlAffinity.leading,
                      contentPadding: EdgeInsets.zero,
                      activeColor: ColorPalette.primaryGreen,
                    )
                  else
                    RadioListTile(
                      key: ValueKey(index),
                      value: filter,
                      onChanged: (value) {
                        context.read<LeadFilterBloc>().add(SelectFilterEvent(itemLeadFilterModel: filter, selectedFilterCategory: selectedCategoryItem));
                      },
                      visualDensity: const VisualDensity(horizontal: -4, vertical: -2),
                      title: Row(
                        children: [
                          Expanded(child: Text(filter.displayName, style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.primary, overflow: TextOverflow.ellipsis))),
                          if (!filter.isActive) Text("(disabled)", style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.fadedRed, overflow: TextOverflow.ellipsis)),
                        ],
                      ),
                      controlAffinity: ListTileControlAffinity.leading,
                      contentPadding: EdgeInsets.zero,
                      activeColor: ColorPalette.primaryGreen,
                      groupValue: categoryFilters.firstWhereOrNull((element) => element.isSelected) ?? false,
                    ),
                  if (filter.hasCustomView && filter.isSelected) _buildFilterCustomView(selectedCategoryItem.filterKey, state, filter.hasMonthFilter),
                ],
              );
            },
          );
  }

  // Select all widget
  _buildSelectAllItem(BuildContext context, ItemLeadFilterCategoryModel selectedCategoryItem) {
    return [
      const SizedBox(height: 10),
      CheckboxListTile(
        value: (selectedCategoryItem.isAllSelected) ? null : false,
        onChanged: (value) {
          context.read<LeadFilterBloc>().add(SelectFilterEvent(selectedFilterCategory: selectedCategoryItem, isSelectAll: true));
        },
        tristate: true,
        visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
        side: const BorderSide(color: ColorPalette.gray500, width: 1),
        checkboxShape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        title: Text((selectedCategoryItem.isAllSelected) ? "Deselect all" : "Select all", style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.primary)),
        controlAffinity: ListTileControlAffinity.leading,
        contentPadding: EdgeInsets.zero,
        activeColor: ColorPalette.primaryGreen,
      ),
      const Divider(color: ColorPalette.tertiaryTextColor, thickness: .5, indent: 10, endIndent: 20),
    ];
  }

  _buildSearchWidget(BuildContext context, ItemLeadFilterCategoryModel selectedCategoryItem, LeadFilterState state) {
    return [
      if (selectedCategoryItem.hasSearch)
        SizedBox(
          height: 65,
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 10, 14, 14),
            child: TextField(
              controller: selectedCategoryItem.searchController,
              onChanged: (value) => context.read<LeadFilterBloc>().add(SearchFiltersItemsEvent(searchText: selectedCategoryItem.searchController?.text, filterKey: selectedCategoryItem.filterKey)),
              style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.gray500),
              cursorColor: ColorPalette.primaryGreen,
              decoration: InputDecoration(
                fillColor: ColorPalette.primaryColor,
                filled: true,
                border: const OutlineInputBorder(borderSide: BorderSide(color: ColorPalette.primaryLightColor)),
                enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: const BorderSide(color: ColorPalette.primaryLightColor)),
                focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: const BorderSide(color: ColorPalette.primaryLightColor)),
                contentPadding: const EdgeInsets.fromLTRB(14, 5, 5, 10),
                hintStyle: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.gray700),
                hintText: selectedCategoryItem.searchHintText ?? "type here",
                suffixIcon: const Icon(CupertinoIcons.search),
              ),
            ),
          ),
        ),
      if (selectedCategoryItem.filterKey == LeadFilterKey.assignedTo) ..._buildCustomAssignedToHeader(state),
      const Divider(color: ColorPalette.tertiaryTextColor, thickness: .5, indent: 10, endIndent: 20),
    ];
  }

  _buildPinCodeTextField(BuildContext context, ItemLeadFilterCategoryModel selectedCategoryItem, LeadFilterState state) {
    return [
      if (selectedCategoryItem.pinCodeController != null)
        SizedBox(
          height: 65,
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 10, 14, 14),
            child: TextField(
              controller: selectedCategoryItem.pinCodeController,
              style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.gray500),
              cursorColor: ColorPalette.primaryGreen,
              decoration: InputDecoration(
                fillColor: ColorPalette.primaryColor,
                filled: true,
                border: const OutlineInputBorder(borderSide: BorderSide(color: ColorPalette.primaryLightColor)),
                enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: const BorderSide(color: ColorPalette.primaryLightColor)),
                focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(4), borderSide: const BorderSide(color: ColorPalette.primaryLightColor)),
                contentPadding: const EdgeInsets.fromLTRB(14, 5, 5, 10),
                hintStyle: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.gray700),
                hintText: selectedCategoryItem.searchHintText ?? "type here",
              ),
            ),
          ),
        ),
    ];
  }

  Widget _buildFilterCustomView(LeadFilterKey filterKey, LeadFilterState state, bool isMonthPicker) {
    if (isMonthPicker) {
      return _customMonthPicker();
    }
    if (filterKey == LeadFilterKey.dateRange) {
      return _customDatePicker();
    } else if (filterKey == LeadFilterKey.budget) {
      return BudgetRangeSliderWidget(
        startValue: state.customBudget.minBudget?.toDouble() ?? 0,
        endValue: state.customBudget.maxBudget?.toDouble() ?? 1000,
        onRangeChanged: (startValue, endValue) {
          context.read<LeadFilterBloc>().add(CustomBudgetChangeEvent(startValue.round(), endValue.round()));
        },
      );
    }
    return const SizedBox.shrink();
  }

  Widget _customMonthPicker() {
    return BlocBuilder<LeadFilterBloc, LeadFilterState>(
      builder: (context, state) {
        return Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 10, 14, 10),
              child: FilterFirstDateMonthPicker(
                labelText: 'from',
                selectedDate: state.selectedFromDate,
                maxDate: state.selectedToDate,
                onDateSelected: (selectedDate) {
                  if (selectedDate != null) context.read<LeadFilterBloc>().add(SelectFromDateEvent(selectedDate));
                },
              ),
            ),
            Text("&", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.gray600)),
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 10, 14, 0),
              child: FilterCustomMonthPicker(
                labelText: 'to',
                minDate: state.selectedFromDate,
                selectedDate: state.selectedToDate,
                onDateSelected: (selectedDate) {
                  if (selectedDate != null) context.read<LeadFilterBloc>().add(SelectToDateEvent(selectedDate));
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _customDatePicker() {
    return BlocBuilder<LeadFilterBloc, LeadFilterState>(
      builder: (context, state) {
        return Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 10, 14, 10),
              child: FilterCustomDatePicker(
                labelText: 'from',
                selectedDate: state.selectedFromDate,
                maxDate: state.selectedToDate,
                onDateSelected: (selectedDate) {
                  if (selectedDate != null) context.read<LeadFilterBloc>().add(SelectFromDateEvent(selectedDate));
                },
              ),
            ),
            Text("&", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.gray600)),
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 10, 14, 0),
              child: FilterCustomDatePicker(
                labelText: 'to',
                minDate: state.selectedFromDate,
                selectedDate: state.selectedToDate,
                onDateSelected: (selectedDate) {
                  if (selectedDate != null) context.read<LeadFilterBloc>().add(SelectToDateEvent(selectedDate));
                },
              ),
            ),
          ],
        );
      },
    );
  }

  _buildCustomAssignedToHeader(LeadFilterState state) {
    return [
      Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Checkbox(
                value: state.isWithHistory,
                onChanged: (value) => context.read<LeadFilterBloc>().add(ToggleWithHistoryEvent()),
                activeColor: ColorPalette.primaryGreen,
                shape: RoundedRectangleBorder(side: const BorderSide(color: ColorPalette.primaryGreen, width: 1), borderRadius: BorderRadius.circular(4)),
                side: const BorderSide(color: ColorPalette.primaryGreen),
                visualDensity: const VisualDensity(horizontal: -2, vertical: -4),
              ),
              GestureDetector(
                onTap: () => context.read<LeadFilterBloc>().add(ToggleWithHistoryEvent()),
                child: Text('History', style: LexendTextStyles.lexend11SemiBold.copyWith(color: ColorPalette.white)),
              ),
            ],
          ),
          const SizedBox(width: 10),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Checkbox(
                value: state.isWithTeam,
                onChanged: (value) => context.read<LeadFilterBloc>().add(ToggleWithTeamEvent()),
                activeColor: ColorPalette.primaryGreen,
                shape: RoundedRectangleBorder(side: const BorderSide(color: ColorPalette.primaryGreen, width: 1), borderRadius: BorderRadius.circular(4)),
                side: const BorderSide(color: ColorPalette.primaryGreen),
                visualDensity: const VisualDensity(horizontal: -2, vertical: -4),
              ),
              GestureDetector(
                onTap: () => context.read<LeadFilterBloc>().add(ToggleWithTeamEvent()),
                child: Text('With Team', style: LexendTextStyles.lexend11SemiBold.copyWith(color: ColorPalette.white)),
              ),
            ],
          ),
        ],
      ),
    ];
  }

  _buildCustomHeaderView(BuildContext context, ItemLeadFilterCategoryModel selectedCategoryItem, LeadFilterState state) {
    return [
      if (selectedCategoryItem.filterKey == LeadFilterKey.budget)
        Padding(
          padding: const EdgeInsets.fromLTRB(14, 0, 14, 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text("currency", style: LexendTextStyles.lexend14Medium.copyWith(color: ColorPalette.primary)).withFlexible(),
              SelectableItemBottomSheet<String>(
                title: "select currency",
                selectableItems: state.currencies,
                canSearchItems: true,
                onItemSelected: (selectedItem) => context.read<LeadFilterBloc>().add(ChangeCurrencyEvent(selectedItem)),
                selectedItem: state.selectedCurrency,
              ),
            ],
          ),
        ),
      if (selectedCategoryItem.filterKey == LeadFilterKey.dateRange)
        Padding(
          padding: const EdgeInsets.fromLTRB(14, 0, 14, 10),
          child: SelectableItemBottomSheet<DateType>(
            title: "select date type",
            selectableItems: state.dateTypes,
            onItemSelected: (selectedItem) => context.read<LeadFilterBloc>().add(SelectDateTypeEvent(selectedItem)),
            selectedItem: state.selectedDateType,
            child: Container(
              height: 40,
              padding: const EdgeInsets.symmetric(horizontal: 10).copyWith(top: 7),
              margin: const EdgeInsets.only(top: 10),
              decoration: BoxDecoration(
                color: ColorPalette.primaryColor,
                border: Border.all(width: 1, color: ColorPalette.primaryLightColor),
                borderRadius: BorderRadius.circular(5),
              ),
              alignment: Alignment.topCenter,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Text(
                      state.selectedDateType?.title ?? 'select date type',
                      style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.white),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const Icon(Icons.keyboard_arrow_down, size: 23, color: ColorPalette.white),
                ],
              ),
            ),
          ),
        ),
    ];
  }

  Widget _buildCustomFilterWidget(BuildContext context, LeadFilterState state, ItemLeadFilterCategoryModel? selectedCategoryItem) {
    if (LeadFilterKey.buildUpArea == selectedCategoryItem?.filterKey) {
      return _buildMinMaxAreaWidget(
        filterKey: selectedCategoryItem?.filterKey,
        selectableItems: state.selectableAreaUnits ?? [],
        minValue: state.builtUpArea?.min,
        maxValue: state.builtUpArea?.max,
        selectedItem: state.selectedBuildUpAreaUnit,
        moduleName: 'buildup area',
      );
    } else if (LeadFilterKey.salableArea == selectedCategoryItem?.filterKey) {
      return _buildMinMaxAreaWidget(
        filterKey: selectedCategoryItem?.filterKey,
        selectableItems: state.selectableAreaUnits ?? [],
        minValue: state.saleableArea?.min,
        maxValue: state.saleableArea?.max,
        selectedItem: state.selectedSalableAreaUnit,
        moduleName: 'saleable area',
      );
    } else if (LeadFilterKey.carpetArea == selectedCategoryItem?.filterKey) {
      return _buildMinMaxAreaWidget(
        filterKey: selectedCategoryItem?.filterKey,
        selectableItems: state.selectableAreaUnits ?? [],
        minValue: state.carpetArea?.min,
        maxValue: state.carpetArea?.max,
        selectedItem: state.selectedCarpetAreaUnit,
        moduleName: 'carpet area',
      );
    } else if (LeadFilterKey.propertyArea == selectedCategoryItem?.filterKey) {
      return _buildMinMaxAreaWidget(
        filterKey: selectedCategoryItem?.filterKey,
        selectableItems: state.selectableAreaUnits ?? [],
        minValue: state.propertyArea?.min,
        maxValue: state.propertyArea?.max,
        selectedItem: state.selectedPropertyAreaUnit,
        moduleName: 'property area',
      );
    } else if (LeadFilterKey.minBudget == selectedCategoryItem?.filterKey) {
      return _buildMinMaxBudgetWidget(
        filterKey: selectedCategoryItem?.filterKey,
        selectableItems: state.currencies,
        maxValue: state.minBudget?.max,
        minValue: state.minBudget?.min,
        selectedItem: state.selectedCurrency,
      );
    } else if (LeadFilterKey.netArea == selectedCategoryItem?.filterKey) {
      return _buildMinMaxAreaWidget(
        filterKey: selectedCategoryItem?.filterKey,
        selectableItems: state.selectableAreaUnits ?? [],
        minValue: state.netArea?.min,
        maxValue: state.netArea?.max,
        selectedItem: state.selectedNetAreaUnit,
        moduleName: 'net area',
      );
    } else if (LeadFilterKey.maxBudget == selectedCategoryItem?.filterKey) {
      return _buildMinMaxBudgetWidget(
        filterKey: selectedCategoryItem?.filterKey,
        selectableItems: state.currencies,
        maxValue: state.maxBudget?.max,
        minValue: state.maxBudget?.min,
        selectedItem: state.selectedCurrency,
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildMinMaxAreaWidget({required LeadFilterKey? filterKey, required List<SelectableItem<MasterAreaUnitsModel>> selectableItems, double? minValue, double? maxValue, SelectableItem<MasterAreaUnitsModel>? selectedItem, required String moduleName}) {
    return Expanded(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: SelectableItemBottomSheet<MasterAreaUnitsModel>(
              title: "select unit",
              canDeselectSingleItem: true,
              selectableItems: selectableItems,
              onItemSelected: (selectedItem) => context.read<LeadFilterBloc>().add(AreaUnitChangedEvent(selectedItem, filterKey: filterKey)),
              selectedItem: selectedItem,
              child: Container(
                height: 40,
                padding: const EdgeInsets.symmetric(horizontal: 10).copyWith(top: 7),
                margin: const EdgeInsets.only(top: 10),
                decoration: BoxDecoration(
                  color: ColorPalette.primaryColor,
                  border: Border.all(width: 1, color: ColorPalette.primaryLightColor),
                  borderRadius: BorderRadius.circular(5),
                ),
                alignment: Alignment.topCenter,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Text(
                        selectedItem?.title ?? 'select unit',
                        style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.white),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const Icon(Icons.keyboard_arrow_down, size: 23, color: ColorPalette.white),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 10),
          RangeInputWidgetLead(
            key: ValueKey(filterKey),
            onRangeChanged: (rangeInput) => context.read<LeadFilterBloc>().add(AreaSizeInputChangeEvent(rangeInput, filterKey: filterKey)),
            initialMaxValue: maxValue,
            initialMinValue: minValue,
            moduleName: moduleName,
          ),
        ],
      ),
    );
  }

  Widget _buildMinMaxBudgetWidget({
    required LeadFilterKey? filterKey,
    required List<SelectableItem<String>> selectableItems,
    double? minValue,
    double? maxValue,
    SelectableItem<String>? selectedItem,
  }) {
    return Expanded(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: SelectableItemBottomSheet<String>(
              title: "select Currency",
              selectableItems: selectableItems,
              onItemSelected: (selectedItem) => context.read<LeadFilterBloc>().add(ChangeCurrencyEvent(selectedItem)),
              selectedItem: selectedItem,
              child: Container(
                height: 40,
                padding: const EdgeInsets.symmetric(horizontal: 10).copyWith(top: 7),
                margin: const EdgeInsets.only(top: 10),
                decoration: BoxDecoration(
                  color: ColorPalette.primaryColor,
                  border: Border.all(width: 1, color: ColorPalette.primaryLightColor),
                  borderRadius: BorderRadius.circular(5),
                ),
                alignment: Alignment.topCenter,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Text(
                        selectedItem?.title ?? 'select Currency',
                        style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.white),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const Icon(Icons.keyboard_arrow_down, size: 23, color: ColorPalette.white),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 10),
          RangeInputWidgetLead(
            key: ValueKey(filterKey),
            onRangeChanged: (rangeInput) => LeadFilterKey.minBudget == filterKey ? context.read<LeadFilterBloc>().add(OnMinBudgetChangeEvent(rangeInput)) : context.read<LeadFilterBloc>().add(OnMaxBudgetChangeEvent(rangeInput)),
            initialMaxValue: maxValue,
            initialMinValue: minValue,
          ),
        ],
      ),
    );
  }

  Widget _buildReorderAbleListCategory(
    LeadFilterState state,
    ItemLeadFilterCategoryModel category,
  ) {
    List<ItemSimpleModel> result = _getCategoriesOrders(category);
    List<ItemSimpleModel> selectedItems = _getSelectedCategoriesOrders();
    List<ItemSimpleModel> defaultItems = _getDefaultCategoriesOrders();

    return SelectableReorderAbleListWidget(
      categories: result,
      selectedItems: selectedItems,
      defaultSelection: defaultItems,
      onSelectionChanged: (selected) {
        if (selected.isEmpty) {
          LeadratCustomSnackbar.show(context: context, message: "At least one category must be selected", type: SnackbarType.error);
          return;
        }
        context.read<LeadFilterBloc>().add(UpdateCategoryOrdersEvent(selectedCategories: selected));
      },
      maxSelections: 7,
      showDragHandle: true,
      isItemSelectable: (item) {
        // If this is the last selected item, prevent deselection
        if (item.isSelected && (state.categories?.length ?? 0) <= 1) {
          return false;
        }
        return true;
      },
    );
  }

  List<ItemSimpleModel> _getCategoriesOrders(ItemLeadFilterCategoryModel? selectedCategoryItem) {
    List<ItemSimpleModel> categoryOrderItems = (selectedCategoryItem?.filters ?? []).map((filterItem) {
      return ItemSimpleModel(
        title: filterItem.displayName,
        value: filterItem.value,
        isSelected: filterItem.isSelected,
        itemIndex: filterItem.itemIndex,
        description: filterItem.description,
      );
    }).toList();
    return categoryOrderItems;
  }

  List<ItemSimpleModel> _getSelectedCategoriesOrders() {
    if (context.read<LeadFilterBloc>().globalSettingModel?.isCustomStatusEnabled ?? false) {
      List<ItemSimpleModel> categoryOrderItems = (context.read<LeadFilterBloc>().customDefaultLeadStatus ?? []).where((i) => i.isDefault ?? false).map((filterItem) {
        return ItemSimpleModel(
          title: filterItem.name ?? '',
          value: filterItem.orderRank,
          isSelected: filterItem.isDefault ?? false,
          itemIndex: filterItem.orderRank,
          description: filterItem.id,
        );
      }).toList();
      return categoryOrderItems;
    } else {
      List<ItemSimpleModel> categoryOrderItems = (context.read<LeadFilterBloc>().defaultTypes ?? []).where((i) => i.isDefault ?? false).map((filterItem) {
        return ItemSimpleModel(
          title: filterItem.categiryType?.description ?? '',
          value: filterItem.categiryType,
          isSelected: filterItem.isDefault ?? false,
          itemIndex: filterItem.orderRank,
        );
      }).toList();
      return categoryOrderItems;
    }
  }

  List<ItemSimpleModel> _getDefaultCategoriesOrders() {
    if (context.read<LeadFilterBloc>().globalSettingModel?.isCustomStatusEnabled ?? false) {
      List<ItemSimpleModel> categoryOrderItems = (context.read<LeadFilterBloc>().leadCustomCategoriesOrders ?? []).where((i) => i.isDefault ?? false).map((filterItem) {
        return ItemSimpleModel(
          title: filterItem.name ?? '',
          value: filterItem.orderRank,
          isSelected: filterItem.isDefault ?? false,
          itemIndex: filterItem.orderRank,
          description: filterItem.id,
        );
      }).toList();
      return categoryOrderItems;
    } else {
      List<ItemSimpleModel> categoryOrderItems = context.read<LeadFilterBloc>().leadCategoriesOrders.where((i) => i.isDefault ?? false).map((filterItem) {
        return ItemSimpleModel(
          title: filterItem.categiryType?.description ?? '',
          value: filterItem.categiryType,
          isSelected: filterItem.isDefault ?? false,
          itemIndex: filterItem.orderRank,
          description: filterItem.id,
        );
      }).toList();
      return categoryOrderItems;
    }
  }
}
