// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_property_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AddPropertyModel _$AddPropertyModelFromJson(Map<String, dynamic> json) =>
    AddPropertyModel(
      serialNo: json['serialNo'] as String?,
      id: json['id'] as String?,
      title: json['title'] as String?,
      saleType: (json['saleType'] as num?)?.toInt(),
      enquiredFor: (json['enquiredFor'] as num?)?.toInt(),
      notes: json['notes'] as String?,
      furnishStatus: (json['furnishStatus'] as num?)?.toInt(),
      status: (json['status'] as num?)?.toInt(),
      rating: json['rating'] as String?,
      shareCount: (json['shareCount'] as num?)?.toInt(),
      possessionDate: json['possessionDate'] == null
          ? null
          : DateTime.parse(json['possessionDate'] as String),
      isGOListingEnabled: json['isGOListingEnabled'] as bool?,
      facing: (json['facing'] as num?)?.toInt(),
      goPropertyId: json['goPropertyId'] as String?,
      noOfBHK: (json['noOfBHK'] as num?)?.toDouble(),
      bhkType: (json['bhkType'] as num?)?.toInt(),
      monetaryInfo: json['monetaryInfo'] == null
          ? null
          : PropertyMonetaryInfoModel.fromJson(
              json['monetaryInfo'] as Map<String, dynamic>),
      ownerDetails: json['ownerDetails'] == null
          ? null
          : PropertyOwnerDetailsModel.fromJson(
              json['ownerDetails'] as Map<String, dynamic>),
      dimension: json['dimension'] == null
          ? null
          : PropertyDimensionModel.fromJson(
              json['dimension'] as Map<String, dynamic>),
      tagInfo: json['tagInfo'] == null
          ? null
          : PropertyTagInfoModel.fromJson(
              json['tagInfo'] as Map<String, dynamic>),
      images: (json['images'] as List<dynamic>?)
          ?.map((e) => PropertyImageModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      attributes: (json['attributes'] as List<dynamic>?)
          ?.map(
              (e) => PropertyAttributeModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      amenities: (json['amenities'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      brochures: (json['brochures'] as List<dynamic>?)
          ?.map(
              (e) => PropertyBrochureModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      whatsAppShareCount: (json['whatsAppShareCount'] as num?)?.toInt(),
      callShareCount: (json['callShareCount'] as num?)?.toInt(),
      emailShareCount: (json['emailShareCount'] as num?)?.toInt(),
      smsShareCount: (json['smsShareCount'] as num?)?.toInt(),
      aboutProperty: json['aboutProperty'] as String?,
      address: json['address'] == null
          ? null
          : AddressModel.fromJson(json['address'] as Map<String, dynamic>),
      links:
          (json['links'] as List<dynamic>?)?.map((e) => e as String).toList(),
      project: json['project'] as String?,
      assignedTo: (json['assignedTo'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      listingOnBehalf: (json['listingOnBehalf'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      shouldVisisbleOnListing: json['shouldVisisbleOnListing'] as bool?,
      placeId: json['placeId'] as String?,
      propertyTypeId: json['propertyTypeId'] as String?,
      imageUrls: (json['imageUrls'] as Map<String, dynamic>?)?.map(
        (k, e) =>
            MapEntry(k, (e as List<dynamic>).map((e) => e as String).toList()),
      ),
      noOfFloorsOccupied: (json['noOfFloorsOccupied'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      tenantContactInfo: json['tenantContactInfo'] == null
          ? null
          : TenantContactInfo.fromJson(
              json['tenantContactInfo'] as Map<String, dynamic>),
      noticePeriod:
          $enumDecodeNullable(_$NoticePeriodTypeEnumMap, json['noticePeriod']),
      lockInPeriod:
          $enumDecodeNullable(_$LockInPeriodTypeEnumMap, json['lockInPeriod']),
      coWorkingOperator: json['coWorkingOperator'] as String?,
      coWorkingOperatorName: json['coWorkingOperatorName'] as String?,
      coWorkingOperatorPhone: json['coWorkingOperatorPhone'] as String?,
      listingAddresses: (json['listingAddresses'] as List<dynamic>?)
          ?.map((e) =>
              CreateListingAddressModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      dldPermitNumber: json['dldPermitNumber'] as String?,
      refrenceNo: json['refrenceNo'] as String?,
      dtcmPermit: json['dtcmPermit'] as String?,
      offeringType:
          $enumDecodeNullable(_$OfferingTypeEnumMap, json['offeringType']),
      completionStatus: $enumDecodeNullable(
          _$CompletionStatusEnumMap, json['completionStatus']),
      language: json['language'] as String?,
      titleWithLanguage: json['titleWithLanguage'] as String?,
      aboutPropertyWithLanguage: json['aboutPropertyWithLanguage'] as String?,
      view360Url: (json['view360Url'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      taxationMode:
          $enumDecodeNullable(_$TaxationModeEnumMap, json['taxationMode']),
      isWaterMarkEnabled: json['isWaterMarkEnabled'] as bool?,
      possesionType:
          $enumDecodeNullable(_$PossessionTypeEnumMap, json['possesionType']),
      securityDepositAmount:
          (json['securityDepositAmount'] as num?)?.toDouble(),
      securityDepositUnit: json['securityDepositUnit'] as String?,
      propertyOwnerDetails: (json['propertyOwnerDetails'] as List<dynamic>?)
          ?.map((e) =>
              PropertyOwnerDetailsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AddPropertyModelToJson(AddPropertyModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.title case final value?) 'title': value,
      if (instance.saleType case final value?) 'saleType': value,
      if (instance.enquiredFor case final value?) 'enquiredFor': value,
      if (instance.notes case final value?) 'notes': value,
      if (instance.furnishStatus case final value?) 'furnishStatus': value,
      if (instance.status case final value?) 'status': value,
      if (instance.rating case final value?) 'rating': value,
      if (instance.shareCount case final value?) 'shareCount': value,
      if (instance.possessionDate?.toIso8601String() case final value?)
        'possessionDate': value,
      if (instance.isGOListingEnabled case final value?)
        'isGOListingEnabled': value,
      if (instance.facing case final value?) 'facing': value,
      if (instance.goPropertyId case final value?) 'goPropertyId': value,
      if (instance.noOfBHK case final value?) 'noOfBHK': value,
      if (instance.bhkType case final value?) 'bhkType': value,
      if (instance.monetaryInfo?.toJson() case final value?)
        'monetaryInfo': value,
      if (instance.ownerDetails?.toJson() case final value?)
        'ownerDetails': value,
      if (instance.dimension?.toJson() case final value?) 'dimension': value,
      if (instance.tagInfo?.toJson() case final value?) 'tagInfo': value,
      if (instance.images?.map((e) => e.toJson()).toList() case final value?)
        'images': value,
      if (instance.attributes?.map((e) => e.toJson()).toList()
          case final value?)
        'attributes': value,
      if (instance.amenities case final value?) 'amenities': value,
      if (instance.brochures?.map((e) => e.toJson()).toList() case final value?)
        'brochures': value,
      if (instance.noOfFloorsOccupied case final value?)
        'noOfFloorsOccupied': value,
      if (instance.isWaterMarkEnabled case final value?)
        'isWaterMarkEnabled': value,
      if (instance.whatsAppShareCount case final value?)
        'whatsAppShareCount': value,
      if (instance.callShareCount case final value?) 'callShareCount': value,
      if (instance.emailShareCount case final value?) 'emailShareCount': value,
      if (instance.smsShareCount case final value?) 'smsShareCount': value,
      if (instance.aboutProperty case final value?) 'aboutProperty': value,
      if (instance.address?.toJson() case final value?) 'address': value,
      if (instance.links case final value?) 'links': value,
      if (instance.project case final value?) 'project': value,
      if (instance.assignedTo case final value?) 'assignedTo': value,
      if (instance.listingOnBehalf case final value?) 'listingOnBehalf': value,
      if (instance.shouldVisisbleOnListing case final value?)
        'shouldVisisbleOnListing': value,
      if (instance.placeId case final value?) 'placeId': value,
      if (instance.propertyTypeId case final value?) 'propertyTypeId': value,
      if (instance.imageUrls case final value?) 'imageUrls': value,
      if (instance.serialNo case final value?) 'serialNo': value,
      if (instance.coWorkingOperator case final value?)
        'coWorkingOperator': value,
      if (instance.coWorkingOperatorName case final value?)
        'coWorkingOperatorName': value,
      if (instance.coWorkingOperatorPhone case final value?)
        'coWorkingOperatorPhone': value,
      if (_$LockInPeriodTypeEnumMap[instance.lockInPeriod] case final value?)
        'lockInPeriod': value,
      if (_$NoticePeriodTypeEnumMap[instance.noticePeriod] case final value?)
        'noticePeriod': value,
      if (instance.tenantContactInfo?.toJson() case final value?)
        'tenantContactInfo': value,
      if (instance.listingAddresses?.map((e) => e.toJson()).toList()
          case final value?)
        'listingAddresses': value,
      if (instance.dldPermitNumber case final value?) 'dldPermitNumber': value,
      if (instance.refrenceNo case final value?) 'refrenceNo': value,
      if (instance.dtcmPermit case final value?) 'dtcmPermit': value,
      if (_$OfferingTypeEnumMap[instance.offeringType] case final value?)
        'offeringType': value,
      if (_$CompletionStatusEnumMap[instance.completionStatus]
          case final value?)
        'completionStatus': value,
      if (instance.language case final value?) 'language': value,
      if (instance.titleWithLanguage case final value?)
        'titleWithLanguage': value,
      if (instance.aboutPropertyWithLanguage case final value?)
        'aboutPropertyWithLanguage': value,
      if (instance.view360Url case final value?) 'view360Url': value,
      if (_$TaxationModeEnumMap[instance.taxationMode] case final value?)
        'taxationMode': value,
      if (_$PossessionTypeEnumMap[instance.possesionType] case final value?)
        'possesionType': value,
      if (instance.securityDepositAmount case final value?)
        'securityDepositAmount': value,
      if (instance.securityDepositUnit case final value?)
        'securityDepositUnit': value,
      if (instance.propertyOwnerDetails?.map((e) => e.toJson()).toList()
          case final value?)
        'propertyOwnerDetails': value,
    };

const _$NoticePeriodTypeEnumMap = {
  NoticePeriodType.none: 0,
  NoticePeriodType.thirtyDays: 1,
  NoticePeriodType.fortyFiveDays: 2,
  NoticePeriodType.sixtyDays: 3,
  NoticePeriodType.ninetyDays: 4,
  NoticePeriodType.hundredDays: 5,
};

const _$LockInPeriodTypeEnumMap = {
  LockInPeriodType.none: 0,
  LockInPeriodType.oneYear: 1,
  LockInPeriodType.secondYear: 2,
  LockInPeriodType.thirdYear: 3,
  LockInPeriodType.fourthYear: 4,
  LockInPeriodType.fifthYear: 5,
  LockInPeriodType.fivePlusYear: 6,
};

const _$OfferingTypeEnumMap = {
  OfferingType.none: 0,
  OfferingType.ready: 1,
  OfferingType.offPlan: 2,
  OfferingType.secondary: 3,
};

const _$CompletionStatusEnumMap = {
  CompletionStatus.none: 0,
  CompletionStatus.completed: 1,
  CompletionStatus.offPlan: 2,
  CompletionStatus.completedPrimary: 3,
  CompletionStatus.offPlanPrimary: 4,
};

const _$TaxationModeEnumMap = {
  TaxationMode.gstInclusive: 0,
  TaxationMode.gstExclusive: 1,
  TaxationMode.basicCharge: 2,
};

const _$PossessionTypeEnumMap = {
  PossessionType.none: 0,
  PossessionType.underConstruction: 1,
  PossessionType.sixMonths: 2,
  PossessionType.oneYear: 3,
  PossessionType.twoYear: 4,
  PossessionType.customDate: 5,
};

TenantContactInfo _$TenantContactInfoFromJson(Map<String, dynamic> json) =>
    TenantContactInfo(
      id: json['id'] as String?,
      name: json['name'] as String?,
      phone: json['phone'] as String?,
      designation: json['designation'] as String?,
    );

Map<String, dynamic> _$TenantContactInfoToJson(TenantContactInfo instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.phone case final value?) 'phone': value,
      if (instance.designation case final value?) 'designation': value,
    };

CreateListingAddressModel _$CreateListingAddressModelFromJson(
        Map<String, dynamic> json) =>
    CreateListingAddressModel(
      listingSourceId: json['listingSourceId'] as String?,
      locationId: json['locationId'] as String?,
    );

Map<String, dynamic> _$CreateListingAddressModelToJson(
        CreateListingAddressModel instance) =>
    <String, dynamic>{
      if (instance.listingSourceId case final value?) 'listingSourceId': value,
      if (instance.locationId case final value?) 'locationId': value,
    };
