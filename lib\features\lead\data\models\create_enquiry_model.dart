import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/models/address_model.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/common/lead_source_enum.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/purpose_enum.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/property_enums/sale_type.dart';

import '../../../../core_main/enums/common/no_of_beds.dart';

part 'create_enquiry_model.g.dart';

@JsonSerializable(includeIfNull: false, explicitToJson: true)
class CreateLeadEnquiryModel {
  final EnquiryType? enquiredFor;
  final SaleType? saleType;
  final LeadSource? leadSource;
  final String? subSource;
  final int? lowerBudget;
  final int? upperBudget;
  final double? noOfBHK;
  final BHKType? bhkType;
  final double? area;
  final String? areaUnitId;
  final String? areaUnit;
  final bool? isPrimary;
  final AddressModel? address;
  final String? propertyTypeId;
  final List<String>? propertyTypeIds;
  final double? carpetArea;
  final double? saleableArea;
  final double? builtUpArea;
  final double? propertyArea;
  final String? netAreaUnitId;
  final double? netArea;
  final String? unitName;
  final String? carpetAreaUnitId;
  final String? saleableAreaUnitId;
  final String? builtUpAreaUnitId;
  final String? propertyAreaUnitId;
  final double? conversionFactor;
  final double? saleableAreaConversionFactor;
  final double? builtUpAreaConversionFactor;
  final double? propertyAreaConversionFactor;
  final double? netAreaConversionFactor;
  final DateTime? possessionDate;
  final String? currency;
  final List<EnquiryType>? enquiryTypes;
  final List<BHKType>? bhkTypes;
  final List<double>? bhks;
  final List<int>? baths;
  final List<AddressModel>? addresses;
  final List<Beds>? beds;
  final int? furnished;
  final int? offerType;
  final List<String>? floors;
  final String? clusterName;
  final PurposeEnum? purpose;
  final PossessionType? possesionType;

  CreateLeadEnquiryModel({
    this.enquiredFor,
    this.saleType,
    this.leadSource,
    this.subSource,
    this.lowerBudget,
    this.upperBudget,
    this.noOfBHK,
    this.bhkType,
    this.area,
    this.areaUnitId,
    this.areaUnit,
    this.isPrimary,
    this.address,
    this.propertyTypeId,
    this.propertyTypeIds,
    this.carpetArea,
    this.saleableArea,
    this.builtUpArea,
    this.carpetAreaUnitId,
    this.saleableAreaUnitId,
    this.builtUpAreaUnitId,
    this.conversionFactor,
    this.saleableAreaConversionFactor,
    this.builtUpAreaConversionFactor,
    this.possessionDate,
    this.currency,
    this.enquiryTypes,
    this.bhkTypes,
    this.bhks,
    this.addresses,
    this.beds,
    this.baths,
    this.furnished,
    this.offerType,
    this.floors,
    this.propertyArea,
    this.netAreaUnitId,
    this.netArea,
    this.unitName,
    this.propertyAreaUnitId,
    this.propertyAreaConversionFactor,
    this.netAreaConversionFactor,
    this.clusterName,
    this.purpose,
    this.possesionType,
  });

  factory CreateLeadEnquiryModel.fromJson(Map<String, dynamic> json) => _$CreateLeadEnquiryModelFromJson(json);

  Map<String, dynamic> toJson() => _$CreateLeadEnquiryModelToJson(this);

  CreateLeadEnquiryModel copyWith({
    DateTime? createdOn,
    String? createdBy,
    DateTime? lastModifiedOn,
    String? lastModifiedBy,
    EnquiryType? enquiredFor,
    SaleType? saleType,
    LeadSource? leadSource,
    String? subSource,
    int? lowerBudget,
    int? upperBudget,
    double? noOfBHK,
    BHKType? bhkType,
    double? area,
    String? areaUnitId,
    String? areaUnit,
    bool? isPrimary,
    AddressModel? address,
    String? propertyTypeId,
    List<String>? propertyTypeIds,
    double? carpetArea,
    double? saleableArea,
    double? builtUpArea,
    String? carpetAreaUnitId,
    String? saleableAreaUnitId,
    String? builtUpAreaUnitId,
    double? conversionFactor,
    double? saleableAreaConversionFactor,
    double? builtUpAreaConversionFactor,
    DateTime? possessionDate,
    String? currency,
    List<EnquiryType>? enquiryTypes,
    List<BHKType>? bhkTypes,
    List<double>? bhks,
    List<int>? baths,
    List<AddressModel>? addresses,
    List<Beds>? beds,
    int? furnished,
    int? offerType,
    List<String>? floors,
    String? unitName,
    String? netAreaUnitId,
    double? netArea,
    double? netAreaConversionFactor,
    double? propertyArea,
    String? propertyAreaUnitId,
    double? propertyAreaConversionFactor,
    bool resetCarpetAreaUnit = false,
    bool resetSaleableAreaUnit = false,
    bool resetBuiltUpAreaAreaUnit = false,
    bool resetNetAreaUnit = false,
    bool resetPropertyAreaUnit = false,
    String? clusterName,
    PurposeEnum? purpose,
    PossessionType? possesionType,
  }) {
    return CreateLeadEnquiryModel(
      enquiredFor: enquiredFor ?? this.enquiredFor,
      saleType: saleType ?? this.saleType,
      leadSource: leadSource ?? this.leadSource,
      subSource: subSource ?? this.subSource,
      lowerBudget: lowerBudget ?? this.lowerBudget,
      upperBudget: upperBudget ?? this.upperBudget,
      noOfBHK: noOfBHK ?? this.noOfBHK,
      bhkType: bhkType ?? this.bhkType,
      area: area ?? this.area,
      areaUnitId: areaUnitId ?? this.areaUnitId,
      areaUnit: areaUnit ?? this.areaUnit,
      isPrimary: isPrimary ?? this.isPrimary,
      address: address ?? this.address,
      propertyTypeId: propertyTypeId ?? this.propertyTypeId,
      propertyTypeIds: propertyTypeIds ?? this.propertyTypeIds,
      carpetArea: carpetArea ?? this.carpetArea,
      saleableArea: saleableArea ?? this.saleableArea,
      builtUpArea: builtUpArea ?? this.builtUpArea,
      netArea: netArea ?? this.netArea,
      propertyArea: propertyArea ?? this.propertyArea,
      carpetAreaUnitId: resetCarpetAreaUnit ? null : (carpetAreaUnitId ?? this.carpetAreaUnitId),
      saleableAreaUnitId: resetSaleableAreaUnit ? null : (saleableAreaUnitId ?? this.saleableAreaUnitId),
      builtUpAreaUnitId: resetBuiltUpAreaAreaUnit ? null : (builtUpAreaUnitId ?? this.builtUpAreaUnitId),
      netAreaUnitId: resetNetAreaUnit ? null : (netAreaUnitId ?? this.netAreaUnitId),
      propertyAreaUnitId: resetPropertyAreaUnit ? null : (propertyAreaUnitId ?? this.propertyAreaUnitId),
      conversionFactor: conversionFactor ?? this.conversionFactor,
      saleableAreaConversionFactor: saleableAreaConversionFactor ?? this.saleableAreaConversionFactor,
      builtUpAreaConversionFactor: builtUpAreaConversionFactor ?? this.builtUpAreaConversionFactor,
      netAreaConversionFactor: netAreaConversionFactor ?? this.netAreaConversionFactor,
      propertyAreaConversionFactor: netAreaConversionFactor ?? this.netAreaConversionFactor,
      unitName: unitName ?? this.unitName,
      possessionDate: possessionDate ?? this.possessionDate,
      currency: currency ?? this.currency,
      enquiryTypes: enquiryTypes ?? this.enquiryTypes,
      bhkTypes: bhkTypes ?? this.bhkTypes,
      bhks: bhks ?? this.bhks,
      addresses: addresses ?? this.addresses,
      beds: beds ?? this.beds,
      baths: baths ?? this.baths,
      furnished: furnished ?? this.furnished,
      offerType: offerType ?? this.offerType,
      floors: floors ?? this.floors,
      clusterName: clusterName ?? this.clusterName,
      purpose: purpose ?? this.purpose,
      possesionType: possesionType ?? this.possesionType,
    );
  }
}
