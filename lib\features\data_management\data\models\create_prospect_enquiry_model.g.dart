// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_prospect_enquiry_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateProspectEnquiryModel _$CreateProspectEnquiryModelFromJson(
        Map<String, dynamic> json) =>
    CreateProspectEnquiryModel(
      enquiryType:
          $enumDecodeNullable(_$EnquiryTypeEnumEnumMap, json['enquiryType']),
      enquiryTypes: (json['enquiryTypes'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$EnquiryTypeEnumEnumMap, e))
          .toList(),
      bhkType: $enumDecodeNullable(_$BHKTypeEnumMap, json['bhkType']),
      noOfBhks: (json['noOfBhks'] as num?)?.toDouble(),
      bhkTypes: (json['bhkTypes'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$BHKTypeEnumMap, e))
          .toList(),
      bhks: (json['bhks'] as List<dynamic>?)
          ?.map((e) => (e as num).toDouble())
          .toList(),
      subSource: json['subSource'] as String?,
      lowerBudget: (json['lowerBudget'] as num?)?.toInt(),
      upperBudget: (json['upperBudget'] as num?)?.toInt(),
      carpetArea: (json['carpetArea'] as num?)?.toDouble(),
      builtUpArea: (json['builtUpArea'] as num?)?.toDouble(),
      saleableArea: (json['saleableArea'] as num?)?.toDouble(),
      conversionFactor: (json['conversionFactor'] as num?)?.toDouble(),
      carpetAreaUnit: json['carpetAreaUnit'] as String?,
      carpetAreaUnitId: json['carpetAreaUnitId'] as String?,
      address: json['address'] == null
          ? null
          : AddressModel.fromJson(json['address'] as Map<String, dynamic>),
      addresses: (json['addresses'] as List<dynamic>?)
          ?.map((e) => AddressModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      currency: json['currency'] as String?,
      prospectSourceId: json['prospectSourceId'] as String?,
      propertyTypeId: json['propertyTypeId'] as String?,
      propertyTypeIds: (json['propertyTypeIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      furnished: (json['furnished'] as num?)?.toInt(),
      offerType: (json['offerType'] as num?)?.toInt(),
      saleableAreaUnitId: json['saleableAreaUnitId'] as String?,
      builtUpAreaUnitId: json['builtUpAreaUnitId'] as String?,
      builtUpAreaConversionFactor:
          (json['builtUpAreaConversionFactor'] as num?)?.toDouble(),
      saleableAreaConversionFactor:
          (json['saleableAreaConversionFactor'] as num?)?.toDouble(),
      floors:
          (json['floors'] as List<dynamic>?)?.map((e) => e as String).toList(),
      beds: (json['beds'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$BedsEnumMap, e))
          .toList(),
      baths: (json['baths'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      bHKs: (json['bHKs'] as List<dynamic>?)
          ?.map((e) => (e as num).toDouble())
          .toList(),
      propertyAreaUnitId: json['propertyAreaUnitId'] as String?,
      propertyArea: (json['propertyArea'] as num?)?.toDouble(),
      netAreaUnitId: json['netAreaUnitId'] as String?,
      netArea: (json['netArea'] as num?)?.toDouble(),
      propertyAreaConversionFactor:
          (json['propertyAreaConversionFactor'] as num?)?.toDouble(),
      netAreaConversionFactor:
          (json['netAreaConversionFactor'] as num?)?.toDouble(),
      unitName: json['unitName'] as String?,
      clusterName: json['clusterName'] as String?,
      purpose: $enumDecodeNullable(_$PurposeEnumEnumMap, json['purpose']) ??
          PurposeEnum.none,
      possesionType:
          $enumDecodeNullable(_$PossessionTypeEnumMap, json['possesionType']),
    );

Map<String, dynamic> _$CreateProspectEnquiryModelToJson(
        CreateProspectEnquiryModel instance) =>
    <String, dynamic>{
      if (_$EnquiryTypeEnumEnumMap[instance.enquiryType] case final value?)
        'enquiryType': value,
      if (instance.enquiryTypes
              ?.map((e) => _$EnquiryTypeEnumEnumMap[e]!)
              .toList()
          case final value?)
        'enquiryTypes': value,
      if (_$BHKTypeEnumMap[instance.bhkType] case final value?)
        'bhkType': value,
      if (instance.noOfBhks case final value?) 'noOfBhks': value,
      if (instance.bhkTypes?.map((e) => _$BHKTypeEnumMap[e]!).toList()
          case final value?)
        'bhkTypes': value,
      if (instance.bhks case final value?) 'bhks': value,
      if (instance.subSource case final value?) 'subSource': value,
      if (instance.lowerBudget case final value?) 'lowerBudget': value,
      if (instance.upperBudget case final value?) 'upperBudget': value,
      if (instance.carpetArea case final value?) 'carpetArea': value,
      if (instance.saleableArea case final value?) 'saleableArea': value,
      if (instance.builtUpArea case final value?) 'builtUpArea': value,
      if (instance.conversionFactor case final value?)
        'conversionFactor': value,
      if (instance.carpetAreaUnit case final value?) 'carpetAreaUnit': value,
      if (instance.carpetAreaUnitId case final value?)
        'carpetAreaUnitId': value,
      if (instance.address case final value?) 'address': value,
      if (instance.addresses case final value?) 'addresses': value,
      if (instance.currency case final value?) 'currency': value,
      if (instance.prospectSourceId case final value?)
        'prospectSourceId': value,
      if (instance.propertyTypeId case final value?) 'propertyTypeId': value,
      if (instance.propertyTypeIds case final value?) 'propertyTypeIds': value,
      if (_$PossessionTypeEnumMap[instance.possesionType] case final value?)
        'possesionType': value,
      if (instance.furnished case final value?) 'furnished': value,
      if (instance.offerType case final value?) 'offerType': value,
      if (instance.saleableAreaUnitId case final value?)
        'saleableAreaUnitId': value,
      if (instance.builtUpAreaUnitId case final value?)
        'builtUpAreaUnitId': value,
      if (instance.saleableAreaConversionFactor case final value?)
        'saleableAreaConversionFactor': value,
      if (instance.builtUpAreaConversionFactor case final value?)
        'builtUpAreaConversionFactor': value,
      if (instance.floors case final value?) 'floors': value,
      if (instance.beds?.map((e) => _$BedsEnumMap[e]!).toList()
          case final value?)
        'beds': value,
      if (instance.bHKs case final value?) 'bHKs': value,
      if (instance.baths case final value?) 'baths': value,
      if (instance.propertyAreaUnitId case final value?)
        'propertyAreaUnitId': value,
      if (instance.propertyArea case final value?) 'propertyArea': value,
      if (instance.netAreaUnitId case final value?) 'netAreaUnitId': value,
      if (instance.netArea case final value?) 'netArea': value,
      if (instance.propertyAreaConversionFactor case final value?)
        'propertyAreaConversionFactor': value,
      if (instance.netAreaConversionFactor case final value?)
        'netAreaConversionFactor': value,
      if (instance.unitName case final value?) 'unitName': value,
      if (instance.clusterName case final value?) 'clusterName': value,
      if (_$PurposeEnumEnumMap[instance.purpose] case final value?)
        'purpose': value,
    };

const _$EnquiryTypeEnumEnumMap = {
  EnquiryTypeEnum.none: 0,
  EnquiryTypeEnum.buy: 1,
  EnquiryTypeEnum.sale: 2,
  EnquiryTypeEnum.rent: 3,
};

const _$BHKTypeEnumMap = {
  BHKType.none: 0,
  BHKType.simplex: 1,
  BHKType.duplex: 2,
  BHKType.pentHouse: 3,
  BHKType.others: 4,
};

const _$BedsEnumMap = {
  Beds.studio: 0,
  Beds.oneBed: 1,
  Beds.twoBed: 2,
  Beds.threeBed: 3,
  Beds.fourBed: 4,
  Beds.fiveBed: 5,
  Beds.sixBed: 6,
  Beds.sevenBed: 7,
  Beds.eightBed: 8,
  Beds.nineBed: 9,
  Beds.tenBed: 10,
};

const _$PurposeEnumEnumMap = {
  PurposeEnum.none: 0,
  PurposeEnum.investment: 1,
  PurposeEnum.selfUse: 2,
};

const _$PossessionTypeEnumMap = {
  PossessionType.none: 0,
  PossessionType.underConstruction: 1,
  PossessionType.sixMonths: 2,
  PossessionType.oneYear: 3,
  PossessionType.twoYear: 4,
  PossessionType.customDate: 5,
};
