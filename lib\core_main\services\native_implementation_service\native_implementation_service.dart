import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_picker/image_picker.dart';
import 'package:leadrat/core_main/enums/app_enum/file_type.dart';
import 'package:leadrat/core_main/enums/app_enum/white_labeled_enum.dart';

import '../../utilities/type_def.dart';

abstract class NativeImplementationService {
  FutureEitherFailure<String?> launchSMS(String uri, String message);

  Future<XFile?> openCamera();

  FutureEitherFailure<List<XFile>?> openGallery({bool isMultiSelection = false});

  FutureEitherFailure<XFile?> openDocuments({FileType fileType = FileType.all});

  FutureEitherFailure<List<XFile?>?> openMultiDocumentsSelection({FileType fileType = FileType.all});

  Future<String?> getImageExtension(XFile? image);

  Future<Placemark?> getCurrentLocationPlaceMarks(double? latitude, double? longitude);

  Future<Position?> getCurrentLocation({LocationAccuracy desiredAccuracy = LocationAccuracy.best, bool forceAndroidLocationManager = false, Duration? timeLimit = const Duration(seconds: 15)});

  Future<bool> isBusinessWhatsappInstalled();

  FutureEitherFailure<String?> launchWhatsApp({required String phoneNumber, required String message, required String packageName});

  Future<void> sendIntent({String? tenantId, String? userId, bool? isLoggedIn});

  Future<bool> changeAppBranding({required WhiteLabeledEnum whiteLabel});

  Future<void> disableMainActivity();
}
