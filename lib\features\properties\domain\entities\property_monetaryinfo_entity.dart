import 'package:leadrat/core_main/enums/property_enums/payment_frequency.dart';

class PropertyMonetaryInfoEntity {
  final String? id;
  final int? expectedPrice;
  final bool? isNegotiable;
  final double? brokerage;
  final int? brokerageUnit;
  final String? currency;
  final String? brokerageCurrency;
  final int? depositAmount;
  final int? maintenanceCost;
  final bool? isPriceVissible;
  final int? noOfChequesAllowed;
  final int? monthlyRentAmount;
  final int? escalationPercentage;
  final PaymentFrequency? paymentFrequency;
  final double? serviceChange;
  final String? depositAmountUnit;

  PropertyMonetaryInfoEntity({
    this.id,
    this.expectedPrice,
    this.isNegotiable,
    this.brokerage,
    this.brokerageUnit,
    this.currency,
    this.brokerageCurrency,
    this.depositAmount,
    this.maintenanceCost,
    this.isPriceVissible,
    this.noOfChequesAllowed,
    this.monthlyRentAmount,
    this.escalationPercentage,
    this.paymentFrequency,
    this.serviceChange,
    this.depositAmountUnit,
  });
}
