import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/base/usecase/use_case.dart';
import 'package:leadrat/core_main/common/constants/app_analytics_constants.dart';
import 'package:leadrat/core_main/common/constants/constants.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/repository/global_setting_repository.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_type_model.dart';
import 'package:leadrat/core_main/common/data/master_data/repository/masterdata_repository.dart';
import 'package:leadrat/core_main/common/data/user/models/get_all_users_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/models/address_model.dart';
import 'package:leadrat/core_main/common/models/dto_with_name_model.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/common/no_of_bhk.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/property_type_enum.dart';
import 'package:leadrat/core_main/enums/common/purpose_enum.dart';
import 'package:leadrat/core_main/enums/data_management/enquiry_type_enum.dart';
import 'package:leadrat/core_main/enums/leads/profession.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/utilities/date_time_utils.dart';
import 'package:leadrat/core_main/utilities/dialog_manager.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/core_main/utilities/phone_utils.dart';
import 'package:leadrat/features/data_management/data/models/add_prospect_model.dart';
import 'package:leadrat/features/data_management/data/models/create_prospect_enquiry_model.dart';
import 'package:leadrat/features/data_management/domain/entities/prospect_entity.dart';
import 'package:leadrat/features/data_management/domain/entities/prospect_sub_source_entity.dart';
import 'package:leadrat/features/data_management/domain/usecase/add_data_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/check_prospect_assigned_by_prospect_id.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_data_by_contact_no_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_sub_source_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/update_data_usecase.dart';
import 'package:leadrat/features/data_management/presentation/bloc/data_details_bloc/data_details_bloc.dart';
import 'package:leadrat/features/data_management/presentation/bloc/manage_data_bloc/manage_data_bloc.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/features/lead/domain/usecase/get_project_name_with_id_use_case.dart';
import 'package:leadrat/features/lead/domain/usecase/get_property_name_with_id_use_case.dart';
import 'package:leadrat/main.dart';

import '../../../../lead/domain/repository/leads_repository.dart';

part 'add_data_event.dart';
part 'add_data_state.dart';

class AddDataBloc extends Bloc<AddDataEvent, AddDataState> {
  //TextEditing Controller
  TextEditingController? dataNameController;
  TextEditingController? phoneController;
  TextEditingController? altPhoneController;
  TextEditingController? emailController;
  TextEditingController? minBudgetController;
  TextEditingController? maxBudgetController;
  TextEditingController? companyNameController;
  TextEditingController? carpetAreaController;
  TextEditingController? notesController;
  TextEditingController? designationController;
  TextEditingController? saleableAreaAreaController;
  TextEditingController? builtUpAreaController;
  TextEditingController? propertyAreaController;
  TextEditingController? netAreaController;
  TextEditingController? unitNumberOrNameController;
  TextEditingController? executiveNameController;
  TextEditingController? executivePhoneController;
  TextEditingController? referralEmailController;
  TextEditingController? referralPhoneController;
  TextEditingController? referralNameController;

  //Private fields
  List<MasterPropertyTypeModel>? _masterPropertyTypes;
  AddProspectModel _addProspectModel = AddProspectModel(enquiry: CreateProspectEnquiryModel());
  List<ProspectSubSourceEntity?> _allDataSource = [];
  ProspectEntity? prospectEntity;
  bool isEditing = false;
  List<GetAllUsersModel?>? _allUsers = [];
  GlobalSettingModel? globalSettings;

  //DI
  final MasterDataRepository _masterDataRepository;
  final UsersDataRepository _usersDataRepository;
  final GetPropertyNameWithIdUseCase _getPropertyNameWithIdUseCase;
  final GetProjectNameWithIdUseCase _getProjectNameWithIdUseCase;
  final GetProspectSubSourceUseCase _getProspectSubSourceUseCase;
  final GetDataByContactNoUseCase _getDataByContactNoUseCase;
  final AddDataUseCase _addDataUseCase;
  final UpdateDataUseCase _updateDataUseCase;
  final LeadsRepository _leadsRepository;
  final AppAnalysisRepository _appAnalysisRepository;
  final CheckProspectAssignedByProspectIdUseCase _checkProspectAssignedByProspectIdUseCase;

  AddDataBloc(
    this._masterDataRepository,
    this._usersDataRepository,
    this._getPropertyNameWithIdUseCase,
    this._getProjectNameWithIdUseCase,
    this._getProspectSubSourceUseCase,
    this._getDataByContactNoUseCase,
    this._addDataUseCase,
    this._updateDataUseCase,
    this._leadsRepository,
    this._appAnalysisRepository,
    this._checkProspectAssignedByProspectIdUseCase,
  ) : super(const AddDataState()) {
    on<AddDataInitialEvent>(_onAddDataInitial);
    on<ResetStateEvent>(_onResetState);
    on<ToggleEnquiredForEvent>(_onToggleEnquiredFor);
    on<TogglePropertyTypeEvent>(_onTogglePropertyType);
    on<TogglePropertySubTypeEvent>(_onTogglePropertySubType);
    on<ToggleSubTypesExpandedEvent>(_onToggleSubTypesExpanded);
    on<ToggleProfessionEvent>(_onToggleProfession);
    on<ToggleNoOfBhkExpandedEvent>(_onToggleNoOfBhkExpanded);
    on<ToggleNoOfBhkEvent>(_onToggleNoOfBhk);
    on<ToggleBhkTypeEvent>(_onToggleBhkType);
    on<SelectAgencyNameEvent>(_onSelectAgencyName);
    on<SelectCarpetAreaEvent>(_onSelectCarpetArea);
    on<SelectProjectsEvent>(_onSelectProjects);
    on<SelectPropertiesEvent>(_onSelectProperties);
    on<RemoveProjectsEvent>(_onRemoveProjects);
    on<RemovePropertyEvent>(_onRemoveProperty);
    on<RemoveAgencyNameEvent>(_onRemoveAgencyName);
    on<ToggleAltPhoneFieldEvent>(_onToggleAltPhoneField);
    on<ToggleEmailFieldEvent>(_onToggleEmailField);
    on<TogglePossessionDateEvent>(_onTogglePossessionDate);
    on<SelectPossessionDateEvent>(_onSelectPossessionDate);
    on<SelectAssignedUserEvent>(_onSelectAssignedUser);
    on<AddLocationEvent>(_onAddLocation);
    on<RemoveLocationEvent>(_onRemoveLocation);
    on<SelectDataSourceEvent>(_onSelectDataSource);
    on<SelectDataSubSourceEvent>(_onSelectDataSubSource);
    on<CheckDataContactAlreadyExistsEvent>(_onCheckDataContactAlreadyExists);
    on<CheckAltContactAlreadyExistsEvent>(_onCheckAltContactAlreadyExists);
    on<OnDataContactChangedEvent>(_onOnDataContactChanged);
    on<OnAltContactChangedEvent>(_onOnAltContactChanged);
    on<CreateDataEvent>(_onCreateData);
    on<PickContactEvent>(_onPickContact);
    on<SelectCurrencyEvent>(_onSelectCurrency);
    on<SelectCampaignNameEvent>(_onSelectCampaignName);
    on<RemoveCampaignNameEvent>(_onRemoveCampaignName);
    on<SelectSaleableAreaEvent>(_onSelectSaleableAreaEvent);
    on<SelectBuiltUpAreaEvent>(_onSelectBuiltUpAreaEvent);
    on<SelectPurposeEvent>(_onSelectPurpose);
    on<SelectSourcingManagerEvent>(_onSelectSourcingManager);
    on<SelectClosingManagerEvent>(_onSelectClosingManager);
    on<AddCustomerLocationEvent>(_onAddCustomerLocation);
    on<RemoveCustomerLocationEvent>(_onRemoveCustomerLocation);
    on<OnExecutiveContactChangedEvent>(_onExecutiveContactChanged);
    on<AssignedToLoggedInUser>(_onAssignedToLoggedInUser);
    on<SelectPossessionType>(_onSelectPossessionType);
    on<ToggleReferralFieldsEvent>(_onToggleReferralFields);
    on<OnReferralContactChangedEvent>(_onOnReferralContactChanged);
  }

  void initTextController() {
    dataNameController = TextEditingController();
    phoneController = TextEditingController();
    altPhoneController = TextEditingController();
    emailController = TextEditingController();
    minBudgetController = TextEditingController();
    maxBudgetController = TextEditingController();
    companyNameController = TextEditingController();
    carpetAreaController = TextEditingController();
    notesController = TextEditingController();
    designationController = TextEditingController();
    saleableAreaAreaController = TextEditingController();
    builtUpAreaController = TextEditingController();
    unitNumberOrNameController = TextEditingController();
    netAreaController = TextEditingController();
    propertyAreaController = TextEditingController();
    executivePhoneController = TextEditingController();
    executiveNameController = TextEditingController();
    referralEmailController = TextEditingController();
    referralPhoneController = TextEditingController();
    referralNameController = TextEditingController();
  }

  void disposeTextController() {
    dataNameController?.dispose();
    phoneController?.dispose();
    altPhoneController?.dispose();
    emailController?.dispose();
    minBudgetController?.dispose();
    maxBudgetController?.dispose();
    companyNameController?.dispose();
    carpetAreaController?.dispose();
    notesController?.dispose();
    designationController?.dispose();
    saleableAreaAreaController?.dispose();
    builtUpAreaController?.dispose();
    unitNumberOrNameController?.dispose();
    netAreaController?.dispose();
    propertyAreaController?.dispose();
    executivePhoneController?.dispose();
    executiveNameController?.dispose();
    referralEmailController?.dispose();
    referralPhoneController?.dispose();
    referralNameController?.dispose();
  }

  FutureOr<void> _onAddDataInitial(AddDataInitialEvent event, Emitter<AddDataState> emit) async {
    prospectEntity = event.prospectEntity;
    isEditing = prospectEntity != null && (prospectEntity?.id.isNotNullOrEmpty() ?? false);
    emit(state.copyWith(showDialogProgress: isEditing));
    _addProspectModel = AddProspectModel(enquiry: CreateProspectEnquiryModel());
    var globalSettingModel = await getIt<GlobalSettingRepository>().getGlobalSettings();
    globalSettings = globalSettingModel;
    final defaultDialCode = globalSettingModel?.countries?.firstOrNull?.defaultCallingCode;
    await _initMasterData(emit);
    _initEnquiredFor(emit);
    _initPropertyTypes(emit);
    _initProfessions(emit);
    _initPurpose(emit);
    _initPossessionType(emit);
    if (isEditing) await _initInitialData(emit);
    emit(state.copyWith(
      pageState: PageState.initial,
      dialogMessage: '',
      isAltPhoneFieldVisible: isEditing ? altPhoneController?.text.isNotEmpty : state.isAltPhoneFieldVisible,
      isEmailFieldVisible: isEditing ? emailController?.text.isNotEmpty : state.isEmailFieldVisible,
      isReferralDetailsVisible: (referralPhoneController?.text.isNotEmpty ?? false) || (referralNameController?.text.isNotEmpty ?? false) || (referralEmailController?.text.isNotEmpty ?? false),
      isPossessionDateVisible: prospectEntity?.possesionDate != null,
      possessionDate: prospectEntity?.possesionDate?.toUserTimeZone(),
      defaultCountryCode: defaultDialCode,
    ));
    emit(state.copyWith(showDialogProgress: false));
    DialogManager().hideTransparentProgressDialog();
  }

  void _initEnquiredFor(Emitter<AddDataState> emit) {
    final selectedEnquiredTypes = prospectEntity?.enquiry?.enquiryTypes?.whereNotNull();
    final enquiryTypes = EnquiryTypeEnum.values.where((type) => type != EnquiryTypeEnum.none).map((type) => ItemSimpleModel<EnquiryTypeEnum>(title: type.description, value: type)).toList();
    emit(state.copyWith(enquiredFor: enquiryTypes));
    if (selectedEnquiredTypes != null) {
      for (var element in selectedEnquiredTypes) {
        add(ToggleEnquiredForEvent(enquiryTypes.firstWhereOrNull((enquiry) => enquiry.value == element)));
      }
    }
  }

  void _initPropertyTypes(Emitter<AddDataState> emit) {
    final initSelectedPropertyType = prospectEntity?.enquiry?.propertyTypes?.firstOrNull;
    final propertyTypes = PropertyType.values.map((type) => ItemSimpleModel<PropertyType>(title: type.description, value: type, description: type.baseId)).toList();
    emit(state.copyWith(propertyTypes: propertyTypes));
    if (initSelectedPropertyType != null) {
      final selectedPropertyType = propertyTypes.firstWhereOrNull((element) => element.description == initSelectedPropertyType.id);
      if (selectedPropertyType != null) add(TogglePropertyTypeEvent(selectedPropertyType));
    }
  }

  void _initProfessions(Emitter<AddDataState> emit) {
    final initSelectedProfession = prospectEntity?.profession;
    final professions = Profession.values.where((profession) => profession != Profession.none).map((profession) => ItemSimpleModel<Profession>(title: profession.description, value: profession)).toList();
    emit(state.copyWith(professions: professions));
    if (initSelectedProfession != null) add(ToggleProfessionEvent(professions.firstWhereOrNull((element) => element.value == initSelectedProfession)));
  }

  Future<void> _initMasterData(Emitter<AddDataState> emit) async {
    try {
      _masterPropertyTypes = await _masterDataRepository.getCustomPropertyTypes(isPropertyListingEnabled: getIt<LeadratHomeBloc>().isPropertyListingEnabled);
      await Future.wait([
        _initMasterAreaUnits(emit),
        _initMasterDataSource(emit),
        _initAgencyNames(emit),
        _initCampaignNames(emit),
        _initAssignToUsers(emit),
        _initProperties(emit),
        _initProjects(emit),
        _initCurrencies(emit),
        _initClosingManager(emit),
        _initSourcingManager(emit),
      ]);
    } catch (exception, stackTrace) {
      exception.logException(stackTrace);
    }
  }

  Future<void> _initCurrencies(Emitter<AddDataState> emit) async {
    try {
      var globalSettingModel = await getIt<GlobalSettingRepository>().getGlobalSettings();
      final countries = globalSettingModel?.countries;
      List<SelectableItem<String>> allCurrencies = [];
      SelectableItem<String>? selectedCurrency;
      countries?.firstOrNull?.currencies?.forEach((item) => allCurrencies.add(SelectableItem<String>(title: item.currency ?? '', value: item.currency)));
      if (countries != null) {
        var defaultSymbol = prospectEntity?.enquiry?.currency ?? globalSettingModel?.countries?.firstOrNull?.defaultCurrency ?? "INR";
        selectedCurrency = allCurrencies.firstWhereOrNull((element) => element.value == defaultSymbol);
        _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(currency: selectedCurrency?.value));
      }
      emit(state.copyWith(currencies: allCurrencies, selectedCurrency: selectedCurrency));
    } catch (ex, stackTrace) {
      ex.logException(stackTrace);
    }
  }

  Future<void> _initMasterAreaUnits(Emitter<AddDataState> emit) async {
    final initSelectedCarpetArea = (prospectEntity?.enquiry?.carpetAreaUnitId?.id?.isNullOrEmptyGuid() ?? true) ? (globalSettings?.defaultValues?["masterAreaUnit"]) : prospectEntity?.enquiry?.carpetAreaUnitId?.id;
    final initSelectedSaleableArea = (prospectEntity?.enquiry?.saleableAreaUnitId?.id?.isNullOrEmptyGuid() ?? true) ? (globalSettings?.defaultValues?["masterAreaUnit"]) : prospectEntity?.enquiry?.saleableAreaUnitId?.id;
    final initSelectedBuiltUpArea = (prospectEntity?.enquiry?.builtUpAreaUnitId?.id?.isNullOrEmptyGuid() ?? true) ? (globalSettings?.defaultValues?["masterAreaUnit"]) : prospectEntity?.enquiry?.builtUpAreaUnitId?.id;
    var masterAreaUnits = await _masterDataRepository.getAreaUnits();
    if (masterAreaUnits?.isNotEmpty ?? false) {
      final carpetAreaUnits = masterAreaUnits!.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit, isSelected: areaUnit?.id == initSelectedCarpetArea)).toList();
      final saleableAreaUnits = masterAreaUnits.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit, isSelected: areaUnit?.id == initSelectedSaleableArea)).toList();
      final builtUpAreaUnits = masterAreaUnits.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit, isSelected: areaUnit?.id == initSelectedBuiltUpArea)).toList();
      emit(state.copyWith(carpetAreas: carpetAreaUnits, saleableAreas: saleableAreaUnits, builtUpAreas: builtUpAreaUnits, errorMessage: ''));
      final selectedCarpetAea = carpetAreaUnits.firstWhereOrNull((element) => element.isSelected);
      final selectedSaleableArea = saleableAreaUnits.firstWhereOrNull((element) => element.isSelected);
      final selectedBuiltUpArea = builtUpAreaUnits.firstWhereOrNull((element) => element.isSelected);
      if (selectedCarpetAea != null) {
        add(SelectCarpetAreaEvent(selectedCarpetAea));
      }
      if (selectedSaleableArea != null) {
        add(SelectBuiltUpAreaEvent(selectedSaleableArea));
      }
      if (selectedBuiltUpArea != null) {
        add(SelectBuiltUpAreaEvent(selectedBuiltUpArea));
      }
    }
  }

  Future<void> _initMasterDataSource(Emitter<AddDataState> emit) async {
    final initialSelectedDataSource = prospectEntity?.enquiry?.prospectSource;
    final dataSource = await _getProspectSubSourceUseCase(NoParams());
    dataSource.fold((failure) => null, (success) {
      if (success != null && success.isNotEmpty) {
        final allDataSource = success
            .where((element) => element?.source?.isEnabled ?? false)
            .map((e) => SelectableItem<String>(
                  title: e?.source?.displayName ?? "",
                  value: e?.source?.id,
                  isSelected: (e?.source?.id != null) ? initialSelectedDataSource?.id == e?.source?.id : false,
                ))
            .toList();
        _allDataSource = success;
        emit(state.copyWith(dataSource: allDataSource, errorMessage: ''));
        final selectedLeadSource = allDataSource.firstWhereOrNull((element) => element.isSelected);
        if (selectedLeadSource != null) add(SelectDataSourceEvent(selectedLeadSource));
      }
    });
  }

  Future<void> _initAgencyNames(Emitter<AddDataState> emit) async {
    final initialSelectedAgencyName = prospectEntity?.agencies?.map((e) => e?.name).whereNotNull();
    var agencyNames = await _masterDataRepository.getAgencyNames();
    if (agencyNames?.isNotEmpty ?? false) {
      final agenciesNames = agencyNames!.map((name) => SelectableItem<String>(title: name, value: name, isSelected: initialSelectedAgencyName?.contains(name) ?? false)).toList();
      final selectedAgencyNames = agenciesNames.where((element) => element.isSelected).toList();
      emit(state.copyWith(agencyNames: agenciesNames, errorMessage: '', selectedAgencyNames: selectedAgencyNames));
      _addProspectModel = _addProspectModel.copyWith(agencies: selectedAgencyNames.map((e) => DTOWithNameModel(name: e.title)).toList());
    }
  }

  Future<void> _initCampaignNames(Emitter<AddDataState> emit) async {
    final initialSelectedCampaignNames = prospectEntity?.campaigns?.map((e) => e.name).nonNulls;
    var campaignNames = await _leadsRepository.getCampaignNames();
    if (campaignNames?.isNotEmpty ?? false) {
      final campaignsNames = campaignNames!.map((name) => SelectableItem<String>(title: name, value: name, isSelected: initialSelectedCampaignNames?.contains(name) ?? false)).toList();
      final selectedCamapaignNames = campaignsNames.where((element) => element.isSelected).toList();
      emit(state.copyWith(campaignNames: campaignsNames, errorMessage: '', selectedCampaignNames: selectedCamapaignNames));
      _addProspectModel = _addProspectModel.copyWith(campaigns: selectedCamapaignNames.map((e) => DTOWithNameModel(name: e.title)).toList());
    }
  }

  Future<void> _initAssignToUsers(Emitter<AddDataState> emit) async {
    final initSelectedPrimaryUserId = prospectEntity?.assignedUser?.id;
    _allUsers = await _usersDataRepository.getAssignUser();
    if (_allUsers?.isNotEmpty ?? false) {
      final currentUser = _usersDataRepository.getLoggedInUser();
      var assignToUsers = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id, isSelected: initSelectedPrimaryUserId == user?.id)).toList();
      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => assignToUsers.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isSelected: initSelectedPrimaryUserId == disabledUsers?.id, isEnabled: false)));
      assignToUsers.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId, isSelected: currentUser?.userId == initSelectedPrimaryUserId));
      emit(state.copyWith(assignToUsers: assignToUsers, errorMessage: ''));
      final selectedPrimaryUser = assignToUsers.firstWhereOrNull((element) => element.isSelected);
      if (selectedPrimaryUser != null) add(SelectAssignedUserEvent(selectedPrimaryUser));
    }
  }

  Future<void> _initProperties(Emitter<AddDataState> emit) async {
    final initialSelectedPropertiesId = prospectEntity?.properties?.map((e) => e?.id).nonNulls.toSet() ?? {};
    final properties = await _getPropertyNameWithIdUseCase(NoParams());
    properties.fold((failure) => null, (success) {
      if (success != null && success.isNotEmpty) {
        final allProperties = success.map((e) => SelectableItem<String>(title: e.title ?? "", value: e.id, isSelected: initialSelectedPropertiesId?.contains(e.id) ?? false)).toList();
        final selectedProperties = allProperties.where((element) => element.isSelected).toList();
        emit(state.copyWith(properties: allProperties, errorMessage: '', selectedProperties: selectedProperties));
        _addProspectModel = _addProspectModel.copyWith(propertiesList: selectedProperties.map((e) => e.title).toList());
      }
    });
  }

  Future<void> _initProjects(Emitter<AddDataState> emit) async {
    final initialSelectedProjectsId = prospectEntity?.projects?.map((e) => e?.id).nonNulls.toSet() ?? {};
    final projects = await _getProjectNameWithIdUseCase(NoParams());
    projects.fold((failure) => null, (success) {
      if (success != null && success.isNotEmpty) {
        final allProjects = success.map((e) => SelectableItem<String>(title: e.name ?? "", value: e.id, isSelected: initialSelectedProjectsId?.contains(e.id) ?? false)).toList();
        final selectedProjects = allProjects.where((element) => element.isSelected).toList();
        emit(state.copyWith(projects: allProjects, errorMessage: '', selectedProjects: selectedProjects));
        _addProspectModel = _addProspectModel.copyWith(projectsList: selectedProjects.map((e) => e.title).toList());
      }
    });
  }

  Future<void> _initInitialData(Emitter<AddDataState> emit) async {
    dataNameController?.text = prospectEntity?.name ?? '';
    phoneController?.text = prospectEntity?.contactNo ?? '';
    emailController?.text = prospectEntity?.email ?? '';
    altPhoneController?.text = prospectEntity?.alternateContactNo ?? '';
    designationController?.text = prospectEntity?.designation ?? '';
    notesController?.text = prospectEntity?.notes ?? '';
    companyNameController?.text = prospectEntity?.companyName ?? '';
    executiveNameController?.text = prospectEntity?.executiveName ?? '';
    executivePhoneController?.text = prospectEntity?.executiveContactNo ?? '';
    referralEmailController?.text = prospectEntity?.referralEmail ?? '';
    referralNameController?.text = prospectEntity?.referralName ?? '';
    referralPhoneController?.text = prospectEntity?.referralContactNo ?? '';
    if (prospectEntity?.enquiry?.upperBudget != null && prospectEntity!.enquiry!.upperBudget! > 0) {
      maxBudgetController?.text = prospectEntity?.enquiry!.upperBudget!.toString() ?? '';
    }
    if (prospectEntity?.enquiry?.lowerBudget != null && prospectEntity!.enquiry!.lowerBudget! > 0) {
      minBudgetController?.text = prospectEntity?.enquiry!.lowerBudget!.toString() ?? '';
    }
    if (prospectEntity?.enquiry?.carpetArea != null && prospectEntity!.enquiry!.carpetArea! > 0.0) {
      carpetAreaController?.text = prospectEntity?.enquiry!.carpetArea!.toString().replaceAll('.0', '') ?? '';
    }
    if (prospectEntity?.enquiry?.saleableArea != null && prospectEntity!.enquiry!.saleableArea! > 0.0) {
      saleableAreaAreaController?.text = prospectEntity?.enquiry?.saleableArea?.toString().replaceAll('.0', '') ?? '';
    }
    if (prospectEntity?.enquiry?.builtUpArea != null && prospectEntity!.enquiry!.builtUpArea! > 0.0) {
      builtUpAreaController?.text = prospectEntity?.enquiry?.builtUpArea?.toString().replaceAll('.0', '') ?? '';
    }
    final initSelectedLocations = prospectEntity?.enquiry?.addresses;
    if (initSelectedLocations != null) {
      final locations = initSelectedLocations.map((e) => ItemSimpleModel<AddressModel>(title: e?.subLocality ?? '${e?.locality ?? ''}, ${e?.city ?? ''}, ${e?.state ?? ''}', value: e?.toModel())).whereNotNull().toList();
      emit(state.copyWith(locations: locations));
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(addresses: locations.map((e) => e.value).whereNotNull().toList()));
    }

    final initSelectedCustomerLocation = prospectEntity?.addressDto;
    if (initSelectedCustomerLocation != null) {
      final customerLocation = ItemSimpleModel<AddressModel>(
        title: '${initSelectedCustomerLocation.subLocality ?? ''}, ${initSelectedCustomerLocation.locality ?? ''}, ${initSelectedCustomerLocation.state ?? ''}, ${initSelectedCustomerLocation.city ?? ''}, ${initSelectedCustomerLocation.country ?? ''}',
        value: initSelectedCustomerLocation.toModel(),
      );
      emit(state.copyWith(customerLocations: customerLocation));
      _addProspectModel = _addProspectModel.copyWith(addressDto: customerLocation.value);
    }
    emit(state);
  }

  Future<void> _initSourcingManager(Emitter<AddDataState> emit) async {
    final initSourcingManager = prospectEntity?.sourcingManager?.id;
    if (_allUsers?.isNotEmpty ?? false) {
      final currentUser = _usersDataRepository.getLoggedInUser();
      var sourcingManagerUsers = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id, isSelected: initSourcingManager == user?.id)).toList();
      //adding disabled users
      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => sourcingManagerUsers.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isSelected: initSourcingManager == disabledUsers?.id, isEnabled: false)));
      sourcingManagerUsers.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId, isSelected: currentUser?.userId == initSourcingManager));
      emit(state.copyWith(sourcingManager: sourcingManagerUsers, errorMessage: ''));
      final selectedSourcingManager = sourcingManagerUsers.firstWhereOrNull((element) => element.isSelected);
      if (selectedSourcingManager != null) {
        add(SelectSourcingManagerEvent(selectedSourcingManager));
      }
    }
  }

  Future<void> _initClosingManager(Emitter<AddDataState> emit) async {
    final initClosingManager = prospectEntity?.closingManagerUser?.id;
    if (_allUsers?.isNotEmpty ?? false) {
      final currentUser = _usersDataRepository.getLoggedInUser();
      var closingManagerUsers = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id, isSelected: initClosingManager == user?.id)).toList();
      //adding disabled users
      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => closingManagerUsers.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isSelected: initClosingManager == disabledUsers?.id, isEnabled: false)));
      closingManagerUsers.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId, isSelected: currentUser?.userId == initClosingManager));
      emit(state.copyWith(closingManager: closingManagerUsers, errorMessage: ''));
      final selectedClosingManager = closingManagerUsers.firstWhereOrNull((element) => element.isSelected);
      if (selectedClosingManager != null) {
        add(SelectClosingManagerEvent(selectedClosingManager));
      }
    }
  }

  FutureOr<void> _onToggleEnquiredFor(ToggleEnquiredForEvent event, Emitter<AddDataState> emit) async {
    if (event.selectedEnquiredFor == null) return;
    final updatedEnquiredFor = state.enquiredFor.map((e) => e.value == event.selectedEnquiredFor?.value ? e.copyWith(isSelected: !e.isSelected) : e).toList();
    emit(state.copyWith(enquiredFor: updatedEnquiredFor, errorMessage: ''));
    final selectedEnquiryTypes = state.enquiredFor.where((element) => element.isSelected).map((e) => e.value).whereNotNull().toList();
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(enquiryTypes: selectedEnquiryTypes));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonEnquiredForClick);
  }

  FutureOr<void> _onTogglePropertyType(TogglePropertyTypeEvent event, Emitter<AddDataState> emit) async {
    final updatedPropertyTypes = state.propertyTypes.map((e) => e.description == event.selectedPropertyType.description ? e.copyWith(isSelected: !e.isSelected) : e.copyWith(isSelected: false)).toList();
    if (event.selectedPropertyType.isSelected) {
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(propertyTypeId: StringConstants.emptyGuidId, propertyTypeIds: [], bhks: [], bhkTypes: []));
      emit(state.copyWith(propertyTypes: updatedPropertyTypes, propertySubTypes: [], errorMessage: '', bhkTypes: [], noOfBhk: []));
    } else {
      final propertySubTypes = initPropertySubTypes(event.selectedPropertyType.description ?? '');
      emit(state.copyWith(propertyTypes: updatedPropertyTypes, propertySubTypes: propertySubTypes, errorMessage: ''));
      _initBhkAndBhkTypes(emit);
    }
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonPropertyTypeClick);
  }

  List<ItemSimpleModel<String>> initPropertySubTypes(String propertyTypeId) {
    if (_masterPropertyTypes == null) return [];
    final initSelectedPropertySubTypesId = prospectEntity?.enquiry?.propertyTypes?.map((p) => p.childType?.id).toList();
    final propertySubTypes = _masterPropertyTypes!
        .where((type) => type.id == propertyTypeId && type.childTypes != null)
        .expand((type) => type.childTypes!)
        .map(
          (subType) => ItemSimpleModel<String>(title: subType.displayName ?? "", value: subType.id, description: subType.baseId),
        )
        .toList();
    if (initSelectedPropertySubTypesId?.isNotEmpty ?? false) {
      final selectedPropertySubTypes = propertySubTypes.where((element) => initSelectedPropertySubTypesId?.contains(element.value) ?? false).toList();
      for (var selectedPropertySubType in selectedPropertySubTypes) {
        add(TogglePropertySubTypeEvent(selectedPropertySubType));
      }
    }
    return propertySubTypes;
  }

  void _initBhkAndBhkTypes(Emitter<AddDataState> emit) {
    final initSelectedNoOfBhk = prospectEntity?.enquiry?.bhKs?.whereNotNull();
    final initSelectedBhkTypes = prospectEntity?.enquiry?.bhkTypes?.whereNotNull();

    final selectedPropertyType = state.propertyTypes.firstWhereOrNull((element) => element.isSelected);
    final selectedPropertySubType = state.propertySubTypes.firstWhereOrNull((element) => element.isSelected);
    if (selectedPropertySubType == null || selectedPropertyType == null) {
      emit(state.copyWith(noOfBhk: [], bhkTypes: [], errorMessage: ''));
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(bhkTypes: [], bhks: []));
    } else if (selectedPropertyType.value == PropertyType.residential && selectedPropertySubType.title.toLowerCase() != "plot") {
      final noOfBhk = NoOfBHK.values.map((bhk) => ItemSimpleModel<double>(title: bhk.description, value: bhk.noOfBhk, isSelected: initSelectedNoOfBhk?.contains(bhk.noOfBhk) ?? false)).toList();
      final bhkTypes = BHKType.values.map((type) => ItemSimpleModel<BHKType>(title: type.description, value: type, isSelected: initSelectedBhkTypes?.contains(type) ?? false)).toList();
      bhkTypes.removeWhere((element) => element.value == BHKType.none);
      emit(state.copyWith(noOfBhk: noOfBhk, bhkTypes: bhkTypes));
      final selectedBhks = state.noOfBhk.where((element) => element.isSelected).map((bhk) => bhk.value).whereNotNull().toList();
      final selectedBhkTypes = state.bhkTypes.where((element) => element.isSelected).map((e) => e.value).whereNotNull().toList();
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(bhkTypes: selectedBhkTypes, bhks: selectedBhks));
    } else {
      emit(state.copyWith(noOfBhk: [], bhkTypes: [], errorMessage: ''));
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(bhkTypes: [], bhks: []));
    }
  }

  FutureOr<void> _onSelectSourcingManager(SelectSourcingManagerEvent event, Emitter<AddDataState> emit) async {
    List<SelectableItem<String>>? updatedSourcingManager;
    final currentUser = _usersDataRepository.getLoggedInUser();
    updatedSourcingManager = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id)).toList();
    _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => updatedSourcingManager?.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isEnabled: false)));
    updatedSourcingManager.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId));
    updatedSourcingManager.removeWhere((element) => element.value == event.selectedSourcingManager.value);
    emit(state.copyWith(selectedSourcingManager: event.selectedSourcingManager, errorMessage: ''));
    _addProspectModel = _addProspectModel.copyWith(sourcingManager: event.selectedSourcingManager.value);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonSourcingManagerClick);
  }

  FutureOr<void> _onSelectClosingManager(SelectClosingManagerEvent event, Emitter<AddDataState> emit) async {
    List<SelectableItem<String>>? updatedClosingManager;
    final currentUser = _usersDataRepository.getLoggedInUser();
    updatedClosingManager = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id)).toList();
    _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => updatedClosingManager?.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isEnabled: false)));
    updatedClosingManager.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId));
    updatedClosingManager.removeWhere((element) => element.value == event.selectedClosingManager.value);
    emit(state.copyWith(selectedClosingManager: event.selectedClosingManager, errorMessage: ''));
    _addProspectModel = _addProspectModel.copyWith(closingManager: event.selectedClosingManager.value);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonClosingManagerClick);
  }

  FutureOr<void> _onAddCustomerLocation(AddCustomerLocationEvent event, Emitter<AddDataState> emit) async {
    if (event.customerLocation == null) return;
    ItemSimpleModel<AddressModel> location;
    var locality = event.customerLocation?.locality ?? event.customerLocation?.subLocality ?? '';
    var state_ = event.customerLocation?.state ?? '';
    var city = event.customerLocation?.city ?? '';
    var country = event.customerLocation?.country ?? '';
    String title = [locality, city, state_, country].where((element) => element.isNotEmpty).join(', ');
    location = ItemSimpleModel<AddressModel>(title: title, value: event.customerLocation);
    emit(state.copyWith(customerLocations: location));
    _addProspectModel = _addProspectModel.copyWith(addressDto: state.customerLocations?.value);
  }

  FutureOr<void> _onRemoveCustomerLocation(RemoveCustomerLocationEvent event, Emitter<AddDataState> emit) {
    emit(state.copyWith(customerLocations: ItemSimpleModel<AddressModel>(title: 'None')));
    _addProspectModel = _addProspectModel.copyWith(addressDto: state.customerLocations?.value);
  }

  FutureOr<void> _onTogglePropertySubType(TogglePropertySubTypeEvent event, Emitter<AddDataState> emit) async {
    final updatedPropertySubTypes = state.propertySubTypes.map((e) => e.value == event.selectedPropertySubType.value ? e.copyWith(isSelected: !e.isSelected) : e).toList();
    emit(state.copyWith(propertySubTypes: updatedPropertySubTypes, errorMessage: ''));
    final propertyTypeIds = state.propertySubTypes.where((element) => element.isSelected).map((e) => e.value).nonNulls.toList();
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(propertyTypeIds: propertyTypeIds));
    _initBhkAndBhkTypes(emit);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonPropertySubTypeClick);
  }

  FutureOr<void> _onToggleSubTypesExpanded(ToggleSubTypesExpandedEvent event, Emitter<AddDataState> emit) {
    emit(state.copyWith(isSubTypesExpanded: !state.isSubTypesExpanded, errorMessage: ''));
  }

  FutureOr<void> _onToggleProfession(ToggleProfessionEvent event, Emitter<AddDataState> emit) async {
    if (event.selectedProfession == null) return;
    final updatedProfession = state.professions.map((e) => e.value == event.selectedProfession!.value ? e.copyWith(isSelected: !e.isSelected) : e.copyWith(isSelected: false)).toList();
    emit(state.copyWith(professions: updatedProfession, errorMessage: ''));
    final selectedProfession = event.selectedProfession!.isSelected ? null : event.selectedProfession!.value;
    _addProspectModel = _addProspectModel.copyWith(profession: selectedProfession);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonProfessionClick);
  }

  FutureOr<void> _onToggleNoOfBhkExpanded(ToggleNoOfBhkExpandedEvent event, Emitter<AddDataState> emit) {
    emit(state.copyWith(isNoOfBhkExpanded: !state.isNoOfBhkExpanded, errorMessage: ''));
  }

  FutureOr<void> _onToggleNoOfBhk(ToggleNoOfBhkEvent event, Emitter<AddDataState> emit) async {
    final updatedNoOfBhk = state.noOfBhk.map((e) => e.value == event.selectedNoOfBhk.value ? e.copyWith(isSelected: !e.isSelected) : e).toList();
    emit(state.copyWith(noOfBhk: updatedNoOfBhk, errorMessage: ''));
    final selectedBhks = state.noOfBhk.where((element) => element.isSelected).map((bhk) => bhk.value).whereNotNull().toList();
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(bhks: selectedBhks));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonBHKClick);
  }

  FutureOr<void> _onToggleBhkType(ToggleBhkTypeEvent event, Emitter<AddDataState> emit) async {
    final updatedNoOfBhkType = state.bhkTypes.map((e) => e.value == event.selectedBhkTypes.value ? e.copyWith(isSelected: !e.isSelected) : e).toList();
    emit(state.copyWith(bhkTypes: updatedNoOfBhkType, errorMessage: ''));
    final selectedBhkTypes = state.bhkTypes.where((element) => element.isSelected).map((e) => e.value).whereNotNull().toList();
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(bhkTypes: selectedBhkTypes));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddLeadButtonBHKTypeClick);
  }

  FutureOr<void> _onResetState(ResetStateEvent event, Emitter<AddDataState> emit) {
    emit(state.initialState());
  }

  FutureOr<void> _onSelectAgencyName(SelectAgencyNameEvent event, Emitter<AddDataState> emit) async {
    emit(state.copyWith(selectedAgencyNames: event.selectedAgencyName, errorMessage: ''));
    final selectedAgencyNames = event.selectedAgencyName.map((e) => DTOWithNameModel(name: e.title)).toList();
    _addProspectModel = _addProspectModel.copyWith(agencies: selectedAgencyNames);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonAgencyNameClick);
  }

  FutureOr<void> _onSelectCarpetArea(SelectCarpetAreaEvent event, Emitter<AddDataState> emit) async {
    if (event.selectedCarpetArea == null) return;
    emit(state.copyWith(selectedCarpetArea: event.selectedCarpetArea, errorMessage: ''));
    final selectedCarpetAreaValue = event.selectedCarpetArea!.isSelected ? event.selectedCarpetArea : null;
    _addProspectModel = _addProspectModel.copyWith(
        enquiry: _addProspectModel.enquiry?.copyWith(
      carpetAreaUnitId: selectedCarpetAreaValue?.value?.id,
      conversionFactor: selectedCarpetAreaValue?.value?.conversionFactor,
    ));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonCarpetAreaUnitClick);
  }

  FutureOr<void> _onSelectSaleableAreaEvent(SelectSaleableAreaEvent event, Emitter<AddDataState> emit) async {
    if (event.selectedSaleableArea == null) return;
    emit(state.copyWith(selectedSaleableArea: event.selectedSaleableArea, errorMessage: ''));
    final selectedSaleableAreaValue = event.selectedSaleableArea!.isSelected ? event.selectedSaleableArea : null;
    _addProspectModel = _addProspectModel.copyWith(
        enquiry: _addProspectModel.enquiry?.copyWith(
      saleableAreaUnitId: selectedSaleableAreaValue?.value?.id,
      saleableAreaConversionFactor: selectedSaleableAreaValue?.value?.conversionFactor,
    ));
  }

  FutureOr<void> _onSelectBuiltUpAreaEvent(SelectBuiltUpAreaEvent event, Emitter<AddDataState> emit) async {
    if (event.selectedBuiltUpArea == null) return;
    emit(state.copyWith(selectedBuiltUpArea: event.selectedBuiltUpArea, errorMessage: ''));
    final selectedBuiltUpAreaValue = event.selectedBuiltUpArea!.isSelected ? event.selectedBuiltUpArea : null;

    _addProspectModel = _addProspectModel.copyWith(
        enquiry: _addProspectModel.enquiry?.copyWith(
      builtUpAreaUnitId: selectedBuiltUpAreaValue?.value?.id,
      builtUpAreaConversionFactor: selectedBuiltUpAreaValue?.value?.conversionFactor,
    ));
  }

  FutureOr<void> _onSelectProjects(SelectProjectsEvent event, Emitter<AddDataState> emit) async {
    emit(state.copyWith(selectedProjects: event.selectedProjects, errorMessage: ''));
    final selectedProjects = event.selectedProjects.map((e) => e.title).toList();
    _addProspectModel = _addProspectModel.copyWith(projectsList: selectedProjects);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonSelectProjectsClick);
  }

  FutureOr<void> _onSelectProperties(SelectPropertiesEvent event, Emitter<AddDataState> emit) async {
    emit(state.copyWith(selectedProperties: event.selectedProperties, errorMessage: ''));
    final selectedProperties = event.selectedProperties.map((e) => e.title).toList();
    _addProspectModel = _addProspectModel.copyWith(propertiesList: selectedProperties);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonSelectPropertiesClick);
  }

  FutureOr<void> _onRemoveProjects(RemoveProjectsEvent event, Emitter<AddDataState> emit) {
    final updatedSelectedProjects = state.selectedProjects?.whereNot((element) => element.title == event.selectedProjects.title).toList();
    emit(state.copyWith(selectedProjects: updatedSelectedProjects, errorMessage: ''));
    _addProspectModel.projectsList?.removeWhere((title) => title == event.selectedProjects.title);
  }

  FutureOr<void> _onRemoveProperty(RemovePropertyEvent event, Emitter<AddDataState> emit) {
    final updatedSelectedProperties = state.selectedProperties?.whereNot((element) => element.title == event.selectedProperty.title).toList();
    emit(state.copyWith(selectedProperties: updatedSelectedProperties, errorMessage: ''));
    _addProspectModel.propertiesList?.removeWhere((title) => title == event.selectedProperty.title);
  }

  FutureOr<void> _onRemoveAgencyName(RemoveAgencyNameEvent event, Emitter<AddDataState> emit) {
    final updatedSelectedAgency = state.selectedAgencyNames?.whereNot((element) => element.title == event.selectedAgencyName.title).toList();
    emit(state.copyWith(selectedAgencyNames: updatedSelectedAgency, errorMessage: ''));
    _addProspectModel.agencies?.removeWhere((element) => element?.name == event.selectedAgencyName.title);
  }

  FutureOr<void> _onToggleAltPhoneField(ToggleAltPhoneFieldEvent event, Emitter<AddDataState> emit) async {
    emit(state.copyWith(isAltPhoneFieldVisible: event.hideAltPhoneField ?? !state.isAltPhoneFieldVisible, errorMessage: '', altContactNumber: ''));
    if (!state.isAltPhoneFieldVisible) altPhoneController?.text = "";
    await _appAnalysisRepository.sendAppAnalysis(name: state.isAltPhoneFieldVisible ? AppAnalyticsConstants.mobileAddDataButtonRemoveAlternateNumberClick : AppAnalyticsConstants.mobileAddDataButtonAddAlternateNumberClick);
  }

  FutureOr<void> _onToggleEmailField(ToggleEmailFieldEvent event, Emitter<AddDataState> emit) async {
    emit(state.copyWith(isEmailFieldVisible: !state.isEmailFieldVisible, errorMessage: ''));
    if (!state.isEmailFieldVisible) emailController?.text = "";
    await _appAnalysisRepository.sendAppAnalysis(name: state.isEmailFieldVisible ? AppAnalyticsConstants.mobileAddDataButtonAddEmailClick : AppAnalyticsConstants.mobileAddDataButtonRemoveEmailClick);
  }

  FutureOr<void> _onTogglePossessionDate(TogglePossessionDateEvent event, Emitter<AddDataState> emit) {
    emit(state.copyWith(isPossessionDateVisible: !state.isPossessionDateVisible, errorMessage: '', updatePossessionDate: state.isPossessionDateVisible));
  }

  FutureOr<void> _onSelectPossessionDate(SelectPossessionDateEvent event, Emitter<AddDataState> emit) async {
    emit(state.copyWith(possessionDate: event.selectedDate));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonPossessionDateClick);
  }

  FutureOr<void> _onSelectAssignedUser(SelectAssignedUserEvent event, Emitter<AddDataState> emit) async {
    emit(state.copyWith(selectedAssignedUser: event.selectedUser, errorMessage: ''));
    _addProspectModel = _addProspectModel.copyWith(assignTo: event.selectedUser.value);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonAssignDataToClick);
  }

  FutureOr<void> _onSelectCampaignName(SelectCampaignNameEvent event, Emitter<AddDataState> emit) {
    emit(state.copyWith(selectedCampaignNames: event.selectedCampaignName, errorMessage: ''));
    final selectedCampaignNames = event.selectedCampaignName.map((e) => DTOWithNameModel(name: e.title)).toList();
    _addProspectModel = _addProspectModel.copyWith(campaigns: selectedCampaignNames);
  }

  FutureOr<void> _onRemoveCampaignName(RemoveCampaignNameEvent event, Emitter<AddDataState> emit) {
    final updatedSelectedCampaign = state.selectedCampaignNames?.whereNot((element) => element.title == event.selectedCampaignName.title).toList();
    emit(state.copyWith(selectedCampaignNames: updatedSelectedCampaign, errorMessage: ''));
    _addProspectModel.campaigns?.removeWhere((element) => element.name == event.selectedCampaignName.title);
  }

  FutureOr<void> _onAddLocation(AddLocationEvent event, Emitter<AddDataState> emit) async {
    if (event.location == null) return;
    List<ItemSimpleModel<AddressModel>> locations = [];

    var locality = event.location?.locality ?? event.location?.subLocality ?? '';
    var subCommunity = event.location?.subCommunity ?? '';
    var community = event.location?.community ?? '';
    var towerName = event.location?.towerName ?? '';
    var city = event.location?.city ?? '';
    var state_ = event.location?.state ?? '';
    var country = event.location?.country ?? '';
    var postalCode = event.location?.postalCode ?? '';
    String title = [locality, subCommunity, community, towerName, city, state_, country, postalCode].where((element) => element.isNotEmpty).join(', ');
    locations.add(ItemSimpleModel<AddressModel>(title: title, value: event.location));
    emit(state.copyWith(locations: [...locations, ...state.locations]));
    final selectedAddresses = state.locations.map((e) => e.value).whereNotNull().toList();
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(addresses: selectedAddresses));
  }

  FutureOr<void> _onRemoveLocation(RemoveLocationEvent event, Emitter<AddDataState> emit) {
    final updatedLocations = state.locations.whereNot((element) => element.value == event.selectedItem.value).toList();
    emit(state.copyWith(locations: updatedLocations));
    final selectedAddresses = updatedLocations.map((e) => e.value).whereNotNull().toList();
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(addresses: selectedAddresses));
  }

  FutureOr<void> _onSelectDataSource(SelectDataSourceEvent event, Emitter<AddDataState> emit) async {
    emit(state.copyWith(selectedDataSource: event.selectedLeadSource, errorMessage: ''));
    _initDataSubSource(event.selectedLeadSource.value, emit);
    final selectedSource = event.selectedLeadSource.isSelected ? event.selectedLeadSource.value : null;
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(prospectSourceId: selectedSource));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonDataSourceClick);
  }

  void _initDataSubSource(String? value, Emitter<AddDataState> emit) {
    final initSelectedDataSubSource = prospectEntity?.enquiry?.subSource;
    final subSource = _allDataSource.where((element) => element?.source?.isEnabled ?? false).firstWhereOrNull((e) => e?.source?.id == value)?.subSource?.map((e) => SelectableItem<String>(title: e, value: e, isSelected: initSelectedDataSubSource == e)).toList();
    emit(state.copyWith(dataSubSource: subSource ?? [], selectedDataSubSource: null, updateSubSource: false, errorMessage: ''));
    final selectedSubSource = subSource?.firstWhereOrNull((element) => element.isSelected);
    if (selectedSubSource != null) add(SelectDataSubSourceEvent(selectedSubSource));
  }

  FutureOr<void> _onSelectDataSubSource(SelectDataSubSourceEvent event, Emitter<AddDataState> emit) {
    emit(state.copyWith(selectedDataSubSource: event.selectedLeadSubSource, errorMessage: ''));
    final selectedSubSource = event.selectedLeadSubSource.isSelected ? event.selectedLeadSubSource.value : null;
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(subSource: selectedSubSource));
  }

  FutureOr<void> _onCheckDataContactAlreadyExists(CheckDataContactAlreadyExistsEvent event, Emitter<AddDataState> emit) async {
    final contactWithCountryCode = "${event.countryCode}${event.contactNo}";
    if (isEditing && contactWithCountryCode == prospectEntity?.contactNo) return;

    final checkIfDataContactExists = await _getDataByContactNoUseCase(GetDataByContactNoUseCaseParams(event.countryCode.trim(), event.contactNo.trim()));
    checkIfDataContactExists.fold(
      (failure) => null,
      (prospectContact) {
        if (prospectContact != null && _usersDataRepository.checkHasPermission(AppModule.prospect, CommandType.createDuplicateProspects)) {
          if (!(state.isDuplicateLeadBottomSheetVisible)) {
            emit(
              state.copyWith(isDuplicateDataBottomSheetVisible: (prospectContact.canNavigate ?? false) || prospectContact.id != null, existingDataId: prospectContact.id, primaryOrSecondary: 'primary'),
            );
          }
        } else if (prospectContact != null && !(state.isDataAlreadyExits)) {
          emit(
            state.copyWith(isDataAlreadyExits: (prospectContact.canNavigate ?? false) || prospectContact.id != null, existingDataId: prospectContact.id, successMessage: "Data already exists with this number"),
          );
        } else if (prospectContact == null) {
          emit(state.copyWith(isDataAlreadyExits: false, contactNumber: contactWithCountryCode, isDuplicateDataBottomSheetVisible: false, existingDataId: ''));
        }
        if (!(_usersDataRepository.checkHasPermission(AppModule.prospect, CommandType.viewAllProspects))) {
          if (state.existingDataId.isNotNullOrEmpty()) {
            add(AssignedToLoggedInUser());
          }
        }
      },
    );
  }

  FutureOr<void> _onCheckAltContactAlreadyExists(CheckAltContactAlreadyExistsEvent event, Emitter<AddDataState> emit) async {
    final contactWithCountryCode = "${event.countryCode}${event.contactNo}";
    if (isEditing && contactWithCountryCode == prospectEntity?.alternateContactNo || contactWithCountryCode == prospectEntity?.contactNo) return;

    final checkIfDataContactExists = await _getDataByContactNoUseCase(GetDataByContactNoUseCaseParams(event.countryCode.trim(), event.contactNo.trim()));
    checkIfDataContactExists.fold(
      (failure) => null,
      (prospectContact) {
        if (prospectContact != null && _usersDataRepository.checkHasPermission(AppModule.prospect, CommandType.createDuplicateProspects)) {
          if (!(state.isDuplicateDataBottomSheetVisibleForAltNumber)) {
            emit(
              state.copyWith(isDuplicateDataBottomSheetVisibleForAltNumber: (prospectContact.canNavigate ?? false) || prospectContact.id != null, existingDataId: prospectContact.id, primaryOrSecondary: 'alternative'),
            );
          }
        }
        if (prospectContact != null && !(state.isDataAlreadyExitsOnAltNumber)) {
          emit(state.copyWith(isDataAlreadyExitsOnAltNumber: (prospectContact.canNavigate ?? false) || prospectContact.id != null, existingDataId: prospectContact.id, successMessage: "Data already exists with this number"));
        }
        if (!(_usersDataRepository.checkHasPermission(AppModule.prospect, CommandType.viewAllProspects))) {
          if (state.existingDataId.isNotNullOrEmpty()) {
            add(AssignedToLoggedInUser());
          }
        }
      },
    );
  }

  FutureOr<void> _onOnDataContactChanged(OnDataContactChangedEvent event, Emitter<AddDataState> emit) {
    final prospectContact = "+${event.countryCode}${event.contactNumber}";
    if (event.countryCode.isNotEmpty && event.contactNumber.isNotEmpty) emit(state.copyWith(contactNumber: prospectContact, isDuplicateDataBottomSheetVisible: false, isDataAlreadyExits: false));
  }

  FutureOr<void> _onOnAltContactChanged(OnAltContactChangedEvent event, Emitter<AddDataState> emit) {
    final altContact = "+${event.countryCode}${event.contactNumber}";
    emit(state.copyWith(altContactNumber: altContact, isDuplicateDataBottomSheetVisibleForAltNumber: false, isDataAlreadyExitsOnAltNumber: false));
  }

  FutureOr<void> _onCreateData(CreateDataEvent event, Emitter<AddDataState> emit) async {
    if (dataNameController?.text.isEmpty ?? true) {
      _emitFailureState(emit, "Please enter a data name");
      return;
    }
    if (state.contactNumber?.isEmpty ?? true) {
      _emitFailureState(emit, "Please enter a contact number");
      return;
    }
    if (minBudgetController?.text.isNotEmpty ?? false) {
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(lowerBudget: int.tryParse(minBudgetController?.text ?? '0')));
    }
    if (maxBudgetController?.text.isNotEmpty ?? false) {
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(upperBudget: int.tryParse(maxBudgetController?.text ?? '0')));
    }
    final isPropertyTypeSelected = state.propertyTypes.any((element) => element.isSelected);
    final isPropertySubTypeSelected = state.propertySubTypes.any((element) => element.isSelected);
    if (isPropertyTypeSelected && !isPropertySubTypeSelected) {
      _emitFailureState(emit, "Please select property sub-type");
      return;
    }
    _addProspectModel = _addProspectModel.copyWith(
      alternateContactNo: state.altContactNumber.isNotNullOrEmpty() ? state.altContactNumber?.trim() : null,
      email: (emailController?.text.isEmpty ?? true) ? null : emailController?.text.trim(),
      notes: (notesController?.text.isEmpty ?? true) ? null : notesController?.text.trim(),
      companyName: (companyNameController?.text.isEmpty ?? true) ? null : companyNameController?.text.trim(),
      designation: (designationController?.text.isEmpty ?? true) ? null : designationController?.text.trim(),
      possesionDate: state.possessionDate?.getBasedOnTimeZone(),
      executiveName: (executiveNameController?.text.isEmpty ?? true) ? null : executiveNameController?.text.trim(),
      executiveContactNo: state.executiveContact?.trim(),
      referralName: (referralNameController?.text.isEmpty ?? true) ? null : referralNameController?.text.trim(),
      referralContactNo: state.referralContact.isNullOrEmpty() ? null : state.referralContact?.trim(),
      referralEmail: (referralEmailController?.text.isEmpty ?? true) ? null : referralEmailController?.text.trim(),
    );

    if (carpetAreaController?.text.isNotEmpty ?? false) {
      var carpetValue = double.parse(carpetAreaController!.text);
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(carpetArea: carpetValue));
      if (state.selectedCarpetArea == null) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'please select the carpet area'));
        return;
      }
    }
    if (saleableAreaAreaController?.text.isNotEmpty ?? false) {
      var saleableValue = double.parse(saleableAreaAreaController!.text);
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(saleableArea: saleableValue));
      if (state.selectedSaleableArea == null) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'please select the saleable unit'));
        return;
      }
    } else {
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(resetSaleableAreaUnit: true));
    }

    if (builtUpAreaController?.text.isNotEmpty ?? false) {
      var builtUpValue = double.parse(builtUpAreaController!.text);
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(builtUpArea: builtUpValue));
      if (state.selectedBuiltUpArea == null) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'please select the builtUp unit'));
        return;
      }
    } else {
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(resetBuiltUpAreaAreaUnit: true));
    }
    _addProspectModel = _addProspectModel.copyWith(name: dataNameController?.text.trim(), contactNo: state.contactNumber?.trim() ?? "");
    if (state.possessionTypeSelectedItem != null) {
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(possesionType: state.possessionTypeSelectedItem?.value));
    }
    if (isEditing) {
      emit(state.copyWith(pageState: PageState.loading, dialogMessage: "updating data"));
      var statusId = prospectEntity != null && prospectEntity?.status?.id != null ? prospectEntity?.status?.id : '';
      _addProspectModel = _addProspectModel.copyWith(statusId: statusId, id: prospectEntity?.id, serialNumber: prospectEntity?.serialNumber);
      final updateProspect = await _updateDataUseCase(_addProspectModel.toUpdateProspectModel());
      updateProspect.fold((failure) => _emitFailureState(emit, failure.message), (success) {
        if (success != null) getIt<DataDetailsBloc>().add(GetProspectDetailsEvent(success));
        emit(state.copyWith(pageState: PageState.success, errorMessage: ''));
      });
    } else {
      emit(state.copyWith(pageState: PageState.loading, dialogMessage: "saving data"));
      final addLead = await _addDataUseCase(_addProspectModel);
      addLead.fold((failure) => _emitFailureState(emit, failure.message), (success) {
        emit(state.copyWith(pageState: PageState.success, errorMessage: ''));
        getIt<ManageDataBloc>().add(ManageDataInitialEvent(filterType: null, dataFilter: null));
      });
    }
  }

  void _emitFailureState(Emitter<AddDataState> emit, String errorMessage) => emit(state.copyWith(
        errorMessage: errorMessage,
        pageState: PageState.failure,
      ));

  FutureOr<void> _onPickContact(PickContactEvent event, Emitter<AddDataState> emit) async {
    final contact = await PhoneUtils.pickContact();
    if (contact != null && contact.fullName != null && contact.phoneNumber != null) {
      dataNameController?.text = contact.fullName ?? '';
      final contactNumber = contact.phoneNumber?.number.toString().replaceAll(' ', '').replaceAll('-', '').trim() ?? '';
      phoneController?.text = contactNumber;
    }
  }

  FutureOr<void> _onSelectCurrency(SelectCurrencyEvent event, Emitter<AddDataState> emit) async {
    emit(state.copyWith(selectedCurrency: event.selectedCurrency));
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(currency: event.selectedCurrency.value));

    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonBudgetCurrencyClick);
  }

  bool _validatePropertyAreas() {
    double? saleableArea = (saleableAreaAreaController?.text.isNotNullOrEmpty() ?? false) ? double.parse(saleableAreaAreaController!.text) : null;
    double? builtUpArea = (builtUpAreaController?.text.isNotNullOrEmpty() ?? false) ? double.parse(builtUpAreaController!.text) : null;
    double? carpetArea = (carpetAreaController?.text.isNotNullOrEmpty() ?? false) ? double.parse(carpetAreaController!.text) : null;
    if (saleableArea != null && builtUpArea != null && saleableArea <= builtUpArea) {
      LeadratCustomSnackbar.show(message: "Built-up Area should be less than Saleable Area.", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (builtUpArea != null && carpetArea != null && builtUpArea <= carpetArea) {
      LeadratCustomSnackbar.show(message: "Carpet Area should be less than Built-up Area.", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (saleableArea != null && carpetArea != null && saleableArea <= carpetArea) {
      LeadratCustomSnackbar.show(message: "Carpet Area should be less than Saleable Area.", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    return true;
  }

  FutureOr<void> _onSelectPurpose(SelectPurposeEvent event, Emitter<AddDataState> emit) async {
    if (event.selectedPurpose == null) return;
    emit(state.copyWith(selectedPurpose: event.selectedPurpose));
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(purpose: state.selectedPurpose?.value));
  }

  void _initPurpose(Emitter<AddDataState> emit) {
    final initialSelectedPurpose = prospectEntity?.enquiry?.purpose;
    final purposes = PurposeEnum.values.where((element) => element != PurposeEnum.none).map((e) => SelectableItem<PurposeEnum>(title: e.description, value: e));
    emit(state.copyWith(purposes: purposes.toList()));
    if (purposes.isNotEmpty) add(SelectPurposeEvent(purposes.firstWhereOrNull((element) => element.value == initialSelectedPurpose)));
  }

  FutureOr<void> _onAssignedToLoggedInUser(AssignedToLoggedInUser event, Emitter<AddDataState> emit) async {
    final response = await _checkProspectAssignedByProspectIdUseCase.call(state.existingDataId ?? '');
    response.fold((failure) => {}, (result) {
      emit(state.copyWith(isAssignedLoggedInUser: result));
    });
  }

  void _initPossessionType(Emitter<AddDataState> emit) {
    PossessionType? initialPossessionType = prospectEntity?.enquiry?.possesionType;
    if (prospectEntity?.possesionDate != null) initialPossessionType ??= PossessionType.customDate;
    List<SelectableItem<PossessionType?>> possessionTypeItem = PossessionType.values.where((e) => e.description != 'None').map((e) => SelectableItem<PossessionType?>(title: e.description, value: e)).toList();
    emit(state.copyWith(possessionTypeSelectableItems: possessionTypeItem));
    if (possessionTypeItem.isNotEmpty) add(SelectPossessionType(possessionTypeItem.firstWhereOrNull((element) => element.value == initialPossessionType)));
  }

  FutureOr<void> _onSelectPossessionType(SelectPossessionType event, Emitter<AddDataState> emit) async {
    emit(state.copyWith(possessionTypeSelectedItem: event.selectPossessionType, isPossessionDateCustomSelected: event.selectPossessionType?.value == PossessionType.customDate, possessionDate: DateTimeUtils.getPossessionDate(event.selectPossessionType?.value ?? PossessionType.none), updatePossessionDate: PossessionType.underConstruction != event.selectPossessionType?.value));
  }

  FutureOr<void> _onExecutiveContactChanged(OnExecutiveContactChangedEvent event, Emitter<AddDataState> emit) {
    final executiveContact = "+${event.countryCode}${event.contactNumber}";
    if (event.countryCode.isNotEmpty && event.contactNumber.isNotEmpty) emit(state.copyWith(executiveContact: executiveContact));
  }

  FutureOr<void> _onOnReferralContactChanged(OnReferralContactChangedEvent event, Emitter<AddDataState> emit) {
    final referralContact = "+${event.countryCode}${event.contactNumber}";
    if (event.countryCode.isNotEmpty && event.contactNumber.isNotEmpty) emit(state.copyWith(referralContact: referralContact));
  }

  FutureOr<void> _onToggleReferralFields(ToggleReferralFieldsEvent event, Emitter<AddDataState> emit) async {
    emit(state.copyWith(isReferralDetailsVisible: !state.isReferralDetailsVisible, errorMessage: '', referralContact: state.isReferralDetailsVisible ? "" : state.referralContact));
    if (!state.isReferralDetailsVisible) {
      referralNameController?.text = "";
      referralEmailController?.text = "";
      referralPhoneController?.text = "";
    }
    await _appAnalysisRepository.sendAppAnalysis(name: state.isReferralDetailsVisible ? AppAnalyticsConstants.mobileAddDataButtonAddReferralDetailsClick : AppAnalyticsConstants.mobileAddDataButtonRemoveReferralDetailsClick);
  }
}
