import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:leadrat/core_main/common/constants/app_analytics_constants.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/common/widgets/lrb_phone_field/countries.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/purpose_enum.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/utilities/date_time_utils.dart';
import 'package:leadrat/features/data_management/domain/usecase/check_prospect_assigned_by_prospect_id.dart';
import 'package:leadrat/features/data_management/presentation/bloc/data_details_bloc/data_details_bloc.dart';
import 'package:leadrat/features/data_management/presentation/bloc/data_notes_bloc/data_notes_bloc.dart';
import 'package:leadrat/features/data_management/presentation/bloc/manage_data_bloc/manage_data_bloc.dart';

import '../../../../../core_main/common/base/models/item_simple_model.dart';
import '../../../../../core_main/common/base/usecase/use_case.dart';
import '../../../../../core_main/common/constants/constants.dart';
import '../../../../../core_main/common/data/global_settings/models/global_setting_model.dart';
import '../../../../../core_main/common/data/global_settings/repository/global_setting_repository.dart';
import '../../../../../core_main/common/data/master_data/models/master_area_unit_model.dart';
import '../../../../../core_main/common/data/master_data/models/master_property_type_model.dart';
import '../../../../../core_main/common/data/master_data/repository/masterdata_repository.dart';
import '../../../../../core_main/common/data/user/models/get_all_users_model.dart';
import '../../../../../core_main/common/data/user/repository/users_repository.dart';
import '../../../../../core_main/common/models/address_model.dart';
import '../../../../../core_main/common/models/dto_with_name_model.dart';
import '../../../../../core_main/common/widgets/selectable_item_bottom_sheet.dart';
import '../../../../../core_main/di/injection_container.dart';
import '../../../../../core_main/enums/app_enum/page_state_enum.dart';
import '../../../../../core_main/enums/common/no_of_baths.dart';
import '../../../../../core_main/enums/common/no_of_beds.dart';
import '../../../../../core_main/enums/common/no_of_br.dart';
import '../../../../../core_main/enums/common/property_type_enum.dart';
import '../../../../../core_main/enums/data_management/enquiry_type_enum.dart';
import '../../../../../core_main/enums/leads/profession.dart';
import '../../../../../core_main/enums/property_enums/furnish_status.dart';
import '../../../../../core_main/enums/property_enums/offering_type.dart';
import '../../../../../core_main/utilities/leadrat_custom_snackbar.dart';
import '../../../../../core_main/utilities/phone_utils.dart';
import '../../../../../main.dart';
import '../../../../lead/domain/repository/leads_repository.dart';
import '../../../../lead/domain/usecase/get_channel_partner_names_use_case.dart';
import '../../../../lead/domain/usecase/get_project_name_with_id_use_case.dart';
import '../../../../lead/domain/usecase/get_property_name_with_id_use_case.dart';
import '../../../data/models/add_prospect_model.dart';
import '../../../data/models/create_prospect_enquiry_model.dart';
import '../../../domain/entities/prospect_entity.dart';
import '../../../domain/entities/prospect_sub_source_entity.dart';
import '../../../domain/usecase/add_data_usecase.dart';
import '../../../domain/usecase/get_data_by_contact_no_usecase.dart';
import '../../../domain/usecase/get_prospect_sub_source_usecase.dart';
import '../../../domain/usecase/update_data_usecase.dart';

part 'custom_add_data_event.dart';
part 'custom_add_data_state.dart';

class CustomAddDataBloc extends Bloc<CustomAddDataEvent, CustomAddDataState> {
  //TextEditing Controller
  TextEditingController? dataNameController;
  TextEditingController? phoneController;
  TextEditingController? altPhoneController;
  TextEditingController? emailController;
  TextEditingController? minBudgetController;
  TextEditingController? maxBudgetController;
  TextEditingController? companyNameController;
  TextEditingController? carpetAreaController;
  TextEditingController? saleableAreaAreaController;
  TextEditingController? builtUpAreaController;
  TextEditingController? notesController;
  TextEditingController? referralNameController;
  TextEditingController? referralPhoneController;
  TextEditingController? designationController;
  TextEditingController? propertyAreaController;
  TextEditingController? netAreaController;
  TextEditingController? unitNumberOrNameController;
  TextEditingController? clusterNameController;
  TextEditingController? executiveNameController;
  TextEditingController? executivePhoneController;
  TextEditingController? referralEmailController;

  List<MasterPropertyTypeModel>? _masterPropertyTypes;
  AddProspectModel _addProspectModel = AddProspectModel(enquiry: CreateProspectEnquiryModel());
  List<ProspectSubSourceEntity?> _allDataSource = [];
  ProspectEntity? getProspectEntity;
  bool isEditing = false;
  List<GetAllUsersModel?>? _allUsers = [];
  GlobalSettingModel? globalSettings;

  //DI
  final MasterDataRepository _masterDataRepository;
  final UsersDataRepository _usersDataRepository;
  final AddDataUseCase _addDataUseCase;
  final LeadsRepository _leadsRepository;
  final UpdateDataUseCase _updateDataUseCase;
  final GetPropertyNameWithIdUseCase _getPropertyNameWithIdUseCase;
  final GetProjectNameWithIdUseCase _getProjectNameWithIdUseCase;
  final GetProspectSubSourceUseCase _getProspectSubSourceUseCase;
  final GetDataByContactNoUseCase _getDataByContactNoUseCase;
  final GlobalSettingRepository _globalSettingRepository;
  final GetChannelPartnerNamesUseCase _getChannelPartnerNamesUseCase;
  final AppAnalysisRepository _appAnalysisRepository;
  final CheckProspectAssignedByProspectIdUseCase _checkProspectAssignedByProspectIdUseCase;

  CustomAddDataBloc(
    this._masterDataRepository,
    this._usersDataRepository,
    this._addDataUseCase,
    this._leadsRepository,
    this._getProspectSubSourceUseCase,
    this._getProjectNameWithIdUseCase,
    this._getPropertyNameWithIdUseCase,
    this._updateDataUseCase,
    this._getDataByContactNoUseCase,
    this._globalSettingRepository,
    this._getChannelPartnerNamesUseCase,
    this._appAnalysisRepository,
    this._checkProspectAssignedByProspectIdUseCase,
  ) : super(const CustomAddDataState()) {
    on<AddDataInitialEvent>(_onAddDataInitial);
    on<ToggleEnquiredForEvent>(_onToggleEnquiredFor);
    on<TogglePropertyTypeEvent>(_onTogglePropertyType);
    on<ToggleFurnishStatusEvent>(_onToggleFurnishStatus);
    on<ToggleOfferingTypeEvent>(_onToggleOfferingType);
    on<TogglePropertySubTypeEvent>(_onTogglePropertySubType);
    on<ToggleSubTypesExpandedEvent>(_onToggleSubTypesExpanded);
    on<ToggleProfessionEvent>(_onToggleProfession);
    on<ToggleNoOfBhkExpandedEvent>(_onToggleNoOfBhkExpanded);

    on<ToggleBedsEvent>(_onToggleBeds);
    on<ToggleBathsEvent>(_onToggleBaths);
    on<CreateDataEvent>(_onCreateData);
    on<SelectDataSourceEvent>(_onSelectDataSource);
    on<SelectDataSubSourceEvent>(_onSelectDataSubSource);
    on<SelectAgencyNameEvent>(_onSelectAgencyName);
    on<SelectFloorEvent>(_onSelectFloor);
    on<SelectCarpetAreaEvent>(_onSelectCarpetArea);
    on<SelectProjectsEvent>(_onSelectProjects);
    on<SelectPropertiesEvent>(_onSelectProperties);
    on<RemoveProjectsEvent>(_onRemoveProjects);
    on<RemovePropertyEvent>(_onRemoveProperty);
    on<RemoveAgencyNameEvent>(_onRemoveAgencyName);
    on<ResetStateEvent>(_onResetState);
    on<ToggleReferralFieldsEvent>(_onToggleReferralFields);
    on<ToggleAltPhoneFieldEvent>(_onToggleAltPhoneField);
    on<ToggleEmailFieldEvent>(_onToggleEmailField);
    on<TogglePossessionDateEvent>(_onTogglePossessionDate);
    on<SelectPossessionDateEvent>(_onSelectPossessionDate);
    on<SelectAssignedUserEvent>(_onSelectAssignedUser);
    on<SelectSourcingManagerEvent>(_onSelectSourcingManager);
    on<SelectClosingManagerEvent>(_onSelectClosingManager);
    on<AddLocationEvent>(_onAddLocation);
    on<RemoveLocationEvent>(_onRemoveLocation);
    on<RemoveCustomerLocationEvent>(_onRemoveCustomerLocation);
    on<CheckDataContactAlreadyExistsEvent>(_onCheckDataContactAlreadyExists);
    on<CheckAltContactAlreadyExistsEvent>(_onCheckAltContactAlreadyExists);
    on<OnDataContactChangedEvent>(_onOnLeadContactChanged);
    on<OnAltContactChangedEvent>(_onOnAltContactChanged);
    on<OnReferralContactChangedEvent>(_onOnReferralContactChanged);
    on<PickContactEvent>(_onPickContact);
    on<SelectCurrency>(_onCurrencySelect);
    on<SelectChannelPartnerEvent>(_onSelectedChannelPartners);
    on<RemoveChannelPartnerNameEvent>(_onRemoveChannelPartnerName);
    on<AddCustomerLocationEvent>(_onAddCustomerLocation);
    on<SelectCampaignNameEvent>(_onSelectCampaignName);
    on<RemoveCampaignNameEvent>(_onRemoveCampaignName);
    on<SelectLeadNationalityEvent>(_onSelectLeadNationalityEvent);
    on<SelectSaleableAreaEvent>(_onSelectSaleableAreaEvent);
    on<SelectBuiltUpAreaEvent>(_onSelectBuiltUpAreaEvent);
    on<SelectPropertyAreaEvent>(_onSelectPropertyAreaEvent);
    on<SelectNetAreaEvent>(_onSelectNetAreaEvent);
    on<SelectPurposeEvent>(_onSelectPurpose);
    on<SelectPossessionType>(_onSelectPossessionType);
    on<AssignedToLoggedInUser>(_onAssignedToLoggedInUser);
    on<OnExecutiveContactChangedEvent>(_onExecutiveContactChanged);
  }

  void initTextController() {
    dataNameController = TextEditingController();
    phoneController = TextEditingController();
    altPhoneController = TextEditingController();
    emailController = TextEditingController();
    minBudgetController = TextEditingController();
    maxBudgetController = TextEditingController();
    companyNameController = TextEditingController();
    carpetAreaController = TextEditingController();
    saleableAreaAreaController = TextEditingController();
    builtUpAreaController = TextEditingController();
    notesController = TextEditingController();
    designationController = TextEditingController();
    referralNameController = TextEditingController();
    referralPhoneController = TextEditingController();
    unitNumberOrNameController = TextEditingController();
    netAreaController = TextEditingController();
    propertyAreaController = TextEditingController();
    clusterNameController = TextEditingController();
    executivePhoneController = TextEditingController();
    executiveNameController = TextEditingController();
    referralEmailController = TextEditingController();
  }

  void disposeTextController() {
    dataNameController?.dispose();
    phoneController?.dispose();
    altPhoneController?.dispose();
    emailController?.dispose();
    minBudgetController?.dispose();
    maxBudgetController?.dispose();
    companyNameController?.dispose();
    carpetAreaController?.dispose();
    notesController?.dispose();
    designationController?.dispose();
    referralNameController?.dispose();
    referralPhoneController?.dispose();
    unitNumberOrNameController?.dispose();
    netAreaController?.dispose();
    propertyAreaController?.dispose();
    clusterNameController?.dispose();
    executivePhoneController?.dispose();
    executiveNameController?.dispose();
    referralEmailController?.dispose();
  }

  FutureOr<void> _onAddDataInitial(AddDataInitialEvent event, Emitter<CustomAddDataState> emit) async {
    getProspectEntity = event.getDataEntity;
    isEditing = getProspectEntity != null && (getProspectEntity?.id.isNotNullOrEmpty() ?? false);
    globalSettings = await _globalSettingRepository.getGlobalSettings();
    _addProspectModel = AddProspectModel(enquiry: CreateProspectEnquiryModel(), scheduleDate: getProspectEntity?.scheduleDate, assignedFrom: getProspectEntity?.assignedFromUser?.id);
    emit(state.copyWith(showDialogProgress: isEditing, defaultCountryCode: (globalSettings?.hasInternationalSupport ?? false) ? globalSettings!.countries?.firstOrNull?.defaultCallingCode : "+971"));
    _allUsers = await _usersDataRepository.getAssignUser();
    await _initRemoteData(emit);
    _initEnquiredFor(emit);
    _initPropertyTypes(emit);
    _initProfessions(emit);
    _initNationality(emit);
    _initOfferingType(emit);
    _initPurpose(emit);
    _initPossessionType(emit);
    if (isEditing) await _initInitialData(emit);
    emit(state.copyWith(
      pageState: PageState.initial,
      globalSettingModel: globalSettings,
      isAltPhoneFieldVisible: isEditing ? altPhoneController?.text.isNotEmpty : state.isAltPhoneFieldVisible,
      isEmailFieldVisible: isEditing ? emailController?.text.isNotEmpty : state.isEmailFieldVisible,
      isReferralDetailsVisible: (referralPhoneController?.text.isNotEmpty ?? false) || (referralNameController?.text.isNotEmpty ?? false) || (referralEmailController?.text.isNotEmpty ?? false),
      isPossessionDateVisible: getProspectEntity?.possesionDate != null,
      possessionDate: getProspectEntity?.possesionDate?.toUserTimeZone(),
    ));
    emit(state.copyWith(showDialogProgress: false));
  }

  void _initNationality(Emitter<CustomAddDataState> emit) {
    final initialSelectedCountry = getProspectEntity?.nationality;

    final allCountries = countryCodes
        .map(
          (e) => SelectableItem<Country>(title: e.name, value: e, isSelected: e.name == initialSelectedCountry),
        )
        .toList();
    emit(state.copyWith(nationality: allCountries));
    final selectedCountry = allCountries.firstWhereOrNull(
      (element) => element.isSelected,
    );
    if (selectedCountry != null) add(SelectLeadNationalityEvent(selectedCountry));
  }

  void _initEnquiredFor(Emitter<CustomAddDataState> emit) {
    final selectedEnquiredTypes = getProspectEntity?.enquiry?.enquiryTypes?.nonNulls;
    final enquiryTypes = EnquiryTypeEnum.values.where((type) => type != EnquiryTypeEnum.none).map((type) => ItemSimpleModel<EnquiryTypeEnum>(title: type.description, value: type)).toList();
    emit(state.copyWith(enquiredFor: enquiryTypes));
    if (selectedEnquiredTypes != null) {
      for (var element in selectedEnquiredTypes) {
        final selectedEnquiry = enquiryTypes.firstWhereOrNull((enquiry) => enquiry.value == element);
        add(ToggleEnquiredForEvent(selectedEnquiry));
      }
    }
  }

  void _initPropertyTypes(Emitter<CustomAddDataState> emit) {
    final initSelectedPropertyType = getProspectEntity?.enquiry?.propertyType;
    final propertyTypes = PropertyType.values.map((type) => ItemSimpleModel<PropertyType>(title: type.description, value: type, description: type.baseId)).toList();
    emit(state.copyWith(propertyTypes: propertyTypes));
    if (initSelectedPropertyType != null) {
      final selectedPropertyType = propertyTypes.firstWhereOrNull((element) => element.description == initSelectedPropertyType.id);
      if (selectedPropertyType != null) add(TogglePropertyTypeEvent(selectedPropertyType));
    }
  }

  void _initFurnishStatus(Emitter<CustomAddDataState> emit) {
    final initSelectedFurnishStatus = getProspectEntity?.enquiry?.furnished;
    final furnishStatuses = FurnishStatus.values.where((type) => type != FurnishStatus.none).map((type) => ItemSimpleModel<FurnishStatus>(title: type.description, value: type, description: type.value.toString())).toList();
    emit(state.copyWith(furnished: furnishStatuses));
    if (initSelectedFurnishStatus != null) {
      final selectedFurnishStatus = furnishStatuses.firstWhereOrNull((element) => element.value?.value == initSelectedFurnishStatus);
      if (selectedFurnishStatus != null) add(ToggleFurnishStatusEvent(selectedFurnishStatus));
    }
  }

  void _initOfferingType(Emitter<CustomAddDataState> emit) {
    final initSelectedOfferingType = getProspectEntity?.enquiry?.offerType;
    final offeringTypes = OfferingType.values.where((type) => type != OfferingType.none).map((type) => ItemSimpleModel<OfferingType>(title: type.description, value: type, description: type.value.toString())).toList();
    emit(state.copyWith(offerType: offeringTypes));
    if (initSelectedOfferingType != null) {
      final selectedOfferingType = offeringTypes.firstWhereOrNull((element) => element.value?.value == initSelectedOfferingType);
      if (selectedOfferingType != null) add(ToggleOfferingTypeEvent(selectedOfferingType));
    }
  }

  void _initProfessions(Emitter<CustomAddDataState> emit) {
    final initSelectedProfession = getProspectEntity?.profession;
    final professions = Profession.values.where((profession) => profession != Profession.none).map((profession) => ItemSimpleModel<Profession>(title: profession.description, value: profession)).toList();
    emit(state.copyWith(professions: professions));
    if (initSelectedProfession != null) add(ToggleProfessionEvent(professions.firstWhereOrNull((element) => element.value == initSelectedProfession)));
  }

  Future<void> _initRemoteData(Emitter<CustomAddDataState> emit) async {
    try {
      _masterPropertyTypes = await _masterDataRepository.getCustomPropertyTypes(isCustomFormEnabled: true);
      await Future.wait([
        _initMasterAreaUnits(emit),
        _initMasterDataSource(emit),
        _initProperties(emit),
        _initProjects(emit),
        _initAgencyNames(emit),
        _initCampaignNames(emit),
        _initAssignToUsers(emit),
        _initCurrencies(emit),
        _initChannelPartnerNames(emit),
        _initClosingManager(emit),
        _initSourcingManager(emit),
      ]);
    } catch (exception) {
      exception.logException();
    }
  }

  Future<void> _initChannelPartnerNames(Emitter<CustomAddDataState> emit) async {
    try {
      final initSelectedChannelPartner = getProspectEntity?.channelPartners;
      final channelPartnersResponse = await _getChannelPartnerNamesUseCase(NoParams());
      channelPartnersResponse.fold(
        (failure) => null,
        (success) {
          if (success != null && success.isNotEmpty) {
            final selectedAgencyNames = initSelectedChannelPartner?.nonNulls.map((e) => e.firmName).toList();
            final channelPartners = success.map((name) => SelectableItem<String>(title: name, isSelected: selectedAgencyNames?.contains(name) ?? false, value: name)).toList();
            final selectedChannelPartners = channelPartners.where((element) => element.isSelected).toList();
            if (selectedChannelPartners.isNotEmpty) add(SelectChannelPartnerEvent(selectedChannelPartners));
            emit(state.copyWith(channelPartners: channelPartners, selectedChannelPartners: selectedChannelPartners));
          }
        },
      );
    } catch (ex) {
      ex.logException();
    }
  }

  Future<void> _initCurrencies(Emitter<CustomAddDataState> emit) async {
    try {
      final countries = globalSettings?.countries;
      List<SelectableItem<String>> allCurrencies = [];
      SelectableItem<String>? selectedCurrency;
      countries?.firstOrNull?.currencies?.forEach((item) => allCurrencies.add(SelectableItem<String>(title: item.currency ?? '', value: item.currency)));
      if (countries != null) {
        var defaultSymbol = getProspectEntity?.enquiry?.currency ?? globalSettings?.countries?.firstOrNull?.defaultCurrency ?? "AED";
        selectedCurrency = allCurrencies.firstWhereOrNull((element) => element.value == defaultSymbol);
        _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(currency: selectedCurrency?.value ?? 'AED'));
      }
      emit(state.copyWith(currencies: allCurrencies, selectedCurrency: selectedCurrency ?? SelectableItem<String>(title: 'AED', value: 'AED')));
    } catch (ex) {
      ex.logException();
    }
  }

  Future<void> _initMasterAreaUnits(Emitter<CustomAddDataState> emit) async {
    final initSelectedCarpetArea = (getProspectEntity?.enquiry?.carpetAreaUnitId?.id.isNullOrEmptyGuid() ?? true) ? (globalSettings?.defaultValues?["masterAreaUnit"]) : getProspectEntity?.enquiry?.carpetAreaUnitId?.id;
    final initSelectedSaleableArea = (getProspectEntity?.enquiry?.saleableAreaUnitId?.id.isNullOrEmptyGuid() ?? true) ? (globalSettings?.defaultValues?["masterAreaUnit"]) : getProspectEntity?.enquiry?.saleableAreaUnitId?.id;
    final initSelectedBuiltUpArea = (getProspectEntity?.enquiry?.builtUpAreaUnitId?.id.isNullOrEmptyGuid() ?? true) ? (globalSettings?.defaultValues?["masterAreaUnit"]) : getProspectEntity?.enquiry?.builtUpAreaUnitId?.id;
    final initSelectedNetArea = (getProspectEntity?.enquiry?.netAreaUnitId?.id.isNullOrEmptyGuid() ?? true) ? (globalSettings?.defaultValues?["masterAreaUnit"]) : getProspectEntity?.enquiry?.netAreaUnitId?.id;
    final initSelectedPropertyArea = (getProspectEntity?.enquiry?.propertyAreaUnitId?.id.isNullOrEmptyGuid() ?? true) ? (globalSettings?.defaultValues?["masterAreaUnit"]) : getProspectEntity?.enquiry?.propertyAreaUnitId?.id;
    var masterAreaUnits = await _masterDataRepository.getAreaUnits();
    if (masterAreaUnits?.isNotEmpty ?? false) {
      final carpetAreaUnits = masterAreaUnits!.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit, isSelected: areaUnit?.id == initSelectedCarpetArea)).toList();
      final saleableAreaUnits = masterAreaUnits.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit, isSelected: areaUnit?.id == initSelectedSaleableArea)).toList();
      final builtUpAreaUnits = masterAreaUnits.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit, isSelected: areaUnit?.id == initSelectedBuiltUpArea)).toList();
      final netAreaUnits = masterAreaUnits.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit, isSelected: areaUnit?.id == initSelectedNetArea)).toList();
      final propertyAreaUnits = masterAreaUnits.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit, isSelected: areaUnit?.id == initSelectedPropertyArea)).toList();
      emit(state.copyWith(carpetAreas: carpetAreaUnits, saleableAreas: saleableAreaUnits, builtUpAreas: builtUpAreaUnits, errorMessage: '', netAreas: netAreaUnits, propertyAreas: propertyAreaUnits));
      final selectedCarpetArea = carpetAreaUnits.firstWhereOrNull((element) => element.isSelected);
      final selectedSaleableArea = saleableAreaUnits.firstWhereOrNull((element) => element.isSelected);
      final selectedBuiltUpArea = builtUpAreaUnits.firstWhereOrNull((element) => element.isSelected);
      final selectedNetArea = netAreaUnits.firstWhereOrNull((element) => element.isSelected);
      final selectedPropertyArea = propertyAreaUnits.firstWhereOrNull((element) => element.isSelected);
      if (selectedCarpetArea != null) {
        add(SelectCarpetAreaEvent(selectedCarpetArea));
      }
      if (selectedSaleableArea != null) {
        add(SelectSaleableAreaEvent(selectedSaleableArea));
      }
      if (selectedBuiltUpArea != null) {
        add(SelectBuiltUpAreaEvent(selectedBuiltUpArea));
      }
      if (selectedNetArea != null) {
        add(SelectNetAreaEvent(selectedNetArea));
      }
      if (selectedPropertyArea != null) {
        add(SelectPropertyAreaEvent(selectedPropertyArea));
      }
    }
  }

  Future<void> _initMasterDataSource(Emitter<CustomAddDataState> emit) async {
    final initialSelectedProspectSource = getProspectEntity?.enquiry?.prospectSource;
    final prospectSource = await _getProspectSubSourceUseCase(NoParams());
    prospectSource.fold((failure) => null, (success) {
      if (success != null && success.isNotEmpty) {
        final allProspectSource = success
            .where((element) => element?.source?.isEnabled ?? false)
            .map((e) => SelectableItem<String>(
                  title: e?.source?.displayName ?? "",
                  value: e?.source?.id,
                  isSelected: (e?.source?.id.isNullOrEmptyGuid() ?? false) ? false : initialSelectedProspectSource?.id == e?.source?.id,
                ))
            .toList();
        _allDataSource = success;
        emit(state.copyWith(dataSource: allProspectSource, errorMessage: ''));
        final selectedDataSource = allProspectSource.firstWhereOrNull((element) => element.isSelected);
        if (selectedDataSource != null) add(SelectDataSourceEvent(selectedDataSource));
      }
    });
  }

  Future<void> _initAgencyNames(Emitter<CustomAddDataState> emit) async {
    final initialSelectedAgencyName = getProspectEntity?.agencies?.map((e) => e?.name).nonNulls;
    var agencyNames = await _masterDataRepository.getAgencyNames();
    if (agencyNames?.isNotEmpty ?? false) {
      final agenciesNames = agencyNames!.map((name) => SelectableItem<String>(title: name, value: name, isSelected: initialSelectedAgencyName?.contains(name) ?? false)).toList();
      final selectedAgencyNames = agenciesNames.where((element) => element.isSelected).toList();
      emit(state.copyWith(agencyNames: agenciesNames, errorMessage: '', selectedAgencyNames: selectedAgencyNames));
      _addProspectModel = _addProspectModel.copyWith(agencies: selectedAgencyNames.map((e) => DTOWithNameModel(name: e.title)).toList());
    }
  }

  Future<void> _initFloors(Emitter<CustomAddDataState> emit) async {
    List<SelectableItem<String>> floors = [
      SelectableItem(title: 'upper basement', value: 'upper basement'),
      SelectableItem(title: 'lower basement', value: 'lower basement'),
      SelectableItem(title: 'ground floor', value: 'ground floor'),
    ];

    for (int i = 1; i <= 200; i++) {
      floors.add(SelectableItem(title: '$i', value: '$i'));
    }
    final initialSelectedFloors = getProspectEntity?.enquiry?.floors?.map((e) => e).nonNulls;
    if (initialSelectedFloors != null) {
      floors.forEach((floor) {
        if (initialSelectedFloors.contains(floor.title)) {
          floor.isSelected = true;
        }
      });
    }
    final selectedFloors = floors.where((element) => element.isSelected).toList();
    final floorsNumber = floors.where((element) => element.isSelected).map((e) => e.title).toList();
    emit(state.copyWith(floors: floors, errorMessage: '', selectedFloors: selectedFloors));
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(floors: floorsNumber));
  }

  Future<void> _initAssignToUsers(Emitter<CustomAddDataState> emit) async {
    final initSelectedPrimaryUserId = getProspectEntity?.assignedUser?.id;
    // final initSelectedSecondaryUserId = getProspectEntity?.secondaryUser?.id;
    if (_allUsers?.isNotEmpty ?? false) {
      final currentUser = _usersDataRepository.getLoggedInUser();
      var assignToUsers = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id, isSelected: initSelectedPrimaryUserId == user?.id)).toList();
      //adding disabled users
      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => assignToUsers.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isSelected: initSelectedPrimaryUserId == disabledUsers?.id, isEnabled: false)));

      assignToUsers.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId, isSelected: currentUser?.userId == initSelectedPrimaryUserId));

      emit(state.copyWith(assignToUsers: assignToUsers, errorMessage: ''));
      final selectedPrimaryUser = assignToUsers.firstWhereOrNull((element) => element.isSelected);

      if (selectedPrimaryUser != null) add(SelectAssignedUserEvent(selectedPrimaryUser));
    }
  }

  Future<void> _initSourcingManager(Emitter<CustomAddDataState> emit) async {
    final initSourcingManager = getProspectEntity?.sourcingManager?.id;
    if (_allUsers?.isNotEmpty ?? false) {
      final currentUser = _usersDataRepository.getLoggedInUser();
      var sourcingManagerUsers = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id, isSelected: initSourcingManager == user?.id)).toList();
      //adding disabled users
      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => sourcingManagerUsers.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isSelected: initSourcingManager == disabledUsers?.id, isEnabled: false)));
      sourcingManagerUsers.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId, isSelected: currentUser?.userId == initSourcingManager));
      emit(state.copyWith(sourcingManager: sourcingManagerUsers, errorMessage: ''));
      final selectedSourcingManager = sourcingManagerUsers.firstWhereOrNull((element) => element.isSelected);
      if (selectedSourcingManager != null) add(SelectSourcingManagerEvent(selectedSourcingManager));
    }
  }

  Future<void> _initClosingManager(Emitter<CustomAddDataState> emit) async {
    final initClosingManager = getProspectEntity?.closingManagerUser?.id;
    if (_allUsers?.isNotEmpty ?? false) {
      final currentUser = _usersDataRepository.getLoggedInUser();
      var closingManagerUsers = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id, isSelected: initClosingManager == user?.id)).toList();
      //adding disabled users
      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => closingManagerUsers.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isSelected: initClosingManager == disabledUsers?.id, isEnabled: false)));
      closingManagerUsers.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId, isSelected: currentUser?.userId == initClosingManager));
      emit(state.copyWith(closingManager: closingManagerUsers, errorMessage: ''));
      final selectedClosingManager = closingManagerUsers.firstWhereOrNull((element) => element.isSelected);
      if (selectedClosingManager != null) add(SelectClosingManagerEvent(selectedClosingManager));
    }
  }

  Future<void> _initCampaignNames(Emitter<CustomAddDataState> emit) async {
    final initialSelectedCampaignNames = getProspectEntity?.campaigns?.map((e) => e.name).nonNulls;
    var campaignNames = await _leadsRepository.getCampaignNames();
    if (campaignNames?.isNotEmpty ?? false) {
      final campaignsNames = campaignNames!.map((name) => SelectableItem<String>(title: name, value: name, isSelected: initialSelectedCampaignNames?.contains(name) ?? false)).toList();
      final selectedCamapaignNames = campaignsNames.where((element) => element.isSelected).toList();
      emit(state.copyWith(campaignNames: campaignsNames, errorMessage: '', selectedCampaignNames: selectedCamapaignNames));
      _addProspectModel = _addProspectModel.copyWith(campaigns: selectedCamapaignNames.map((e) => DTOWithNameModel(name: e.title)).toList());
    }
  }

  Future<void> _initProperties(Emitter<CustomAddDataState> emit) async {
    final initialSelectedPropertiesId = getProspectEntity?.properties?.map((e) => e?.id).nonNulls.toSet() ?? {};
    final properties = await _getPropertyNameWithIdUseCase(NoParams());
    properties.fold((failure) => null, (success) {
      if (success != null && success.isNotEmpty) {
        final allProperties = success.map((e) => SelectableItem<String>(title: e.title ?? "", value: e.id, isSelected: initialSelectedPropertiesId?.contains(e.id) ?? false)).toList();
        final selectedProperties = allProperties.where((element) => element.isSelected).toList();
        emit(state.copyWith(properties: allProperties, errorMessage: '', selectedProperties: selectedProperties));
        _addProspectModel = _addProspectModel.copyWith(propertiesList: selectedProperties.map((e) => e.title).toList());
      }
    });
  }

  Future<void> _initProjects(Emitter<CustomAddDataState> emit) async {
    final initialSelectedProjectsId = getProspectEntity?.projects?.map((e) => e?.id).nonNulls.toSet() ?? {};
    final projects = await _getProjectNameWithIdUseCase(NoParams());
    projects.fold((failure) => null, (success) {
      if (success != null && success.isNotEmpty) {
        final allProjects = success.map((e) => SelectableItem<String>(title: e.name ?? "", value: e.id, isSelected: initialSelectedProjectsId?.contains(e.id) ?? false)).toList();
        final selectedProjects = allProjects.where((element) => element.isSelected).toList();
        emit(state.copyWith(projects: allProjects, errorMessage: '', selectedProjects: selectedProjects));
        _addProspectModel = _addProspectModel.copyWith(projectsList: selectedProjects.map((e) => e.title).toList());
      }
    });
  }

  FutureOr<void> _onToggleEnquiredFor(ToggleEnquiredForEvent event, Emitter<CustomAddDataState> emit) async {
    if (event.selectedEnquiredFor == null) return;
    final updatedEnquiredFor = state.enquiredFor.map((e) => e.value == event.selectedEnquiredFor?.value ? e.copyWith(isSelected: !e.isSelected) : e).toList();
    emit(state.copyWith(enquiredFor: updatedEnquiredFor, errorMessage: ''));
    final selectedEnquiryTypes = state.enquiredFor.where((element) => element.isSelected).map((e) => e.value).nonNulls.toList();
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(enquiryTypes: selectedEnquiryTypes));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonEnquiredForClick);
  }

  FutureOr<void> _onTogglePropertyType(TogglePropertyTypeEvent event, Emitter<CustomAddDataState> emit) async {
    final updatedPropertyTypes = state.propertyTypes.map((e) => e.description == event.selectedPropertyType.description ? e.copyWith(isSelected: !e.isSelected) : e.copyWith(isSelected: false)).toList();
    if (event.selectedPropertyType.isSelected) {
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(propertyTypeId: StringConstants.emptyGuidId, propertyTypeIds: [], bhks: [], bhkTypes: [], beds: []));
      emit(state.copyWith(propertyTypes: updatedPropertyTypes, propertySubTypes: [], errorMessage: '', bhkTypes: [], beds: [], noOfBhk: [], baths: [], furnished: []));
    } else {
      final propertySubTypes = initPropertySubTypes(event.selectedPropertyType.description ?? '', emit);
      emit(state.copyWith(propertyTypes: updatedPropertyTypes, propertySubTypes: propertySubTypes, floors: [], errorMessage: ''));
      _initBhkAndBhkTypes(emit);
    }
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonPropertyTypeClick);
  }

  FutureOr<void> _onToggleFurnishStatus(ToggleFurnishStatusEvent event, Emitter<CustomAddDataState> emit) async {
    final updatedFurnishStatus = state.furnished.map((e) => e.description == event.selectedFurnishStatus.description ? e.copyWith(isSelected: !e.isSelected) : e.copyWith(isSelected: false)).toList();
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(furnished: event.selectedFurnishStatus.value?.value ?? 0));
    emit(state.copyWith(
      furnished: updatedFurnishStatus,
      errorMessage: '',
    ));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonFurnishedClick);
  }

  FutureOr<void> _onToggleOfferingType(ToggleOfferingTypeEvent event, Emitter<CustomAddDataState> emit) async {
    final updatedOfferingType = state.offerType.map((e) => e.description == event.selectedOfferingType.description ? e.copyWith(isSelected: !e.isSelected) : e.copyWith(isSelected: false)).toList();
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(offerType: updatedOfferingType.firstOrNull?.value?.value ?? 0));
    emit(state.copyWith(
      offerType: updatedOfferingType,
      errorMessage: '',
    ));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonOfferingTypeClick);
  }

  List<ItemSimpleModel<String>> initPropertySubTypes(String propertyTypeId, [Emitter<CustomAddDataState>? emit]) {
    if (_masterPropertyTypes == null) return [];
    final initSelectedPropertySubTypesId = getProspectEntity?.enquiry?.propertyTypes?.map((p) => p.childType?.id).toList();
    final propertySubTypes = _masterPropertyTypes!
        .where((type) => type.id == propertyTypeId && type.childTypes != null)
        .expand((type) => type.childTypes!)
        .map(
          (subType) => ItemSimpleModel<String>(title: subType.displayName ?? "", value: subType.id, description: subType.baseId),
        )
        .toList();
    if (initSelectedPropertySubTypesId?.isNotEmpty ?? false) {
      final bool shouldExpand = propertySubTypes.any((element) {
        final isSelected = initSelectedPropertySubTypesId!.contains(element.value);
        final index = propertySubTypes.indexOf(element);
        return isSelected && index > 3;
      });

      if (emit != null && shouldExpand) {
        emit(state.copyWith(isSubTypesExpanded: true));
      }

      final selectedPropertySubTypes = propertySubTypes.where((element) => initSelectedPropertySubTypesId?.contains(element.value) ?? false).toList();
      for (var selectedPropertySubType in selectedPropertySubTypes) {
        add(TogglePropertySubTypeEvent(selectedPropertySubType));
      }
    }
    return propertySubTypes;
  }

  FutureOr<void> _onSelectDataSource(SelectDataSourceEvent event, Emitter<CustomAddDataState> emit) async {
    emit(state.copyWith(selectedDataSource: event.selectedDataSource, errorMessage: ''));
    _initDataSubSource(event.selectedDataSource.value, emit);
    final selectedSource = event.selectedDataSource.isSelected ? event.selectedDataSource.value : null;
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(prospectSourceId: selectedSource));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonDataSourceClick);
  }

  void _initDataSubSource(String? value, Emitter<CustomAddDataState> emit) {
    final initSelectedDataSubSource = getProspectEntity?.enquiry?.subSource;
    final subSource = _allDataSource.where((element) => element?.source?.isEnabled ?? false).firstWhereOrNull((e) => e?.source?.id == value)?.subSource?.map((e) => SelectableItem<String>(title: e, value: e, isSelected: initSelectedDataSubSource == e)).toList();
    emit(state.copyWith(dataSubSource: subSource ?? [], selectedDataSubSource: null, updateSubSource: false, errorMessage: ''));
    final selectedSubSource = subSource?.firstWhereOrNull((element) => element.isSelected);
    if (selectedSubSource != null) add(SelectDataSubSourceEvent(selectedSubSource));
  }

  FutureOr<void> _onSelectDataSubSource(SelectDataSubSourceEvent event, Emitter<CustomAddDataState> emit) {
    emit(state.copyWith(selectedDataSubSource: event.selectedDataSubSource, errorMessage: ''));
    final selectedSubSource = event.selectedDataSubSource.isSelected ? event.selectedDataSubSource.value : null;
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(subSource: selectedSubSource));
  }

  FutureOr<void> _onTogglePropertySubType(TogglePropertySubTypeEvent event, Emitter<CustomAddDataState> emit) async {
    final updatedPropertySubTypes = state.propertySubTypes.map((e) => e.value == event.selectedPropertySubType.value ? e.copyWith(isSelected: !e.isSelected) : e).toList();

    emit(state.copyWith(propertySubTypes: updatedPropertySubTypes, errorMessage: ''));

    final propertyTypeIds = state.propertySubTypes.where((element) => element.isSelected).map((e) => e.value).nonNulls.toList();
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(propertyTypeIds: propertyTypeIds));
    _initBhkAndBhkTypes(emit);
    _initFurnishStatus(emit);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonPropertySubTypeClick);
  }

  FutureOr<void> _onToggleBeds(ToggleBedsEvent event, Emitter<CustomAddDataState> emit) async {
    final updatedNoOfBeds = state.beds.map((e) => e.value == event.selectedBeds.value ? e.copyWith(isSelected: !e.isSelected) : e).toList();
    emit(state.copyWith(beds: updatedNoOfBeds, errorMessage: ''));
    final selectedBeds = state.beds.where((element) => element.isSelected).map((e) => e.value).nonNulls.toList();
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(beds: selectedBeds));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonBedsClick);
  }

  FutureOr<void> _onToggleBaths(ToggleBathsEvent event, Emitter<CustomAddDataState> emit) async {
    final updatedBaths = state.baths.map((e) => e.value == event.selectedBaths.value ? e.copyWith(isSelected: !e.isSelected) : e).toList();
    emit(state.copyWith(baths: updatedBaths, errorMessage: ''));
    final selectedBaths = state.baths.where((element) => element.isSelected).map((bath) => bath.value).nonNulls.toList();
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(baths: selectedBaths));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonBathsClick);
  }

  FutureOr<void> _onToggleProfession(ToggleProfessionEvent event, Emitter<CustomAddDataState> emit) async {
    if (event.selectedProfession == null) return;
    final updatedProfession = state.professions.map((e) => e.value == event.selectedProfession!.value ? e.copyWith(isSelected: !e.isSelected) : e.copyWith(isSelected: false)).toList();
    emit(state.copyWith(professions: updatedProfession, errorMessage: ''));
    final selectedProfession = event.selectedProfession!.isSelected ? null : event.selectedProfession!.value;
    _addProspectModel = _addProspectModel.copyWith(profession: selectedProfession);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonProfessionClick);
  }

  FutureOr<void> _onSelectAgencyName(SelectAgencyNameEvent event, Emitter<CustomAddDataState> emit) async {
    emit(state.copyWith(selectedAgencyNames: event.selectedAgencyName, errorMessage: ''));
    final selectedAgencyNames = event.selectedAgencyName.map((e) => DTOWithNameModel(name: e.title)).toList();
    _addProspectModel = _addProspectModel.copyWith(agencies: selectedAgencyNames);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonAgencyNameClick);
  }

  FutureOr<void> _onSelectFloor(SelectFloorEvent event, Emitter<CustomAddDataState> emit) {
    emit(state.copyWith(selectedFloors: event.selectedFloor, errorMessage: ''));
    final selectedFloors = event.selectedFloor.map((e) => e.title).toList();
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(floors: selectedFloors));
  }

  FutureOr<void> _onSelectCampaignName(SelectCampaignNameEvent event, Emitter<CustomAddDataState> emit) {
    emit(state.copyWith(selectedCampaignNames: event.selectedCampaignName, errorMessage: ''));
    final selectedCampaignNames = event.selectedCampaignName.map((e) => DTOWithNameModel(name: e.title)).toList();
    _addProspectModel = _addProspectModel.copyWith(campaigns: selectedCampaignNames);
  }

  FutureOr<void> _onRemoveCampaignName(RemoveCampaignNameEvent event, Emitter<CustomAddDataState> emit) {
    final updatedSelectedCampaign = state.selectedCampaignNames?.whereNot((element) => element.title == event.selectedCampaignName.title).toList();
    emit(state.copyWith(selectedCampaignNames: updatedSelectedCampaign, errorMessage: ''));
    _addProspectModel.campaigns?.removeWhere((element) => element.name == event.selectedCampaignName.title);
  }

  FutureOr<void> _onRemoveAgencyName(RemoveAgencyNameEvent event, Emitter<CustomAddDataState> emit) {
    final updatedSelectedAgency = state.selectedAgencyNames?.whereNot((element) => element.title == event.selectedAgencyName.title).toList();
    emit(state.copyWith(selectedAgencyNames: updatedSelectedAgency, errorMessage: ''));
    _addProspectModel.agencies?.removeWhere((element) => element?.name == event.selectedAgencyName.title);
  }

  FutureOr<void> _onSelectCarpetArea(SelectCarpetAreaEvent event, Emitter<CustomAddDataState> emit) async {
    if (event.selectedCarpetArea == null) return;
    emit(state.copyWith(selectedCarpetArea: event.selectedCarpetArea, errorMessage: ''));
    final selectedCarpetAreaValue = event.selectedCarpetArea!.isSelected ? event.selectedCarpetArea : null;
    _addProspectModel = _addProspectModel.copyWith(
        enquiry: _addProspectModel.enquiry?.copyWith(
      carpetAreaUnitId: selectedCarpetAreaValue?.value?.id,
      conversionFactor: selectedCarpetAreaValue?.value?.conversionFactor,
    ));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonCarpetAreaUnitClick);
  }

  FutureOr<void> _onSelectSaleableAreaEvent(SelectSaleableAreaEvent event, Emitter<CustomAddDataState> emit) async {
    if (event.selectedSaleableArea == null) return;
    emit(state.copyWith(selectedSaleableArea: event.selectedSaleableArea, errorMessage: ''));
    final selectedSaleableAreaValue = event.selectedSaleableArea!.isSelected ? event.selectedSaleableArea : null;
    _addProspectModel = _addProspectModel.copyWith(
        enquiry: _addProspectModel.enquiry?.copyWith(
      saleableAreaUnitId: selectedSaleableAreaValue?.value?.id,
      saleableAreaConversionFactor: selectedSaleableAreaValue?.value?.conversionFactor,
    ));
  }

  FutureOr<void> _onSelectBuiltUpAreaEvent(SelectBuiltUpAreaEvent event, Emitter<CustomAddDataState> emit) async {
    if (event.selectedBuiltUpArea == null) return;
    emit(state.copyWith(selectedBuiltUpArea: event.selectedBuiltUpArea, errorMessage: ''));
    final selectedBuiltUpAreaValue = event.selectedBuiltUpArea!.isSelected ? event.selectedBuiltUpArea : null;
    _addProspectModel = _addProspectModel.copyWith(
        enquiry: _addProspectModel.enquiry?.copyWith(
      builtUpAreaUnitId: selectedBuiltUpAreaValue?.value?.id,
      builtUpAreaConversionFactor: selectedBuiltUpAreaValue?.value?.conversionFactor,
    ));
  }

  FutureOr<void> _onSelectPropertyAreaEvent(SelectPropertyAreaEvent event, Emitter<CustomAddDataState> emit) async {
    if (event.selectedPropertyArea == null) return;
    emit(state.copyWith(selectedPropertyArea: event.selectedPropertyArea, errorMessage: ''));
    final selectedPropertyAreaValue = event.selectedPropertyArea!.isSelected ? event.selectedPropertyArea : null;
    _addProspectModel = _addProspectModel.copyWith(
        enquiry: _addProspectModel.enquiry?.copyWith(
      propertyAreaUnitId: selectedPropertyAreaValue?.value?.id,
      propertyAreaConversionFactor: selectedPropertyAreaValue?.value?.conversionFactor,
    ));
  }

  FutureOr<void> _onSelectNetAreaEvent(SelectNetAreaEvent event, Emitter<CustomAddDataState> emit) async {
    if (event.selectedNetArea == null) return;
    emit(state.copyWith(selectedNetArea: event.selectedNetArea, errorMessage: ''));
    final selectedNetAreaValue = event.selectedNetArea!.isSelected ? event.selectedNetArea : null;
    _addProspectModel = _addProspectModel.copyWith(
        enquiry: _addProspectModel.enquiry?.copyWith(
      netAreaUnitId: selectedNetAreaValue?.value?.id,
      netAreaConversionFactor: selectedNetAreaValue?.value?.conversionFactor,
    ));
  }

  void _initBhkAndBhkTypes(Emitter<CustomAddDataState> emit) async {
    final initSelectedNoOfBhk = getProspectEntity?.enquiry?.bhKs?.nonNulls;
    final initSelectedBhkTypes = getProspectEntity?.enquiry?.beds?.nonNulls;
    final initSelectedBaths = getProspectEntity?.enquiry?.baths?.nonNulls;

    final selectedPropertyType = state.propertyTypes.firstWhereOrNull((element) => element.isSelected);
    final isOnlyPlotSelected = state.propertySubTypes.firstWhereOrNull((element) => element.title.toLowerCase() == "plot")?.isSelected ?? false;
    final isAnyOtherSelected = state.propertySubTypes.any((element) => element.title.toLowerCase() != "plot" && element.isSelected);
    final isOnlyPlotSelectedAndNoOthers = isOnlyPlotSelected && !isAnyOtherSelected;

    final isPropertySubTypesSelected = state.propertySubTypes.any((element) => element.isSelected);
    if (!isPropertySubTypesSelected || selectedPropertyType == null) {
      emit(state.copyWith(noOfBhk: [], bhkTypes: [], beds: [], baths: [], furnished: [], errorMessage: ''));
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(beds: [], bhks: []));
    } else if (selectedPropertyType.value == PropertyType.residential && isPropertySubTypesSelected && !isOnlyPlotSelectedAndNoOthers) {
      final noOfBr = NoOfBr.values.map((br) => ItemSimpleModel<double>(title: br.description, value: br.noOfBr, isSelected: initSelectedNoOfBhk?.contains(br.noOfBr) ?? false)).toList();
      final noOfBaths = NoOfBaths.values.map((bath) => ItemSimpleModel<int>(title: bath.description, value: bath.noOfBaths, isSelected: initSelectedBaths?.contains(bath.noOfBaths) ?? false)).toList();
      final noOfBeds = Beds.values.map((type) => ItemSimpleModel<Beds>(title: type.description, value: type, isSelected: initSelectedBhkTypes?.contains(type) ?? false)).toList();
      final selectedBhkIndex = noOfBr.lastIndexWhere((element) => element.isSelected);

      emit(state.copyWith(noOfBhk: noOfBr, baths: noOfBaths, beds: noOfBeds, floors: [], isNoOfBhkExpanded: selectedBhkIndex > 3));
      final selectedBhks = state.noOfBhk.where((element) => element.isSelected).map((bhk) => bhk.value).nonNulls.toList();
      final selectedBaths = state.baths.where((element) => element.isSelected).map((bath) => bath.value).nonNulls.toList();
      final selectedBhkTypes = state.beds.where((element) => element.isSelected).map((e) => e.value).nonNulls.toList();
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(beds: selectedBhkTypes, bhks: selectedBhks, baths: selectedBaths));
    } else {
      emit(state.copyWith(noOfBhk: [], baths: [], bhkTypes: [], beds: [], furnished: [], errorMessage: ''));
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(beds: [], bhks: [], baths: [], furnished: 0));
    }
    if (selectedPropertyType?.value == PropertyType.residential || selectedPropertyType?.value == PropertyType.commercial) {
      _initFloors(emit);
    }
  }

  FutureOr<void> _onSelectProperties(SelectPropertiesEvent event, Emitter<CustomAddDataState> emit) async {
    emit(state.copyWith(selectedProperties: event.selectedProperties, errorMessage: ''));
    final selectedProperties = event.selectedProperties.map((e) => e.title).toList();
    _addProspectModel = _addProspectModel.copyWith(propertiesList: selectedProperties);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonSelectPropertiesClick);
  }

  FutureOr<void> _onRemoveProperty(RemovePropertyEvent event, Emitter<CustomAddDataState> emit) {
    final updatedSelectedProperties = state.selectedProperties?.whereNot((element) => element.title == event.selectedProperty.title).toList();
    emit(state.copyWith(selectedProperties: updatedSelectedProperties, errorMessage: ''));
    _addProspectModel.propertiesList?.removeWhere((title) => title == event.selectedProperty.title);
  }

  FutureOr<void> _onSelectProjects(SelectProjectsEvent event, Emitter<CustomAddDataState> emit) async {
    emit(state.copyWith(selectedProjects: event.selectedProjects, errorMessage: ''));
    final selectedProjects = event.selectedProjects.map((e) => e.title).toList();
    _addProspectModel = _addProspectModel.copyWith(projectsList: selectedProjects);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonSelectProjectsClick);
  }

  FutureOr<void> _onRemoveProjects(RemoveProjectsEvent event, Emitter<CustomAddDataState> emit) {
    final updatedSelectedProjects = state.selectedProjects?.whereNot((element) => element.title == event.selectedProjects.title).toList();
    emit(state.copyWith(selectedProjects: updatedSelectedProjects, errorMessage: ''));
    _addProspectModel.projectsList?.removeWhere((title) => title == event.selectedProjects.title);
  }

  FutureOr<void> _onSelectAssignedUser(SelectAssignedUserEvent event, Emitter<CustomAddDataState> emit) async {
    List<SelectableItem<String>>? updatedSecondaryUsers;
    if (globalSettings?.isDualOwnershipEnabled ?? false) {
      final currentUser = _usersDataRepository.getLoggedInUser();
      updatedSecondaryUsers = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id)).toList();
      _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => updatedSecondaryUsers?.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isEnabled: false)));
      updatedSecondaryUsers.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId));
      updatedSecondaryUsers.removeWhere((element) => element.value == event.selectedUser.value);
    }
    emit(state.copyWith(selectedAssignedUser: event.selectedUser, errorMessage: '', secondaryUsers: updatedSecondaryUsers));
    _addProspectModel = _addProspectModel.copyWith(assignTo: event.selectedUser.value);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonAssignDataToClick);
  }

  FutureOr<void> _onSelectSourcingManager(SelectSourcingManagerEvent event, Emitter<CustomAddDataState> emit) async {
    List<SelectableItem<String>>? updatedSourcingManager;
    final currentUser = _usersDataRepository.getLoggedInUser();
    updatedSourcingManager = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id)).toList();
    _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => updatedSourcingManager?.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isEnabled: false)));
    updatedSourcingManager.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId));
    updatedSourcingManager.removeWhere((element) => element.value == event.selectedSourcingManager.value);
    emit(state.copyWith(selectedSourcingManager: event.selectedSourcingManager, errorMessage: ''));
    _addProspectModel = _addProspectModel.copyWith(sourcingManager: event.selectedSourcingManager.value);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonSourcingManagerClick);
  }

  FutureOr<void> _onSelectClosingManager(SelectClosingManagerEvent event, Emitter<CustomAddDataState> emit) async {
    List<SelectableItem<String>>? updatedClosingManager;
    final currentUser = _usersDataRepository.getLoggedInUser();
    updatedClosingManager = _allUsers!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => SelectableItem<String>(title: user?.fullName ?? '', value: user?.id)).toList();
    _allUsers!.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => updatedClosingManager?.add(SelectableItem<String>(title: disabledUsers?.fullName ?? '', value: disabledUsers?.id, isEnabled: false)));
    updatedClosingManager.insert(0, SelectableItem<String>(title: "You", value: currentUser?.userId));
    updatedClosingManager.removeWhere((element) => element.value == event.selectedClosingManager.value);
    emit(state.copyWith(selectedClosingManager: event.selectedClosingManager, errorMessage: ''));
    _addProspectModel = _addProspectModel.copyWith(closingManager: event.selectedClosingManager.value);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonClosingManagerClick);
  }

  FutureOr<void> _onToggleSubTypesExpanded(ToggleSubTypesExpandedEvent event, Emitter<CustomAddDataState> emit) {
    emit(state.copyWith(isSubTypesExpanded: !state.isSubTypesExpanded, errorMessage: ''));
  }

  FutureOr<void> _onToggleNoOfBhkExpanded(ToggleNoOfBhkExpandedEvent event, Emitter<CustomAddDataState> emit) {
    emit(state.copyWith(isNoOfBhkExpanded: !state.isNoOfBhkExpanded, errorMessage: ''));
  }

  void _emitFailureState(Emitter<CustomAddDataState> emit, String errorMessage) => emit(state.copyWith(
        errorMessage: errorMessage,
        pageState: PageState.failure,
      ));

  FutureOr<void> _onResetState(ResetStateEvent event, Emitter<CustomAddDataState> emit) {
    emit(state.initialState());
  }

  FutureOr<void> _onToggleReferralFields(ToggleReferralFieldsEvent event, Emitter<CustomAddDataState> emit) async {
    emit(state.copyWith(isReferralDetailsVisible: !state.isReferralDetailsVisible, errorMessage: ''));
    if (!state.isReferralDetailsVisible) {
      referralNameController?.text = (referralPhoneController?.text = "") ?? "";
      referralEmailController?.text = "";
    }
    await _appAnalysisRepository.sendAppAnalysis(name: state.isReferralDetailsVisible ? AppAnalyticsConstants.mobileAddDataButtonAddReferralDetailsClick : AppAnalyticsConstants.mobileAddDataButtonRemoveReferralDetailsClick);
  }

  FutureOr<void> _onToggleAltPhoneField(ToggleAltPhoneFieldEvent event, Emitter<CustomAddDataState> emit) async {
    emit(state.copyWith(isAltPhoneFieldVisible: !state.isAltPhoneFieldVisible, errorMessage: '', altContactNumber: ''));
    if (!state.isAltPhoneFieldVisible) altPhoneController?.text = "";
    await _appAnalysisRepository.sendAppAnalysis(name: state.isAltPhoneFieldVisible ? AppAnalyticsConstants.mobileAddDataButtonRemoveAlternateNumberClick : AppAnalyticsConstants.mobileAddDataButtonAddAlternateNumberClick);
  }

  FutureOr<void> _onToggleEmailField(ToggleEmailFieldEvent event, Emitter<CustomAddDataState> emit) async {
    emit(state.copyWith(isEmailFieldVisible: !state.isEmailFieldVisible, errorMessage: ''));
    if (!state.isEmailFieldVisible) emailController?.text = "";
    await _appAnalysisRepository.sendAppAnalysis(name: state.isEmailFieldVisible ? AppAnalyticsConstants.mobileAddDataButtonAddEmailClick : AppAnalyticsConstants.mobileAddDataButtonRemoveEmailClick);
  }

  FutureOr<void> _onTogglePossessionDate(TogglePossessionDateEvent event, Emitter<CustomAddDataState> emit) {
    emit(state.copyWith(isPossessionDateVisible: !state.isPossessionDateVisible, errorMessage: '', updatePossessionDate: state.isPossessionDateVisible));
  }

  FutureOr<void> _onSelectPossessionDate(SelectPossessionDateEvent event, Emitter<CustomAddDataState> emit) async {
    emit(state.copyWith(possessionDate: event.selectedDate));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonPossessionDateClick);
  }

  Future<void> _initInitialData(Emitter<CustomAddDataState> emit) async {
    dataNameController?.text = getProspectEntity?.name ?? '';
    phoneController?.text = getProspectEntity?.contactNo ?? '';
    emailController?.text = getProspectEntity?.email ?? '';
    altPhoneController?.text = getProspectEntity?.alternateContactNo ?? '';
    referralNameController?.text = getProspectEntity?.referralName ?? '';
    referralPhoneController?.text = getProspectEntity?.referralContactNo ?? '';
    designationController?.text = getProspectEntity?.designation ?? '';
    notesController?.text = getProspectEntity?.notes ?? '';
    companyNameController?.text = getProspectEntity?.companyName ?? '';
    unitNumberOrNameController?.text = getProspectEntity?.enquiry?.unitName ?? '';
    clusterNameController?.text = getProspectEntity?.enquiry?.clusterName ?? '';
    executiveNameController?.text = getProspectEntity?.executiveName ?? '';
    executivePhoneController?.text = getProspectEntity?.executiveContactNo ?? '';
    referralEmailController?.text = getProspectEntity?.referralEmail ?? '';
    if (getProspectEntity?.enquiry?.upperBudget != null && (getProspectEntity?.enquiry?.upperBudget ?? 0) > 0) {
      maxBudgetController?.text = getProspectEntity?.enquiry?.upperBudget?.toString() ?? '';
    }
    if (getProspectEntity?.enquiry?.lowerBudget != null && (getProspectEntity?.enquiry?.lowerBudget ?? 0) > 0) {
      minBudgetController?.text = getProspectEntity?.enquiry?.lowerBudget?.toString() ?? '';
    }
    if (getProspectEntity?.enquiry?.carpetArea != null && (getProspectEntity?.enquiry?.carpetArea ?? 0.0) > 0.0) {
      carpetAreaController?.text = getProspectEntity?.enquiry?.carpetArea?.toString().replaceAll('.0', '') ?? '';
    }
    if (getProspectEntity?.enquiry?.saleableArea != null && (getProspectEntity?.enquiry?.saleableArea ?? 0.0) > 0.0) {
      saleableAreaAreaController?.text = getProspectEntity?.enquiry?.saleableArea?.toString().replaceAll('.0', '') ?? '';
    }
    if (getProspectEntity?.enquiry?.builtUpArea != null && (getProspectEntity?.enquiry?.builtUpArea ?? 0.0) > 0.0) {
      builtUpAreaController?.text = getProspectEntity?.enquiry?.builtUpArea?.toString().replaceAll('.0', '') ?? '';
    }
    if (getProspectEntity?.enquiry?.netArea != null && getProspectEntity!.enquiry!.netArea! > 0.0) {
      netAreaController?.text = getProspectEntity?.enquiry?.netArea?.toString().replaceAll('.0', '') ?? '';
    }
    if (getProspectEntity?.enquiry?.propertyArea != null && getProspectEntity!.enquiry!.propertyArea! > 0.0) {
      propertyAreaController?.text = getProspectEntity?.enquiry?.propertyArea?.toString().replaceAll('.0', '') ?? '';
    }

    final initSelectedLocations = getProspectEntity?.enquiry?.addresses;
    if (initSelectedLocations != null) {
      final locations = initSelectedLocations
          .map((e) {
            final addressString = [e?.subLocality, e?.locality, e?.state, e?.city, e?.country].where((part) => part != null).join(', ');
            return ItemSimpleModel<AddressModel>(
              title: addressString,
              value: e?.toModel(),
            );
          })
          .where((e) => e.value != null)
          .toList();
      emit(state.copyWith(locations: locations));
      _addProspectModel = _addProspectModel.copyWith(
        enquiry: _addProspectModel.enquiry?.copyWith(
          addresses: locations.map((e) => e.value).whereType<AddressModel>().toList(),
        ),
      );
    }

    final initSelectedCustomerLocation = getProspectEntity?.addressDto;
    if (initSelectedCustomerLocation != null) {
      final customerLocation = ItemSimpleModel<AddressModel>(
        title: '${initSelectedCustomerLocation.subLocality ?? ''}, ${initSelectedCustomerLocation.locality ?? ''}, ${initSelectedCustomerLocation.state ?? ''}, ${initSelectedCustomerLocation.city ?? ''}, ${initSelectedCustomerLocation.country ?? ''}',
        value: initSelectedCustomerLocation.toModel(),
      );
      emit(state.copyWith(customerLocations: customerLocation));
      _addProspectModel = _addProspectModel.copyWith(addressDto: customerLocation.value);
    }

    emit(state);
  }

  FutureOr<void> _onAddLocation(AddLocationEvent event, Emitter<CustomAddDataState> emit) async {
    if (event.location == null) return;
    List<ItemSimpleModel<AddressModel>> locations = [];
    var locality = event.location?.locality ?? event.location?.subLocality ?? '';
    var state_ = event.location?.state ?? '';
    var city = event.location?.city ?? '';
    var country = event.location?.country ?? '';
    String title = [locality, city, state_, country].where((element) => element.isNotEmpty).join(', ');
    locations.add(ItemSimpleModel<AddressModel>(title: title, value: event.location));
    emit(state.copyWith(locations: [...locations, ...state.locations]));
    final selectedAddresses = state.locations.map((e) => e.value).nonNulls.toList();
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(addresses: selectedAddresses));
  }

  FutureOr<void> _onAddCustomerLocation(AddCustomerLocationEvent event, Emitter<CustomAddDataState> emit) async {
    if (event.customerLocation == null) return;
    ItemSimpleModel<AddressModel> location;
    var locality = event.customerLocation?.locality ?? event.customerLocation?.subLocality ?? '';
    var state_ = event.customerLocation?.state ?? '';
    var city = event.customerLocation?.city ?? '';
    var country = event.customerLocation?.country ?? '';
    String title = [locality, city, state_, country].where((element) => element.isNotEmpty).join(', ');
    location = ItemSimpleModel<AddressModel>(title: title, value: event.customerLocation);
    emit(state.copyWith(customerLocations: location));
    _addProspectModel = _addProspectModel.copyWith(addressDto: state.customerLocations?.value);
  }

  FutureOr<void> _onRemoveLocation(RemoveLocationEvent event, Emitter<CustomAddDataState> emit) {
    final updatedLocations = state.locations.whereNot((element) => element.value == event.selectedItem.value).toList();
    emit(state.copyWith(locations: updatedLocations));
    final selectedAddresses = updatedLocations.map((e) => e.value).nonNulls.toList();
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(addresses: selectedAddresses));
  }

  FutureOr<void> _onRemoveCustomerLocation(RemoveCustomerLocationEvent event, Emitter<CustomAddDataState> emit) {
    emit(state.copyWith(customerLocations: ItemSimpleModel<AddressModel>(title: 'None')));
    _addProspectModel = _addProspectModel.copyWith(addressDto: state.customerLocations?.value);
  }

  FutureOr<void> _onCheckDataContactAlreadyExists(CheckDataContactAlreadyExistsEvent event, Emitter<CustomAddDataState> emit) async {
    final contactWithCountryCode = "${event.countryCode}${event.contactNo}";
    if (isEditing && contactWithCountryCode == getProspectEntity?.contactNo) return;
    final dataContactExists = await _getDataByContactNoUseCase(GetDataByContactNoUseCaseParams(event.countryCode.trim(), event.contactNo.trim()));
    dataContactExists.fold(
      (failure) => null,
      (dataContact) {
        if (dataContact != null && _usersDataRepository.checkHasPermission(AppModule.prospect, CommandType.createDuplicateProspects)) {
          if (!(state.isDuplicateLeadBottomSheetVisible)) {
            emit(
              state.copyWith(isDuplicateLeadBottomSheetVisible: (dataContact.canNavigate ?? false) || dataContact.id != null, existingDataId: dataContact.id, primaryOrSecondary: 'primary'),
            );
          }
        } else if (dataContact != null && !(state.isDataAlreadyExits)) {
          LeadratCustomSnackbar.show(message: "Prospect already exists with this number.", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
          emit(state.copyWith(isDataAlreadyExits: (dataContact.canNavigate ?? false) || dataContact.id != null, existingDataId: dataContact.id));
        } else if (dataContact == null) {
          emit(state.copyWith(isDataAlreadyExits: false, contactNumber: contactWithCountryCode));
        }
        if (!(_usersDataRepository.checkHasPermission(AppModule.prospect, CommandType.viewAllProspects))) {
          if (state.existingDataId.isNotNullOrEmpty()) {
            add(AssignedToLoggedInUser());
          }
        }
      },
    );
  }

  FutureOr<void> _onCheckAltContactAlreadyExists(CheckAltContactAlreadyExistsEvent event, Emitter<CustomAddDataState> emit) async {
    final contactWithCountryCode = "${event.countryCode}${event.contactNo}";
    if (isEditing && contactWithCountryCode == getProspectEntity?.alternateContactNo || contactWithCountryCode == getProspectEntity?.contactNo) return;
    final checkIfDataContactExists = await _getDataByContactNoUseCase(GetDataByContactNoUseCaseParams(event.countryCode.trim(), event.contactNo.trim()));
    checkIfDataContactExists.fold(
      (failure) => null,
      (dataContact) {
        if (dataContact != null && _usersDataRepository.checkHasPermission(AppModule.prospect, CommandType.createDuplicateProspects)) {
          if (!(state.isDuplicateDataBottomSheetVisibleForAltNumber)) {
            emit(
              state.copyWith(isDuplicateDataBottomSheetVisibleForAltNumber: (dataContact.canNavigate ?? false) || dataContact.id != null, existingDataId: dataContact.id, primaryOrSecondary: 'alternative'),
            );
          }
        } else if (dataContact != null && !(state.isDataAlreadyExitsOnAltNumber)) {
          LeadratCustomSnackbar.show(message: "data already exists with this number.", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
          emit(state.copyWith(isDataAlreadyExitsOnAltNumber: (dataContact.canNavigate ?? false) || dataContact.id != null, existingDataId: dataContact.id));
        }
        if (!(_usersDataRepository.checkHasPermission(AppModule.prospect, CommandType.viewAllProspects))) {
          if (state.existingDataId.isNotNullOrEmpty()) {
            add(AssignedToLoggedInUser());
          }
        }
      },
    );
  }

  FutureOr<void> _onOnLeadContactChanged(OnDataContactChangedEvent event, Emitter<CustomAddDataState> emit) {
    final dataContact = "+${event.countryCode}${event.contactNumber}";
    if (event.countryCode.isNotEmpty && event.contactNumber.isNotEmpty) emit(state.copyWith(contactNumber: dataContact, isDataAlreadyExits: false, isDuplicateLeadBottomSheetVisible: false));
  }

  FutureOr<void> _onOnAltContactChanged(OnAltContactChangedEvent event, Emitter<CustomAddDataState> emit) {
    final altContact = "+${event.countryCode}${event.contactNumber}";
    emit(state.copyWith(altContactNumber: altContact, isDataAlreadyExitsOnAltNumber: false, isDuplicateDataBottomSheetVisibleForAltNumber: false));
  }

  FutureOr<void> _onOnReferralContactChanged(OnReferralContactChangedEvent event, Emitter<CustomAddDataState> emit) {
    final referralContact = "+${event.countryCode}${event.contactNumber}";
    if (event.countryCode.isNotEmpty && event.contactNumber.isNotEmpty) emit(state.copyWith(referralContact: referralContact));
  }

  FutureOr<void> _onPickContact(PickContactEvent event, Emitter<CustomAddDataState> emit) async {
    final contact = await PhoneUtils.pickContact();
    if (contact != null && contact.fullName != null && contact.phoneNumber != null) {
      dataNameController?.text = contact.fullName ?? '';
      final contactNumber = contact.phoneNumber?.number.toString().replaceAll(' ', '').replaceAll('-', '').trim() ?? '';
      phoneController?.text = contactNumber;
      await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonImportContactsClick);
    }
  }

  FutureOr<void> _onCurrencySelect(SelectCurrency event, Emitter<CustomAddDataState> emit) async {
    emit(state.copyWith(selectedCurrency: event.selectedCurrency));
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(currency: event.selectedCurrency.value));
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonBudgetCurrencyClick);
  }

  FutureOr<void> _onSelectedChannelPartners(SelectChannelPartnerEvent event, Emitter<CustomAddDataState> emit) async {
    final selectedChannelPartners = event.selectedChannelPartners.map((e) => e.title).toList();
    emit(state.copyWith(selectedChannelPartners: event.selectedChannelPartners));
    _addProspectModel = _addProspectModel.copyWith(channelPartnerList: selectedChannelPartners);
    await _appAnalysisRepository.sendAppAnalysis(name: AppAnalyticsConstants.mobileAddDataButtonChannelPartnerNameClick);
  }

  FutureOr<void> _onRemoveChannelPartnerName(RemoveChannelPartnerNameEvent event, Emitter<CustomAddDataState> emit) {
    final updatedSelectedChannelPartners = state.selectedChannelPartners.whereNot((element) => element.title == event.selectedChannelPartner.title).toList();
    emit(state.copyWith(selectedChannelPartners: updatedSelectedChannelPartners, errorMessage: ''));
    // _addProspectModel.channelPartners?.removeWhere((element) => element.firmName == event.selectedChannelPartner.title);
    _addProspectModel.channelPartnerList?.removeWhere((element) => element == event.selectedChannelPartner.title);
  }

  FutureOr<void> _onCreateData(CreateDataEvent event, Emitter<CustomAddDataState> emit) async {
    if (dataNameController?.text.trim().isEmpty ?? false) {
      _emitFailureState(emit, "Please enter a prospect name");
      return;
    }
    if (state.contactNumber?.isEmpty ?? false) {
      _emitFailureState(emit, "Please enter a contact number");
      return;
    }
    if (minBudgetController?.text.isNotEmpty ?? false) {
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(lowerBudget: int.tryParse(minBudgetController?.text ?? '0')));
    }
    if (maxBudgetController?.text.isNotEmpty ?? false) {
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(upperBudget: int.tryParse(maxBudgetController?.text ?? '0')));
    }
    final isPropertyTypeSelected = state.propertyTypes.any((element) => element.isSelected);
    final isPropertySubTypeSelected = state.propertySubTypes.any((element) => element.isSelected);
    if (isPropertyTypeSelected && !isPropertySubTypeSelected) {
      _emitFailureState(emit, "Please select property sub-type");
      return;
    }
    _addProspectModel = _addProspectModel.copyWith(
      alternateContactNo: state.altContactNumber.isNotNullOrEmpty() ? state.altContactNumber?.trim() : null,
      email: (emailController?.text.isEmpty ?? true) ? null : emailController?.text.trim(),
      notes: (notesController?.text.isEmpty ?? true) ? null : notesController?.text.trim(),
      companyName: (companyNameController?.text.isEmpty ?? true) ? null : companyNameController?.text.trim(),
      referralName: (referralNameController?.text.isEmpty ?? true) ? null : referralNameController?.text.trim(),
      referralContactNo: state.referralContact?.trim(),
      referralEmail: (referralEmailController?.text.isEmpty ?? true) ? null : referralEmailController?.text.trim(),
      designation: (designationController?.text.isEmpty ?? true) ? null : designationController?.text.trim(),
      enquiry: _addProspectModel.enquiry?.copyWith(
        unitName: (unitNumberOrNameController?.text.isEmpty ?? true) ? null : unitNumberOrNameController?.text.trim(),
        clusterName: (clusterNameController?.text.isEmpty ?? true) ? null : clusterNameController?.text.trim(),
      ),
      possesionDate: state.possessionDate?.getBasedOnTimeZone()?.toUtcFormat(),
      executiveName: (executiveNameController?.text.isEmpty ?? true) ? null : executiveNameController?.text.trim(),
      executiveContactNo: state.executiveContact?.trim(),
    );

    if (carpetAreaController?.text.isNotEmpty ?? false) {
      var carpetValue = double.parse(carpetAreaController!.text);
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(carpetArea: carpetValue));
      if (state.selectedCarpetArea == null) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'please select the carpet unit'));
        return;
      }
    } else {
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(resetCarpetAreaUnit: true));
    }

    if (saleableAreaAreaController?.text.isNotEmpty ?? false) {
      var saleableValue = double.parse(saleableAreaAreaController!.text);
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(saleableArea: saleableValue));
      if (state.selectedSaleableArea == null) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'please select the saleable unit'));
        return;
      }
    } else {
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(resetSaleableAreaUnit: true));
    }

    if (builtUpAreaController?.text.isNotEmpty ?? false) {
      var builtUpValue = double.parse(builtUpAreaController!.text);
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(builtUpArea: builtUpValue));
      if (state.selectedBuiltUpArea == null) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'please select the builtUp unit'));
        return;
      }
    } else {
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(resetBuiltUpAreaAreaUnit: true));
    }
    if (netAreaController?.text.isNotEmpty ?? false) {
      var netAreaValue = double.parse(netAreaController!.text);
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(netArea: netAreaValue));
      if (state.selectedNetArea == null) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'please select the net unit'));
        return;
      }
    } else {
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(resetNetAreaUnit: true));
    }
    if (propertyAreaController?.text.isNotEmpty ?? false) {
      var propertyAreaValue = double.parse(propertyAreaController!.text);
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(propertyArea: propertyAreaValue));
      if (state.selectedPropertyArea == null) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: 'please select the property area unit'));
        return;
      }
    } else {
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(resetPropertyAreaUnit: true));
    }
    _addProspectModel = _addProspectModel.copyWith(name: dataNameController?.text.trim() ?? "", contactNo: state.contactNumber?.trim() ?? "");
    if (state.possessionTypeSelectedItem != null) {
      _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(possesionType: state.possessionTypeSelectedItem?.value));
    }
    if (isEditing) {
      emit(state.copyWith(pageState: PageState.loading, dialogMessage: "updating prospect"));
      var statusId = getProspectEntity != null && getProspectEntity?.status?.id != null ? getProspectEntity?.status?.id : '';
      _addProspectModel = _addProspectModel.copyWith(statusId: statusId, id: getProspectEntity?.id, serialNumber: getProspectEntity?.serialNumber);
      final updateData = await _updateDataUseCase(_addProspectModel.toUpdateProspectModel());
      updateData.fold((failure) => _emitFailureState(emit, failure.message), (success) {
        if (success != null) {
          getIt<DataDetailsBloc>().add(GetProspectDetailsEvent(success));
          emit(state.copyWith(pageState: PageState.success, errorMessage: '', successMessage: "prospect edited successfully"));
          getIt<ManageDataBloc>().add(ManageDataInitialEvent(dataFilter: getIt<ManageDataBloc>().dataFilter));
          getIt<DataNotesBloc>().add(GetAllDataNotesEvent(success));
        }
      });
    } else {
      emit(state.copyWith(pageState: PageState.loading, dialogMessage: "saving prospect"));
      final addData = await _addDataUseCase(_addProspectModel);
      addData.fold((failure) => _emitFailureState(emit, failure.message), (success) {
        emit(state.copyWith(pageState: PageState.success, errorMessage: '', successMessage: "prospect added successfully"));
        getIt<ManageDataBloc>().add(ManageDataInitialEvent(filterType: null, dataFilter: null));
      });
    }
  }

  bool _validatePropertyAreas() {
    double? propertyArea = (propertyAreaController?.text.isNotNullOrEmpty() ?? false) ? double.parse(propertyAreaController!.text) : null;
    double? saleableArea = (saleableAreaAreaController?.text.isNotNullOrEmpty() ?? false) ? double.parse(saleableAreaAreaController!.text) : null;
    double? builtUpArea = (builtUpAreaController?.text.isNotNullOrEmpty() ?? false) ? double.parse(builtUpAreaController!.text) : null;
    double? carpetArea = (carpetAreaController?.text.isNotNullOrEmpty() ?? false) ? double.parse(carpetAreaController!.text) : null;
    double? netArea = (netAreaController?.text.isNotNullOrEmpty() ?? false) ? double.parse(netAreaController!.text) : null;

    if (propertyArea != null && saleableArea != null && propertyArea <= saleableArea) {
      LeadratCustomSnackbar.show(message: "Saleable Area should be less than Property Area.", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (propertyArea != null && builtUpArea != null && propertyArea <= builtUpArea) {
      LeadratCustomSnackbar.show(message: 'Built-up Area should be less than Property Area.', navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (propertyArea != null && carpetArea != null && propertyArea <= carpetArea) {
      LeadratCustomSnackbar.show(message: 'Carpet Area should be less than Property Area.', navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);

      return false;
    }
    if (propertyArea != null && netArea != null && propertyArea <= netArea) {
      LeadratCustomSnackbar.show(message: 'Net Area should be less than Property Area.', navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (saleableArea != null && builtUpArea != null && saleableArea <= builtUpArea) {
      LeadratCustomSnackbar.show(message: 'Built-up Area should be less than Saleable Area.', navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (saleableArea != null && carpetArea != null && saleableArea <= carpetArea) {
      LeadratCustomSnackbar.show(message: 'Carpet Area should be less than Saleable Area.', navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (saleableArea != null && netArea != null && saleableArea <= netArea) {
      LeadratCustomSnackbar.show(message: 'Net Area should be less than Saleable Area.', navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (builtUpArea != null && carpetArea != null && builtUpArea <= carpetArea) {
      LeadratCustomSnackbar.show(message: 'Carpet Area should be less than Built-up Area.', navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (builtUpArea != null && netArea != null && builtUpArea <= netArea) {
      LeadratCustomSnackbar.show(message: 'Net Area should be less than Built-up Area.', navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    if (carpetArea != null && netArea != null && carpetArea <= netArea) {
      LeadratCustomSnackbar.show(message: "Net Area should be less than Carpet Area.", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      return false;
    }
    return true;
  }

  FutureOr<void> _onSelectLeadNationalityEvent(SelectLeadNationalityEvent event, Emitter<CustomAddDataState> emit) {
    emit(state.copyWith(selectedCountry: event.selectedCountry));
    _addProspectModel = _addProspectModel.copyWith(nationality: state.selectedCountry?.title);
  }

  FutureOr<void> _onSelectPurpose(SelectPurposeEvent event, Emitter<CustomAddDataState> emit) async {
    if (event.selectedPurpose == null) return;
    emit(state.copyWith(selectedPurpose: event.selectedPurpose));
    _addProspectModel = _addProspectModel.copyWith(enquiry: _addProspectModel.enquiry?.copyWith(purpose: state.selectedPurpose?.value));
  }

  void _initPurpose(Emitter<CustomAddDataState> emit) {
    final initialSelectedPurpose = getProspectEntity?.enquiry?.purpose;
    final purposes = PurposeEnum.values.where((element) => element != PurposeEnum.none).map((e) => SelectableItem<PurposeEnum>(title: e.description, value: e));
    emit(state.copyWith(purposes: purposes.toList()));
    if (purposes.isNotEmpty) add(SelectPurposeEvent(purposes.firstWhereOrNull((element) => element.value == initialSelectedPurpose)));
  }

  FutureOr<void> _onAssignedToLoggedInUser(AssignedToLoggedInUser event, Emitter<CustomAddDataState> emit) async {
    final response = await _checkProspectAssignedByProspectIdUseCase.call(state.existingDataId ?? '');
    response.fold((failure) => {}, (result) {
      emit(state.copyWith(isAssignedLoggedInUser: result));
    });
  }

  void _initPossessionType(Emitter<CustomAddDataState> emit) {
    PossessionType? initialPossessionType = getProspectEntity?.enquiry?.possesionType;
    if (getProspectEntity?.possesionDate != null) initialPossessionType ??= PossessionType.customDate;
    List<SelectableItem<PossessionType?>> possessionTypeItem = PossessionType.values.where((e) => e.description != 'None').map((e) => SelectableItem<PossessionType?>(title: e.description, value: e)).toList();
    emit(state.copyWith(possessionTypeSelectableItems: possessionTypeItem));
    if (possessionTypeItem.isNotEmpty) add(SelectPossessionType(possessionTypeItem.firstWhereOrNull((element) => element.value == initialPossessionType)));
  }

  FutureOr<void> _onSelectPossessionType(SelectPossessionType event, Emitter<CustomAddDataState> emit) async {
    emit(state.copyWith(possessionTypeSelectedItem: event.selectPossessionType, isPossessionDateCustomSelected: event.selectPossessionType?.value == PossessionType.customDate, possessionDate: DateTimeUtils.getPossessionDate(event.selectPossessionType?.value ?? PossessionType.none), updatePossessionDate: PossessionType.underConstruction == event.selectPossessionType?.value));
  }

  FutureOr<void> _onExecutiveContactChanged(OnExecutiveContactChangedEvent event, Emitter<CustomAddDataState> emit) {
    final executiveContact = "+${event.countryCode}${event.contactNumber}";
    if (event.countryCode.isNotEmpty && event.contactNumber.isNotEmpty) emit(state.copyWith(executiveContact: executiveContact));
  }
}
