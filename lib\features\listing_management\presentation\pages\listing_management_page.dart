import 'package:collection/collection.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:jumping_dot/jumping_dot.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/pages/search_page.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/property_enums/property_status.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/dialog_manager.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/listing_management_bloc/listing_management_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/listing_management_filter_bloc/listing_management_filter_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/item/item_listing_management_model.dart';
import 'package:leadrat/features/listing_management/presentation/pages/listing_managemenet_filter_page.dart';
import 'package:leadrat/features/listing_management/presentation/widgets/listing_management_item_widget.dart';
import 'package:leadrat/features/notification/presentation/pages/notification_page.dart';
import 'package:leadrat/features/properties/presentation/bloc/add_property_bloc/basic_info_bloc/basic_info_bloc.dart';
import 'package:leadrat/features/properties/presentation/bloc/add_property_bloc/gallery_tab_bloc/gallery_tab_bloc.dart';
import 'package:leadrat/features/properties/presentation/bloc/add_property_bloc/property_info_tab_bloc/property_info_tab_bloc.dart';
import 'package:leadrat/features/properties/presentation/pages/add_property_page.dart';
import 'package:leadrat/features/properties/presentation/pages/property_share_page.dart';
import 'package:leadrat/features/properties/presentation/widgets/property_listing_app_bar_widget.dart';
import 'package:leadrat/features/properties/presentation/widgets/property_listing_category_widget.dart';
import 'package:leadrat/features/properties/presentation/widgets/property_skeleton_view_widget.dart';

class ListingManagementPage extends LeadratStatefulWidget {
  const ListingManagementPage({super.key});

  @override
  State<ListingManagementPage> createState() => _ListingManagementPageState();
}

class _ListingManagementPageState extends LeadratState<ListingManagementPage> {
  final ListingManagementBloc listingManagementBloc = getIt<ListingManagementBloc>();

  ScrollController scrollController = ScrollController();

  @override
  void initState() {
    listingManagementBloc.add(ListingManagementInitialEvent());
    scrollController.addListener(() {
      if (scrollController.position.pixels >= scrollController.position.maxScrollExtent * 0.3) {
        listingManagementBloc.add(LoadMorePropertiesEvent());
      }
    });
    super.initState();
  }

  @override
  Widget buildContent(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorPalette.primaryDarkColor,
      floatingActionButton: FloatingActionButton(
        heroTag: 'property_listing',
        shape: const CircleBorder(),
        backgroundColor: ColorPalette.primaryGreen,
        onPressed: _openAddPropertyPage,
        child: SvgPicture.asset(ImageResources.iconAdd),
      ),
      body: SafeArea(
        child: Stack(
          children: [
            SvgPicture.asset(ImageResources.imagePropertyListingBgPattern, width: context.width(100), height: context.height(100), fit: BoxFit.cover),
            BlocConsumer<ListingManagementBloc, ListingManagementState>(
              listener: (context, state) {
                if (state.pageState == PageState.failure && (state.errorMessage.isNotNullOrEmpty())) {
                  LeadratCustomSnackbar.show(message: state.errorMessage ?? "Something went wrong");
                  DialogManager().hideTransparentProgressDialog();
                } else if (state.pageState == PageState.success) {
                  DialogManager().hideTransparentProgressDialog();
                }
              },
              builder: (context, state) {
                final properties = state.properties;
                return Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 24, top: 16, right: 14),
                      child: PropertyListingAppBarWidget(
                          onFilterPressed: _openFilterPage,
                          oNotificationPressed: () {
                            Navigator.push(context, MaterialPageRoute(builder: (context) => const NotificationPage()));
                          },
                          onSearchPressed: _openSearchPage),
                    ),
                    if (state.propertyCounts.isEmpty)
                      const Padding(
                        padding: EdgeInsets.only(left: 24, top: 14),
                        child: PropertyListingCategorySkeletonWidget(),
                      )
                    else if (state.propertyCounts.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(left: 24, top: 14),
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: state.propertyCounts
                                .map(
                                  (item) => PropertyListingCategoryWidget(
                                    propertyCountItem: item,
                                    onTap: () => context.read<ListingManagementBloc>().add(TogglePropertyVisibilityEvent(item.value ?? PropertyVisibility.all)),
                                  ),
                                )
                                .toList(),
                          ),
                        ),
                      ),
                    if (state.selectedFilters.isNotEmpty) _buildSelectedFilterView(state),
                    if (state.selectedProperties.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(left: 24, right: 14, bottom: 8, top: 14),
                        child: _buildPropertyActionsWidget(context, state),
                      ),
                    if (state.pageState == PageState.loading)
                      const Expanded(
                          child: Padding(
                        padding: EdgeInsets.only(top: 10),
                        child: SkeletonViewWidget(),
                      ))
                    else if (state.pageState == PageState.success)
                      if (state.properties?.isEmpty ?? false)
                        Expanded(child: Center(child: Text("No properties found", style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.primaryTextColor))))
                      else
                        Expanded(
                          child: RefreshIndicator(
                            color: ColorPalette.primaryGreen,
                            onRefresh: () async {
                              context.read<ListingManagementBloc>().add(RefreshPropertiesEvent());
                            },
                            child: Padding(
                              padding: const EdgeInsets.only(top: 10),
                              child: ListView.builder(
                                physics: const AlwaysScrollableScrollPhysics(),
                                controller: scrollController,
                                itemCount: (properties != null && properties.isNotEmpty) ? properties.length + 1 : 0,
                                itemBuilder: (context, index) {
                                  return properties!.length > index
                                      ? Padding(
                                          padding: const EdgeInsets.only(left: 24, right: 12, bottom: 10),
                                          child: ListingManagementItemWidget(propertyItem: properties[index]),
                                        )
                                      : properties.isNotEmpty && state.totalPropertiesCount > properties.length
                                          ? JumpingDots(color: Colors.greenAccent, radius: 5, numberOfDots: 5, verticalOffset: 3)
                                          : const SizedBox();
                                },
                              ),
                            ),
                          ),
                        )
                    else if (state.pageState == PageState.failure)
                      _buildErrorWidget()
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyActionsWidget(BuildContext context, ListingManagementState state) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                if (getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.publishProperty)) ...[
                  if (getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.bulkList))
                    if (state.selectedPropertyVisibility != PropertyVisibility.approved) ...[
                      InkWell(
                        onTap: () => listingManagementBloc.add(ToggleListDelistPropertyEvent(true)),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: Text("List", style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.primaryGreen, decorationColor: ColorPalette.primaryGreen, decoration: TextDecoration.underline)),
                        ),
                      ),
                      const SizedBox(width: 16),
                    ],
                  if (getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.bulkDeList))
                    InkWell(
                      onTap: () => listingManagementBloc.add(ToggleListDelistPropertyEvent(false)),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        child: Text("Delist", style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.primaryGreen, decorationColor: ColorPalette.primaryGreen, decoration: TextDecoration.underline)),
                      ),
                    ),
                  const SizedBox(width: 16),
                ],
                if (getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.bulkShare))
                  InkWell(
                    onTap: () => _shareProperty(state.properties),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: Text("Share", style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.primaryGreen, decorationColor: ColorPalette.primaryGreen, decoration: TextDecoration.underline)),
                    ),
                  ),
                const SizedBox(width: 16),
                if (getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.bulkReAssign))
                  SelectableItemBottomSheet<String?>(
                    title: "assign property",
                    selectableItems: state.allUsers ?? [],
                    isMultipleSelection: true,
                    onItemsSelected: (value) => _assignProperty(value, state),
                    canSearchItems: true,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: Text("Assign", style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.primaryGreen, decorationColor: ColorPalette.primaryGreen, decoration: TextDecoration.underline)),
                    ),
                  ),
              ],
            ),
          ),
        ),
        GestureDetector(
          onTap: () => listingManagementBloc.add(DeSelectAllPropertiesEvent()),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Text("Deselect all", style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.mediumRed, decoration: TextDecoration.underline, decorationColor: ColorPalette.mediumRed)),
          ),
        )
      ],
    );
  }

  Widget _buildSelectedFilterView(ListingManagementState state) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(24, 10, 14, 0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: state.selectedFilters.map((selectedFilter) {
            return Padding(
                padding: const EdgeInsets.only(right: 5),
                child: Container(
                  padding: const EdgeInsets.fromLTRB(12, 8, 0, 8),
                  decoration: BoxDecoration(
                    color: ColorPalette.lightBackground.withOpacity(.4),
                    borderRadius: BorderRadius.circular(100),
                    border: Border.all(color: ColorPalette.primaryLightColor, width: 1.5),
                  ),
                  child: Row(
                    children: [
                      Text(
                        "${selectedFilter.title} ${selectedFilter.description ?? ''}",
                        style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.gray200),
                      ),
                      GestureDetector(
                        onTap: () => listingManagementBloc.add(RemoveFilterEvent(selectedFilter.value)),
                        child: const Padding(
                          padding: EdgeInsets.only(right: 8, left: 8),
                          child: Icon(Icons.remove_circle_outline_rounded, color: ColorPalette.tertiaryTextColor, size: 16),
                        ),
                      ),
                    ],
                  ),
                ));
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          children: [
            TextSpan(
              text: "Oops, Something went wrong\n",
              style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.secondaryTextColor),
            ),
            TextSpan(
              text: "Click here to refresh\n",
              style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.primaryTextColor),
              recognizer: TapGestureRecognizer()..onTap = () => getIt<ListingManagementBloc>().add(ListingManagementInitialEvent()),
            ),
          ],
        ),
      ),
    );
  }

  void _openAddPropertyPage() {
    if (getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.create)) {
      context.read<BasicInfoBloc>().add(ClearBasicInfoTabStateEvent());
      context.read<PropertyInfoTabBloc>().add(ClearPropertyInfoTabStateEvent());
      context.read<GalleryTabBloc>().add(ClearGalleryTabStateEvent());
      Navigator.of(context).push(MaterialPageRoute(builder: (context) => const AddPropertyPage()));
    } else {
      LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: 'you do not have permission to create property');
    }
  }

  void _openFilterPage() {
    context.read<ListingManagementFilterBloc>().add(InitListingManagementFilterEvent(listingFilterModel: listingManagementBloc.listingFilterModel, globalSettingModel: listingManagementBloc.globalSettings));
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      useSafeArea: true,
      isDismissible: false,
      backgroundColor: ColorPalette.darkToneInk,
      barrierColor: ColorPalette.darkToneInk,
      builder: (context) {
        return ListingManagementFilterPage(
          onApplyFilter: (selectedFilter) {
            if (selectedFilter != null) {
              listingManagementBloc.add(ListingManagementInitialEvent(listingFilter: selectedFilter));
            }
          },
        );
      },
    );
  }

  void _openSearchPage() {
    if (getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.search)) {
      Navigator.push(context, MaterialPageRoute(builder: (context) => const SearchPage(appModule: AppModule.property)));
    } else {
      LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: 'you do not have permission to search property');
    }
  }

  void _shareProperty(List<ItemListingManagementModel>? itemPropertyModels) {
    if (getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.bulkShare)) {
      List<String>? selectedPropertyIds = [];
      String title = '';
      itemPropertyModels?.forEach((selectedProperty) {
        if (selectedProperty.isSelected) {
          selectedPropertyIds.add(selectedProperty.id ?? '');
          title = "$title${(selectedProperty.property?.title ?? '')}, ";
        }
      });
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PropertySharePage(title: title, propertyIds: selectedPropertyIds),
        ),
      );
    } else {
      LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: 'you do not have permission to share the properties in bulk');
    }
  }

  void _assignProperty(List<SelectableItem<String?>> value, ListingManagementState state) {
    if (!getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.assign)) {
      LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: 'you do not have permission to assign');
      return;
    }
    List<String>? userIds = [];
    for (var user in value) {
      userIds.add(user.value ?? '');
    }
    List<String>? selectedPropertyIds = state.properties?.where((element) => element.isSelected).map((e) => e.id).whereNotNull().toList();
    DialogManager().showTransparentProgressDialog(context, message: "assigning properties");
    context.read<ListingManagementBloc>().add(ReAssignPropertyEvent(userIds, selectedPropertyIds));
  }
}
