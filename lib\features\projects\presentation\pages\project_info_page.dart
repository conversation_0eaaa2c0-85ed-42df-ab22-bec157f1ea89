import 'package:carousel_slider/carousel_controller.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/common/shapes/sharp_divider_painter.dart';
import 'package:leadrat/core_main/common/widgets/readmore_text_widget.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/projects/presentation/blocs/project_info_bloc/project_info_bloc.dart';
import 'package:leadrat/features/projects/presentation/items/item_matching_lead_by_project_model.dart';
import 'package:leadrat/features/projects/presentation/items/item_project_Info_model.dart';
import 'package:leadrat/features/projects/presentation/pages/matching_leads_by_project_page.dart';
import 'package:leadrat/features/projects/presentation/widgets/project_amenities_widget.dart';
import 'package:leadrat/features/projects/presentation/widgets/project_unit_info_widget.dart';
import 'package:leadrat/features/properties/presentation/widgets/properties_images_carousel_view.dart';
import 'package:leadrat/features/properties/presentation/widgets/property_info_appbar.dart';
import 'package:leadrat/features/properties/presentation/widgets/property_info_footer.dart';
import 'package:leadrat/features/properties/presentation/widgets/property_info_skeleton_view.dart';

import '../../../../core_main/common/widgets/text_underline.dart';

class ProjectInfoPage extends LeadratStatefulWidget {
  const ProjectInfoPage({required this.projectId, super.key});

  final String projectId;

  @override
  State<ProjectInfoPage> createState() => _ProjectInfoPageState();
}

class _ProjectInfoPageState extends LeadratState<ProjectInfoPage> {
  @override
  void initState() {
    projectInfoBloc.add(ProjectInfoInitialEvent(projectId: widget.projectId));
    super.initState();
  }

  CarouselSliderController carouselSliderController = CarouselSliderController();
  ProjectInfoBloc projectInfoBloc = getIt<ProjectInfoBloc>();

  @override
  Widget buildContent(BuildContext context) {
    return BlocConsumer<ProjectInfoBloc, ProjectInfoState>(
      listener: (context, state) {
        if (state.pageState == PageState.success && state.errorMessage != null && state.errorMessage!.isNotEmpty) {
          LeadratCustomSnackbar.show(context: context, type: SnackbarType.success, message: state.errorMessage!);
        }
      },
      builder: (context, state) {
        return state.pageState == PageState.success || state.pageState == PageState.initial
            ? Scaffold(
                appBar: propertyInfoAppBar(context: context, id: state.itemProjectModel?.projectByIdEntity?.id ?? '', name: state.itemProjectModel?.projectByIdEntity?.name ?? '', isProject: true, projectSerialNumber: state.itemProjectModel?.projectByIdEntity?.serialNo ?? ''),
                body: RefreshIndicator(
                  onRefresh: () async => projectInfoBloc.add(ProjectInfoInitialEvent(projectId: widget.projectId)),
                  child: SafeArea(
                      child: SingleChildScrollView(
                    child: Stack(children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 24, right: 14, top: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Stack(children: [
                              PropertiesCarouselViewWidget(images: state.itemProjectModel?.images ?? [], carouselSliderController: carouselSliderController, propertyImageIndex: state.selectedImageIndex ?? 0, function: (index, reason) => projectInfoBloc.add(ToggleImageEvent(index: index))),
                              Positioned(
                                top: context.height(17),
                                left: 13,
                                child: RichText(text: TextSpan(text: 'Posted on ', style: LexendTextStyles.lexend9Regular.copyWith(color: ColorPalette.primary), children: [TextSpan(text: state.itemProjectModel?.postedDate ?? "--", style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.white))])),
                              ),
                              // state.isHighlighted! ? Positioned(top: 0, left: 0, child: SvgPicture.asset(ImageResources.highlightedProperty)) : const SizedBox.shrink()
                            ]),
                            _aboutProjectWidget(itemProjectInfoModel: state.itemProjectModel, context: context),
                            featuresWidget(context: context, projectFeatures: state.itemProjectModel?.features ?? []),
                            if (state.itemProjectModel != null && state.itemProjectModel!.aboutProject.isNotEmpty)
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 4),
                                  const UnderlineTextWidget(title: 'About Project'),
                                  ReadMoreTextWidget(notes: state.itemProjectModel?.aboutProject ?? '--'),
                                ],
                              ),
                            if (state.itemProjectModel != null && state.itemProjectModel!.associatedBanks.isNotEmpty)
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 4),
                                  const UnderlineTextWidget(title: 'Associated Banks'),
                                  SizedBox(
                                    height: 60,
                                    child: ListView.builder(
                                        itemCount: state.itemProjectModel?.associatedBanks.length ?? 0,
                                        scrollDirection: Axis.horizontal,
                                        itemBuilder: (context, index) {
                                          var imageUrl = state.itemProjectModel!.associatedBanks[index].imageUrl ?? '';
                                          return Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: SvgPicture.network(
                                              imageUrl,
                                              height: 18,
                                            ),
                                          );
                                        }),
                                  ),
                                ],
                              ),
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const UnderlineTextWidget(title: "Matching leads"),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      RichText(
                                        text: TextSpan(text: state.matchingLeads.doubleToWord(), style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.white), children: [
                                          TextSpan(text: " matching leads", style: LexendTextStyles.lexend12Light.copyWith(color: ColorPalette.white)),
                                        ]),
                                      ),
                                      if (state.matchingLeads > 0)
                                        GestureDetector(
                                            onTap: () {
                                              Navigator.push(context, MaterialPageRoute(builder: (context) => MatchingLeadsByProjectPage(matchingLeadProjectModel: ItemMatchingProjectDetailsByProject(getProjectEntity: state.itemProjectModel?.projectByIdEntity))));
                                            },
                                            child: Container(padding: const EdgeInsets.only(top: 6, bottom: 6, right: 25, left: 25), decoration: const BoxDecoration(color: ColorPalette.leadratGreen, borderRadius: BorderRadius.all(Radius.circular(500))), child: Text('view all', style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.white)))),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            ProjectUnitInfoWidget(
                              projectItem: state,
                            ),
                            if (state.itemProjectModel != null && state.itemProjectModel!.isOwnerDetailsNotEmpty) getProjectOwnerDetailsWidget(ownerDetails: state.itemProjectModel?.propertyOwnerDetails),
                            if (state.itemProjectModel?.isAmenityFound ?? false) ...[
                              const UnderlineTextWidget(title: "Amenities"),
                              Column(
                                  children: (state.itemProjectModel?.isAmenityFound ?? false)
                                      ? List.generate(
                                          state.itemProjectModel?.projectAmenities.length ?? 0,
                                          (index) => state.itemProjectModel!.projectAmenities.values.elementAt(index).isNotEmpty
                                              ? ProjectAmenitiesWidget(
                                                  amenity: state.itemProjectModel?.projectAmenities.keys.elementAt(index),
                                                  amenityDetails: state.itemProjectModel?.projectAmenities.values.elementAt(index),
                                                )
                                              : const SizedBox(),
                                        )
                                      : [amenitiesEmptyWidget('No Amenities Found')])
                            ],
                            CustomPaint(size: const Size(double.infinity, 4), painter: SharpDividerPainter(width: 1)),
                            const PropertyInfoTailWidget(),
                          ],
                        ),
                      ),
                    ]),
                  )),
                ),
              )
            : state.pageState == PageState.failure
                ? SafeArea(
                    child: Scaffold(
                        body: Center(
                      child: RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          children: [
                            TextSpan(text: "Oops, Something went wrong\n", style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.secondaryTextColor)),
                            TextSpan(
                              text: "Click here to refresh\n",
                              style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.primaryTextColor),
                              recognizer: TapGestureRecognizer()..onTap = () => projectInfoBloc.add(ProjectInfoInitialEvent(projectId: widget.projectId)),
                            ),
                          ],
                        ),
                      ),
                    )),
                  )
                : const PropertyInfoSkeletonView();
      },
    );
  }

  Widget amenitiesEmptyWidget(String text) {
    return SizedBox(
      height: context.height(40),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(ImageResources.iconNotFound),
            Text(
              text,
              style: LexendTextStyles.lexend15Bold.copyWith(color: ColorPalette.white),
            ),
          ],
        ),
      ),
    );
  }
}

Widget _aboutProjectWidget({ItemProjectInfoModel? itemProjectInfoModel, required BuildContext context}) {
  return Padding(
    padding: const EdgeInsets.only(top: 10),
    child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
      Expanded(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              (itemProjectInfoModel?.title.isNotNullOrEmpty() ?? false) ? '"${itemProjectInfoModel!.title}"' : '--',
              style: LexendTextStyles.lexend11Bold.copyWith(color: ColorPalette.primaryGreen, overflow: TextOverflow.ellipsis, height: 1.8),
            ),
            Text(
              itemProjectInfoModel != null && itemProjectInfoModel.subTitle.isNotNullOrEmpty() ? itemProjectInfoModel.subTitle : '--',
              style: LexendTextStyles.lexend12Bold.copyWith(color: ColorPalette.white, height: 1.8),
            ),
            SizedBox(
                width: context.width(34),
                child: itemProjectInfoModel != null && itemProjectInfoModel.address.isNotNullOrEmpty()
                    ? Text(
                        itemProjectInfoModel.address,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: LexendTextStyles.lexend11SemiBold.copyWith(color: ColorPalette.primary, decoration: TextDecoration.underline, decorationColor: ColorPalette.primary),
                      )
                    : Text(
                        '--',
                        style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.primary, decorationColor: ColorPalette.primary, height: 1.8),
                      )),
          ],
        ),
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(itemProjectInfoModel != null && itemProjectInfoModel.expectedPrice.isNotNullOrEmpty() ? itemProjectInfoModel.expectedPrice : "", style: LexendTextStyles.lexend13Bold.copyWith(color: ColorPalette.white)),
          if (itemProjectInfoModel?.projectStatus.isNotEmpty ?? false)
            Container(
              margin: const EdgeInsets.only(top: 5),
              padding: const EdgeInsets.only(top: 6, bottom: 6, right: 15, left: 15),
              decoration: const BoxDecoration(color: ColorPalette.primaryGreen, borderRadius: BorderRadius.all(Radius.circular(100))),
              child: Text("${itemProjectInfoModel?.projectStatus}", style: LexendTextStyles.lexend12Bold.copyWith(color: ColorPalette.white)),
            )
        ],
      )
    ]),
  );
}

Widget featuresWidget({required BuildContext context, required List<ItemSimpleModel> projectFeatures}) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 15),
    child: Column(
      children: [
        CustomPaint(size: const Size(double.infinity, 4), painter: SharpDividerPainter(width: 1)),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 14),
          child: Wrap(
            crossAxisAlignment: WrapCrossAlignment.center,
            runAlignment: WrapAlignment.center,
            children: List.generate(projectFeatures.length, (index) {
              return Container(
                  width: (MediaQuery.of(context).size.width / 3) - 16, // Width for 3 items
                  padding: const EdgeInsets.symmetric(vertical: 8.5),
                  child: Row(
                    children: [
                      SvgPicture.asset(projectFeatures[index].imageResource ?? ''),
                      const SizedBox(width: 4),
                      SizedBox(
                        width: MediaQuery.of(context).size.width * 0.2,
                        child: RichText(
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          text: TextSpan(
                            text: '${projectFeatures[index].title}\n',
                            style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.primaryWhite300TextColor),
                            children: [
                              TextSpan(text: '${projectFeatures[index].description}', style: LexendTextStyles.lexend11Bold.copyWith(color: ColorPalette.primary)),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ));
            }),
          ),
        ),
        CustomPaint(size: const Size(double.infinity, 4), painter: SharpDividerPainter(width: 1)),
      ],
    ),
  );
}

Widget getProjectOwnerDetailsWidget({Map<String, String>? ownerDetails}) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 10),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const UnderlineTextWidget(
          title: "Property Owner Details",
        ),
        const SizedBox(
          height: 6,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: List.generate(
            ownerDetails?.length ?? 0,
            (index) => getRichText(ownerDetails!.keys.elementAt(index), ownerDetails.values.elementAt(index)),
          ),
        )
      ],
    ),
  );
}

getRichText(String title, String desc) {
  return RichText(
    text: TextSpan(
        text: "$title - ",
        style: LexendTextStyles.lexend11Light.copyWith(
          color: ColorPalette.primary,
        ),
        children: [
          TextSpan(
            text: "$desc ",
            style: LexendTextStyles.lexend11Medium.copyWith(color: ColorPalette.white, height: 1.9),
          ),
        ]),
  );
}

Widget _getHeadTailWidget(String? text1, String? text2) {
  return RichText(
      textAlign: TextAlign.start,
      text: TextSpan(
          text: '$text1\n',
          style: LexendTextStyles.lexend11Bold.copyWith(
            color: ColorPalette.primaryWhite300TextColor,
          ),
          children: [
            TextSpan(text: '$text2', style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.white)),
          ]));
}
