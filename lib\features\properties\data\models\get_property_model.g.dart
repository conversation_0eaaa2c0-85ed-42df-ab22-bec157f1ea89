// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_property_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetPropertyModel _$GetPropertyModelFromJson(Map<String, dynamic> json) =>
    GetPropertyModel(
      id: json['id'] as String?,
      title: json['title'] as String?,
      saleType: $enumDecodeNullable(_$SaleTypeEnumMap, json['saleType']),
      enquiredFor:
          $enumDecodeNullable(_$EnquiryTypeEnumMap, json['enquiredFor']),
      notes: json['notes'] as String?,
      furnishStatus:
          $enumDecodeNullable(_$FurnishStatusEnumMap, json['furnishStatus']),
      status: $enumDecodeNullable(_$PropertyStatusEnumMap, json['status']),
      rating: json['rating'] as String?,
      shareCount: (json['shareCount'] as num?)?.toInt(),
      possessionDate: json['possessionDate'] == null
          ? null
          : DateTime.parse(json['possessionDate'] as String),
      isGOListingEnabled: json['isGOListingEnabled'] as bool?,
      facing: $enumDecodeNullable(_$FacingEnumMap, json['facing']),
      goPropertyId: json['goPropertyId'] as String?,
      noOfBHK: (json['noOfBHK'] as num?)?.toDouble(),
      bhkType: $enumDecodeNullable(_$BHKTypeEnumMap, json['bhkType']),
      monetaryInfo: json['monetaryInfo'] == null
          ? null
          : PropertyMonetaryInfoModel.fromJson(
              json['monetaryInfo'] as Map<String, dynamic>),
      ownerDetails: json['ownerDetails'] == null
          ? null
          : PropertyOwnerDetailsModel.fromJson(
              json['ownerDetails'] as Map<String, dynamic>),
      dimension: json['dimension'] == null
          ? null
          : PropertyDimensionModel.fromJson(
              json['dimension'] as Map<String, dynamic>),
      tagInfo: json['tagInfo'] == null
          ? null
          : PropertyTagInfoModel.fromJson(
              json['tagInfo'] as Map<String, dynamic>),
      images: (json['images'] as List<dynamic>?)
          ?.map((e) => PropertyImageModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      imageUrls: (json['imageUrls'] as Map<String, dynamic>?)?.map(
        (k, e) =>
            MapEntry(k, (e as List<dynamic>).map((e) => e as String).toList()),
      ),
      attributes: (json['attributes'] as List<dynamic>?)
          ?.map(
              (e) => PropertyAttributeModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      amenities: (json['amenities'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      brochures: (json['brochures'] as List<dynamic>?)
          ?.map(
              (e) => PropertyBrochureModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      whatsAppShareCount: (json['whatsAppShareCount'] as num?)?.toInt(),
      callShareCount: (json['callShareCount'] as num?)?.toInt(),
      emailShareCount: (json['emailShareCount'] as num?)?.toInt(),
      smsShareCount: (json['smsShareCount'] as num?)?.toInt(),
      aboutProperty: json['aboutProperty'] as String?,
      address: json['address'] == null
          ? null
          : AddressModel.fromJson(json['address'] as Map<String, dynamic>),
      links:
          (json['links'] as List<dynamic>?)?.map((e) => e as String).toList(),
      project: json['project'] as String?,
      assignedTo: (json['assignedTo'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      createdBy: json['createdBy'] as String?,
      lastModifiedBy: json['lastModifiedBy'] as String?,
      propertyType: json['propertyType'] == null
          ? null
          : PropertyTypeModel.fromJson(
              json['propertyType'] as Map<String, dynamic>),
      micrositeURL: json['micrositeURL'] as String?,
      serialNo: json['serialNo'] as String?,
      coWorkingOperator: json['coWorkingOperator'] as String?,
      coWorkingOperatorName: json['coWorkingOperatorName'] as String?,
      coWorkingOperatorPhone: json['coWorkingOperatorPhone'] as String?,
      lockInPeriod:
          $enumDecodeNullable(_$LockInPeriodTypeEnumMap, json['lockInPeriod']),
      noticePeriod:
          $enumDecodeNullable(_$NoticePeriodTypeEnumMap, json['noticePeriod']),
      noOfFloorsOccupied: (json['noOfFloorsOccupied'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      tenantContactInfo: json['tenantContactInfo'] == null
          ? null
          : TenantContactInfo.fromJson(
              json['tenantContactInfo'] as Map<String, dynamic>),
      listingStatus: $enumDecodeNullable(
          _$ListingStatusEnumMap, json['listingStatus'],
          unknownValue: ListingStatus.none),
      listingExpireDate: json['listingExpireDate'] == null
          ? null
          : DateTime.parse(json['listingExpireDate'] as String),
      listingSources: (json['listingSources'] as List<dynamic>?)
          ?.map((e) =>
              CustomListingSourceModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      dldPermitNumber: json['dldPermitNumber'] as String?,
      refrenceNo: json['refrenceNo'] as String?,
      dtcmPermit: json['dtcmPermit'] as String?,
      offeringType:
          $enumDecodeNullable(_$OfferingTypeEnumMap, json['offeringType']),
      completionStatus: $enumDecodeNullable(
          _$CompletionStatusEnumMap, json['completionStatus']),
      language: json['language'] as String?,
      titleWithLanguage: json['titleWithLanguage'] as String?,
      aboutPropertyWithLanguage: json['aboutPropertyWithLanguage'] as String?,
      view360Url: (json['view360Url'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      listingLevel:
          $enumDecodeNullable(_$ListingLevelEnumMap, json['listingLevel']),
      listingSourceAddresses: (json['listingSourceAddresses'] as List<dynamic>?)
          ?.map((e) =>
              ViewListingSourceAddressModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      taxationMode:
          $enumDecodeNullable(_$TaxationModeEnumMap, json['taxationMode']),
      isWaterMarkEnabled: json['isWaterMarkEnabled'] as bool?,
      possesionType:
          $enumDecodeNullable(_$PossessionTypeEnumMap, json['possesionType']),
      securityDepositAmount:
          (json['securityDepositAmount'] as num?)?.toDouble(),
      securityDepositUnit: json['securityDepositUnit'] as String?,
      listingOnBehalf: (json['listingOnBehalf'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      shouldVisisbleOnListing: json['shouldVisisbleOnListing'] as bool?,
      propertyOwnerDetails: (json['propertyOwnerDetails'] as List<dynamic>?)
          ?.map((e) =>
              PropertyOwnerDetailsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$GetPropertyModelToJson(GetPropertyModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'saleType': _$SaleTypeEnumMap[instance.saleType],
      'enquiredFor': _$EnquiryTypeEnumMap[instance.enquiredFor],
      'notes': instance.notes,
      'furnishStatus': _$FurnishStatusEnumMap[instance.furnishStatus],
      'status': _$PropertyStatusEnumMap[instance.status],
      'rating': instance.rating,
      'shareCount': instance.shareCount,
      'possessionDate': instance.possessionDate?.toIso8601String(),
      'isGOListingEnabled': instance.isGOListingEnabled,
      'facing': _$FacingEnumMap[instance.facing],
      'goPropertyId': instance.goPropertyId,
      'noOfBHK': instance.noOfBHK,
      'bhkType': _$BHKTypeEnumMap[instance.bhkType],
      'monetaryInfo': instance.monetaryInfo,
      'ownerDetails': instance.ownerDetails,
      'dimension': instance.dimension,
      'tagInfo': instance.tagInfo,
      'images': instance.images,
      'imageUrls': instance.imageUrls,
      'attributes': instance.attributes,
      'amenities': instance.amenities,
      'brochures': instance.brochures,
      'whatsAppShareCount': instance.whatsAppShareCount,
      'callShareCount': instance.callShareCount,
      'emailShareCount': instance.emailShareCount,
      'smsShareCount': instance.smsShareCount,
      'aboutProperty': instance.aboutProperty,
      'address': instance.address,
      'links': instance.links,
      'project': instance.project,
      'assignedTo': instance.assignedTo,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'createdOn': instance.createdOn?.toIso8601String(),
      'createdBy': instance.createdBy,
      'lastModifiedBy': instance.lastModifiedBy,
      'propertyType': instance.propertyType,
      'micrositeURL': instance.micrositeURL,
      'serialNo': instance.serialNo,
      'coWorkingOperator': instance.coWorkingOperator,
      'coWorkingOperatorName': instance.coWorkingOperatorName,
      'coWorkingOperatorPhone': instance.coWorkingOperatorPhone,
      'lockInPeriod': _$LockInPeriodTypeEnumMap[instance.lockInPeriod],
      'noticePeriod': _$NoticePeriodTypeEnumMap[instance.noticePeriod],
      'noOfFloorsOccupied': instance.noOfFloorsOccupied,
      'tenantContactInfo': instance.tenantContactInfo,
      'listingStatus': _$ListingStatusEnumMap[instance.listingStatus],
      'listingExpireDate': instance.listingExpireDate?.toIso8601String(),
      'listingSources': instance.listingSources,
      'listingLevel': _$ListingLevelEnumMap[instance.listingLevel],
      'listingSourceAddresses': instance.listingSourceAddresses,
      'dldPermitNumber': instance.dldPermitNumber,
      'refrenceNo': instance.refrenceNo,
      'dtcmPermit': instance.dtcmPermit,
      'offeringType': _$OfferingTypeEnumMap[instance.offeringType],
      'completionStatus': _$CompletionStatusEnumMap[instance.completionStatus],
      'language': instance.language,
      'titleWithLanguage': instance.titleWithLanguage,
      'aboutPropertyWithLanguage': instance.aboutPropertyWithLanguage,
      'view360Url': instance.view360Url,
      'taxationMode': _$TaxationModeEnumMap[instance.taxationMode],
      'isWaterMarkEnabled': instance.isWaterMarkEnabled,
      'possesionType': _$PossessionTypeEnumMap[instance.possesionType],
      'securityDepositAmount': instance.securityDepositAmount,
      'securityDepositUnit': instance.securityDepositUnit,
      'listingOnBehalf': instance.listingOnBehalf,
      'shouldVisisbleOnListing': instance.shouldVisisbleOnListing,
      'propertyOwnerDetails': instance.propertyOwnerDetails,
    };

const _$SaleTypeEnumMap = {
  SaleType.none: 0,
  SaleType.neu: 1,
  SaleType.resale: 2,
};

const _$EnquiryTypeEnumMap = {
  EnquiryType.none: 0,
  EnquiryType.buy: 1,
  EnquiryType.sale: 2,
  EnquiryType.rent: 3,
};

const _$FurnishStatusEnumMap = {
  FurnishStatus.none: 0,
  FurnishStatus.unfurnished: 1,
  FurnishStatus.semifunrished: 2,
  FurnishStatus.furnished: 3,
};

const _$PropertyStatusEnumMap = {
  PropertyStatus.active: 0,
  PropertyStatus.sold: 1,
};

const _$FacingEnumMap = {
  Facing.unknown: 0,
  Facing.east: 1,
  Facing.west: 2,
  Facing.north: 3,
  Facing.south: 4,
  Facing.northEast: 5,
  Facing.northWest: 6,
  Facing.southEast: 7,
  Facing.southWest: 8,
};

const _$BHKTypeEnumMap = {
  BHKType.none: 0,
  BHKType.simplex: 1,
  BHKType.duplex: 2,
  BHKType.pentHouse: 3,
  BHKType.others: 4,
};

const _$LockInPeriodTypeEnumMap = {
  LockInPeriodType.none: 0,
  LockInPeriodType.oneYear: 1,
  LockInPeriodType.secondYear: 2,
  LockInPeriodType.thirdYear: 3,
  LockInPeriodType.fourthYear: 4,
  LockInPeriodType.fifthYear: 5,
  LockInPeriodType.fivePlusYear: 6,
};

const _$NoticePeriodTypeEnumMap = {
  NoticePeriodType.none: 0,
  NoticePeriodType.thirtyDays: 1,
  NoticePeriodType.fortyFiveDays: 2,
  NoticePeriodType.sixtyDays: 3,
  NoticePeriodType.ninetyDays: 4,
  NoticePeriodType.hundredDays: 5,
};

const _$ListingStatusEnumMap = {
  ListingStatus.none: 0,
  ListingStatus.approved: 1,
  ListingStatus.draft: 2,
  ListingStatus.refused: 3,
  ListingStatus.sold: 4,
  ListingStatus.archived: 5,
};

const _$OfferingTypeEnumMap = {
  OfferingType.none: 0,
  OfferingType.ready: 1,
  OfferingType.offPlan: 2,
  OfferingType.secondary: 3,
};

const _$CompletionStatusEnumMap = {
  CompletionStatus.none: 0,
  CompletionStatus.completed: 1,
  CompletionStatus.offPlan: 2,
  CompletionStatus.completedPrimary: 3,
  CompletionStatus.offPlanPrimary: 4,
};

const _$ListingLevelEnumMap = {
  ListingLevel.none: 0,
  ListingLevel.standard: 1,
  ListingLevel.featured: 2,
  ListingLevel.premium: 3,
};

const _$TaxationModeEnumMap = {
  TaxationMode.gstInclusive: 0,
  TaxationMode.gstExclusive: 1,
  TaxationMode.basicCharge: 2,
};

const _$PossessionTypeEnumMap = {
  PossessionType.none: 0,
  PossessionType.underConstruction: 1,
  PossessionType.sixMonths: 2,
  PossessionType.oneYear: 3,
  PossessionType.twoYear: 4,
  PossessionType.customDate: 5,
};

PropertyWithDegreeMatchedModel _$PropertyWithDegreeMatchedModelFromJson(
        Map<String, dynamic> json) =>
    PropertyWithDegreeMatchedModel(
      totalNoOfFields: (json['totalNoOfFields'] as num?)?.toInt(),
      noOfFieldsMatched: (json['noOfFieldsMatched'] as num?)?.toInt(),
      percentageOfFieldsMatched: json['percentageOfFieldsMatched'] as String?,
      property: json['property'] == null
          ? null
          : GetPropertyModel.fromJson(json['property'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PropertyWithDegreeMatchedModelToJson(
        PropertyWithDegreeMatchedModel instance) =>
    <String, dynamic>{
      'totalNoOfFields': instance.totalNoOfFields,
      'noOfFieldsMatched': instance.noOfFieldsMatched,
      'percentageOfFieldsMatched': instance.percentageOfFieldsMatched,
      'property': instance.property,
    };

CustomListingSourceModel _$CustomListingSourceModelFromJson(
        Map<String, dynamic> json) =>
    CustomListingSourceModel(
      id: json['id'] as String?,
      displayName: json['displayName'] as String?,
      value: (json['value'] as num?)?.toInt(),
      orderRank: (json['orderRank'] as num?)?.toInt(),
      imageURL: json['imageURL'] as String?,
      progressColor: json['progressColor'] as String?,
      backgroundColor: json['backgroundColor'] as String?,
      isDefault: json['isDefault'] as bool?,
    );

Map<String, dynamic> _$CustomListingSourceModelToJson(
        CustomListingSourceModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'displayName': instance.displayName,
      'value': instance.value,
      'orderRank': instance.orderRank,
      'imageURL': instance.imageURL,
      'progressColor': instance.progressColor,
      'backgroundColor': instance.backgroundColor,
      'isDefault': instance.isDefault,
    };
