// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_enquiry_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateLeadEnquiryModel _$CreateLeadEnquiryModelFromJson(
        Map<String, dynamic> json) =>
    CreateLeadEnquiryModel(
      enquiredFor:
          $enumDecodeNullable(_$EnquiryTypeEnumMap, json['enquiredFor']),
      saleType: $enumDecodeNullable(_$SaleTypeEnumMap, json['saleType']),
      leadSource: $enumDecodeNullable(_$LeadSourceEnumMap, json['leadSource']),
      subSource: json['subSource'] as String?,
      lowerBudget: (json['lowerBudget'] as num?)?.toInt(),
      upperBudget: (json['upperBudget'] as num?)?.toInt(),
      noOfBHK: (json['noOfBHK'] as num?)?.toDouble(),
      bhkType: $enumDecodeNullable(_$BHKTypeEnumMap, json['bhkType']),
      area: (json['area'] as num?)?.toDouble(),
      areaUnitId: json['areaUnitId'] as String?,
      areaUnit: json['areaUnit'] as String?,
      isPrimary: json['isPrimary'] as bool?,
      address: json['address'] == null
          ? null
          : AddressModel.fromJson(json['address'] as Map<String, dynamic>),
      propertyTypeId: json['propertyTypeId'] as String?,
      propertyTypeIds: (json['propertyTypeIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      carpetArea: (json['carpetArea'] as num?)?.toDouble(),
      saleableArea: (json['saleableArea'] as num?)?.toDouble(),
      builtUpArea: (json['builtUpArea'] as num?)?.toDouble(),
      carpetAreaUnitId: json['carpetAreaUnitId'] as String?,
      saleableAreaUnitId: json['saleableAreaUnitId'] as String?,
      builtUpAreaUnitId: json['builtUpAreaUnitId'] as String?,
      conversionFactor: (json['conversionFactor'] as num?)?.toDouble(),
      saleableAreaConversionFactor:
          (json['saleableAreaConversionFactor'] as num?)?.toDouble(),
      builtUpAreaConversionFactor:
          (json['builtUpAreaConversionFactor'] as num?)?.toDouble(),
      possessionDate: json['possessionDate'] == null
          ? null
          : DateTime.parse(json['possessionDate'] as String),
      currency: json['currency'] as String?,
      enquiryTypes: (json['enquiryTypes'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$EnquiryTypeEnumMap, e))
          .toList(),
      bhkTypes: (json['bhkTypes'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$BHKTypeEnumMap, e))
          .toList(),
      bhks: (json['bhks'] as List<dynamic>?)
          ?.map((e) => (e as num).toDouble())
          .toList(),
      addresses: (json['addresses'] as List<dynamic>?)
          ?.map((e) => AddressModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      beds: (json['beds'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$BedsEnumMap, e))
          .toList(),
      baths: (json['baths'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      furnished: (json['furnished'] as num?)?.toInt(),
      offerType: (json['offerType'] as num?)?.toInt(),
      floors:
          (json['floors'] as List<dynamic>?)?.map((e) => e as String).toList(),
      propertyArea: (json['propertyArea'] as num?)?.toDouble(),
      netAreaUnitId: json['netAreaUnitId'] as String?,
      netArea: (json['netArea'] as num?)?.toDouble(),
      unitName: json['unitName'] as String?,
      propertyAreaUnitId: json['propertyAreaUnitId'] as String?,
      propertyAreaConversionFactor:
          (json['propertyAreaConversionFactor'] as num?)?.toDouble(),
      netAreaConversionFactor:
          (json['netAreaConversionFactor'] as num?)?.toDouble(),
      clusterName: json['clusterName'] as String?,
      purpose: $enumDecodeNullable(_$PurposeEnumEnumMap, json['purpose']),
      possesionType:
          $enumDecodeNullable(_$PossessionTypeEnumMap, json['possesionType']),
    );

Map<String, dynamic> _$CreateLeadEnquiryModelToJson(
        CreateLeadEnquiryModel instance) =>
    <String, dynamic>{
      if (_$EnquiryTypeEnumMap[instance.enquiredFor] case final value?)
        'enquiredFor': value,
      if (_$SaleTypeEnumMap[instance.saleType] case final value?)
        'saleType': value,
      if (_$LeadSourceEnumMap[instance.leadSource] case final value?)
        'leadSource': value,
      if (instance.subSource case final value?) 'subSource': value,
      if (instance.lowerBudget case final value?) 'lowerBudget': value,
      if (instance.upperBudget case final value?) 'upperBudget': value,
      if (instance.noOfBHK case final value?) 'noOfBHK': value,
      if (_$BHKTypeEnumMap[instance.bhkType] case final value?)
        'bhkType': value,
      if (instance.area case final value?) 'area': value,
      if (instance.areaUnitId case final value?) 'areaUnitId': value,
      if (instance.areaUnit case final value?) 'areaUnit': value,
      if (instance.isPrimary case final value?) 'isPrimary': value,
      if (instance.address?.toJson() case final value?) 'address': value,
      if (instance.propertyTypeId case final value?) 'propertyTypeId': value,
      if (instance.propertyTypeIds case final value?) 'propertyTypeIds': value,
      if (instance.carpetArea case final value?) 'carpetArea': value,
      if (instance.saleableArea case final value?) 'saleableArea': value,
      if (instance.builtUpArea case final value?) 'builtUpArea': value,
      if (instance.propertyArea case final value?) 'propertyArea': value,
      if (instance.netAreaUnitId case final value?) 'netAreaUnitId': value,
      if (instance.netArea case final value?) 'netArea': value,
      if (instance.unitName case final value?) 'unitName': value,
      if (instance.carpetAreaUnitId case final value?)
        'carpetAreaUnitId': value,
      if (instance.saleableAreaUnitId case final value?)
        'saleableAreaUnitId': value,
      if (instance.builtUpAreaUnitId case final value?)
        'builtUpAreaUnitId': value,
      if (instance.propertyAreaUnitId case final value?)
        'propertyAreaUnitId': value,
      if (instance.conversionFactor case final value?)
        'conversionFactor': value,
      if (instance.saleableAreaConversionFactor case final value?)
        'saleableAreaConversionFactor': value,
      if (instance.builtUpAreaConversionFactor case final value?)
        'builtUpAreaConversionFactor': value,
      if (instance.propertyAreaConversionFactor case final value?)
        'propertyAreaConversionFactor': value,
      if (instance.netAreaConversionFactor case final value?)
        'netAreaConversionFactor': value,
      if (instance.possessionDate?.toIso8601String() case final value?)
        'possessionDate': value,
      if (instance.currency case final value?) 'currency': value,
      if (instance.enquiryTypes?.map((e) => _$EnquiryTypeEnumMap[e]!).toList()
          case final value?)
        'enquiryTypes': value,
      if (instance.bhkTypes?.map((e) => _$BHKTypeEnumMap[e]!).toList()
          case final value?)
        'bhkTypes': value,
      if (instance.bhks case final value?) 'bhks': value,
      if (instance.baths case final value?) 'baths': value,
      if (instance.addresses?.map((e) => e.toJson()).toList() case final value?)
        'addresses': value,
      if (instance.beds?.map((e) => _$BedsEnumMap[e]!).toList()
          case final value?)
        'beds': value,
      if (instance.furnished case final value?) 'furnished': value,
      if (instance.offerType case final value?) 'offerType': value,
      if (instance.floors case final value?) 'floors': value,
      if (instance.clusterName case final value?) 'clusterName': value,
      if (_$PurposeEnumEnumMap[instance.purpose] case final value?)
        'purpose': value,
      if (_$PossessionTypeEnumMap[instance.possesionType] case final value?)
        'possesionType': value,
    };

const _$EnquiryTypeEnumMap = {
  EnquiryType.none: 0,
  EnquiryType.buy: 1,
  EnquiryType.sale: 2,
  EnquiryType.rent: 3,
};

const _$SaleTypeEnumMap = {
  SaleType.none: 0,
  SaleType.neu: 1,
  SaleType.resale: 2,
};

const _$LeadSourceEnumMap = {
  LeadSource.any: -1,
  LeadSource.direct: 0,
  LeadSource.iVR: 1,
  LeadSource.facebook: 2,
  LeadSource.linkedIn: 3,
  LeadSource.googleAds: 4,
  LeadSource.magicBricks: 5,
  LeadSource.ninetyNineAcres: 6,
  LeadSource.housing: 7,
  LeadSource.gharOffice: 8,
  LeadSource.referral: 9,
  LeadSource.walkIn: 10,
  LeadSource.website: 11,
  LeadSource.gmail: 12,
  LeadSource.propertyMicrosite: 13,
  LeadSource.portfolioMicrosite: 14,
  LeadSource.phonebook: 15,
  LeadSource.callLogs: 16,
  LeadSource.leadPool: 17,
  LeadSource.squareYards: 18,
  LeadSource.quikrHomes: 19,
  LeadSource.justLead: 20,
  LeadSource.whatsApp: 21,
  LeadSource.youTube: 22,
  LeadSource.qRCode: 23,
  LeadSource.instagram: 24,
  LeadSource.oLX: 25,
  LeadSource.estateDekho: 26,
  LeadSource.googleSheet: 27,
  LeadSource.channelPartner: 28,
  LeadSource.realEstateIndia: 29,
  LeadSource.commonFloor: 30,
  LeadSource.data: 31,
  LeadSource.roofAndFloor: 32,
  LeadSource.microsoftAds: 33,
  LeadSource.propertyWala: 34,
  LeadSource.projectMicrosite: 35,
  LeadSource.myGate: 36,
  LeadSource.flipkart: 37,
  LeadSource.propertyFinder: 38,
  LeadSource.bayut: 39,
  LeadSource.dubizzle: 40,
  LeadSource.webhook: 41,
  LeadSource.tikTok: 42,
  LeadSource.snapchat: 43,
};

const _$BHKTypeEnumMap = {
  BHKType.none: 0,
  BHKType.simplex: 1,
  BHKType.duplex: 2,
  BHKType.pentHouse: 3,
  BHKType.others: 4,
};

const _$BedsEnumMap = {
  Beds.studio: 0,
  Beds.oneBed: 1,
  Beds.twoBed: 2,
  Beds.threeBed: 3,
  Beds.fourBed: 4,
  Beds.fiveBed: 5,
  Beds.sixBed: 6,
  Beds.sevenBed: 7,
  Beds.eightBed: 8,
  Beds.nineBed: 9,
  Beds.tenBed: 10,
};

const _$PurposeEnumEnumMap = {
  PurposeEnum.none: 0,
  PurposeEnum.investment: 1,
  PurposeEnum.selfUse: 2,
};

const _$PossessionTypeEnumMap = {
  PossessionType.none: 0,
  PossessionType.underConstruction: 1,
  PossessionType.sixMonths: 2,
  PossessionType.oneYear: 3,
  PossessionType.twoYear: 4,
  PossessionType.customDate: 5,
};
