part of 'property_filter_bloc.dart';

@immutable
class PropertyFilterState {
  final PageState pageState;
  final String? errorMessage;
  final List<ItemPropertyFilterCategoryModel>? itemPropertyFilterCategoryModels;
  final bool isInitialized;
  final List<SelectableItem<String>>? currencies;
  final SelectableItem<String>? selectedCurrency;
  final SelectableItem<PropertyDateType>? selectedDateType;
  final List<SelectableItem<PropertyDateType>>? dateTypes;
  final DateTime? fromDate;
  final DateTime? toDate;
  final RangeInput? propertySize;
  final RangeInput? saleableArea;
  final RangeInput? builtUpArea;
  final RangeInput? carpetArea;
  final double? managePropertyCarpetArea;
  final double? managePropertySalableArea;
  final double? managePropertyBuiltUpArea;
  final double? managePropertyArea;
  final List<SelectableItem<MasterAreaUnitsModel>> areaUnits;
  final SelectableItem<MasterAreaUnitsModel>? selectedPropertyAreaUnit;
  final SelectableItem<MasterAreaUnitsModel>? selectedCarpetAreaUnit;
  final SelectableItem<MasterAreaUnitsModel>? selectedSaleableAreaUnit;
  final SelectableItem<MasterAreaUnitsModel>? selectedBuiltUpAreaUnit;
  final RangeInput? minBudget;
  final RangeInput? maxBudget;

  const PropertyFilterState({
    this.pageState = PageState.initial,
    this.errorMessage,
    this.itemPropertyFilterCategoryModels,
    this.isInitialized = false,
    this.currencies,
    this.selectedCurrency,
    this.selectedDateType,
    this.dateTypes,
    this.fromDate,
    this.toDate,
    this.propertySize,
    this.saleableArea,
    this.builtUpArea,
    this.carpetArea,
    this.areaUnits = const [],
    this.selectedPropertyAreaUnit,
    this.selectedCarpetAreaUnit,
    this.selectedSaleableAreaUnit,
    this.selectedBuiltUpAreaUnit,
    this.managePropertyBuiltUpArea,
    this.managePropertyCarpetArea,
    this.managePropertySalableArea,
    this.managePropertyArea,
    this.minBudget,
    this.maxBudget,
  });

  PropertyFilterState copyWith({
    PageState? pageState,
    String? errorMessage,
    List<ItemPropertyFilterCategoryModel>? itemPropertyFilterCategoryModels,
    bool? isInitialized,
    List<SelectableItem<String>>? currencies,
    SelectableItem<String>? selectedCurrency,
    SelectableItem<PropertyDateType>? selectedDateType,
    List<SelectableItem<PropertyDateType>>? dateTypes,
    DateTime? fromDate,
    DateTime? toDate,
    RangeInput? propertySize,
    RangeInput? saleableArea,
    RangeInput? builtUpArea,
    RangeInput? carpetArea,
    List<SelectableItem<MasterAreaUnitsModel>>? areaUnits,
    SelectableItem<MasterAreaUnitsModel>? selectedPropertyAreaUnit,
    SelectableItem<MasterAreaUnitsModel>? selectedCarpetAreaUnit,
    SelectableItem<MasterAreaUnitsModel>? selectedSaleableAreaUnit,
    SelectableItem<MasterAreaUnitsModel>? selectedBuiltUpAreaUnit,
    double? managePropertyCarpetArea,
    double? managePropertySalableArea,
    double? managePropertyBuiltUpArea,
    double? managePropertyArea,
    bool updateSelectMinBudget = true,
    bool updateSelectMaxBudget = true,
    RangeInput? minBudget,
    RangeInput? maxBudget,
    bool updatePropertyAreaUnit = false,
    bool updateCarpetAreaUnit = false,
    bool updateSaleableAreaUnit = false,
    bool updateBuiltUpAreaUnit = false,
    bool updateToDate = true,
    bool updateFromDate = true,
  }) {
    return PropertyFilterState(
      pageState: pageState ?? PageState.initial,
      errorMessage: errorMessage,
      itemPropertyFilterCategoryModels: itemPropertyFilterCategoryModels ?? this.itemPropertyFilterCategoryModels,
      isInitialized: isInitialized ?? this.isInitialized,
      currencies: currencies ?? this.currencies,
      selectedCurrency: selectedCurrency ?? this.selectedCurrency,
      selectedDateType: selectedDateType ?? this.selectedDateType,
      dateTypes: dateTypes ?? this.dateTypes,
      fromDate: updateFromDate ? fromDate ?? this.fromDate : null,
      toDate: updateToDate ? toDate ?? this.toDate : null,
      propertySize: propertySize ?? this.propertySize,
      saleableArea: saleableArea ?? this.saleableArea,
      builtUpArea: builtUpArea ?? this.builtUpArea,
      carpetArea: carpetArea ?? this.carpetArea,
      areaUnits: areaUnits ?? this.areaUnits,
      selectedPropertyAreaUnit: updatePropertyAreaUnit ? null : selectedPropertyAreaUnit ?? this.selectedPropertyAreaUnit,
      selectedCarpetAreaUnit: updateCarpetAreaUnit ? null : selectedCarpetAreaUnit ?? this.selectedCarpetAreaUnit,
      selectedSaleableAreaUnit: updateSaleableAreaUnit ? null : selectedSaleableAreaUnit ?? this.selectedSaleableAreaUnit,
      selectedBuiltUpAreaUnit: updateBuiltUpAreaUnit ? null : selectedBuiltUpAreaUnit ?? this.selectedBuiltUpAreaUnit,
      managePropertyBuiltUpArea: managePropertyBuiltUpArea ?? this.managePropertyBuiltUpArea,
      managePropertyCarpetArea: managePropertyCarpetArea ?? this.managePropertyCarpetArea,
      managePropertySalableArea: managePropertySalableArea ?? this.managePropertySalableArea,
      managePropertyArea: managePropertyArea ?? this.managePropertyArea,
      minBudget: updateSelectMinBudget ? minBudget ?? this.minBudget : null,
      maxBudget: updateSelectMaxBudget ? maxBudget ?? this.maxBudget : null,
    );
  }

  PropertyFilterState resetState({
    List<ItemPropertyFilterCategoryModel>? itemPropertyFilterCategoryModels,
    bool? isInitialized,
    List<SelectableItem<String>>? currencies,
    SelectableItem<String>? selectedCurrency,
    SelectableItem<PropertyDateType>? selectedDateType,
    List<SelectableItem<PropertyDateType>>? dateTypes,
    DateTime? fromDate,
    DateTime? toDate,
    RangeInput? propertySize,
    RangeInput? saleableArea,
    RangeInput? builtUpArea,
    RangeInput? carpetArea,
    List<SelectableItem<MasterAreaUnitsModel>>? areaUnits,
    SelectableItem<MasterAreaUnitsModel>? selectedPropertyAreaUnit,
    SelectableItem<MasterAreaUnitsModel>? selectedCarpetAreaUnit,
    SelectableItem<MasterAreaUnitsModel>? selectedSaleableAreaUnit,
    SelectableItem<MasterAreaUnitsModel>? selectedBuiltUpAreaUnit,
    double? managePropertyCarpetArea,
    double? managePropertySalableArea,
    double? managePropertyBuiltUpArea,
    double? managePropertyArea,
    RangeInput? minBudget,
    RangeInput? maxBudget,
  }) {
    return PropertyFilterState(
      selectedDateType: selectedDateType,
      pageState: PageState.initial,
      toDate: toDate,
      fromDate: fromDate,
      selectedCurrency: selectedCurrency,
      dateTypes: dateTypes,
      currencies: currencies,
      isInitialized: itemPropertyFilterCategoryModels == null ? false : true,
      itemPropertyFilterCategoryModels: itemPropertyFilterCategoryModels,
      errorMessage: null,
      propertySize: propertySize,
      saleableArea: saleableArea,
      builtUpArea: builtUpArea,
      carpetArea: carpetArea,
      selectedPropertyAreaUnit: selectedPropertyAreaUnit,
      selectedCarpetAreaUnit: selectedCarpetAreaUnit,
      selectedSaleableAreaUnit: selectedSaleableAreaUnit,
      selectedBuiltUpAreaUnit: selectedBuiltUpAreaUnit,
      managePropertyBuiltUpArea: managePropertyBuiltUpArea,
      managePropertyCarpetArea: managePropertyCarpetArea,
      managePropertySalableArea: managePropertySalableArea,
      managePropertyArea: managePropertyArea,
      minBudget: minBudget,
      maxBudget: maxBudget,
    );
  }
}

enum AreaType {
  sealable("sealable area"),
  carpet("carpet area"),
  builtUp("builtUp area"),
  propertyArea("property area");

  final String description;

  const AreaType(this.description);
}
