import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/data/user/models/user_documents.dart';
import 'package:leadrat/core_main/common/data/user/models/user_time_zone_info_model.dart';
import 'package:leadrat/core_main/common/models/base_user_model.dart';
import 'package:leadrat/core_main/common/models/dto_with_name_model.dart';
import 'package:leadrat/core_main/common/models/role_permission_model.dart';
import 'package:leadrat/core_main/enums/common/call_through_type_enum.dart';
import 'package:leadrat/core_main/enums/common/whatsapp_through_type_enum.dart';

import '../../../../enums/user_profile/blood_group_type_enum.dart';
import '../../../../enums/user_profile/gender_enum.dart';
import '../../../constants/hive_model_constants.dart';

part 'user_details_model.g.dart';

@HiveType(typeId: HiveModelConstants.getUserDetailsTypeId)
@JsonSerializable(explicitToJson: true, includeIfNull: true)
class UserDetailsModel {
  @HiveField(0)
  final String? userId;
  @HiveField(1)
  final String? userName;
  @HiveField(2)
  final String? firstName;
  @HiveField(3)
  final String? lastName;
  @HiveField(4)
  final bool? isActive;
  @HiveField(5)
  final bool? emailConfirmed;
  @HiveField(6)
  final String? imageUrl;
  @HiveField(7)
  final String? altPhoneNumber;
  @HiveField(8)
  final String? altEmail;
  @HiveField(9)
  final String? address;
  @HiveField(10)
  final String? email;
  @HiveField(11)
  final BloodGroupType? bloodGroup;
  @HiveField(12)
  final GenderEnum? gender;
  @HiveField(13)
  final String? permanentAddress;
  @HiveField(14)
  final String? phoneNumber;
  @HiveField(15)
  final String? empNo;
  @HiveField(16)
  final String? officeName;
  @HiveField(17)
  final String? officeAddress;
  @HiveField(18)
  final BaseUserModel? reportsTo;
  @HiveField(19)
  final DTOWithNameModel? department;
  @HiveField(20)
  final DTOWithNameModel? designation;
  @HiveField(21)
  final String? description;
  @HiveField(22)
  final double? profileCompletion;
  @HiveField(23)
  final Map<String, List<UserDocumentModel>?>? documents;
  @HiveField(24)
  final int? leadCount;
  @HiveField(25)
  final List<RolePermissionModel?>? rolePermission;
  @HiveField(36)
  final UserTimeZoneInfoModel? timeZoneInfo;
  @HiveField(37)
  final bool? shouldShowTimeZone;
  @HiveField(38)
  final CallThroughTypeEnum? callThrough;
  @HiveField(39)
  final WhatsappThroughTypeEnum? whatsappThrough;
  @HiveField(40)
  final String? organizationName;


  UserDetailsModel({
    this.userId,
    this.userName,
    this.firstName,
    this.lastName,
    this.isActive,
    this.emailConfirmed,
    this.imageUrl,
    this.altPhoneNumber,
    this.altEmail,
    this.address,
    this.email,
    this.bloodGroup,
    this.gender,
    this.permanentAddress,
    this.phoneNumber,
    this.empNo,
    this.officeName,
    this.officeAddress,
    this.reportsTo,
    this.department,
    this.designation,
    this.description,
    this.profileCompletion,
    this.documents,
    this.leadCount,
    this.rolePermission,
    this.timeZoneInfo,
    this.shouldShowTimeZone,
    this.callThrough,
    this.whatsappThrough,
    this.organizationName,
  });

  String get fullName => "$firstName $lastName";

  factory UserDetailsModel.fromJson(Map<String, dynamic> json) => _$UserDetailsModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserDetailsModelToJson(this);

  BaseUserModel toBaseUser() {
    return BaseUserModel(
      id: userId,
      name: fullName,
      contactNo: phoneNumber,
      firstName: firstName,
      lastName: lastName,
      userName: userName,
      imageUrl: imageUrl,
      isActive: isActive,
    );
  }
}