import 'package:intl/intl.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/data_source/local/custom_amenities_and_attributes_local_data_source.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_amenity_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/data_source/local/global_settings_local_data_source.dart';
import 'package:leadrat/core_main/common/data/global_settings/repository/global_setting_repository.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/repository/masterdata_repository.dart';
import 'package:leadrat/core_main/common/entites/address_entity.dart';
import 'package:leadrat/core_main/common/entites/property_type_entity.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/property_enums/furnish_status.dart';
import 'package:leadrat/core_main/enums/property_enums/lockin_period_type.dart';
import 'package:leadrat/core_main/enums/property_enums/notice_period_type.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/mapper/property_mapper.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/features/properties/domain/entities/get_property_entity.dart';
import 'package:leadrat/features/properties/domain/entities/property_monetaryinfo_entity.dart';

import 'item_property_model.dart';

class ItemPropertyInfoModel {
  static List<MasterAreaUnitsModel?>? areaUnits;
  GetPropertyEntity? propertyByIdEntity;
  List<String?> images = [];
  String title = '';
  String subTitle = '';
  String propertyStatus = '';
  String address = '';
  bool isNegotiable = false;
  String expectedPrice = '';
  String area = '';
  List<ItemSimpleModel> features = List.filled(12, ItemSimpleModel(title: ''));
  String postedDate = '';
  String aboutProperty = "";
  String notes = "";
  bool isOwnerDetailsNotEmpty = false;
  Map<String, String> propertyOwnerDetails = {};
  Map<String, List<CustomAmenityModel?>> propertyAmenities = {};
  bool isAmenityFound = false;
  String? project = '';
  List<List<String>> areasList = [];
  List<List<String>> additionalInfoList = [];
  final bool isPropertyListingEnabled;

  ItemPropertyInfoModel({this.propertyByIdEntity, this.isPropertyListingEnabled = false}) {
    if (propertyByIdEntity != null) {
      title = propertyByIdEntity?.title ?? "";
      subTitle = getSubTitle(noOfBhks: propertyByIdEntity?.noOfBHK, propertyChildModel: propertyByIdEntity?.propertyType, isEnabled: isPropertyListingEnabled);
      propertyStatus = getPropertyStatus(propertyType: propertyByIdEntity?.propertyType, enquiredType: propertyByIdEntity?.enquiredFor);
      address = getAddress(addressEntity: propertyByIdEntity?.address);
      getImages();
      postedDate = propertyByIdEntity!.createdOn != null ? formatDateString(propertyByIdEntity!.createdOn.toString()) : '--';
      expectedPrice = getExpectedPrice(monetoryInfo: propertyByIdEntity?.monetaryInfo);
      isNegotiable = propertyByIdEntity!.monetaryInfo != null && propertyByIdEntity!.monetaryInfo!.isNegotiable != null ? propertyByIdEntity!.monetaryInfo!.isNegotiable! : false;
      getFeaturesList();
      aboutProperty = propertyByIdEntity!.aboutProperty != null ? propertyByIdEntity!.aboutProperty! : '--';
      project = propertyByIdEntity!.project != null ? propertyByIdEntity!.project! : '--';
      notes = propertyByIdEntity!.notes != null ? propertyByIdEntity!.notes! : '--';
      if (aboutProperty.isEmpty) aboutProperty = '--';
      if (notes.isEmpty) notes = '--';
      addPropertyOwnerDetails();
      addPropertyAmenities();
      getArea();
      getAdditionalInfo();
    }
  }

  getImages() {
    propertyByIdEntity?.images?.sort((a, b) {
      final rankA = a.orderRank ?? 0;
      final rankB = b.orderRank ?? 0;
      return rankA.compareTo(rankB);
    });
    images = propertyByIdEntity!.images != null && propertyByIdEntity!.images!.isNotEmpty ? propertyByIdEntity?.images?.where((e) => e.imageFilePath?.substring(e.imageFilePath!.length - 4) != ".mp4").map((e) => e.imageFilePath).toList() ?? [] : [];
  }

  getFeaturesList() {
    if (propertyByIdEntity?.dimension != null && propertyByIdEntity?.dimension?.area != null) {
      features[0] = ItemSimpleModel(title: propertyByIdEntity!.dimension!.area!.doubleToWord(), description: propertyByIdEntity?.dimension?.areaUnit, imageResource: ImageResources.imagePropertyArea);
    } else {
      features[0] = ItemSimpleModel(title: '--', description: '--', imageResource: ImageResources.imagePropertyArea);
    }
    addFeature(index: 1, attributeName: Attributes.floorNumber.name, description: '', imageUrl: ImageResources.imagePropertyFloor);

    if (propertyByIdEntity?.furnishStatus != null && propertyByIdEntity?.furnishStatus != FurnishStatus.none) {
      features[2] = ItemSimpleModel(title: propertyByIdEntity?.furnishStatus?.description ?? "", imageResource: ImageResources.imagePropertyFurnishing);
    } else {
      features[2] = ItemSimpleModel(title: "--", imageResource: ImageResources.imagePropertyFurnishing);
    }

    addFeature(index: 3, attributeName: Attributes.numberOfBedrooms.name, description: ' Bedrooms', imageUrl: ImageResources.imagePropertyBedroom);
    if (propertyByIdEntity?.possessionDate != null) {
      // features[4] = ItemSimpleModel(title: formatDateString(propertyByIdEntity?.possessionDate.toUserTimeZone()?.toString().substring(0, 10)), imageResource: ImageResources.imagePropertyPossesionDate);
      features[4] = ItemSimpleModel(
        title: ((propertyByIdEntity?.possessionDate?.toUserTimeZone()?.isBefore(DateTime.now()) ?? false) ? 'Ready to Move - ' : formatDateString(propertyByIdEntity?.possessionDate.toUserTimeZone()?.toString().substring(0, 10))),
        imageResource: ImageResources.imagePropertyPossesionDate,
      );
    } else {
      features[4] = ItemSimpleModel(title: "--", imageResource: ImageResources.imagePropertyPossesionDate);
    }

    addFeature(index: 5, attributeName: Attributes.numberOfKitchens.name, description: ' Kitchen', imageUrl: ImageResources.imagePropertyKitchen);
    if (propertyByIdEntity?.facing != null) {
      features[6] = ItemSimpleModel(title: propertyByIdEntity?.facing?.description ?? "--", imageResource: ImageResources.imagePropertyFacing);
    } else {
      features[6] = ItemSimpleModel(title: "--", imageResource: ImageResources.imagePropertyFacing);
    }
    addFeature(index: 7, attributeName: Attributes.numberOfBathrooms.name, description: ' Bathroom(s)', imageUrl: ImageResources.imagePropertyBathroom);
    addFeature(index: 8, attributeName: Attributes.numberOfDrawingOrLivingRooms.name, description: ' Living Room', imageUrl: ImageResources.imagePropertyLivingRoom);
    addFeature(index: 9, attributeName: Attributes.numberOfBalconies.name, description: ' Balcony', imageUrl: ImageResources.imagePropertyBalcony);
    addFeature(index: 10, attributeName: Attributes.numberOfUtilities.name, description: ' Utility', imageUrl: ImageResources.imagePropertyUtility);
    features[11] = ItemSimpleModel(title: propertyByIdEntity?.serialNo ?? '--', imageResource: ImageResources.imagePropertySerialNumber);
  }

  addFeature({required int index, required String attributeName, required String description, required String imageUrl}) {
    if (propertyByIdEntity?.attributes != null) {
      String? attributeValue = getAttributeValueByName(attributeName: attributeName);
      String totalNoFloors = '';
      if (index == 1) {
        totalNoFloors = getAttributeValueByName(attributeName: Attributes.numberOfFloors.name) ?? '';
      }
      if (attributeValue.isNotNullOrEmpty() && attributeValue != '0') {
        features[index] = ItemSimpleModel(title: attributeValue ?? "", description: index == 1 ? ' of $totalNoFloors' : description, imageResource: imageUrl);
      } else {
        features[index] = ItemSimpleModel(title: "--", imageResource: imageUrl);
      }
    } else {
      features[index] = ItemSimpleModel(title: "--", imageResource: imageUrl);
    }
  }

  String formatDateString(String? dateString) {
    if (dateString == null) return '--';
    DateTime dateTime = DateTime.parse(dateString);
    DateFormat dateFormat = DateFormat('dd/MM/yyyy');
    return dateFormat.format(dateTime);
  }

  String getPropertyStatus({PropertyTypeEntity? propertyType, EnquiryType? enquiredType}) {
    String propertyStatus = '';
    if (propertyType != null && enquiredType != null) {
      if (propertyType.childType != null && propertyType.childType?.displayName != null && enquiredType != EnquiryType.none) {
        propertyStatus = '${propertyType.childType?.displayName}';
      } else {
        propertyStatus = '${propertyType.displayName}';
      }
      propertyStatus = '$propertyStatus for ${enquiredType.name}';
    }
    return propertyStatus;
  }

  String getExpectedPrice({PropertyMonetaryInfoEntity? monetoryInfo}) {
    String expectedPrice = '';
    if (propertyByIdEntity?.monetaryInfo != null && propertyByIdEntity?.monetaryInfo?.expectedPrice != null && propertyByIdEntity?.monetaryInfo?.expectedPrice != 0) {
      final globalSettings = getIt<GlobalSettingsLocalDataSource>().getGlobalSettings();
      expectedPrice = propertyByIdEntity?.monetaryInfo?.expectedPrice?.toDouble().budgetToWord(propertyByIdEntity?.monetaryInfo?.currency ?? (isPropertyListingEnabled ? globalSettings?.countries?.firstOrNull?.defaultCurrency ?? "AED" : (globalSettings?.countries?.firstOrNull?.defaultCurrency ?? 'INR'))) ?? '';
      if (!isPropertyListingEnabled) expectedPrice += propertyByIdEntity?.taxationMode != null ? ' (${propertyByIdEntity?.taxationMode?.description})' : '';
    }
    return expectedPrice;
  }

  String? getAttributeValueByName({required String? attributeName}) {
    String? value;
    if (attributeName != null) {
      String attributeId = getIt<PropertyEntityMapper>().getAttributeIdByName(attributeName)?.id ?? "";
      propertyByIdEntity?.attributes?.forEach((attribute) {
        if (attributeName == attribute?.attributeName) value = attribute?.defaultValue;
      });
    }
    return value;
  }

  void addPropertyOwnerDetails() {
    if (propertyByIdEntity?.ownerDetails != null) {
      propertyOwnerDetails["name"] = propertyByIdEntity?.ownerDetails?.name ?? "";
      propertyOwnerDetails["phone"] = propertyByIdEntity?.ownerDetails?.phone ?? "";
      propertyOwnerDetails["email"] = propertyByIdEntity?.ownerDetails?.email ?? "";
      isOwnerDetailsNotEmpty = propertyOwnerDetails["name"]!.isNotEmpty || propertyOwnerDetails["email"]!.isNotEmpty || propertyOwnerDetails["phone"]!.isNotEmpty;
    }
  }

  void addPropertyAmenities() {
    var categories = getIt<CustomAmenitiesAndAttributesLocalDataSource>().getCustomAmenityCategories();
    categories?.forEach(
      (element) {
        if (element != null) propertyAmenities[element] = [];
      },
    );
    if (propertyByIdEntity?.amenities != null) {
      propertyByIdEntity?.amenities?.forEach(
        (amenity) {
          if (amenity?.isActive ?? false) {
            for (String category in propertyAmenities.keys) {
              if (amenity?.category == category) {
                propertyAmenities[category]?.add(amenity);
                if (!isAmenityFound) isAmenityFound = true;
                break;
              }
            }
          }
        },
      );
    }
    if (propertyAmenities.values.isNotEmpty) {
      propertyAmenities.forEach((key, value) {
        value.sort((a, b) => (a?.amenityDisplayName?.toLowerCase() ?? '').compareTo(b?.amenityDisplayName?.toLowerCase() ?? ''));
      });
    }
  }

  Future<void> getArea() async {
    if (propertyByIdEntity?.dimension != null) {
      var dimension = propertyByIdEntity?.dimension!;
      String projectSize = await getFormattedAreaSize(dimension?.area, dimension?.areaUnitId);
      if (projectSize.isNotEmpty) {
        final globalSettings = await getIt<GlobalSettingRepository>().getGlobalSettings();
        areasList.add([(globalSettings?.shouldEnablePropertyListing ?? false) ? 'property area' : 'Property size', projectSize]);
      }
      String carpetArea = await getFormattedAreaSize(dimension?.carpetArea, dimension?.carpetAreaId);
      if (carpetArea.isNotEmpty) {
        areasList.add(['Carpet area', carpetArea]);
      }
      String builtUpArea = await getFormattedAreaSize(dimension?.buildUpArea, dimension?.buildUpAreaId);
      if (builtUpArea.isNotEmpty) {
        areasList.add(['Built-up area', builtUpArea]);
      }
      String sealableArea = await getFormattedAreaSize(dimension?.saleableArea, dimension?.saleableAreaId);
      if (sealableArea.isNotEmpty) {
        areasList.add(['Sealable area', sealableArea]);
      }
      String netArea = await getFormattedAreaSize(dimension?.netArea, dimension?.netAreaUnitId);
      if (netArea.isNotEmpty) {
        areasList.add(['Net area', netArea]);
      }
    }
  }

  Future<String> getFormattedAreaSize(double? area, String? unit) async {
    String formattedAreaSize = '';
    if (area != null) {
      formattedAreaSize = area.doubleToWord();
      if (unit != null) {
        String units = await getAreaUnits(unit) ?? '';
        formattedAreaSize = '$formattedAreaSize $units';
      }
    }
    return formattedAreaSize;
  }

  Future<String?> getAreaUnits(String? unitId) async {
    if (areaUnits == null) {
      var masterAreaUnits = await getIt<MasterDataRepository>().getAreaUnits();
      if (masterAreaUnits?.isNotEmpty ?? false) {
        areaUnits = masterAreaUnits;
      }
    }
    if (areaUnits != null && unitId != null) {
      String? area = areaUnits!.where((areaUnit) => areaUnit?.id == unitId).map((matchedArea) => matchedArea?.unit).firstOrNull;
      return area;
    }
    return null;
  }

  getAdditionalInfo() async {
    String lockInPeriod = LockInPeriodType.values.where((e) => e == propertyByIdEntity?.lockInPeriod && propertyByIdEntity?.lockInPeriod != LockInPeriodType.none).firstOrNull?.description ?? '';
    if (lockInPeriod.isNotEmpty) {
      additionalInfoList.add(['Lock In Period', lockInPeriod]);
    }
    String noticeInPeriod = NoticePeriodType.values.where((e) => e == propertyByIdEntity?.noticePeriod && propertyByIdEntity?.noticePeriod != NoticePeriodType.none).firstOrNull?.description ?? '';
    if (noticeInPeriod.isNotEmpty) {
      additionalInfoList.add(['Notice In Period', noticeInPeriod]);
    }
    String commonAReaChargesUnits = await getAreaUnits(propertyByIdEntity?.dimension?.commonAreaChargesId) ?? '';
    if (propertyByIdEntity?.dimension?.commonAreaCharges != null) {
      String commonAreaCharges = '${propertyByIdEntity?.dimension?.commonAreaCharges ?? ''} ${commonAReaChargesUnits.isNotEmpty ? 'per $commonAReaChargesUnits' : ''}';
      if (commonAreaCharges.isNotEmpty) {
        additionalInfoList.add(['Common Area Charges', commonAreaCharges]);
      }
    }
    if (propertyByIdEntity?.tenantContactInfoEntity != null) {
      var tenantInfo = propertyByIdEntity?.tenantContactInfoEntity!;
      if (tenantInfo!.name.isNotNullOrEmpty()) {
        String tenantPocName = tenantInfo.name!;
        if (tenantPocName.isNotEmpty) {
          additionalInfoList.add(['Tenant Poc Name', tenantPocName]);
        }
      }
      if (tenantInfo.phone.isNotNullOrEmpty()) {
        String tenantPocPhone = tenantInfo.phone!;
        if (tenantPocPhone.isNotEmpty) {
          additionalInfoList.add(['Tenant Poc Phone', tenantPocPhone]);
        }
      }
    }
    String noOfFloorsOccupied = (propertyByIdEntity!.noOfFloorsOccupied != null && propertyByIdEntity!.noOfFloorsOccupied!.isNotEmpty) ? propertyByIdEntity!.noOfFloorsOccupied!.map((element) => element).toList().toString() : '';
    if (noOfFloorsOccupied.isNotEmpty) {
      if (noOfFloorsOccupied.length > 2) {
        noOfFloorsOccupied = noOfFloorsOccupied.substring(1, noOfFloorsOccupied.length - 1);
      }
      additionalInfoList.add(['No. Of floors occupied', noOfFloorsOccupied]);
    }
  }
}

String getAddress({AddressEntity? addressEntity}) {
  String address = '';
  if (addressEntity != null) {
    if (addressEntity.subLocality != null && addressEntity.subLocality!.trim().isNotEmpty) {
      address = '${addressEntity.subLocality} ,';
    }
    if (addressEntity.locality != null && addressEntity.locality!.trim().isNotEmpty) {
      address = '$address ${addressEntity.locality},';
    }
    if (addressEntity.state != null && addressEntity.state!.trim().isNotEmpty) {
      address = '$address ${addressEntity.state} ';
    }
  }
  return address;
}

enum Attributes { floorNumber, numberOfFloors, numberOfBalconies, numberOfBathrooms, numberOfBedrooms, numberOfUtilities, numberOfKitchens, numberOfDrawingOrLivingRooms }
