import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/common/no_of_baths.dart';
import 'package:leadrat/core_main/enums/common/no_of_beds.dart';
import 'package:leadrat/core_main/enums/common/purpose_enum.dart';
import 'package:leadrat/core_main/enums/leads/profession.dart';
import 'package:leadrat/core_main/enums/property_enums/furnish_status.dart';
import 'package:leadrat/core_main/enums/property_enums/offering_type.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:leadrat/core_main/extensions/integer_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/features/data_management/domain/entities/prospect_entity.dart';
import 'package:leadrat/features/lead/presentation/bloc/lead_info_bloc/lead_info_bloc.dart';

class ItemProspectEnquiryModel {
  final String sectionTitle;
  final String sectionIcon;
  final List<ItemSimpleModel> columnItems;
  final List<ItemSimpleModel> gridItems;

  ItemProspectEnquiryModel({
    required this.sectionTitle,
    required this.sectionIcon,
    this.columnItems = const [],
    this.gridItems = const [],
  });

  static List<ItemProspectEnquiryModel> getProspectEnquires({
    required ProspectEntity prospect,
    GlobalSettingModel? globalSettingModel,
    String? bhkType,
    String? noOfBHK,
    List<String>? customerLocations,
    List<String>? locations,
  }) {
    List<ItemProspectEnquiryModel> leadEnquires = [];
    final defaultCurrency = getIt<LeadInfoBloc>().getDefaultCurrency();
    //details
    leadEnquires.add(
      ItemProspectEnquiryModel(
        sectionTitle: "details",
        sectionIcon: ImageResources.iconBuilding,
        columnItems: [
          ItemSimpleModel(title: "property${(prospect.properties?.length ?? 0) > 1 ? '(s)' : ''}", items: prospect.properties?.map((e) => ItemSimpleModel(title: e?.title ?? '')).toList()),
          ItemSimpleModel(title: 'project${(prospect.projects?.length ?? 0) > 1 ? '(s)' : ''}', items: prospect.projects?.map((e) => ItemSimpleModel(title: e?.name ?? '')).toList()),
          ItemSimpleModel(title: 'agencies', items: prospect.agencies?.map((e) => ItemSimpleModel(title: e?.name ?? '')).toList()),
          ItemSimpleModel(title: 'campaigns', items: prospect.campaigns?.map((e) => ItemSimpleModel(title: e.name ?? '')).toList()),
        ],
        gridItems: [
          ItemSimpleModel(title: "serial number", description: prospect.serialNumber),
          ItemSimpleModel(title: "type", description: prospect.enquiry?.propertyType?.displayName),
          ItemSimpleModel(title: "sub-type", description: prospect.enquiry?.propertyTypes?.map((e) => e.childType?.displayName).join(",") ?? "--"),
          if (!(globalSettingModel?.isCustomLeadFormEnabled ?? false)) ItemSimpleModel(title: !(globalSettingModel?.isCustomLeadFormEnabled ?? false) ? "bhk" : "br", description: noOfBHK),
          if (!(globalSettingModel?.isCustomLeadFormEnabled ?? false)) ItemSimpleModel(title: "bhk type", description: bhkType, isEnabled: (!(globalSettingModel?.isCustomLeadFormEnabled ?? true))),
          ItemSimpleModel(title: "beds", description: prospect.enquiry?.beds?.map((e) => getEnumFromBeds(Beds.values, e.value)).map((element) => element?.description).nonNulls.join(", ") ?? "--", isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          ItemSimpleModel(title: "baths", description: prospect.enquiry?.baths?.map((e) => getEnumFromNoOfBr(NoOfBaths.values, e)).map((element) => element?.description).nonNulls.join(", ") ?? "--", isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          ItemSimpleModel(title: "furnished status", description: getFurnishStatusDescription(prospect.enquiry?.furnished ?? 0), isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          ItemSimpleModel(title: "min budget", description: prospect.enquiry?.lowerBudget?.convertCurrencyFormat(currency: prospect.enquiry?.currency, isInternationalFormat: globalSettingModel?.isCustomLeadFormEnabled ?? false)),
          ItemSimpleModel(title: "max budget", description: prospect.enquiry?.upperBudget?.convertCurrencyFormat(currency: prospect.enquiry?.currency ?? defaultCurrency, isInternationalFormat: globalSettingModel?.isCustomLeadFormEnabled ?? false)),
          ItemSimpleModel(title: "company name", description: prospect.companyName),
          ItemSimpleModel(title: "designation", description: prospect.designation),
          ItemSimpleModel(title: "profession", description: prospect.profession == null || prospect.profession == Profession.none ? "" : prospect.profession?.description),
          ItemSimpleModel(
            title: "possession date",
            description: prospect.possesionDate?.convertDateFormatToPossessionDateFormat() ?? "",
          ),
          ItemSimpleModel(title: "carpet area", description: prospect.enquiry?.carpetArea != null ? '${prospect.enquiry?.carpetArea?.toString()} ${prospect.enquiry?.carpetAreaUnitId?.unit}' : null),
          ItemSimpleModel(title: "built up area", description: (prospect.enquiry?.builtUpArea != null && prospect.enquiry!.builtUpArea! > 0) ? '${prospect.enquiry?.builtUpArea?.toString()} ${prospect.enquiry?.builtUpAreaUnitId?.unit}' : null),
          ItemSimpleModel(title: "saleable area", description: (prospect.enquiry?.saleableArea != null && prospect.enquiry!.saleableArea! > 0) ? '${prospect.enquiry?.saleableArea?.toString()} ${prospect.enquiry?.saleableAreaUnitId?.unit}' : null),
          ItemSimpleModel(title: "property area", description: (prospect.enquiry?.propertyArea != null && prospect.enquiry!.propertyArea! > 0) ? '${prospect.enquiry?.propertyArea == 0 ? '--' : prospect.enquiry?.propertyArea?.toString()} ${prospect.enquiry?.propertyAreaUnitId?.unit ?? ''}' : '--', isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          ItemSimpleModel(title: "net area", description: (prospect.enquiry?.netArea != null && prospect.enquiry!.netArea! > 0) ? '${prospect.enquiry?.netArea == 0 ? '--' : prospect.enquiry?.netArea?.toString()} ${prospect.enquiry?.netAreaUnitId?.unit ?? ''}' : '--', isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          ItemSimpleModel(title: "unit number/name", description: prospect.enquiry?.unitName, isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          ItemSimpleModel(title: "offering type", description: getOfferingTypeDescription(prospect.enquiry?.offerType ?? 0), isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          ItemSimpleModel(title: "sourcing manager", description: prospect.sourcingManager?.fullName),
          ItemSimpleModel(title: "closing manager", description: prospect.closingManager?.fullName),
          ItemSimpleModel(title: "cluster name", description: prospect.enquiry?.clusterName, isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          ItemSimpleModel(title: "nationality", description: prospect.nationality, isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          ItemSimpleModel(title: "purpose", description: (prospect.enquiry?.purpose != PurposeEnum.none) ? prospect.enquiry?.purpose?.description : ''),
          ItemSimpleModel(title: "referral name", description: prospect.referralName),
          ItemSimpleModel(title: "referral contact", description: prospect.referralContactNo),
          ItemSimpleModel(title: "referral email", description: prospect.referralEmail),
        ],
      ),
    );

    //location
    leadEnquires.add(
      ItemProspectEnquiryModel(sectionTitle: "location", sectionIcon: ImageResources.iconLocation, columnItems: [
        ItemSimpleModel(title: "locations", items: locations?.map((e) => ItemSimpleModel(title: e)).toList()),
      ]),
    );

    //customer location
    leadEnquires.add(
      ItemProspectEnquiryModel(sectionTitle: "customer location", sectionIcon: ImageResources.iconLocation, columnItems: [
        ItemSimpleModel(title: "community", items: customerLocations?.map((e) => ItemSimpleModel(title: e)).toList()),
      ]),
    );

    return leadEnquires;
  }
}
