import 'package:collection/collection.dart';
import 'package:leadrat/core_main/common/models/base_url_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_environment.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/services/secret_manager_service/secret_manager_local_service.dart';

class RestResources {
  static AppEnvironment appEnvironment = AppEnvironment.prd;

  static void setAppEnvironment(AppEnvironment env) {
    RestResources.appEnvironment = env;
  }

  static const String _devIdentityApi = "https://lrb-dev-identity.leadratd.com/";
  static const String _devCoreApi = "https://lrb-dev-mobile.leadratd.com/";

  static const String _qaIdentityApi = "https://lrb-qa-identity.leadrat.info/";
  static const String _qaCoreApi = "https://lrb-qa-mobile.leadrat.info/";

  static const String _prdIdentityApi = "https://lrb-identity-api-prd.leadrat.com/";
  static const String _prdCoreApi = "https://prd-mobile.leadrat.com/";

  static const String _uatIdentityApi = "https://lrb-identity-api-uat-gtf2dfdpd9cfa2b7.centralindia-01.azurewebsites.net/";
  static const String _uatCoreApi = "https://lrb-mobile-api-uat.azurewebsites.net/";

  static String leadratBaseUrl = _getLeadratBaseUrl();
  static String leadratIdentityBaseUrl = _getLeadratIdentityBaseUrl();
  static String leadratBlobStorageBucketName = _getLeadratBlobStorageBucketName();

  static const String api = "api/";
  static const String apiVersion = "${api}v1/";
  static const String apiVersion2 = "${api}v2/";
  static const String apiVersion3 = "${api}v3/";
  static const String pagination = "?PageNumber={1}&PageSize={2}";

  static BaseUrlModel? _baseUrl;

  static String _getLeadratBaseUrl() {
    _baseUrl = _baseUrl ?? getIt<SecretManagerLocalService>().getBaseUrl();
    switch (appEnvironment) {
      case AppEnvironment.dev:
        final devBaseUrl = _baseUrl?.environments?["Dev"]?.firstWhereOrNull((element) => element.key == "core")?.value;
        return devBaseUrl ?? _devCoreApi;
      case AppEnvironment.qa:
        final qaBaseUrl = _baseUrl?.environments?["QA"]?.firstWhereOrNull((element) => element.key == "core")?.value;
        return qaBaseUrl ?? _qaCoreApi;
      case AppEnvironment.prd:
        final prdBaseUrl = _baseUrl?.environments?["Production"]?.firstWhereOrNull((element) => element.key == "core")?.value;
        return prdBaseUrl ?? _prdCoreApi;
      case AppEnvironment.uat:
        final uatBaseUrl = _baseUrl?.environments?["Uat"]?.firstWhereOrNull((element) => element.key == "core")?.value;
        return uatBaseUrl ?? _uatCoreApi;
    }
  }

  static String _getLeadratIdentityBaseUrl() {
    _baseUrl = _baseUrl ?? getIt<SecretManagerLocalService>().getBaseUrl();
    switch (appEnvironment) {
      case AppEnvironment.dev:
        final devBaseUrl = _baseUrl?.environments?["Dev"]?.firstWhereOrNull((element) => element.key == "identity")?.value;
        return devBaseUrl ?? _devIdentityApi;
      case AppEnvironment.qa:
        final qaBaseUrl = _baseUrl?.environments?["QA"]?.firstWhereOrNull((element) => element.key == "identity")?.value;
        return qaBaseUrl ?? _qaIdentityApi;
      case AppEnvironment.prd:
        final prdBaseUrl = _baseUrl?.environments?["Production"]?.firstWhereOrNull((element) => element.key == "identity")?.value;
        return prdBaseUrl ?? _prdIdentityApi;
      case AppEnvironment.uat:
        final uatBaseUrl = _baseUrl?.environments?["Uat"]?.firstWhereOrNull((element) => element.key == "identity")?.value;
        return uatBaseUrl ?? _uatIdentityApi;
    }
  }

  static String _getLeadratBlobStorageBucketName() {
    switch (appEnvironment) {
      case AppEnvironment.dev:
        return "dleadrat-black";
      case AppEnvironment.qa:
        return "qleadrat-black";
      case AppEnvironment.prd:
        return "leadrat-black";
      case AppEnvironment.uat:
        return "leadrat-black";
    }
  }
}

class ResetPasswordRestResources {
  static String get _prefix => "${RestResources.api}/users";
  static String forgotPassword = '$_prefix/forgot-password';
  static String otpVerification = '$_prefix/otp-verification';
  static String resetPassword = '$_prefix/reset-password';
}

class QrCodeRestResources {
  static String get _prefix => "${RestResources.apiVersion}/utility";
  static String getQrCodeTemplate = "$_prefix/template";
}

class QrCodeGeneratorRestResources {
  static String get _prefix => "${RestResources.apiVersion}profile/qr-code";

  static String generateQrCode(String url) => "$_prefix?url=$url";
}

class UserProfileRestResources {
  static String get _prefix => "${RestResources.apiVersion}userprofile";

  static String userById(String id) => "$_prefix/$id";

  static String userDocument(String documentId) => "$_prefix/document/$documentId";

  static String get userProfileImage => "$_prefix/image";

  static String get userProfileCompletion => "$_prefix/profilecompletion";

  static String getAllReportees() => "$_prefix/getallreportees";

  static String getSettings(String userId) => "$_prefix/settings?UserId=$userId";

  static String get updateSettings => "$_prefix/settings";

  static String deleteDocument(String id) => "$_prefix/document/$id";
}

class BlobStorageRestService {
  static String get _prefix => "${RestResources.api}blobstorage";

  static String uploadFileBase64(String bucketName, String folderName, String fileName) => "$_prefix/image/base64/$bucketName/$folderName/$fileName";
}

class UserRestResources {
  static String get _prefix => "${RestResources.api}users";

  static String changePassword(String userId) => "$_prefix/change-password/$userId";
}

class AuthResources {
  static String get _tenantPrefix => "${RestResources.api}tenants";

  static String getTenantById(String tenantId) => "$_tenantPrefix/$tenantId";

  static String getUserByUserName(String userName) => "${RestResources.api}users/username-verify/$userName";
}

class MasterDataRestResources {
  static const String _prefix = "${RestResources.apiVersion}masterdata/";

  static const String getPropertyTypes = "${_prefix}propertytypes";
  static const String getCustomPropertyTypes = "${_prefix}propertytypes/listing";
  static const String getLeadStatuses = "${_prefix}masterleadstatuses";
  static const String getAreaUnits = "${_prefix}masterareaunits";
  static const String getLeadSources = "${_prefix}leadsources";
  static const String getLastModifiedDates = "${_prefix}lastmodifieddates";
  static const String getPropertyAmenities = "${_prefix}masterpropertyamenities";
  static const String getPropertyAttributes = "${_prefix}masterpropertyattributes";
  static const String getModifiedDates = "${_prefix}modifieddates";
  static const String getProjectAttributes = "${_prefix}projectattributes";
  static const String getProjectAmenities = "${_prefix}projectamenities";
  static const String getAssociatedBank = "${_prefix}associatedbank";
  static const String getProjectType = "${_prefix}masterprojecttypes";
  static const String getUserServices = "${_prefix}masteruserservices";
  static const String getListingPropertyTypes = "${_prefix}propertytypes/listing";
  static const String getPropertyListingAmenities = "${_prefix}masterpropertyamenities/listing";
}

class UsersRestResources {
  static const String _prefix = "${RestResources.apiVersion}user";
  static const String getAllUsers = _prefix;

  static String getUserById(userId) => '$_prefix/$userId';

  static String getCallThrough(userId) => '$_prefix/settings/call-through?UserId=$userId';

  static String getAdminsAndReportees = '$_prefix/adminsandreportees';

  static String getAllReportees = '$_prefix/getallreportees';
  static String getAllDesignationsWithUsers = '$_prefix/get/all/users/desiganation';
  static String getAllDesignations = '$_prefix/getalldesignations';
}

class GlobalSettings {
  static const String _prefix = "${RestResources.apiVersion}globalsettings/";
  static const String getGlobalSettings = _prefix;
  static const getCountries = "$_prefix/countriesinfo";
}

class CustomAmenitiesAndAttributes {
  static const String _prefix = "${RestResources.apiVersion}customamenityandattribute/";
  static const String getAllCustomAmenityCategories = '${_prefix}amenitycategory';
  static const String getAllCustomAmenities = '${_prefix}get/all/categories/with/amenities';
  static const getAllCustomAttributes = "${_prefix}get/all/attributes";
}

class TokenServices {
  static const String _prefix = "${RestResources.api}mobile/tokens";
  static const String refreshToken = "$_prefix/refresh";
  static const String getToken = _prefix;
  static const String postOtpForVerify = "$_prefix/verify-otp";
  static const String postForOtp = "$_prefix/otp";
}

class NotificationRestResources {
  static const String _prefix = "${RestResources.apiVersion}notification/";

  static String getAllNotification(int pageNumber) => '${_prefix}all?PageNumber=$pageNumber&PageSize=10';

  static String getNotificationCount() => '${_prefix}count/total';

  static String updateNotificationStatus() => '${_prefix}updateopenedstate';
}

class PropertyRestResources {
  static const String _prefix = "${RestResources.apiVersion}property";

  static String getPropertiesByPageNo(int number, String filters) => '$_prefix?PageNumber=$number&PageSize=10$filters';

  static String getPropertiesBySearchText(String searchText, int pageSize) => '$_prefix?PropertySearch=$searchText&PageNumber=$pageSize&PageSize=10';

  static String getPropertiesByID(String propertyId) => '$_prefix/$propertyId';

  static String getMatchingLeadsByPropertyId({required int pageNumber, required int pageSize, required String propertyId}) => '$_prefix/matchingleads?Id=$propertyId&PageNumber=$pageNumber&PageSize=$pageSize';

  static String getMatchingLeadsByPropertyListingId({required int pageNumber, required int pageSize, required String propertyId}) => '$_prefix/listing/matchingleads?Id=$propertyId&PageNumber=$pageNumber&PageSize=$pageSize';

  static String getMatchingAssociateLeadsByPropertyId(String propertyId) => '$_prefix/lead-count?Ids=$propertyId';

  static String getOwnerNames() => '$_prefix/ownernames';

  static String getAddresses() => '$_prefix/addresses';
  static String getPropertiesWithIds = '$_prefix/idwithname';

  static const String getPropertiesWithId = "$_prefix/idwithname";

  static const String propertyReassign = "$_prefix/assigntomultipleproperties";
  static  String updatePropertyAssignedTo(String propertyId) => "$_prefix/UpdatePropertyAssignedTo/$propertyId";

  static const String addProperty = _prefix;

  static String updateProperty(String propertyId) => '$_prefix/$propertyId';
  static String updatePropertyShareCount = '$_prefix/sharecount/new';

  static String deleteProperty(String propertyId) => '$_prefix/$propertyId';
  static String softDeleteProperty = '$_prefix/softdeleteproperties';

  static String getAllListingProperties(int pageNumber, int pageSize, String filters) => "$_prefix/all/listing?PageNumber=$pageNumber&PageSize=$pageSize$filters";

  static const String getAllListingCountProperties = "$_prefix/listing/count?";

  static const String propertyPublish = "$_prefix/publish";

  static const String propertyDeList = "$_prefix/delist";

  static String getPropertiesListingBySearchText(String searchText, int pageSize) => '$_prefix/all/listing?PropertySearch=$searchText&PageNumber=$pageSize&PageSize=10';

  static String getCurrency = "$_prefix/currency";

  static String cloneProperty = "$_prefix/cloneproperty";
}

class SettingsRestResources {
  static String get _prefix => "${RestResources.apiVersion}userprofile";

  static String userById(String id) => "$_prefix/$id";

  static String deleteRelink(String id) => "${RestResources.apiVersion}pnsregistration/$id";

  static String registerDeviceInfo() => "${RestResources.apiVersion}pnsregistration/deviceinfo";

  static String updateDeviceInfoById(String deviceToken) => "${RestResources.apiVersion}pnsregistration/$deviceToken";

  static String sendRelinkNotification() => "${RestResources.apiVersion}utility/notification/send";

  static const String callDetectionAppUrl = 'https://leadrat-resources-blob.s3.ap-south-1.amazonaws.com/LeadratServices/leadrat_services_call_recording.apk';
}

class LeadRestResources {
  static const String _leadPrefix = "${RestResources.apiVersion}lead";
  static const String _leadPrefixV2 = "${RestResources.apiVersion2}lead";
  static const String _leadPrefixV3 = "${RestResources.apiVersion3}lead";

  static String lead = _leadPrefix;

  static String pagination(int pageNumber, int pageSize) => "PageNumber=$pageNumber&PageSize=$pageSize";

  static String getInitialLeads({int pageNumber = 1, int pageSize = 10}) => "$_leadPrefix?${pagination(pageNumber, pageSize)}";

  static String getLeadById(String leadId) => "$_leadPrefix/$leadId";

  static String getLeadBasicInfo(String leadId) => "$_leadPrefix/basicinfo/$leadId";

  static String getCategorisedLeads({int pageNumber = 1, int pageSize = 10}) => "$_leadPrefix/getallleadcategory${pagination(pageNumber, pageSize)}";

  static String searchLeads({int pageNumber = 1, int pageSize = 10, required String searchQuery}) => "$_leadPrefixV2/searchleads?${pagination(pageNumber, pageSize)}&SearchByNameOrNumber=$searchQuery";

  static String getLeadHistory(String leadId) => "$_leadPrefix/history/?Id=$leadId";

  static String getLeadHistoryBasedOnTimeZone(String leadId) => "$_leadPrefix/histories?Id=$leadId";

  static String updateNote(String noteId) => "$_leadPrefix/notes/$noteId/";

  static String getLeadNoteHistory(String leadId) => "$_leadPrefix/leadnotehistory/$leadId/";
  static const String assignLead = "$_leadPrefix/assign";

  static String updateDocuments(String documentId) => "$_leadPrefix/document/$documentId";

  static String getDocuments(String documentId) => "$_leadPrefix/documents/$documentId";

  static String tag(String tagId) => "$_leadPrefix/tag/$tagId";

  static String contactCount(String id) => "$_leadPrefix/contactCount/$id";
  static const String leadTemplate = "$_leadPrefixV2/message";

  static String enquiries(String enquiryId) => "$_leadPrefix/enquiries/$enquiryId";
  static const String meetingOrSiteVisitDone = "$_leadPrefix/meetingorsitevisitdone";
  static const String leadAddresses = "$_leadPrefix/addresses";

  static String status(String statusId) => "$_leadPrefix/status/$statusId";
  static const String leadSubSource = "$_leadPrefixV3/subsource";
  static const String agencyNames = "$_leadPrefix/agencynames";

  static String matchingProperties(String leadId, {int pageNumber = 1, int pageSize = 10}) => "$_leadPrefix/matchingproperties?Id=$leadId&PageNumber=$pageNumber&PageSize=$pageSize";

  static String getLeadByIdAnonymous(String leadId, String currentUserId, String tenantId) => "$_leadPrefix/basicinfo/anonymous?Id=$leadId&CurrentUserId=$currentUserId&TenantId=$tenantId";
  static const String addProjectsInLead = "$_leadPrefix/add-projects";

  static String getAppointmentsByProjects(String leadId) => "$_leadPrefix/getappointmentsbyprojects?LeadId=$leadId";

  static String leadCommunications(String leadIds) => "$_leadPrefixV2/communications?$leadIds";
  static const String getLeadsCount = "$_leadPrefix/counts?";

  static String updateLeadFlag(String customFlagId) => "$_leadPrefixV2/customflag/$customFlagId";
  static String updateClickLink = "$_leadPrefix/click-link";

  static String getLeadByContactNumber(String contactNumber, String countryCode) => "$_leadPrefix/contactnumber?contactNumber=$contactNumber&countryCode=$countryCode";

  static String getBookingDetails(String bookingId) => "$_leadPrefix/booked-details/$bookingId";

  static String updateBookingForm(String? bookingId) => "$_leadPrefix/booked/$bookingId";

  static String getCustomLeads({int pageNumber = 1, int pageSize = 10}) => "$_leadPrefixV3/custom-filters?${pagination(pageNumber, pageSize)}";
  static String getLeadNationality = '$_leadPrefix/nationality';
  static String getLeadClusterNames = '$_leadPrefix/clustername';
  static String getLeadUnitNames = '$_leadPrefix/unitname';
  static String addDocument = "$_leadPrefix/documents/";
  static String deleteDocument = "$_leadPrefix/documents/";

  static String updateLeadStatus(String leadId) => "$_leadPrefixV2/status/$leadId";

  static const String updateAppointments = "$_leadPrefix/meetingorsitevisitdone";

  static String getMatchingProjects({required String leadId, int pageNumber = 1, int pageSize = 10}) => "$_leadPrefix/matchingprojects?Id=$leadId&PageNumber=$pageNumber&PageSize=$pageSize";

  static const String reAssignLead = "$_leadPrefixV2/assign";

  static const String communities = "$_leadPrefix/communities";

  static const String subCommunities = "$_leadPrefix/subcommunities";

  static const String towerName = "$_leadPrefix/towername";

  static const String countries = "$_leadPrefix/countries";

  static const String currency = "$_leadPrefixV2/currency";

  static String checkLeadAssignedByLeadId(String? leadId) => "$_leadPrefix/assignto/$leadId";

  static const String campaignNames = "$_leadPrefix/campaigns";
  static const String uploadTypeNames = "$_leadPrefix/uploadtypename";
  static const String additionalPropertykeys = "$_leadPrefix/additionalproperties/keys";

  static String additionalpropertyValues(String key) => "$_leadPrefix/additionalproperties/values?key=$key";

  static const String leadCities = "$_leadPrefix/cities";
  static const String leadZones = "$_leadPrefix/zones";
  static const String leadStates = "$_leadPrefix/states";
  static const String leadLocality = "$_leadPrefix/localites";
}

class StatusRestResources {
  static const String _prefix = "${RestResources.apiVersion}status";
  static const String getStatus = _prefix;
}

class CustomFilterRestResources {
  static const String _prefix = "${RestResources.apiVersion}customfilter";
  static const String defaultFilter = "$_prefix/default";
  static const String customFilterBulkUpdate = "$_prefix/bulk";
  static const String defaultFilterCardView = "$_prefix/cardview";
}

class DataManagementRestResources {
  static const String _prefix = "${RestResources.apiVersion}prospect";
  static const String createProspect = _prefix;
  static const String getAllProspect = "$_prefix/custom-filters";
  static const String searchProspect = "$_prefix/search";
  static const String getAllProspectCount = "$_prefix/custom-filters-count";
  static const String updateProspectReAssign = "$_prefix/bulk/assign";
  static const String convertToLead = "$_prefix/convert";
  static const String getAllProspectStatus = "$_prefix/all/statuses";
  static const String updateProspectStatus = "$_prefix/update/status";
  static const String getProspectSubSource = "$_prefix/subsource";
  static const String getProspectLocation = "$_prefix/addresses";
  static const String getProspectSource = "$_prefix/all/source";
  static const String getProspectNationality = "$_prefix/nationality";
  static const String getProspectClusterNames = "$_prefix/clustername";
  static const String getProspectUnitNames = "$_prefix/unitname";
  static String checkProspectAssignedByProspectId(String? prospectId) => "$_prefix/assignto/$prospectId";
  static const String uploadTypeNames = "$_prefix/uploadtypename";

  static String getProspectById(String id) => "$_prefix/$id";

  static String getProspectNotesById(String id) => "$_prefix/history/notes/$id";

  static String putProspectNotesById(String id) => "$_prefix/notes?id=$id";

  static String updateProspectActionCount(String id) => "$_prefix/contactcount/$id";

  static String getProspectHistoryById(String id) => "$_prefix/history/$id";

  static String getProspectHistoryBasedOnTimeZoneById(String id) => "$_prefix/histories/$id";

  static String getProspectByContactNumber(String contactNumber, String countryCode) => "$_prefix/v2contactno?contactNo=$contactNumber&countryCode=$countryCode";

  static String currency = "$_prefix/currency";

  static const String communities = "$_prefix/communities";

  static const String subCommunities = "$_prefix/subcommunities";

  static const String towerName = "$_prefix/towername";

  static const String countries = "$_prefix/countries";

  static const String prospectTemplate = "$_prefix/message";

  static String dataCommunications(String dataIds) => "$_prefix/communications?$dataIds";
}

class IvrRestResources {
  static const String _prefix = "${RestResources.apiVersion}integration";

  static String getVirtualNumberCheckById(String id, String userId) => "$_prefix/ivr/virtual-number/assignment-check?UserId=$userId&LeadId=$id";
  static const String getVirtualNumbers = "$_prefix/ivr/virtual-numbers";
  static const String postClickToCall = "$_prefix/common/clicktocall/config";
}

class TemplateResources {
  static const String _prefix = "${RestResources.apiVersion}template";

  static String getTemplatesByModule(int module) => "$_prefix/modulename?ModuleName=$module";
}

class AttendanceRestResources {
  static String get _prefix => "${RestResources.apiVersion}attendance";

  static String getAttendanceLogByUser(String userId, String timeZoneId) => "$_prefix/dapper/$userId?timeZoneId=$timeZoneId";

  // static String getAllAttendanceLogs(String userId, String timeZoneId) => "$_prefix/$userId?timeZoneId=$timeZoneId";
  static String getAttendanceSettings() => "$_prefix/settings";

  static String postClockInAttendance() => "$_prefix/clockin";

  static String postClockOutAttendance() => "$_prefix/clockout";

  static String getAttendanceHistory() => "$_prefix/logs/all";

  static String getAttendanceLogBasedOnTimeZoneByUserId(String userId, String timeZoneId, String baseUtcOffset, String startTime) => "$_prefix/dapper/timezone/$userId?timeZoneId=$timeZoneId&BaseUTcOffset=$baseUtcOffset&StartTime=$startTime";
}

class FlagRestResources {
  static const String _prefix = "${RestResources.apiVersion}flags";

  static String getFlagsByModule(String moduleName) => "$_prefix/module?Module=$moduleName";
}

class ProjectRestResources {
  static const String _prefix = "${RestResources.apiVersion}project";

  static String getProjectById(String id) => "$_prefix/$id";

  static String archiveProject(String id) => "$_prefix/archive?id=$id";
  static String getProjectsWithIds = '$_prefix/idwithname';
  static String getBuilderInfo = '$_prefix/builder/infos';
  static String updateProjectShareCountUrl = '$_prefix/contactrecordscount';

  static String getAllProjects({int? pageNumber = 1, int? pageSize = 10, String? searchText, String? projectFilterUrl}) => "$_prefix?PageNumber=$pageNumber&PageSize=$pageSize${searchText.isNotNullOrEmpty() ? '&Search=$searchText' : ''}${projectFilterUrl ?? ''}";

  static String getMatchingLeadsByProjectUrl({int? pageNumber = 1, int? pageSize = 10, double? radiusInKms, required String projectId}) => "$_prefix/matchingleadsbyproject?Id=$projectId${radiusInKms != null ? '&RadiusInKms=$radiusInKms' : ''}";

  static String getProjectsUnitInfo(String projectId) => '$_prefix/unitInfo?id=$projectId';

  static String getProjectAmenities(String projectId) => '$_prefix/amenities?id=$projectId';
  static String softDeleteProjects = '$_prefix/softdeleteprojects';
  static String getCurrency = '$_prefix/currency';
  static String getLocations = '$_prefix/location';
}

class DashBoardRestResources {
  static const String _prefix = "${RestResources.apiVersion}dashboard/";

  static String getUserStatusCount() => '$_prefix${'countstatusbyuser'}';

  static String getSiteVisitStatus(String? url) => '$_prefix${'sitevisitstatuscount'}?${url}';

  static String getMeetingStatus(String? url) => '$_prefix${'meetingstatuscount'}?${url}';

  static String getCallStatus(String? url) => '$_prefix${'callreport'}?${url}';

  static String getLeadReSource(String? url) => '$_prefix${'leadsource'}?${url}';

  static String getLeadReceived(String? url) => '$_prefix${'leadreceived'}?${url}';

  static String getLeadPipeLine(String? url) => '$_prefix${'leadspipeline'}?${url}';

  static String getLeadTracker(String? url) => '$_prefix${'leadtracker'}?${url}';

  static String getAllCustomStatusCount(String? url,) => '${_prefix}count/all/status/new?${url ?? ''}';

  static String getCustomLeads(String? url, {int pageNumber = 1, int pageSize = 10}) =>
      '$_prefix${'leadsource/v1'}?PageNumber=$pageNumber&PageSize=$pageSize${url ?? ''}';

  static String getLeadStatus(String? url, {int pageNumber = 1, int pageSize = 10}) =>
      '$_prefix${'leadstatus/custom/new'}?PageNumber=$pageNumber&PageSize=$pageSize${url ?? ''}';

  static String getDataStatus(String? url, {int pageNumber = 1, int pageSize = 10}) =>
      '$_prefix${'datastatus'}?PageNumber=$pageNumber&PageSize=$pageSize${url ?? ''}';

  static String getWhatsappChats(String? url, {int pageNumber = 1, int pageSize = 10}) =>
      '$_prefix${'whatsapp/chats'}?PageNumber=$pageNumber&PageSize=$pageSize${url ?? ''}';

  static String getCalls(String? url, {int pageNumber = 1, int pageSize = 10}) => '$_prefix${'calls'}?${'PageNumber=$pageNumber&PageSize=$pageSize'}${url ?? ''}';


  static String getCustomLeadsCount(String? url) => '$_prefix${'lead/status/custom-count'}?${url ?? ''}';
}

class SiteRestResources {
  static const String _prefix = "${RestResources.api}site";

  static String getLocation(String location) => '$_prefix/location/lead/googlemap?SearchText=$location&PageNumber=1&PageSize=50';

  static String getCustomLocation(String location) => '$_prefix/custom/all/addresses?SearchText=$location&PageNumber=1&PageSize=50';
}

class ChannelPartner {
  static const String _prefix = "${RestResources.apiVersion}channelpartner";
  static const String channelPartnerNames = _prefix;
}

class WhatsappRestResources {
  static const String _prefix = "${RestResources.apiVersion}wa";

  static String getAllMessages({required String customerId, required int pageNumber, int pageSize = 10, required String customerNo, required String userId}) => "$_prefix/message?CustomerId=$customerId&CustomerNo=$customerNo&PageNumber=$pageNumber&PageSize=$pageSize";

  static String twentyFourHourValidation(String customerId, String uTCCurrentDateTime) => "$_prefix/24-hour-validation?CustomerId=$customerId&UTCCurrentDateTime=$uTCCurrentDateTime";
  static const String getAllTemplate = "$_prefix/template";
}

class LeadratDomain {
  static const String dev = "leadratd.com";
  static const String qa = "leadrat.info";
  static const String prd = "leadrat.com";
}

class ListingSiteResources {
  static const String _prefix = "${RestResources.apiVersion}listingsite";
  static const String getAllListingSource = "$_prefix/listing-source";
  static const String getAllListingSourceWithIds = "$getAllListingSource/namewithid";
  static const String getAllCommunity = "$_prefix/community";
  static const String getAllSubCommunity = "$_prefix/sub-community";
  static const String addresses = "$_prefix/addresses";
}

class AppAnalysisResources {
  static const String baseUrl = 'https://lrb-prd-analytics.leadrat.com/api/';
  static const String _prefix = baseUrl;
  static const String getFeatures = "${_prefix}features";
  static const String tracks = "${_prefix}tracks";
}

class UtilityRestResources {
  static const String _prefix = "${RestResources.apiVersion}utility";
  static const String sendEmail = "$_prefix/sendemail/form";
}

class LeadCallLogRestResources {
  static const String _prefixV2 = "${RestResources.apiVersion2}leadCallLog";

  static const String leadCallLog = _prefixV2;
}

class ProspectCallLogRestResources {
  static const String _prefix = "${RestResources.apiVersion}prospectcalllogs";

  static const String prospectCallLog = _prefix;
}

class MobileCardViewRestResources{
  static const String _prefix = "${RestResources.apiVersion}mobilecardview";
  static const String getCardViews = _prefix;
}

class SavedFilterRestResources {
  static const String _prefix ="${RestResources.apiVersion}filter";
  static const String savedFilter = _prefix;
  static String savedFilterById(String id) => "$_prefix/$id";
  static String filterExists({required String filterName,required String module}) => "$_prefix/filter-exist?filter=$filterName&${module.isNotNullOrEmpty() ? 'module=$module' : ''}";
}

class SourceRestResource {
  static  const String _prefix = "${RestResources.apiVersion}source";
  static const String source = _prefix;
  static const String leadSubSource = "$_prefix/lead/subsource";
  static const String prospectSubSource = "$_prefix/prospect/subsource";
}