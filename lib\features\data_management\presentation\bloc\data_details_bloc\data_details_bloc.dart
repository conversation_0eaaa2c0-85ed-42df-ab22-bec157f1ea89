import 'dart:async';
import 'dart:io';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_phone_direct_caller/flutter_phone_direct_caller.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/repository/global_setting_repository.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/entites/address_entity.dart';
import 'package:leadrat/core_main/common/models/base_user_model.dart';
import 'package:leadrat/core_main/common/models/ivr_config_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/call_direction_enum.dart';
import 'package:leadrat/core_main/enums/common/call_status_enum.dart';
import 'package:leadrat/core_main/enums/common/contact_type_enum.dart';
import 'package:leadrat/core_main/extensions/integer_extension.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/services/ivr_service/ivr_service.dart';
import 'package:leadrat/core_main/services/microphone_detection_service/microphone_detection_service.dart';
import 'package:leadrat/core_main/utilities/dialog_manager.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/core_main/utilities/tuple.dart';
import 'package:leadrat/features/data_management/data/models/data_reassign_model.dart';
import 'package:leadrat/features/data_management/data/models/prospect_call_log_model.dart';
import 'package:leadrat/features/data_management/data/models/prospect_communication_model.dart';
import 'package:leadrat/features/data_management/data/models/update_count_model.dart';
import 'package:leadrat/features/data_management/domain/entities/data_ivr_call_recording_entity.dart';
import 'package:leadrat/features/data_management/domain/entities/prospect_entity.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_data_communication_use_case.dart';
import 'package:leadrat/features/data_management/domain/usecase/get_prospect_details_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/prospect_reassign_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/update_prospect_action_count_usecase.dart';
import 'package:leadrat/features/data_management/domain/usecase/update_prospect_call_log_use_case.dart';
import 'package:leadrat/features/data_management/presentation/bloc/data_history_bloc/data_history_bloc.dart';
import 'package:leadrat/features/data_management/presentation/bloc/data_notes_bloc/data_notes_bloc.dart';
import 'package:leadrat/features/data_management/presentation/bloc/manage_data_bloc/manage_data_bloc.dart';
import 'package:leadrat/features/data_management/presentation/items/item_prospect_enquiry_model.dart';
import 'package:leadrat/main.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

part 'data_details_event.dart';
part 'data_details_state.dart';

class DataDetailsBloc extends Bloc<DataDetailsEvent, DataDetailsState> {
  final GetProspectDetailsUseCase _getProspectDetailsUseCase;
  final GlobalSettingRepository _globalSettingRepository;
  final IvrService _ivrService;
  final UpdateProspectActionCountUseCase _updateProspectActionCountUseCase;
  final UsersDataRepository _usersDataRepository;
  final ProspectReAssignUseCase _prospectReAssignUseCase;
  final GetDataCommunicationUseCase _getDataCommunicationUseCase;
  final manageDataBloc = getIt<ManageDataBloc>();
  bool _isPaginationInProgress = false;

  final UpdateProspectCallLogUseCase _updateProspectCallLogUseCase;

  final MicrophoneStatusService _microphoneStatusService = MicrophoneStatusService();
  StreamSubscription<bool>? _microphoneStatusSubscription;
  DateTime? _callStartTime;
  DateTime? _callEndTime;

  GlobalSettingModel? _globalSettingModel;

  getDefaultCurrency() => _globalSettingModel?.countries?.firstOrNull?.defaultCurrency ?? ((_globalSettingModel?.isCustomLeadFormEnabled ?? false) ? "AED" : "INR");

  List<ItemSimpleModel> communicationActions = [
    ItemSimpleModel<int>(title: ContactType.call.name, imageResource: ImageResources.iconCalling, isSelected: true),
    ItemSimpleModel<int>(title: ContactType.sms.name, imageResource: ImageResources.iconChat, isSelected: false),
    ItemSimpleModel<int>(title: ContactType.whatsApp.name, imageResource: ImageResources.iconWhatsapp, isSelected: false),
    ItemSimpleModel<int>(title: ContactType.email.name, imageResource: ImageResources.iconEmail, isSelected: false),
  ];

  IvrConfigModel? ivrConfigModel;

  DataDetailsBloc(
    this._getProspectDetailsUseCase,
    this._globalSettingRepository,
    this._ivrService,
    this._updateProspectActionCountUseCase,
    this._usersDataRepository,
    this._prospectReAssignUseCase,
    this._updateProspectCallLogUseCase,
    this._getDataCommunicationUseCase,
  ) : super(const DataDetailsState()) {
    on<DataDetailsInitialEvent>(_onDataDetailsInitial);
    on<GetProspectDetailsEvent>(_onGetProspectDetails);
    on<ChangeBottomNavbarItemEvent>(_onChangeBottomNavbarItem);
    on<CommunicationActionToggleEvent>(_onCommunicationActionToggle);
    on<CallThroughIVREvent>(_onCallThroughIVR);
    on<CallThroughDialerEvent>(_onCallThroughDialer);
    on<UpdateActionButtonCountEvent>(_onUpdateActionButtonCount);
    on<UpdateProspectEvent>(_onUpdateProspect);
    on<ProspectReAssignEvent>(_onProspectReAssign);
    on<PostIvrConfigurationEvent>(_onPostIvrConfiguration);
    on<SwipeToNextDataEvent>(_onSwipeToNextData);
    on<SwipeToPreviousDataEvent>(_onSwipeToPreviousData);
    on<ShowLoadingDialogEvent>(_onShowLoadingDialog);
    on<ToggleIsCallInitiatingEvent>(_onToggleIsCallInitiating);
    on<DisposeProspectInfoEvent>(_onDisposeProspectInfo);
    on<UpdateProspectCallLogEvent>(_onUpdateProspectCallLog);
    on<GetProspectCommunicationsEvent>(_onGetProspectCommunications);
  }

  FutureOr<void> _onDataDetailsInitial(DataDetailsInitialEvent event, Emitter<DataDetailsState> emit) async {
    _globalSettingModel = await _globalSettingRepository.getGlobalSettings();
    _isPaginationInProgress = false;
    communicationActions[0] = communicationActions[0].copyWith(value: 0, isEnabled: event.prospect?.contactNo?.isNotNullOrEmpty() ?? false);
    communicationActions[1] = communicationActions[1].copyWith(value: 0, isEnabled: event.prospect?.contactNo?.isNotNullOrEmpty() ?? false);
    communicationActions[2] = communicationActions[2].copyWith(value: 0, isEnabled: event.prospect?.contactNo?.isNotNullOrEmpty() ?? false);
    communicationActions[3] = communicationActions[3].copyWith(value: 0, isEnabled: event.prospect?.email?.isNotNullOrEmpty() ?? false);
    num count = 0;
    List<Map<String, dynamic>> callRecords = [];
    if (event.prospect != null && event.prospect?.callRecordingUrls != null && (event.prospect?.callRecordingUrls?.isNotEmpty ?? false)) {
      var data = event.prospect?.callRecordingUrls;
      data!.forEach((year, value) {
        List<DataIvrCallRecordingEntity> ivrRecordingList = [];
        int mon = 0;
        value!.forEach((month, value) {
          mon = month;
          value!.forEach((date, value) {
            DataIvrCallRecordingEntity model = DataIvrCallRecordingEntity(
              year: year,
              month: month,
              dateTime: date,
              value: value,
            );
            ivrRecordingList.add(model);
          });
        });
        if (ivrRecordingList.isNotEmpty) {
          String key = '${mon.getMonthNameByMonthNumber()} $year';
          Map<String, dynamic> map = {
            'key': key,
            'value': ivrRecordingList,
          };
          callRecords.add(map);
        }
      });

      for (int i = 0; i < callRecords.length; i++) {
        var item = callRecords[i]['value'];
        if (item.isNotEmpty) {
          count = count + item.length;
        }
      }
    }

    List<ItemProspectEnquiryModel> prospectEnquires = event.prospect != null
        ? ItemProspectEnquiryModel.getProspectEnquires(
            prospect: event.prospect!,
            noOfBHK: getBhk(event.prospect!),
            bhkType: getBhkType(event.prospect!),
            globalSettingModel: _globalSettingModel,
            locations: getAddressList(event.prospect!),
            customerLocations: getAddressList(event.prospect!, isCustomerLocation: true),
          )
        : [];
    emit(state.copyWith(
      pageState: PageState.initial,
      prospectEntity: event.prospect,
      prospectItems: event.prospectItems,
      totalCount: event.totalCount,
      errorMessage: null,
      selectedItem: event.selectedItem ?? DataDetailsBottomNavItem.info,
      communicationActions: [...communicationActions],
      isContactOption: false,
      phoneNumber: null,
      ivrState: IvrState.initial,
      successMessage: '',
      callRecords: callRecords,
      ivrRecordingCount: count.toString(),
      reAssignState: ReAssignState.initial,
      showLoadingProgress: false,
      userDetails: _usersDataRepository.getLoggedInUser(),
      prospectEnquires: prospectEnquires,
    ));

    add(GetProspectDetailsEvent(event.prospect?.id));
    getIt<DataHistoryBloc>().add(GetDataHistoryInitialEvent(event.prospect?.id ?? ''));
    getIt<DataNotesBloc>().add(GetAllDataNotesEvent(event.prospect?.id ?? ''));
  }

  FutureOr<void> _onGetProspectDetails(GetProspectDetailsEvent event, Emitter<DataDetailsState> emit) async {
    emit(state.copyWith(pageState: PageState.loading));
    final prospectDetails = await _getProspectDetailsUseCase.call(event.id ?? '');
    final userList = await _usersDataRepository.getAssignUser();
    final currentUser = _usersDataRepository.getLoggedInUser();

    var selectableItems = userList!.whereNot((element) => element?.id == currentUser?.userId || !(element?.isActive ?? true)).map((user) => ItemSimpleModel<BaseUserModel>(title: user?.fullName ?? '', description: user?.id, value: user?.toBaseUserModel(), isSelected: false, isEnabled: true)).toList();
    userList.where((element) => !(element?.isActive ?? true)).forEach((disabledUsers) => selectableItems.add(ItemSimpleModel<BaseUserModel>(title: disabledUsers?.fullName ?? '', description: disabledUsers?.id, value: disabledUsers?.toBaseUserModel(), isSelected: false, isEnabled: false)));
    selectableItems.insert(0, ItemSimpleModel<BaseUserModel>(title: "You", description: currentUser?.toBaseUser().id, value: currentUser?.toBaseUser(), isSelected: false, isEnabled: currentUser?.toBaseUser().isActive));

    prospectDetails.fold(
      (failure) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: failure.message, prospectEntity: null, communicationActions: [...communicationActions]));
      },
      (success) {
        List<ItemProspectEnquiryModel> prospectEnquires = success != null ? ItemProspectEnquiryModel.getProspectEnquires(prospect: success!, noOfBHK: getBhk(success!), bhkType: getBhkType(success!), globalSettingModel: _globalSettingModel, locations: getAddressList(success!), customerLocations: getAddressList(success!, isCustomerLocation: true)) : [];

        final selectedUserItem = ItemSimpleModel(title: success?.assignTo?.fullName ?? '', description: success?.assignTo?.id, isSelected: true, value: success?.assignTo);
        add(GetProspectCommunicationsEvent([event.id!]));
        emit(state.copyWith(
          pageState: PageState.success,
          prospectEntity: success,
          communicationActions: [...communicationActions],
          selectableItems: [...selectableItems],
          selectedUserItem: selectedUserItem,
          prospectEnquires: prospectEnquires,
        ));
      },
    );
  }

  FutureOr<void> _onChangeBottomNavbarItem(ChangeBottomNavbarItemEvent event, Emitter<DataDetailsState> emit) {
    emit(state.copyWith(selectedItem: event.bottomNavbarItem, successMessage: ''));
  }

  AddressEntity? getAddressObject(ProspectEntity prospect) {
    return prospect.enquiry != null && prospect.enquiry?.addresses != null
        ? prospect.enquiry!.addresses!.isNotEmpty
            ? prospect.enquiry!.addresses![0]
            : null
        : null;
  }

  List<String>? getAddressList(ProspectEntity prospect, {bool isCustomerLocation = false}) {
    if (isCustomerLocation) {
      if (prospect.enquiry == null || prospect.addressDto == null || prospect.addressDto == null) {
        return null;
      }

      return ['${prospect.addressDto?.subLocality ?? prospect.addressDto?.locality}, ${prospect.addressDto?.city}, ${prospect.addressDto?.state}'];
    } else {
      if (prospect.enquiry == null || prospect.enquiry!.addresses == null || prospect.enquiry!.addresses!.isEmpty) {
        return null;
      }

      List<String> addressList = prospect.enquiry!.addresses!.map((address) {
        return '${address?.subLocality ?? address?.locality}, ${address?.city}, ${address?.state}';
      }).toList();

      return addressList;
    }
  }

  String? getEnquiredFor(ProspectEntity prospect) {
    if (prospect.enquiry == null || prospect.enquiry!.enquiryTypes == null || prospect.enquiry!.enquiryTypes!.isEmpty) {
      return null;
    }
    List<String> enquiryTypeList = prospect.enquiry!.enquiryTypes!.map((type) => type.description).toList();
    return enquiryTypeList.join(', ');
  }

  String? getBhkType(ProspectEntity prospect) {
    if (prospect.enquiry == null || prospect.enquiry!.bhkTypes == null || prospect.enquiry!.bhkTypes!.isEmpty) {
      return null;
    }
    List<String> bhkTypeList = prospect.enquiry!.bhkTypes!.map((type) => type.description).toList();
    return bhkTypeList.join(', ');
  }

  String? getBhk(ProspectEntity prospect) {
    if (prospect.enquiry == null || prospect.enquiry!.bhKs == null || prospect.enquiry!.bhKs!.isEmpty) {
      return null;
    }
    List<String> bhkList = [];
    for (var bhk in prospect.enquiry!.bhKs!) {
      if (bhk == 0.5) {
        bhkList.add('1 RK');
      } else {
        bhkList.add("${convertNumber(bhk ?? 0.0)} ${_globalSettingModel?.isCustomLeadFormEnabled ?? false ? 'BR' : 'BHK'}");
      }
    }
    return bhkList.join(', ');
  }

  String convertNumber(double number) {
    if (number % 1 == 0) {
      return number.toInt().toString();
    } else {
      return number.toStringAsFixed(1);
    }
  }

  FutureOr<void> _onCommunicationActionToggle(CommunicationActionToggleEvent event, Emitter<DataDetailsState> emit) async {
    int? callOption = await _globalSettingRepository.getGlobalSettings().then(
      (value) {
        return value?.callSettings?.callType?.index;
      },
    );
    if (callOption != null) {
      switch (callOption) {
        case 0:
        case 1:
          emit(state.copyWith(isContactOption: false, phoneNumber: event.contactNo, ivrState: IvrState.initial));
          add(CallThroughDialerEvent(event.id, true));
          break;
        case 2:
          emit(state.copyWith(isContactOption: true, phoneNumber: event.contactNo, ivrState: IvrState.initial));
          break;
      }
    }
  }

  FutureOr<void> _onCallThroughIVR(CallThroughIVREvent event, Emitter<DataDetailsState> emit) async {
    final userDetails = await getIt<UsersDataRepository>().getUser();
    if (_globalSettingModel?.isIVROutboundEnabled ?? false) {
      if (_globalSettingModel?.isVirtualNumberRequiredForOutbound ?? false) {
        emit(state.copyWith(ivrState: IvrState.validatingAgent, isContactOption: false));
        ivrConfigModel = IvrConfigModel(
          destinationNumber: state.phoneNumber,
          agentNumber: userDetails?.phoneNumber,
          callerIdOrVirtualNumber: null,
          prospectId: event.id,
          userId: userDetails?.userId,
        );

        VirtualNumberModel? virtualNumberModel = await _ivrService.getVirtualNumberCheck(event.id);
        if (virtualNumberModel?.isVirtualNumberAssigned ?? false) {
          ivrConfigModel = IvrConfigModel(
            destinationNumber: state.phoneNumber,
            agentNumber: userDetails?.phoneNumber,
            callerIdOrVirtualNumber: virtualNumberModel?.virtualNumber,
            prospectId: event.id,
            userId: userDetails?.userId,
          );

          emit(state.copyWith(ivrState: IvrState.callingAgent, isContactOption: false));
          IvrResponseModel? ivrResponseModel = await _ivrService.postIvrConfiguration(ivrConfigModel ?? IvrConfigModel());
          if (ivrResponseModel?.success ?? false) {
            add(UpdateActionButtonCountEvent(event.id, ContactType.call));

            emit(state.copyWith(ivrState: IvrState.success, isContactOption: false));
          } else {
            emit(state.copyWith(ivrState: IvrState.error, errorMessage: 'There was some problem with the IVR', isContactOption: false));
          }
        } else {
          if (virtualNumberModel?.shouldFetchVirtualNumbers ?? false) {
            final numbers = await _ivrService.getVirtualNumbers();
            final List<ItemSimpleModel>? virtualNumbers = numbers?.map((number) => ItemSimpleModel(title: number, isSelected: false)).toList();
            emit(state.copyWith(ivrState: IvrState.virtualNumber, isContactOption: false, selectableVirtualNumbers: virtualNumbers));
          } else {
            emit(state.copyWith(ivrState: IvrState.error, errorMessage: 'There are no virtual numbers', isContactOption: false));
          }
        }
      } else {
        ivrConfigModel = IvrConfigModel(
          destinationNumber: state.phoneNumber,
          agentNumber: userDetails?.phoneNumber,
          callerIdOrVirtualNumber: null,
          prospectId: event.id,
          userId: userDetails?.userId,
        );
        IvrResponseModel? ivrResponseModel = await _ivrService.postIvrConfiguration(ivrConfigModel ?? IvrConfigModel());
        if (ivrResponseModel?.success ?? false) {
          emit(state.copyWith(ivrState: IvrState.success, isContactOption: false));
        } else {
          emit(state.copyWith(ivrState: IvrState.error, errorMessage: (ivrResponseModel?.message?.isNotNullOrEmpty() ?? false) ? ivrResponseModel?.message : 'There was some problem with the IVR', isContactOption: false));
        }
      }
    } else {
      emit(state.copyWith(ivrState: IvrState.error, errorMessage: "Oops, No primary IVR account found in Integration, please integrate an 'Outbound' account with valid token and make it primary.", isContactOption: false));
    }
  }

  FutureOr<void> _onCallThroughDialer(CallThroughDialerEvent event, Emitter<DataDetailsState> emit) async {
    try {
      final status = await Permission.phone.status;

      if (status.isGranted) {
        FlutterPhoneDirectCaller.callNumber(state.phoneNumber ?? '');
        add(ToggleIsCallInitiatingEvent(true));
        emit(state.copyWith(ivrState: event.isDirectCallEvent ? IvrState.initial : IvrState.success, isContactOption: false));
        add(UpdateActionButtonCountEvent(event.id, ContactType.call));
      } else if (status.isDenied) {
        // Request permission if it's not permanently denied
        if (status.isDenied) {
          final result = await Permission.phone.request();
          if (result.isGranted) {
            // Retry making the call if the user grants permission
            FlutterPhoneDirectCaller.callNumber(state.phoneNumber ?? '');
            add(ToggleIsCallInitiatingEvent(true));
            emit(state.copyWith(ivrState: event.isDirectCallEvent ? IvrState.initial : IvrState.success, isContactOption: false));
            add(UpdateActionButtonCountEvent(event.id, ContactType.call));
          } else {
            final call = Uri.parse('tel:${state.phoneNumber}');
            if (await canLaunchUrl(call)) {
              add(UpdateActionButtonCountEvent(event.id, ContactType.call));

              emit(state.copyWith(ivrState: event.isDirectCallEvent ? IvrState.initial : IvrState.success, isContactOption: false));
              launchUrl(call);
              add(ToggleIsCallInitiatingEvent(true));
            } else {
              throw 'Could not launch $call';
            }
          }
        }
      } else {
        final call = Uri.parse('tel:${state.phoneNumber}');
        if (await canLaunchUrl(call)) {
          add(UpdateActionButtonCountEvent(event.id, ContactType.call));

          emit(state.copyWith(ivrState: event.isDirectCallEvent ? IvrState.initial : IvrState.success, isContactOption: false));
          launchUrl(call);
          add(ToggleIsCallInitiatingEvent(true));
        } else {
          throw 'Could not launch $call';
        }
      }
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      final call = Uri.parse('tel:${state.phoneNumber}');
      if (await canLaunchUrl(call)) {
        add(UpdateActionButtonCountEvent(event.id, ContactType.call));

        emit(state.copyWith(ivrState: event.isDirectCallEvent ? IvrState.initial : IvrState.success, isContactOption: false));
        launchUrl(call);
      } else {
        throw 'Could not launch $call';
      }
    }
  }

  FutureOr<void> _onUpdateActionButtonCount(UpdateActionButtonCountEvent event, Emitter<DataDetailsState> emit) async {
    final result = await _updateProspectActionCountUseCase.call(Tuple2(
      UpdateCountModel(id: event.id, contactType: event.leadAction),
      ProspectCommunicationModel(contactType: event.leadAction, prospectId: event.id, message: event.message),
    ));

    if (state.communicationActions == null) return;
    result.fold(
      (failure) {},
      (success) {
        if (success ?? false) {
          switch (event.leadAction.name) {
            case 'call':
              communicationActions[0] = communicationActions[0].copyWith(value: state.communicationActions![0].value + 1);
              break;
            case 'sms':
              communicationActions[1] = communicationActions[1].copyWith(value: state.communicationActions![1].value + 1);
              break;
            case 'whatsApp':
              communicationActions[2] = communicationActions[2].copyWith(value: state.communicationActions![2].value + 1);
              break;
            case 'email':
              communicationActions[3] = communicationActions[3].copyWith(value: state.communicationActions![3].value + 1);
              break;
          }
          add(UpdateProspectEvent());
          emit(state.copyWith(communicationActions: [...communicationActions], ivrState: IvrState.initial));
        }
      },
    );
  }

  FutureOr<void> _onUpdateProspect(UpdateProspectEvent event, Emitter<DataDetailsState> emit) async {
    if (state.prospectEntity == null) return;
    ProspectEntity? prospectEntity = state.prospectEntity;
    emit(state.copyWith(ivrState: IvrState.initial, successMessage: ''));
    add(GetProspectDetailsEvent(prospectEntity?.id));
  }

  FutureOr<void> _onProspectReAssign(ProspectReAssignEvent event, Emitter<DataDetailsState> emit) async {
    final reAssign = await _prospectReAssignUseCase.call(DataReAssignModel(ids: [event.id], userIds: [event.userId]));
    reAssign.fold(
      (failure) {
        DialogManager().hideTransparentProgressDialog();
        LeadratCustomSnackbar.show(navigatorKey: MyApp.navigatorKey, type: SnackbarType.error, message: 'unable to re-assign data');
      },
      (success) {
        if (success ?? false) {
          LeadratCustomSnackbar.show(navigatorKey: MyApp.navigatorKey, message: 'Data re-assign successfully');
          emit(state.copyWith(reAssignState: ReAssignState.success, successMessage: "prospect assign to ${event.userModel.fullName}"));
          add(UpdateProspectEvent(prospect: ProspectEntity(assignTo: event.userModel)));
        }
      },
    );
  }

  FutureOr<void> _onPostIvrConfiguration(PostIvrConfigurationEvent event, Emitter<DataDetailsState> emit) async {
    IvrConfigModel? ivrConfigModel = this.ivrConfigModel?.copyWith(callerIdOrVirtualNumber: event.virtualNumber);
    IvrResponseModel? ivrResponseModel = await _ivrService.postIvrConfiguration(ivrConfigModel ?? IvrConfigModel());
    if (ivrResponseModel?.success ?? false) {
      add(UpdateActionButtonCountEvent(event.id, ContactType.call));
      emit(state.copyWith(ivrState: IvrState.initial, isContactOption: false));
    } else {
      emit(state.copyWith(ivrState: IvrState.error, errorMessage: ivrResponseModel?.message, isContactOption: true));
    }
  }

  FutureOr<void> _onSwipeToNextData(SwipeToNextDataEvent event, Emitter<DataDetailsState> emit) async {
    if (_isPaginationInProgress) return;

    if (state.prospectItems != null) {
      var prospectItems = state.prospectItems;
      final currentIndex = prospectItems?.indexWhere((item) => item?.id == state.prospectEntity?.id) ?? -1;

      if (currentIndex + 1 < (prospectItems?.length ?? 0)) {
        final nextData = prospectItems?[currentIndex + 1];
        if (nextData != null) add(DataDetailsInitialEvent(prospect: nextData, prospectItems: state.prospectItems, selectedItem: state.selectedItem));
      } else if (state.totalCount == (currentIndex + 1)) {
        emit(state.copyWith(successMessage: "No more items available"));
      } else {
        _handleManageDataPagination(currentIndex);
      }
    }
  }

  FutureOr<void> _onSwipeToPreviousData(SwipeToPreviousDataEvent event, Emitter<DataDetailsState> emit) async {
    if (_isPaginationInProgress) return;
    if (state.prospectItems == null) return;
    final prospectItems = state.prospectItems ?? [];
    final currentIndex = prospectItems.indexWhere((item) => item?.id == state.prospectEntity?.id);
    if (currentIndex == -1) {
      emit(state.copyWith(errorMessage: 'Current data not found in the list', successMessage: ''));
      return;
    }

    if (currentIndex > 0) {
      final previousData = prospectItems[currentIndex - 1];
      add(DataDetailsInitialEvent(prospect: previousData, prospectItems: prospectItems, selectedItem: state.selectedItem));
    } else {
      emit(state.copyWith(successMessage: 'No previous item available'));
    }
  }

  void _handleManageDataPagination(int currentIndex) {
    if (_isPaginationInProgress) return;
    _isPaginationInProgress = true;

    try {
      manageDataBloc.add(LoadMoreEvent());
      add(ShowLoadingDialogEvent(true));
      StreamSubscription? manageDataSubscription;
      manageDataSubscription = manageDataBloc.stream.listen((manageDataState) {
        if (manageDataState.pageState == PageState.success) {
          add(ShowLoadingDialogEvent(false));
          _isPaginationInProgress = false;
          if (manageDataState.prospectEntityList != null && manageDataState.prospectEntityList!.isNotEmpty) {
            final newList = manageDataState.prospectEntityList!;
            final newCurrentIndex = newList.indexWhere((item) => item?.id == state.prospectEntity?.id);
            if (newCurrentIndex != -1 && newCurrentIndex + 1 < newList.length) {
              final nextData = newList[newCurrentIndex + 1];
              add(DataDetailsInitialEvent(prospect: nextData, prospectItems: newList, selectedItem: state.selectedItem));
            } else if (newList.length > (currentIndex + 1)) {
              final nextData = newList[currentIndex + 1];
              add(DataDetailsInitialEvent(prospect: nextData, prospectItems: newList, selectedItem: state.selectedItem));
            }
            manageDataSubscription?.cancel();
          }
        } else {
          _isPaginationInProgress = false;
          add(ShowLoadingDialogEvent(false));
        }
      });
    } catch (exception) {
      _isPaginationInProgress = false;
      add(ShowLoadingDialogEvent(false));
      rethrow;
    }
  }

  FutureOr<void> _onShowLoadingDialog(ShowLoadingDialogEvent event, Emitter<DataDetailsState> emit) {
    emit(state.copyWith(showLoadingProgress: event.isVisible, successMessage: ''));
  }

  FutureOr<void> _onUpdateProspectCallLog(UpdateProspectCallLogEvent event, Emitter<DataDetailsState> emit) async {
    final userDetails = _usersDataRepository.getLoggedInUser();
    await _updateProspectCallLogUseCase(ProspectCallLogModel(
      callDirection: CallDirection.outgoing,
      callStatus: CallStatusEnum.answered,
      prospectId: state.prospectEntity?.id,
      callStartTime: event.startTime.toUtc(),
      callEndTime: event.endTime.toUtc(),
      callDuration: event.duration,
      userId: userDetails?.userId,
    ));
  }

  FutureOr<void> _onToggleIsCallInitiating(ToggleIsCallInitiatingEvent event, Emitter<DataDetailsState> emit) async {
    if (!Platform.isIOS) return;
    if (!(_globalSettingModel?.isCallDetectionActivated ?? false)) return;
    bool isProspectAssignedToSelf = true;
    if (_globalSettingModel?.isAssignedCallLogsEnabled ?? false) {
      final userDetails = _usersDataRepository.getLoggedInUser();
      isProspectAssignedToSelf = state.prospectEntity?.assignedUser?.id == userDetails?.userId;
    }
    if (!isProspectAssignedToSelf && (_globalSettingModel?.isAssignedCallLogsEnabled ?? false)) return;

    if (event.value) {
      await _cleanUp();
      _microphoneStatusService.startMonitoring();

      _microphoneStatusSubscription = _microphoneStatusService.microphoneStatusStream.listen((isMicActive) {
        if (isMicActive && _callStartTime == null) {
          _callStartTime = DateTime.now();
        } else if (!isMicActive && _callStartTime != null && _callEndTime == null) {
          _callEndTime = DateTime.now();
          final duration = _callEndTime?.difference(_callStartTime!);
          add(ToggleIsCallInitiatingEvent(false));
          if (duration != null && _callEndTime != null && _callStartTime != null) {
            add(UpdateProspectCallLogEvent(duration: double.tryParse(duration.inSeconds.toString()) ?? 0, endTime: _callEndTime!, startTime: _callStartTime!));
          }
        }
      });
    } else {
      await _cleanUp();
      _microphoneStatusService.stopMonitoring();
    }
  }

  Future<void> _cleanUp() async {
    await _microphoneStatusSubscription?.cancel();
    _microphoneStatusSubscription = null;
    _callStartTime = null;
    _callEndTime = null;
  }

  FutureOr<void> _onDisposeProspectInfo(DisposeProspectInfoEvent event, Emitter<DataDetailsState> emit) async {
    if (_callStartTime == null) await _cleanUp();
  }

  FutureOr<void> _onGetProspectCommunications(GetProspectCommunicationsEvent event, Emitter<DataDetailsState> emit) async {
    final communicationsResult = await _getDataCommunicationUseCase(event.dataIds);
    communicationsResult.fold(
      (failure) => null,
      (success) {
        if (success?.isNotEmpty ?? false) {
          final contactRecords = success?.entries.firstOrNull?.value;
          if (contactRecords == null) return;
          for (final key in contactRecords.keys) {
            switch (key) {
              case 'Call':
                communicationActions[0] = communicationActions[0].copyWith(value: contactRecords[key]);
                break;
              case 'SMS':
                communicationActions[1] = communicationActions[1].copyWith(value: contactRecords[key]);
                break;
              case 'WhatsApp':
                communicationActions[2] = communicationActions[2].copyWith(value: contactRecords[key]);
                break;
              case 'Email':
                communicationActions[3] = communicationActions[3].copyWith(value: contactRecords[key]);
                break;
            }
          }
          emit(state.copyWith(communicationActions: [...communicationActions]));
        }
      },
    );
  }
}
