import 'package:leadrat/core_main/common/entites/property_type_entity.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/property_enums/listing_status.dart';
import 'package:leadrat/core_main/enums/property_enums/property_status.dart';
import 'package:leadrat/features/properties/domain/entities/get_all_property_entity.dart';

import '../../../../core_main/enums/property_enums/brokerage_unit.dart';
import '../../../../core_main/extensions/string_extension.dart';

class ItemListingManagementModel {
  String subTitle = '';
  String description = '';
  String brokerageDescription = '';

  String get propertyImage => property?.images?.firstOrNull?.imageFilePath ?? '';

  String get expectedPriceDesc => property?.expectedPrice?.toDouble().budgetToWord(property?.currency) ?? '--';

  GetAllPropertyEntity? property;

  String? get id => property?.id;

  bool isSelected;

  bool isPropertyListed = false;
  bool isPropertySold = false;

  ItemListingManagementModel({this.property, this.isSelected = false}) {
    subTitle = getSubTitle(bhkType: property?.bhkType, noOfBhks: property?.noOfBHKs, propertyChildModel: property?.propertyType);
    description = getDescription(property?.propertyType, property?.area, property?.areaUnit);
    brokerageDescription = getBrokerage(property?.brokerageCurrency, property?.brokerage);
    isPropertyListed = property?.listingStatus == ListingStatus.approved;
    isPropertySold = property?.listingStatus == ListingStatus.sold || property?.status == PropertyStatus.sold;
  }

  ItemListingManagementModel copyWith({
    GetAllPropertyEntity? propertyItem,
    bool? isSelected,
  }) {
    return ItemListingManagementModel(
      property: propertyItem ?? this.property,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  String getDescription(PropertyTypeEntity? propertyChildModel, double? area, String? areaUnits) {
    String description = '';
    if (propertyChildModel != null && propertyChildModel.displayName != null) {
      description = '${propertyChildModel.displayName}';
    }
    if (area != null) {
      description = '$description${description.isEmpty ? "" : ","} ${area.doubleToWord()}';
      if (areaUnits.isNotNullOrEmpty()) {
        description = '$description $areaUnits';
      }
    }

    return description;
  }

  String getBrokerage(String? brokerageUnit, double? brokerage) {
    String brokerageDescription = '';
    String brokerageValue = '0';
    if (brokerage != null && brokerage != 0) {
      brokerageValue = (brokerage % 1 == 0) ? brokerage.toInt().toString() : brokerage.toString();
    }
    if (brokerageValue != '0') {
      if (brokerageUnit == BrokerageUnit.rupees.description) {
        brokerageDescription = '$brokerageUnit $brokerageValue';
      } else if (brokerageUnit == BrokerageUnit.percentage.description) {
        brokerageDescription = '$brokerageValue $brokerageUnit';
      } else {
        brokerageDescription = '$brokerageUnit $brokerageValue';
      }
    }

    return brokerageDescription;
  }
}

String getSubTitle({double? noOfBhks, BHKType? bhkType, PropertyTypeEntity? propertyChildModel}) {
  String subTitle = '';
  if (noOfBhks != null && noOfBhks != 0) {
    subTitle = noOfBhks == 0.5 ? 'studio' : "${convertNumber(noOfBhks)} BR";
  }
  if (bhkType != BHKType.none) {
    subTitle = '$subTitle ${bhkType?.description} ';
  }
  if (propertyChildModel != null && propertyChildModel.childType != null && propertyChildModel.childType?.displayName != null) {
    subTitle = '$subTitle${propertyChildModel.childType?.displayName}';
  }
  return subTitle;
}

String convertNumber(double number) {
  if (number % 1 == 0) {
    return number.toInt().toString();
  } else {
    return number.toStringAsFixed(1);
  }
}
