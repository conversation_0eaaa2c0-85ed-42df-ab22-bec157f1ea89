import 'package:leadrat/core_main/enums/common/module_name.dart';
import 'package:leadrat/core_main/remote/leadrat_rest_service.dart';
import 'package:leadrat/core_main/remote/rest_response/paged_rest_response.dart';
import 'package:leadrat/core_main/remote/rest_response/rest_response_wrapper.dart';
import 'package:leadrat/core_main/resources/common/rest_resources.dart';
import 'package:leadrat/features/data_management/data/data_source/remote/data_management_remote_data_source.dart';
import 'package:leadrat/features/data_management/data/models/add_prospect_model.dart';
import 'package:leadrat/features/data_management/data/models/convert_to_lead_model.dart';
import 'package:leadrat/features/data_management/data/models/data_filter_model.dart';
import 'package:leadrat/features/data_management/data/models/data_history_model.dart';
import 'package:leadrat/features/data_management/data/models/data_reassign_model.dart';
import 'package:leadrat/features/data_management/data/models/notes_model.dart';
import 'package:leadrat/features/data_management/data/models/prospect_call_log_model.dart';
import 'package:leadrat/features/data_management/data/models/prospect_communication_model.dart';
import 'package:leadrat/features/data_management/data/models/prospect_model.dart';
import 'package:leadrat/features/data_management/data/models/prospect_source_model.dart';
import 'package:leadrat/features/data_management/data/models/prospect_sub_source_model.dart';
import 'package:leadrat/features/data_management/data/models/status_model.dart';
import 'package:leadrat/features/data_management/data/models/template_model.dart';
import 'package:leadrat/features/data_management/data/models/update_count_model.dart';
import 'package:leadrat/features/data_management/data/models/update_notes_model.dart';
import 'package:leadrat/features/data_management/data/models/update_prospect_status_model.dart';
import 'package:leadrat/features/lead/data/models/get_lead_model.dart';

import '../../models/prospect_count_filter_model.dart';

class DataManagementRemoteDataSourceImpl extends LeadratRestService implements DataManagementRemoteDataSource {
  @override
  Future<PagedResponse<ProspectModel?, int>> getAllProspect(int pageNo, String filterType, DataFilterModel model) async {
    final restResponse = createGetRequest('${DataManagementRestResources.getAllProspect}?PageNumber=$pageNo&PageSize=10&CustomFilterIds=$filterType${model.toString()}');

    final response = await executeRequestAsync<PagedResponse<ProspectModel?, int>>(
      restResponse,
      (json) => PagedResponse<ProspectModel?, int>.fromJson(json, (items) => fromJsonObject(items, ProspectModel.fromJson), (data) => data as int),
    );
    return response;
  }

  @override
  Future<List<ProspectCountFilterModel>?> getAllProspectCount(DataFilterModel model) async {
    final restResponse = createGetRequest('${DataManagementRestResources.getAllProspectCount}?PageNumber=1&PageSize=10${model.toString()}');
    final response = await executeRequestAsync<ResponseWrapper<List<ProspectCountFilterModel>>>(
      restResponse,
      (json) => ResponseWrapper<List<ProspectCountFilterModel>>.fromJson(
        json,
        (data) => fromJsonList(data, ProspectCountFilterModel.fromJson),
      ),
    );
    return response.data;
  }

  @override
  Future<ProspectModel?> getProspectDetails(String id) async {
    final restResponse = createGetRequest(DataManagementRestResources.getProspectById(id));
    final response = await executeRequestAsync<ResponseWrapper<ProspectModel?>>(
      restResponse,
      (json) => ResponseWrapper<ProspectModel?>.fromJson(json, (data) => fromJsonObject<ProspectModel>(data, ProspectModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<Map<DateTime, List<NotesModel>?>?> getAllProspectNotes(String id) async {
    final restResponse = createGetRequest(DataManagementRestResources.getProspectNotesById(id));
    final response = await executeRequestAsync<ResponseWrapper<Map<DateTime, List<NotesModel>>>>(
      restResponse,
      (json) => ResponseWrapper<Map<DateTime, List<NotesModel>>>.fromJson(
        json,
        (data) => data.map<DateTime, List<NotesModel>>(
          (key, value) => MapEntry(
            DateTime.parse(key as String),
            (value as List<dynamic>).map((item) => NotesModel.fromJson(item as Map<String, dynamic>)).toList(),
          ),
        ),
      ),
    );
    return response.data;
  }

  @override
  Future<bool?> putProspectNotes(String id, UpdateNotesModel model) async {
    final restResponse = createPutRequest(DataManagementRestResources.putProspectNotesById(id), body: model.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restResponse,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );
    return response.data;
  }

  @override
  Future<int?> getCallThrough(String userId) async {
    final restResponse = createGetRequest(UsersRestResources.getCallThrough(userId));
    final response = await executeRequestAsync<ResponseWrapper<int?>>(
      restResponse,
      (json) => ResponseWrapper<int?>.fromJson(json, (data) => data as int),
    );
    return response.data;
  }

  @override
  Future<bool?> updateProspectActionCount(String id, UpdateCountModel model) async {
    final restResponse = createPutRequest(DataManagementRestResources.updateProspectActionCount(id), body: model.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restResponse,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );
    return response.data;
  }

  @override
  Future<List<TemplateModel?>?> getAllTemplate(ModuleName module) async {
    final restResponse = createGetRequest(TemplateResources.getTemplatesByModule(module.index));
    final response = await executeRequestAsync<ResponseWrapper<List<TemplateModel?>?>>(
      restResponse,
      (json) => ResponseWrapper<List<TemplateModel?>?>.fromJson(json, (data) => fromJsonList<TemplateModel?>(data, TemplateModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<Map<String, Map<String, List<DataHistoryModel>>>?> getDataHistory(String prospectId) async {
    final restRequest = createGetRequest(DataManagementRestResources.getProspectHistoryById(prospectId));

    final response = await executeRequestAsync<DataHistoryResponseModel>(
      restRequest,
      (json) => DataHistoryResponseModel.fromJson(json),
    );

    return response.data;
  }

  @override
  Future<bool?> prospectReAssign(DataReAssignModel model) async {
    final restResponse = createPutRequest(DataManagementRestResources.updateProspectReAssign, body: model.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restResponse,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );
    return response.data;
  }

  @override
  Future<bool?> updateConvertToLead(ConvertToLeadModel model) async {
    final restResponse = createPostRequest(DataManagementRestResources.convertToLead, body: model.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restResponse,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );
    return response.data;
  }

  @override
  Future<List<StatusModel?>?> getAllProspectStatus() async {
    final restResponse = createGetRequest(DataManagementRestResources.getAllProspectStatus);
    final response = await executeRequestAsync<ResponseWrapper<List<StatusModel?>?>>(
      restResponse,
      (json) => ResponseWrapper<List<StatusModel?>?>.fromJson(json, (data) => fromJsonList(data, StatusModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<bool?> updateProspectStatus(UpdateProspectStatusModel model) async {
    try {
      final restResponse = createPutRequest(DataManagementRestResources.updateProspectStatus, body: model.toJson());

      final response = await executeRequestAsync<ResponseWrapper<bool?>>(
        restResponse,
        (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
      );
      return response.data;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<PagedResponse<ProspectModel?, int>> getProspectSearch(int pageNo, String value) async {
    final restResponse = createGetRequest('${DataManagementRestResources.searchProspect}?PageNumber=$pageNo&PageSize=10&ProspectSearch=$value');
    final response = await executeRequestAsync<PagedResponse<ProspectModel?, int>>(
      restResponse,
      (json) => PagedResponse<ProspectModel?, int>.fromJson(json, (items) => fromJsonObject(items, ProspectModel.fromJson), (data) => data as int),
    );
    return response;
  }

  @override
  Future<List<ProspectSubSourceModel?>?> getAllProspectSubSource() async {
    final restResponse = createGetRequest(DataManagementRestResources.getProspectSubSource);
    final response = await executeRequestAsync<ResponseWrapper<List<ProspectSubSourceModel?>?>>(
      restResponse,
      (json) => ResponseWrapper<List<ProspectSubSourceModel?>?>.fromJson(json, (data) => fromJsonList(data, ProspectSubSourceModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getAllProspectLocation() async {
    final restResponse = createGetRequest(DataManagementRestResources.getProspectLocation);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restResponse,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<LeadContactModel?> getProspectByContactNo(String countryCode, String contactNo) async {
    final restRequest = createGetRequest(DataManagementRestResources.getProspectByContactNumber(contactNo, countryCode.replaceAll('+', '%2B')));
    final response = await executeRequestAsync<ResponseWrapper<LeadContactModel?>>(
      restRequest,
      (json) => ResponseWrapper<LeadContactModel?>.fromJson(json, (data) => fromJsonObject(data, LeadContactModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<String?> addProspect(AddProspectModel model) async {
    final restRequest = createPostRequest(DataManagementRestResources.createProspect, body: model.toJson());

    final response = await executeRequestAsync<ResponseWrapper<String?>>(restRequest, (json) => ResponseWrapper<String?>.fromJson(json, (data) => data as String));
    return response.data;
  }

  @override
  Future<String?> updateProspect(UpdateProspectModel model) async {
    final restRequest = createPutRequest('${DataManagementRestResources.createProspect}?id=${model.id}', body: model.toJson());
    final response = await executeRequestAsync<ResponseWrapper<String?>>(restRequest, (json) => ResponseWrapper<String?>.fromJson(json, (data) => data as String));
    return response.data;
  }

  @override
  Future<List<ProspectSourceModel?>?> getAllProspectSource() async {
    final restResponse = createGetRequest(DataManagementRestResources.getProspectSource);
    final response = await executeRequestAsync<ResponseWrapper<List<ProspectSourceModel?>?>>(
      restResponse,
      (json) => ResponseWrapper<List<ProspectSourceModel?>?>.fromJson(json, (data) => fromJsonList(data, ProspectSourceModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<List<DataHistoryModel>?> getDataHistoryBasedOnTimeZone(String prospectId) async {
    final restRequest = createGetRequest(DataManagementRestResources.getProspectHistoryBasedOnTimeZoneById(prospectId));
    final response = await executeRequestAsync<ResponseWrapper<List<DataHistoryModel>?>>(
      restRequest,
      (json) => ResponseWrapper<List<DataHistoryModel>?>.fromJson(json, (data) => fromJsonList(data, DataHistoryModel.fromJson)),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getCurrencies() async {
    final restResponse = createGetRequest(DataManagementRestResources.currency);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restResponse,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getCommunities() async {
    final restRequest = createGetRequest(DataManagementRestResources.communities);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getCountries() async {
    final restRequest = createGetRequest(DataManagementRestResources.countries);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getSubCommunities() async {
    final restRequest = createGetRequest(DataManagementRestResources.subCommunities);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getTowerNames() async {
    final restRequest = createGetRequest(DataManagementRestResources.towerName);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<bool?> updateProspectTemplate(ProspectCommunicationModel prospectCommunicationModel) async {
    final restResponse = createPostRequest(DataManagementRestResources.prospectTemplate, body: prospectCommunicationModel.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restResponse,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getProspectNationality() async {
    final restRequest = createGetRequest(DataManagementRestResources.getProspectNationality);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getProspectClusterNames() async {
    final restRequest = createGetRequest(DataManagementRestResources.getProspectClusterNames);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getProspectUnitNames() async {
    final restRequest = createGetRequest(DataManagementRestResources.getProspectUnitNames);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<bool?> updateProspectCallLog(ProspectCallLogModel prospectCallLogModel) async {
    final restRequest = createPostRequest(ProspectCallLogRestResources.prospectCallLog, body: prospectCallLogModel.toJson());
    final response = await executeRequestAsync<ResponseWrapper<bool?>>(
      restRequest,
      (json) => ResponseWrapper<bool?>.fromJson(json, (data) => data as bool),
    );
    return response.data;
  }

  @override
  Future<List<String>?> getProspectExcelData() async {
    final restRequest = createGetRequest(DataManagementRestResources.uploadTypeNames);
    final response = await executeRequestAsync<ResponseWrapper<List<String>?>>(
      restRequest,
      (json) => ResponseWrapper<List<String>?>.fromJson(json, (data) => (data as List).map((item) => item as String).toList()),
    );
    return response.data;
  }

  @override
  Future<Map<String, Map<String, int>>?> getDataCommunications(List<String> dataIds) async {
    var dataIdPath = "";
    if (dataIds.isNotEmpty) {
      dataIdPath = dataIds.map((id) => "ProspectIds=$id").join('&');
    }

    final restRequest = createGetRequest(DataManagementRestResources.dataCommunications(dataIdPath));
    final response = await executeRequestAsync<ResponseWrapper<Map<String, Map<String, int>>>>(
      restRequest,
      (json) => ResponseWrapper<Map<String, Map<String, int>>>.fromJson(
        json,
        (data) => (data as Map<String, dynamic>).map(
          (key, value) => MapEntry(key, (value as Map<String, dynamic>).map((k, v) => MapEntry(k, v as int))),
        ),
      ),
    );
    return response.data;
  }

  @override
  Future<bool?> checkProspectAssignedByProspectId(String prospectId) async {
    try {
      final restRequest = createGetRequest(DataManagementRestResources.checkProspectAssignedByProspectId(prospectId));
      final response = await executeRequestAsync<bool?>(restRequest, (json) => json as bool);
      return response;
    } catch (e) {
      rethrow;
    }
  }
}
