part of 'gallery_tab_bloc.dart';

@immutable
sealed class GalleryTabEvent {}

final class InitGalleryTabEvent extends GalleryTabEvent {
  final AddPropertyModel addPropertyModel;

  InitGalleryTabEvent(this.addPropertyModel);
}

final class AddPhotosEvent extends GalleryTabEvent {
  final SelectFileEnum selectFileEnum;

  AddPhotosEvent(this.selectFileEnum);
}

final class RemovePhotosEvent extends GalleryTabEvent {
  final String? photoPath;

  RemovePhotosEvent(this.photoPath);
}

final class AddBrochuresEvent extends GalleryTabEvent {
  final SelectFileEnum selectFileEnum;

  AddBrochuresEvent(this.selectFileEnum);
}

final class WaterMarkEvent extends GalleryTabEvent {}

final class RemoveBrochuresEvent extends GalleryTabEvent {
  final String? brochurePath;

  RemoveBrochuresEvent(this.brochurePath);
}

final class NavigateBackToAmenitiesEvent extends GalleryTabEvent {}

final class AddPropertyEvent extends GalleryTabEvent {}

final class ClearGalleryTabStateEvent extends GalleryTabEvent {}

final class Add360VideoUrlEvent extends GalleryTabEvent {
  final String videoUrl;

  Add360VideoUrlEvent(this.videoUrl);
}

final class AddThirdPartyVideoUrlEvent extends GalleryTabEvent {
  final String thirdPartyUrl;

  AddThirdPartyVideoUrlEvent(this.thirdPartyUrl);
}

final class Remove360VideoUrlEvent extends GalleryTabEvent {
  final ItemSimpleModel<String> item;

  Remove360VideoUrlEvent(this.item);
}

final class RemoveThirdPartyVideoUrlEvent extends GalleryTabEvent {
  final ItemSimpleModel<String> item;

  RemoveThirdPartyVideoUrlEvent(this.item);
}

final class Toggle360VideoUrlEvent extends GalleryTabEvent {
  final ItemSimpleModel<String> item;

  Toggle360VideoUrlEvent(this.item);
}

final class ToggleThirdPartyEvent extends GalleryTabEvent {
  final ItemSimpleModel<String> item;

  ToggleThirdPartyEvent(this.item);
}

final class UpdatePhotoOrderEvent extends GalleryTabEvent {
  final List<SelectableItem<File>> photos;

  UpdatePhotoOrderEvent(this.photos);
}
