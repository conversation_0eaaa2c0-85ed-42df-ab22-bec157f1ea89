import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/constants/app_analytics_constants.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/repository/masterdata_repository.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';
import 'package:leadrat/features/lead/domain/usecase/get_matching_properties_use_case.dart';
import 'package:leadrat/features/lead/presentation/bloc/lead_info_bloc/lead_info_bloc.dart';
import 'package:leadrat/features/lead/presentation/item/item_matching_properties_model.dart';
import 'package:leadrat/features/properties/domain/entities/get_property_entity.dart';

part 'matching_properties_event.dart';
part 'matching_properties_state.dart';

class MatchingPropertiesBloc extends Bloc<MatchingPropertiesEvent, MatchingPropertiesState> {
  final GetMatchingPropertiesUseCase _getMatchingPropertiesUseCase;
  final MasterDataRepository _masterDataRepository;
  List<ItemMatchingPropertiesModel> _allMatchingProperties = [];
  List<MasterAreaUnitsModel?>? masterAreaUnits;
  int pageNumber = 1;

  MatchingPropertiesBloc(this._masterDataRepository, this._getMatchingPropertiesUseCase) : super(const MatchingPropertiesState()) {
    on<MatchingPropertiesInitialEvent>(_onMatchingPropertiesInitial);
    on<ToggleLeadDetailsEvent>(_onToggleLeadDetails);
    on<SelectRadiusEvent>(_onSelectRadius);
    on<SelectMatchingPropertyEvent>(_onSelectMatchingProperty);
    on<OnSearchPropertiesEvent>(_onOnSearchProperties);
    on<ClearMatchingRadiusEvent>(_onClearMatchingRadius);
    on<LoadMoreMatchingPropertiesEvent>(_onLoadMoreMatchingProperties);
    on<SelectAllMatchingPropertiesEvent>(_onSelectAllMatchingProperties);
  }

  FutureOr<void> _onMatchingPropertiesInitial(MatchingPropertiesInitialEvent event, Emitter<MatchingPropertiesState> emit) async {
    pageNumber = 1;
    masterAreaUnits = masterAreaUnits ?? await _masterDataRepository.getAreaUnits();
    final areaUnits = masterAreaUnits?.map((e) => e?.toEntity()).nonNulls;
    final selectableRadius = [2, 4, 6, 8, 10, 14, 16, 18, 20].map((e) => SelectableItem<double>(title: "${e.toString()} kms", value: e.toDouble())).toList();
    final matchingProperties = event.properties.map((property) => ItemMatchingPropertiesModel(properties: property, lead: event.leadEntity, areaUnits: areaUnits)).toList();
    _allMatchingProperties = matchingProperties;
    emit(state.copyWith(
      totalMatchingProperties: event.totalMatchingProperties,
      pageState: PageState.success,
      errorMessage: null,
      matchingProperties: matchingProperties,
      leadEntity: event.leadEntity,
      isLeadDetailsVisible: false,
      selectableRadiusInKms: selectableRadius,
      selectedMatchingProperties: [],
      selectedPropertyIds: null,
      selectedPropertyTitle: '',
      selectedRadius: null,
      leadInfoState: event.leadInfoState,
      globalSettingModel: event.globalSettingModel,
    ));
  }

  FutureOr<void> _onLoadMoreMatchingProperties(LoadMoreMatchingPropertiesEvent event, Emitter<MatchingPropertiesState> emit) async {
    if (state.matchingProperties.length < state.totalMatchingProperties && !state.isLoadingMore) {
      emit(state.copyWith(isLoadingMore: true));
      pageNumber += 1;
      final getMatchingProperties = await _getMatchingPropertiesUseCase(GetMatchingPropertiesUseCaseParams(leadId: state.leadEntity?.id ?? "", pageSize: 10, pageNumber: pageNumber, radiusInKms: state.selectedRadius?.value));
      getMatchingProperties.fold((failure) => null, (success) {
        final areaUnits = masterAreaUnits?.map((e) => e?.toEntity()).nonNulls;
        final matchingProperties = success?.items.map((property) => ItemMatchingPropertiesModel(properties: property!, lead: state.leadEntity!, areaUnits: areaUnits)).toList();
        final newMatchingProperties = [...state.matchingProperties, ...?matchingProperties];
        final isAllPropertiesSelected = state.matchingProperties.length == newMatchingProperties.length;
        emit(state.copyWith(matchingProperties: newMatchingProperties, pageState: PageState.success, isLoadingMore: false, isAllPropertiesSelected: isAllPropertiesSelected));
      });
    }
  }

  FutureOr<void> _onToggleLeadDetails(ToggleLeadDetailsEvent event, Emitter<MatchingPropertiesState> emit) {
    emit(state.copyWith(isLeadDetailsVisible: !state.isLeadDetailsVisible));
    getIt<AppAnalysisRepository>().sendAppAnalysis(name: state.isLeadDetailsVisible ? AppAnalyticsConstants.mobileMatchingPropertyButtonSeeLessClick : AppAnalyticsConstants.mobileMatchingPropertyButtonSeeMoreClick);
  }

  FutureOr<void> _onSelectRadius(SelectRadiusEvent event, Emitter<MatchingPropertiesState> emit) async {
    pageNumber = 1;
    emit(state.copyWith(selectedRadius: event.selectedItem, pageState: PageState.loading, selectedMatchingProperties: []));
    final getMatchingProperties = await _getMatchingPropertiesUseCase(GetMatchingPropertiesUseCaseParams(leadId: state.leadEntity?.id ?? "", radiusInKms: event.selectedItem.value, pageSize: 10, pageNumber: 1));
    getMatchingProperties.fold((failure) => null, (success) {
      final areaUnits = masterAreaUnits?.map((e) => e?.toEntity()).nonNulls;
      final matchingProperties = success?.items.map((property) => ItemMatchingPropertiesModel(properties: property!, lead: state.leadEntity!, areaUnits: areaUnits)).toList();
      emit(state.copyWith(matchingProperties: matchingProperties, pageState: PageState.success, totalMatchingProperties: success?.totalCount.round() ?? 0, selectedPropertyIds: [], selectedMatchingProperties: []));
    });
  }

  FutureOr<void> _onSelectMatchingProperty(SelectMatchingPropertyEvent event, Emitter<MatchingPropertiesState> emit) {
    List<ItemMatchingPropertiesModel> selectedProperties = state.selectedMatchingProperties;
    List<String> selectedPropertiesId = [];
    String selectedTitle = '';
    final selectedProperty = event.selectedProperty.copyWith(isSelected: !event.selectedProperty.isSelected);
    final updatedProperties = state.matchingProperties.map((e) => e == event.selectedProperty ? selectedProperty : e).toList();
    if (selectedProperty.isSelected) {
      selectedProperties.add(selectedProperty);
    } else {
      selectedProperties.removeWhere((element) => element == event.selectedProperty);
    }
    int i = 0;
    for (var element in selectedProperties) {
      selectedPropertiesId.add(element.properties.property?.id ?? "");
      selectedTitle += (i++ > 0) ? ", ${element.properties.property?.title ?? ''}" : element.properties.property?.title ?? '';
    }
    final isAllPropertiesSelected = state.matchingProperties.length == selectedProperties.length;
    emit(state.copyWith(
      matchingProperties: updatedProperties,
      selectedMatchingProperties: selectedProperties,
      selectedPropertyIds: selectedPropertiesId,
      selectedPropertyTitle: selectedTitle,
      isAllPropertiesSelected: isAllPropertiesSelected,
    ));
  }

  FutureOr<void> _onOnSearchProperties(OnSearchPropertiesEvent event, Emitter<MatchingPropertiesState> emit) async {
    if (event.searchText.isEmpty) {
      emit(state.copyWith(matchingProperties: _allMatchingProperties));
      return;
    }
    final searchedProperties = _allMatchingProperties.where((element) => element.properties.property?.title?.toLowerCase().contains(event.searchText) ?? false).nonNulls.toList();
    emit(state.copyWith(matchingProperties: searchedProperties));
  }

  FutureOr<void> _onClearMatchingRadius(ClearMatchingRadiusEvent event, Emitter<MatchingPropertiesState> emit) async {
    emit(state.copyWith(resetSelectedRadius: true, pageState: PageState.loading, selectedMatchingProperties: []));
    pageNumber = 1;
    final getMatchingProperties = await _getMatchingPropertiesUseCase(GetMatchingPropertiesUseCaseParams(leadId: state.leadEntity?.id ?? "", pageSize: 10, pageNumber: 1));
    getMatchingProperties.fold((failure) => null, (success) {
      final areaUnits = masterAreaUnits?.map((e) => e?.toEntity()).nonNulls;
      final matchingProperties = success?.items.map((property) => ItemMatchingPropertiesModel(properties: property!, lead: state.leadEntity!, areaUnits: areaUnits)).toList();
      emit(state.copyWith(
        matchingProperties: matchingProperties,
        pageState: PageState.success,
        totalMatchingProperties: success?.totalCount.round() ?? 0,
        resetSelectedRadius: true,
        selectedPropertyIds: [],
        selectedMatchingProperties: [],
      ));
    });
  }

  FutureOr<void> _onSelectAllMatchingProperties(SelectAllMatchingPropertiesEvent event, Emitter<MatchingPropertiesState> emit) async {
    List<String> selectedPropertiesId = [];
    String selectedTitle = '';
    final updatedProperties = state.matchingProperties.map((e) => e.copyWith(isSelected: event.selectAll)).toList();
    int i = 0;
    List<ItemMatchingPropertiesModel> selectedProperties = event.selectAll ? updatedProperties : [];
    for (var element in selectedProperties) {
      selectedPropertiesId.add(element.properties.property?.id ?? "");
      selectedTitle += (i++ > 0) ? ", ${element.properties.property?.title ?? ''}" : element.properties.property?.title ?? '';
    }
    final isAllPropertiesSelected = state.matchingProperties.length == selectedProperties.length;
    emit(state.copyWith(
      matchingProperties: updatedProperties,
      selectedMatchingProperties: selectedProperties,
      selectedPropertyIds: selectedPropertiesId,
      selectedPropertyTitle: selectedTitle,
      isAllPropertiesSelected: isAllPropertiesSelected,
    ));
  }
}
