import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_amenity_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_type_model.dart';
import 'package:leadrat/core_main/common/data/user/models/get_all_users_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/models/budget_range_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/common/date_range.dart';
import 'package:leadrat/core_main/enums/common/no_of_br.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/property_type_enum.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/property_enums/completion_status.dart';
import 'package:leadrat/core_main/enums/property_enums/facing.dart';
import 'package:leadrat/core_main/enums/property_enums/feature.dart';
import 'package:leadrat/core_main/enums/property_enums/floors.dart';
import 'package:leadrat/core_main/enums/property_enums/furnish_status.dart';
import 'package:leadrat/core_main/enums/property_enums/listing_status.dart';
import 'package:leadrat/core_main/enums/property_enums/property_date_type.dart';
import 'package:leadrat/core_main/enums/property_enums/property_size.dart';
import 'package:leadrat/core_main/enums/property_enums/property_status.dart';
import 'package:leadrat/core_main/enums/property_enums/sale_type.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/features/projects/domain/entities/project_entity.dart';

class ListingManagementFilterModel {
  PropertyVisibility? propertyVisibility;
  EnquiryType? enquiryType;
  List<SaleType>? saleTypes;
  List<PropertyType>? propertyTypes;
  List<MasterPropertyTypeModel>? propertySubTypes;
  PropertyDateType? dateType;
  String? fromDate;
  String? toDate;
  Facing? facing;
  List<FurnishStatus>? furnishStatus;
  List<NoOfPropertiesBr>? noOfBrs;
  List<BHKType>? bhkTypes;
  BudgetRangeModel? budgetFilters;
  List<Floors>? floors;
  List<String>? locations;
  List<ProjectEntity>? projects;
  List<GetAllUsersModel>? assignTo;
  List<GetAllUsersModel>? listingOnBehalf;
  List<CustomAmenityModel>? amenities;
  PropertySize? propertySize;
  String? currency;
  List<String>? ownerName;
  List<Feature>? noOfBathrooms;
  List<Feature>? noOfLivingRooms;
  List<Feature>? noOfBalconies;
  List<Feature>? noOfBedrooms;
  List<Feature>? noOfKitchens;
  List<Feature>? noOfUtilities;
  ListingLevel? listingLevel;
  OfferingType? offeringType;
  CompletionStatus? completionStatus;
  List<String>? communities;
  List<String>? subCommunities;
  double? minPropertySize;
  double? maxPropertySize;
  MasterAreaUnitsModel? propertySizeUnit;
  double? minCarpetArea;
  double? maxCarpetArea;
  MasterAreaUnitsModel? carpetAreaUnit;
  double? minNetArea;
  double? maxNetArea;
  MasterAreaUnitsModel? netAreaUnit;
  double? minBuiltUpArea;
  double? maxBuiltUpArea;
  MasterAreaUnitsModel? builtUpAreaUnit;
  double? minSaleableArea;
  double? maxSaleableArea;
  MasterAreaUnitsModel? saleableAreaUnit;
  PossessionType? possessionTypeDateRange;
  final double? fromMinBudget;
  final double? toMinBudget;
  final double? fromMaxBudget;
  final double? toMaxBudget;
  String? minLeadCount;
  String? maxLeadCount;
  String? minProspectCount;
  String? maxProspectCount;
  DateRange? dateRange;

  ListingManagementFilterModel({
    this.propertyVisibility,
    this.enquiryType,
    this.saleTypes,
    this.propertyTypes,
    this.propertySubTypes,
    this.dateType,
    this.fromDate,
    this.toDate,
    this.facing,
    this.furnishStatus,
    this.noOfBrs,
    this.bhkTypes,
    this.budgetFilters,
    this.floors,
    this.locations,
    this.projects,
    this.assignTo,
    this.listingOnBehalf,
    this.amenities,
    this.propertySize,
    this.currency,
    this.ownerName,
    this.noOfBathrooms,
    this.noOfLivingRooms,
    this.noOfBalconies,
    this.noOfBedrooms,
    this.noOfKitchens,
    this.noOfUtilities,
    this.completionStatus,
    this.offeringType,
    this.communities,
    this.subCommunities,
    this.minPropertySize,
    this.maxPropertySize,
    this.propertySizeUnit,
    this.minCarpetArea,
    this.maxCarpetArea,
    this.carpetAreaUnit,
    this.minNetArea,
    this.maxNetArea,
    this.netAreaUnit,
    this.minBuiltUpArea,
    this.maxBuiltUpArea,
    this.builtUpAreaUnit,
    this.minSaleableArea,
    this.maxSaleableArea,
    this.saleableAreaUnit,
    this.listingLevel,
    this.possessionTypeDateRange,
    this.fromMinBudget,
    this.toMinBudget,
    this.toMaxBudget,
    this.fromMaxBudget,
    this.minLeadCount,
    this.maxLeadCount,
    this.minProspectCount,
    this.maxProspectCount,
    this.dateRange,
  });

  Future<String> getFiltersUrl() async {
    String filtersUrl = '';
    if (enquiryType != null) {
      filtersUrl = '$filtersUrl&EnquiredFor=${enquiryType?.value ?? ''}';
    }
    if (saleTypes != null) {
      saleTypes?.forEach((element) => filtersUrl = '$filtersUrl&SaleTypes=${element.value}');
    }
    if (locations != null) {
      locations?.forEach((element) => filtersUrl = '$filtersUrl&Locations=$element');
    }
    if (projects != null) {
      projects?.forEach((element) => filtersUrl = '$filtersUrl&Projects=${element.name}');
    }
    if (getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.viewAssigned) && !(getIt<UsersDataRepository>().checkHasPermission(AppModule.property, CommandType.view))) {
      filtersUrl = '$filtersUrl&UserIds=${getIt<UsersDataRepository>().getLoggedInUser()?.userId}';
    } else if (assignTo != null) {
      assignTo?.forEach((element) => filtersUrl = '$filtersUrl&UserIds=${element.id}');
    }
    if (listingOnBehalf != null) {
      listingOnBehalf?.forEach((element) => filtersUrl = '$filtersUrl&listingOnBehalf=${element.id}');
    }
    if (amenities != null) {
      amenities?.forEach((element) => filtersUrl = '$filtersUrl&Amenities=${element.id}');
    }
    if (noOfBalconies != null) {
      noOfBalconies?.forEach((element) => filtersUrl = '$filtersUrl&NoOfBalconies=${element.value}');
    }
    if (noOfBedrooms != null) {
      noOfBedrooms?.forEach((element) => filtersUrl = '$filtersUrl&NoOfBedrooms=${element.value}');
    }
    if (noOfKitchens != null) {
      noOfKitchens?.forEach((element) => filtersUrl = '$filtersUrl&NoOfKitchens=${element.value}');
    }
    if (noOfLivingRooms != null) {
      noOfLivingRooms?.forEach((element) => filtersUrl = '$filtersUrl&NoOfLivingrooms=${element.value}');
    }
    if (noOfUtilities != null) {
      noOfUtilities?.forEach((element) => filtersUrl = '$filtersUrl&NoOfUtilites=${element.value}');
    }
    if (noOfBathrooms != null) {
      noOfBathrooms?.forEach((element) => filtersUrl = '$filtersUrl&noOfBathrooms=${element.value}');
    }
    if (ownerName?.isNotEmpty ?? false) {
      ownerName?.forEach((element) => filtersUrl = '$filtersUrl&OwnerName=$element');
    }
    if (floors != null) {
      floors?.forEach((element) => filtersUrl = '$filtersUrl&NoOfFloor=${element.value}');
    }
    if (propertyTypes?.isNotEmpty ?? false) {
      propertyTypes?.forEach((element) => filtersUrl = '$filtersUrl&PropertyTypes=${element.baseId}');
    }
    if (propertySubTypes?.isNotEmpty ?? false) {
      propertySubTypes?.forEach((element) => filtersUrl = '$filtersUrl&PropertySubTypes=${element.id}');
    }
    if (furnishStatus != null) {
      furnishStatus?.forEach((element) => filtersUrl = '$filtersUrl&FurnishStatuses=${element.value}');
    }
    if (noOfBrs?.isNotEmpty ?? false) {
      noOfBrs?.forEach((element) => filtersUrl = '$filtersUrl&NoOfBHK=${element.noOfBr}');
    }
    if (bhkTypes?.isNotEmpty ?? false) {
      bhkTypes?.forEach((i) => filtersUrl += "&BHKTypes=${i.value}");
    }
    if (budgetFilters != null) {
      filtersUrl = '$filtersUrl&MaxPrice=${budgetFilters?.maxBudget}&MinPrice=${budgetFilters?.minBudget}&Currency=$currency';
    }
    if (propertySize != null) {
      filtersUrl = '$filtersUrl&PropertySize.Area=${propertySize?.value}';
    }

    if (possessionTypeDateRange == PossessionType.customDate) {
      filtersUrl += "&PossesionType=${possessionTypeDateRange?.value}&FromPossesionDate=${fromDate ?? ''}&ToPossesionDate=${toDate ?? ''}";
    }
    if (possessionTypeDateRange != null && possessionTypeDateRange != PossessionType.customDate) {
      filtersUrl += "&PossesionType=${possessionTypeDateRange?.value}";
    }
    if (facing != null) {
      filtersUrl = '$filtersUrl&Facing=${facing?.value}';
    }
    if (propertyVisibility != null) {
      filtersUrl += "&PropertyVisiblity=${propertyVisibility?.index}";
    }

    if (offeringType != OfferingType.none && offeringType != null) {
      filtersUrl += "&FirstLevelFilter=${offeringType?.index}";
    }
    if (completionStatus != CompletionStatus.none && completionStatus != null) {
      filtersUrl += "&CompletionStatus=${completionStatus?.index}";
    }
    if (communities?.isNotEmpty ?? false) {
      communities?.forEach((i) => filtersUrl += "&Communities=$i");
    }
    if (subCommunities?.isNotEmpty ?? false) {
      subCommunities?.forEach((i) => filtersUrl += "&SubCommunities=$i");
    }
    if (minPropertySize != null && minPropertySize! > 0) {
      filtersUrl += "&MinPropertySize=${minPropertySize.toString().replaceAll(".0", "")}";
    }

    if (maxPropertySize != null && maxPropertySize! > 0) {
      filtersUrl += "&MaxPropertySize=${maxPropertySize.toString().replaceAll(".0", "")}";
    }

    if (propertySizeUnit?.id.isNotNullOrEmpty() ?? false) {
      filtersUrl += "&PropertySizeUnit=${propertySizeUnit?.id}";
    }

    if (minCarpetArea != null && minCarpetArea! > 0) {
      filtersUrl += "&MinCarpetArea=${minCarpetArea.toString().replaceAll(".0", "")}";
    }

    if (maxCarpetArea != null && minCarpetArea! > 0) {
      filtersUrl += "&MaxCarpetArea=${maxCarpetArea.toString().replaceAll(".0", "")}";
    }

    if (carpetAreaUnit?.id.isNotNullOrEmpty() ?? false) {
      filtersUrl += "&CarpetAreaUnit=${carpetAreaUnit?.id}";
    }
    if (minNetArea != null && minNetArea! > 0) {
      filtersUrl += "&MinNetArea=${minNetArea.toString().replaceAll(".0", "")}";
    }

    if (maxNetArea != null && maxNetArea! > 0) {
      filtersUrl += "&MaxNetArea=${maxNetArea.toString().replaceAll(".0", "")}";
    }

    if (netAreaUnit?.id.isNotNullOrEmpty() ?? false) {
      filtersUrl += "&NetAreaUnit=${netAreaUnit?.id}";
    }

    if (minBuiltUpArea != null && minBuiltUpArea! > 0) {
      filtersUrl += "&MinBuitUpArea=${minBuiltUpArea.toString().replaceAll(".0", "")}";
    }

    if (maxBuiltUpArea != null && maxBuiltUpArea! > 0) {
      filtersUrl += "&MaxBuitUpArea=${maxBuiltUpArea.toString().replaceAll(".0", "")}";
    }

    if (builtUpAreaUnit?.id.isNotNullOrEmpty() ?? false) {
      filtersUrl += "&BuitUpAreaUnit=${builtUpAreaUnit?.id}";
    }

    if (minSaleableArea != null && minSaleableArea! > 0) {
      filtersUrl += "&MinSaleableArea=${minSaleableArea.toString().replaceAll(".0", "")}";
    }

    if (maxSaleableArea != null && maxSaleableArea! > 0) {
      filtersUrl += "&MaxSaleableArea=${maxSaleableArea.toString().replaceAll(".0", "")}";
    }

    if (saleableAreaUnit?.id.isNotNullOrEmpty() ?? false) {
      filtersUrl += "&SaleableAreaUnit=${saleableAreaUnit?.id}";
    }
    if (listingLevel != null) {
      filtersUrl += "&ListingLevel=${listingLevel?.index}";
    }
    if (dateType != null && dateType != PropertyDateType.possessionDate) {
      filtersUrl += "&DateType=${dateType?.value}&FromDate=${fromDate ?? ''}&ToDate=${toDate ?? ''}";
    }
    if (fromMinBudget != null && fromMinBudget! > 0) {
      filtersUrl += "&MinPrice=${fromMinBudget.toString().replaceAll(".0", "")}";
    }
    if (toMinBudget != null && toMinBudget! > 0) {
      filtersUrl += "&MaxPrice=${toMinBudget.toString().replaceAll(".0", "")}";
    }
    if (fromMaxBudget != null && fromMaxBudget! > 0) {
      filtersUrl += "&MaxPrice=${fromMaxBudget.toString().replaceAll(".0", "")}";
    }
    if (toMaxBudget != null && toMaxBudget! > 0) {
      filtersUrl += "&ToMaxPrice=${toMaxBudget.toString().replaceAll(".0", "")}";
    }
    if (currency != null && (toMinBudget != null || fromMinBudget != null)) {
      filtersUrl += "&Currency=$currency";
    }
    if (minLeadCount?.isNotNullOrEmpty() ?? false) {
      filtersUrl += "&MinLeadCount=${int.parse(minLeadCount!)}";
    }
    if (maxLeadCount?.isNotNullOrEmpty() ?? false) {
      filtersUrl += "&MaxLeadCount=${int.parse(maxLeadCount!)}";
    }
    if (minProspectCount?.isNotNullOrEmpty() ?? false) {
      filtersUrl += "&MinProspectCount=${int.parse(minProspectCount!)}";
    }
    if (maxProspectCount?.isNotNullOrEmpty() ?? false) {
      filtersUrl += "&MaxProspectCount=${int.parse(maxProspectCount!)}";
    }

    return filtersUrl;
  }

  DateTime getFromDate(int days) {
    int tempDays = days;
    switch (days) {
      case 0:
        tempDays = 0;
        break;
      case 1:
        tempDays = 1;
        break;
      case 2:
        tempDays = 7;
        break;
      case 3:
        tempDays = 28;
        break;
    }
    DateTime now = DateTime.now().toUserTimeZone()!;
    return now.subtract(Duration(days: tempDays));
  }

  ListingManagementFilterModel copyWith({
    PropertyVisibility? propertyVisibility,
    EnquiryType? enquiryType,
    List<SaleType>? saleTypes,
    List<PropertyType>? propertyTypes,
    List<MasterPropertyTypeModel>? propertySubTypes,
    PropertyDateType? dateType,
    String? fromDate,
    String? toDate,
    Facing? facing,
    List<FurnishStatus>? furnishStatus,
    List<NoOfPropertiesBr>? noOfBrs,
    List<BHKType>? bhkTypes,
    BudgetRangeModel? budgetFilters,
    List<Floors>? floors,
    List<String>? locations,
    List<ProjectEntity>? projects,
    List<GetAllUsersModel>? assignTo,
    List<GetAllUsersModel>? listingOnBehalf,
    List<CustomAmenityModel>? amenities,
    PropertySize? propertySize,
    String? currency,
    List<String>? ownerName,
    List<Feature>? noOfBathrooms,
    List<Feature>? noOfLivingRooms,
    List<Feature>? noOfBalconies,
    List<Feature>? noOfBedrooms,
    List<Feature>? noOfKitchens,
    List<Feature>? noOfUtilities,
    OfferingType? offeringType,
    CompletionStatus? completionStatus,
    List<String>? communities,
    List<String>? subCommunities,
    double? minPropertySize,
    double? maxPropertySize,
    MasterAreaUnitsModel? propertySizeUnit,
    double? minCarpetArea,
    double? maxCarpetArea,
    MasterAreaUnitsModel? carpetAreaUnit,
    double? minNetArea,
    double? maxNetArea,
    MasterAreaUnitsModel? netAreaUnit,
    double? minBuiltUpArea,
    double? maxBuiltUpArea,
    MasterAreaUnitsModel? builtUpAreaUnit,
    double? minSaleableArea,
    double? maxSaleableArea,
    MasterAreaUnitsModel? saleableAreaUnit,
    ListingLevel? listingLevel,
    PossessionType? possessionTypeDateRange,
    String? minLeadCount,
    String? maxLeadCount,
    String? minProspectCount,
    String? maxProspectCount,
    bool updatePossessionDateRange = true,
    bool resetPropertySize = false,
    bool resetCarpetArea = false,
    bool resetNetArea = false,
    bool resetBuiltUpArea = false,
    bool resetSaleableArea = false,
    bool updateDateType = false,
    double? fromMinBudget,
    double? toMinBudget,
    double? fromMaxBudget,
    double? toMaxBudget,
    bool updateMinBudget = true,
    bool updateMaxBudget = true,
    bool updateCurrency = true,
    bool resetProspectCount = false,
    bool resetLeadCount = false,
    DateRange? dateRange,
    bool updateDateRange = true,
  }) {
    return ListingManagementFilterModel(
      propertyVisibility: propertyVisibility ?? this.propertyVisibility,
      enquiryType: enquiryType == EnquiryType.none ? null : enquiryType ?? this.enquiryType,
      saleTypes: saleTypes ?? this.saleTypes,
      propertyTypes: propertyTypes ?? this.propertyTypes,
      propertySubTypes: propertySubTypes ?? this.propertySubTypes,
      dateType: updateDateType ? null : dateType ?? this.dateType,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      facing: facing ?? this.facing,
      furnishStatus: furnishStatus ?? this.furnishStatus,
      noOfBrs: noOfBrs ?? this.noOfBrs,
      bhkTypes: bhkTypes ?? this.bhkTypes,
      budgetFilters: budgetFilters ?? this.budgetFilters,
      floors: floors ?? this.floors,
      locations: locations ?? this.locations,
      projects: projects ?? this.projects,
      assignTo: assignTo ?? this.assignTo,
      listingOnBehalf: listingOnBehalf ?? this.listingOnBehalf,
      amenities: amenities ?? this.amenities,
      propertySize: propertySize ?? this.propertySize,
      currency: updateCurrency ? currency ?? this.currency : null,
      ownerName: ownerName ?? this.ownerName,
      noOfBathrooms: noOfBathrooms ?? this.noOfBathrooms,
      noOfLivingRooms: noOfLivingRooms ?? this.noOfLivingRooms,
      noOfBalconies: noOfBalconies ?? this.noOfBalconies,
      noOfBedrooms: noOfBedrooms ?? this.noOfBedrooms,
      noOfKitchens: noOfKitchens ?? this.noOfKitchens,
      noOfUtilities: noOfUtilities ?? this.noOfUtilities,
      offeringType: offeringType ?? this.offeringType,
      completionStatus: completionStatus ?? this.completionStatus,
      communities: communities ?? this.communities,
      subCommunities: subCommunities ?? this.subCommunities,
      minPropertySize: resetPropertySize ? null : (minPropertySize ?? this.minPropertySize),
      maxPropertySize: resetPropertySize ? null : (maxPropertySize ?? this.maxPropertySize),
      propertySizeUnit: resetPropertySize ? null : (propertySizeUnit ?? this.propertySizeUnit),
      minCarpetArea: resetCarpetArea ? null : (minCarpetArea ?? this.minCarpetArea),
      maxCarpetArea: resetCarpetArea ? null : (maxCarpetArea ?? this.maxCarpetArea),
      carpetAreaUnit: resetCarpetArea ? null : (carpetAreaUnit ?? this.carpetAreaUnit),
      minNetArea: resetNetArea ? null : (minNetArea ?? this.minNetArea),
      maxNetArea: resetNetArea ? null : (maxNetArea ?? this.maxNetArea),
      netAreaUnit: resetNetArea ? null : (netAreaUnit ?? this.netAreaUnit),
      minBuiltUpArea: resetBuiltUpArea ? null : (minBuiltUpArea ?? this.minBuiltUpArea),
      maxBuiltUpArea: resetBuiltUpArea ? null : (maxBuiltUpArea ?? this.maxBuiltUpArea),
      builtUpAreaUnit: resetBuiltUpArea ? null : (builtUpAreaUnit ?? this.builtUpAreaUnit),
      minSaleableArea: resetSaleableArea ? null : (minSaleableArea ?? this.minSaleableArea),
      maxSaleableArea: resetSaleableArea ? null : (maxSaleableArea ?? this.maxSaleableArea),
      saleableAreaUnit: resetSaleableArea ? null : (saleableAreaUnit ?? this.saleableAreaUnit),
      listingLevel: listingLevel ?? this.listingLevel,
      possessionTypeDateRange: updatePossessionDateRange ? possessionTypeDateRange ?? this.possessionTypeDateRange : null,
      fromMinBudget: updateMinBudget ? fromMinBudget ?? this.fromMinBudget : null,
      toMinBudget: updateMinBudget ? toMinBudget ?? this.toMinBudget : null,
      toMaxBudget: updateMaxBudget ? toMaxBudget ?? this.toMaxBudget : null,
      fromMaxBudget: updateMaxBudget ? fromMaxBudget ?? this.fromMaxBudget : null,
      maxLeadCount: resetLeadCount ? null : (maxLeadCount ?? this.maxLeadCount),
      minLeadCount: resetLeadCount ? null : (minLeadCount ?? this.minLeadCount),
      minProspectCount: resetProspectCount ? null : (minProspectCount ?? this.minProspectCount),
      maxProspectCount: resetProspectCount ? null : (maxProspectCount ?? this.maxProspectCount),
      dateRange: updateDateRange ? dateRange ?? dateRange : null,
    );
  }
}
