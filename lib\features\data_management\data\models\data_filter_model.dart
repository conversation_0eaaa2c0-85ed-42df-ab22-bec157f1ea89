import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/common/date_range.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/purpose_enum.dart';
import 'package:leadrat/core_main/enums/data_management/prospect_date_type.dart';
import 'package:leadrat/core_main/enums/data_management/prospect_visibility.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';

part 'data_filter_model.g.dart';

@JsonSerializable()
class DataFilterModel {
  @JsonKey(name: "ProspectVisiblity")
  final ProspectVisibility? prospectVisiblity;

  @JsonKey(name: "SourceIds")
  final List<String>? sources;

  @JsonKey(name: "SubSources")
  final List<String>? subSources;

  @JsonKey(name: "StatusIds")
  final List<String>? statusIds;

  @JsonKey(name: "FilterTypes")
  final List<String>? filterTypes;

  @JsonKey(name: "AssignTo")
  final List<String>? userIds;

  @JsonKey(name: "AssignedFromIds")
  final List<String>? assignedFromIds;

  @JsonKey(name: "EnquiryTypes")
  final List<EnquiryType>? enquiryTypes;

  @JsonKey(name: "Projects")
  final List<String>? projects;

  @JsonKey(name: "Properties")
  final List<String>? properties;

  @JsonKey(name: "DateType")
  final ProspectDateType? dateType;

  @JsonKey(name: "DateRange")
  final DateRange? dateRange;

  @JsonKey(name: "FromDate")
  final String? fromDate;

  @JsonKey(name: "ToDate")
  final String? toDate;

  @JsonKey(name: "NoOfBHKs")
  final List<double>? noOfBHKs;

  @JsonKey(name: "BhkTypes")
  final List<BHKType>? bhkTypes;

  @JsonKey(name: "PropertyType")
  final List<String>? propertyTypes;

  @JsonKey(name: "PropertySubType")
  final List<String>? propertySubTypes;

  @JsonKey(name: "Locations")
  final List<String>? locations;

  @JsonKey(name: "AgencyNames")
  final List<String>? agencyNames;

  @JsonKey(name: "CreatedByIds")
  final List<String>? createdByIds;

  @JsonKey(name: "QualifiedByIds")
  final List<String>? qualifiedByIds;

  @JsonKey(name: "ConvertedByIds")
  final List<String>? convertedByIds;

  @JsonKey(name: "Currency")
  final String? currency;

  @JsonKey(name: "CampaignNames")
  final List<String>? campaignNames;

  @JsonKey(name: "Furnished")
  final List<int>? furnished;

  @JsonKey(name: "Beds")
  final List<int>? beds;

  @JsonKey(name: "Baths")
  final List<int>? baths;

  @JsonKey(name: "Floors")
  final List<String>? floors;

  @JsonKey(name: "OfferTypes")
  final List<int>? offerTypes;

  @JsonKey(name: "Brs")
  final List<double>? brs;

  @JsonKey(name: "Communities")
  final List<String>? communities;

  @JsonKey(name: "SubCommunities")
  final List<String>? subCommunities;

  @JsonKey(name: "TowerNames")
  final List<String>? towerNames;

  @JsonKey(name: "Countries")
  final List<String>? countries;

  @JsonKey(name: "Pincode")
  final String? pincode;

  @JsonKey(name: "BuiltUpAreaUnitId")
  final String? builtUpAreaUnitId;

  @JsonKey(name: "CarpetAreaUnitId")
  final String? carpetAreaUnitId;

  @JsonKey(name: "SaleableAreaUnitId")
  final String? saleableAreaUnitId;

  @JsonKey(name: "NetAreaUnitId")
  final String? netAreaUnitId;

  @JsonKey(name: "PropertyAreaUnitId")
  final String? propertyAreaUnitId;

  @JsonKey(name: "SaleableArea")
  final double? saleableArea;

  @JsonKey(name: "CarpetArea")
  final double? carpetArea;

  @JsonKey(name: "BuiltUpArea")
  final double? builtUpArea;

  @JsonKey(name: "NetArea")
  final double? netArea;

  @JsonKey(name: "PropertyArea")
  final double? propertyArea;

  @JsonKey(name: "UnitNames")
  final List<String>? unitNames;

  @JsonKey(name: "ClusterName")
  final List<String>? clusterNames;

  @JsonKey(name: "Nationality")
  final List<String>? nationality;

  @JsonKey(name: "Purposes")
  final List<PurposeEnum>? purposes;

  @JsonKey(name: "MaxBuiltUpArea")
  final double? maxBuiltUpArea;

  @JsonKey(name: "MinBuiltUpArea")
  final double? minBuiltUpArea;

  @JsonKey(name: "MaxSaleableArea")
  final double? maxSaleableArea;

  @JsonKey(name: "MinSaleAbleArea")
  final double? minSaleAbleArea;

  @JsonKey(name: "MaxCarpetArea")
  final double? maxCarpetArea;

  @JsonKey(name: "MinCarpetArea")
  final double? minCarpetArea;

  @JsonKey(name: "MaxPropertyArea")
  final double? maxPropertyArea;

  @JsonKey(name: "MinPropertyArea")
  final double? minPropertyArea;

  @JsonKey(name: "FromMinBudget")
  final double? fromMinBudget;

  @JsonKey(name: "ToMinBudget")
  final double? toMinBudget;

  @JsonKey(name: "FromMaxBudget")
  final double? fromMaxBudget;

  @JsonKey(name: "ToMaxBudget")
  final double? toMaxBudget;

  @JsonKey(name: "MinNetArea")
  final double? minNetArea;

  @JsonKey(name: "MaxNetArea")
  final double? maxNetArea;
  @JsonKey(name: "PossessionTypeDateRange")
  final PossessionType? possessionTypeDateRange;
  final List<String>? excelSheets;

  DataFilterModel({
    this.prospectVisiblity,
    this.sources,
    this.subSources,
    this.statusIds,
    this.filterTypes,
    this.userIds,
    this.assignedFromIds,
    this.enquiryTypes,
    this.projects,
    this.properties,
    this.dateType,
    this.dateRange,
    this.fromDate,
    this.toDate,
    this.noOfBHKs,
    this.bhkTypes,
    this.propertyTypes,
    this.propertySubTypes,
    this.locations,
    this.agencyNames,
    this.createdByIds,
    this.qualifiedByIds,
    this.convertedByIds,
    this.currency,
    this.furnished,
    this.baths,
    this.floors,
    this.beds,
    this.offerTypes,
    this.brs,
    this.communities,
    this.subCommunities,
    this.towerNames,
    this.countries,
    this.pincode,
    this.builtUpAreaUnitId,
    this.carpetAreaUnitId,
    this.saleableAreaUnitId,
    this.netAreaUnitId,
    this.propertyAreaUnitId,
    this.saleableArea,
    this.carpetArea,
    this.builtUpArea,
    this.netArea,
    this.propertyArea,
    this.unitNames,
    this.clusterNames,
    this.nationality,
    this.campaignNames,
    this.purposes,
    this.maxBuiltUpArea,
    this.minBuiltUpArea,
    this.maxSaleableArea,
    this.minSaleAbleArea,
    this.maxCarpetArea,
    this.minCarpetArea,
    this.minPropertyArea,
    this.maxPropertyArea,
    this.fromMinBudget,
    this.toMinBudget,
    this.toMaxBudget,
    this.fromMaxBudget,
    this.maxNetArea,
    this.minNetArea,
    this.excelSheets,
    this.possessionTypeDateRange,
  });

  @override
  String toString() {
    String result = "";

    if (sources?.isNotEmpty ?? false) {
      sources?.forEach((i) => result += "&source=$i");
    }

    if (subSources?.isNotEmpty ?? false) {
      subSources?.forEach((i) => result += "&SubSources=$i");
    }
    if (campaignNames?.isNotEmpty ?? false) {
      campaignNames?.forEach((i) => result += "&CampaignNames=$i");
    }

    if (statusIds?.isNotEmpty ?? false) {
      statusIds?.forEach((i) => result += "&StatusIds=$i");
    }

    if (filterTypes?.isNotEmpty ?? false) {
      filterTypes?.forEach((i) => result += "&FilterTypes=$i");
    }

    if (prospectVisiblity != null) {
      result += "&ProspectVisiblity=${prospectVisiblity?.value}";
    }

    if (userIds?.isNotEmpty ?? false) {
      userIds?.forEach((i) => result += "&AssignTo=$i");
    }

    if (assignedFromIds?.isNotEmpty ?? false) {
      assignedFromIds?.forEach((i) => result += "&AssignedFromIds=$i");
    }

    if (createdByIds?.isNotEmpty ?? false) {
      createdByIds?.forEach((i) => result += "&CreatedByIds=$i");
    }

    if (qualifiedByIds?.isNotEmpty ?? false) {
      qualifiedByIds?.forEach((i) => result += "&QualifiedByIds=$i");
    }

    if (convertedByIds?.isNotEmpty ?? false) {
      convertedByIds?.forEach((i) => result += "&ConvertedByIds=$i");
    }

    if (properties?.isNotEmpty ?? false) {
      properties?.forEach((i) => result += "&Properties=$i");
    }

    if (projects?.isNotEmpty ?? false) {
      projects?.forEach((i) => result += "&Projects=$i");
    }

    if (enquiryTypes?.isNotEmpty ?? false) {
      enquiryTypes?.forEach((i) => result += "&EnquiryTypes=${i.value}");
    }

    if (dateType != null && dateType != ProspectDateType.all && dateType != ProspectDateType.possessionDate) {
      result += "&DateType=${dateType?.value}&FromDate=${fromDate ?? ''}&ToDate=${toDate ?? ''}";
    }
    if (dateType != null && dateType == ProspectDateType.possessionDate && possessionTypeDateRange == PossessionType.customDate) {
      result += "&PossesionType=${possessionTypeDateRange?.value}&FromPossesionDate=${fromDate ?? ''}&ToPossesionDate=${toDate ?? ''}";
    }
    if (dateType != null && dateType == ProspectDateType.possessionDate && possessionTypeDateRange != PossessionType.customDate) {
      result += "&PossesionType=${possessionTypeDateRange?.value}";
    }

    if (bhkTypes?.isNotEmpty ?? false) {
      bhkTypes?.forEach((i) => result += "&BHKTypes=${i.value}");
    }

    if (noOfBHKs?.isNotEmpty ?? false) {
      noOfBHKs?.forEach((i) => result += "&NoOfBHKs=$i");
    }

    if (propertyTypes?.isNotEmpty ?? false) {
      propertyTypes?.forEach((i) => result += "&PropertyType=$i");
    }

    if (propertySubTypes?.isNotEmpty ?? false) {
      propertySubTypes?.forEach((i) => result += "&PropertySubType=$i");
    }

    if (locations?.isNotEmpty ?? false) {
      locations?.forEach((i) => result += "&Locations=$i");
    }

    if (agencyNames?.isNotEmpty ?? false) {
      agencyNames?.forEach((i) => result += "&AgencyNames=$i");
    }

    if (currency != null && currency!.isNotEmpty) {
      result += "&Currency=$currency";
    }

    if (furnished?.isNotEmpty ?? false) {
      furnished?.forEach((i) => result += "&Furnished=$i");
    }

    if (beds?.isNotEmpty ?? false) {
      beds?.forEach((i) => result += "&Beds=$i");
    }

    if (baths?.isNotEmpty ?? false) {
      baths?.forEach((i) => result += "&Baths=$i");
    }

    if (floors?.isNotEmpty ?? false) {
      floors?.forEach((i) => result += "&Floors=$i");
    }

    if (offerTypes?.isNotEmpty ?? false) {
      offerTypes?.forEach((i) => result += "&OfferTypes=$i");
    }

    if (brs?.isNotEmpty ?? false) {
      brs?.forEach((i) => result += "&NoOfBHKs=$i");
    }

    if (communities?.isNotEmpty ?? false) {
      communities?.forEach((i) => result += "&Communities=$i");
    }

    if (subCommunities?.isNotEmpty ?? false) {
      subCommunities?.forEach((i) => result += "&SubCommunities=$i");
    }

    if (towerNames?.isNotEmpty ?? false) {
      towerNames?.forEach((i) => result += "&TowerNames=$i");
    }

    if (countries?.isNotEmpty ?? false) {
      countries?.forEach((i) => result += "&Countries=$i");
    }

    if (pincode != null && pincode != '') {
      result += "&Pincode=${pincode}";
    }

    if (carpetArea != null && carpetArea! > 0) {
      result += "&CarpetArea=${carpetArea.toString().replaceAll(".0", "")}";
    }
    if (carpetAreaUnitId?.isNotNullOrEmpty() ?? false) {
      result += "&CarpetAreaUnitId=$carpetAreaUnitId";
    }
    if (builtUpArea != null && builtUpArea! > 0) {
      result += "&BuiltUpArea=${builtUpArea.toString().replaceAll(".0", "")}";
    }
    if (builtUpAreaUnitId?.isNotNullOrEmpty() ?? false) {
      result += "&BuiltUpAreaUnitId=$builtUpAreaUnitId";
    }
    if (saleableArea != null && saleableArea! > 0) {
      result += "&SaleableArea=${saleableArea.toString().replaceAll(".0", "")}";
    }
    if (saleableAreaUnitId?.isNotNullOrEmpty() ?? false) {
      result += "&SaleableAreaUnitId=$saleableAreaUnitId";
    }
    if (netArea != null && netArea! > 0) {
      result += "&NetArea=${netArea.toString().replaceAll(".0", "")}";
    }
    if (netAreaUnitId?.isNotNullOrEmpty() ?? false) {
      result += "&NetAreaUnitId=$netAreaUnitId";
    }
    if (propertyArea != null && propertyArea! > 0) {
      result += "&PropertyArea=${propertyArea.toString().replaceAll(".0", "")}";
    }
    if (propertyAreaUnitId?.isNotNullOrEmpty() ?? false) {
      result += "&PropertyAreaUnitId=$propertyAreaUnitId";
    }
    if (unitNames?.isNotEmpty ?? false) {
      unitNames?.forEach((i) => result += "&UnitNames=$i");
    }
    if (clusterNames?.isNotEmpty ?? false) {
      clusterNames?.forEach((i) => result += "&ClusterName=$i");
    }
    if (nationality?.isNotEmpty ?? false) {
      nationality?.forEach((i) => result += "&Nationality=$i");
    }
    if (purposes?.isNotEmpty ?? false) {
      purposes?.forEach((i) => result += "&Purposes=${i.value}");
    }
    if (minBuiltUpArea != null && minBuiltUpArea! > 0) {
      result += "&MinBuiltUpArea=${minBuiltUpArea.toString().replaceAll(".0", "")}";
    }
    if (minSaleAbleArea != null && minSaleAbleArea! > 0) {
      result += "&MinSaleableArea=${minSaleAbleArea.toString().replaceAll(".0", "")}";
    }
    if (minCarpetArea != null && minCarpetArea! > 0) {
      result += "&MinCarpetArea=${minCarpetArea.toString().replaceAll(".0", "")}";
    }
    if (maxBuiltUpArea != null && maxBuiltUpArea! > 0) {
      result += "&MaxBuiltUpArea=${maxBuiltUpArea.toString().replaceAll(".0", "")}";
    }
    if (maxSaleableArea != null && maxSaleableArea! > 0) {
      result += "&MaxSaleableArea=${maxSaleableArea.toString().replaceAll(".0", "")}";
    }
    if (maxPropertyArea != null && maxPropertyArea! > 0) {
      result += "&MaxPropertyArea=${maxPropertyArea.toString().replaceAll(".0", "")}";
    }
    if (minPropertyArea != null && minPropertyArea! > 0) {
      result += "&MinPropertyArea=${minPropertyArea.toString().replaceAll(".0", "")}";
    }
    if (maxCarpetArea != null && maxCarpetArea! > 0) {
      result += "&MaxCarpetArea=${maxCarpetArea.toString().replaceAll(".0", "")}";
    }
    if (fromMinBudget != null && fromMinBudget! > 0) {
      result += "&FromMinBudget=${fromMinBudget.toString().replaceAll(".0", "")}";
    }
    if (toMinBudget != null && toMinBudget! > 0) {
      result += "&ToMinBudget=${toMinBudget.toString().replaceAll(".0", "")}";
    }
    if (fromMaxBudget != null && fromMaxBudget! > 0) {
      result += "&FromMaxBudget=${fromMaxBudget.toString().replaceAll(".0", "")}";
    }
    if (toMaxBudget != null && toMaxBudget! > 0) {
      result += "&ToMaxBudget=${toMaxBudget.toString().replaceAll(".0", "")}";
    }
    if (minNetArea != null && minNetArea! > 0) {
      result += "&MinNetArea=${minNetArea.toString().replaceAll(".0", "")}";
    }
    if (maxNetArea != null && maxNetArea! > 0) {
      result += "&MaxNetArea=${maxNetArea.toString().replaceAll(".0", "")}";
    }
    if (excelSheets?.isNotEmpty ?? false) {
      excelSheets?.forEach((i) => result += "&UploadTypeName=$i");
    }
    result += "&CanAccessAllProspects=${getIt<UsersDataRepository>().checkHasPermission(AppModule.prospect, CommandType.viewAllProspects)}";

    return result;
  }

  DataFilterModel copyWith({
    ProspectVisibility? prospectVisiblity,
    List<String>? sources,
    List<String>? subSources,
    List<String>? filterTypes,
    List<String>? statusIds,
    List<String>? userIds,
    List<String>? assignedFromIds,
    List<EnquiryType>? enquiryTypes,
    List<String>? projects,
    List<String>? properties,
    ProspectDateType? dateType,
    DateRange? dateRange,
    PossessionType? possessionTypeDateRange,
    String? fromDate,
    String? toDate,
    List<double>? noOfBHKs,
    List<BHKType>? bhkTypes,
    List<String>? propertyTypes,
    List<String>? propertySubTypes,
    List<String>? locations,
    List<String>? agencyNames,
    List<String>? createdByIds,
    List<String>? qualifiedByIds,
    List<String>? convertedByIds,
    List<String>? campaignNames,
    String? currency,
    bool dateRangeSelected = false,
    bool currencySelected = false,
    List<int>? furnished,
    List<int>? beds,
    List<int>? baths,
    List<String>? floors,
    List<int>? offerTypes,
    List<double>? brs,
    List<String>? communities,
    List<String>? subCommunities,
    List<String>? towerNames,
    List<String>? countries,
    String? pincode,
    String? builtUpAreaUnitId,
    String? carpetAreaUnitId,
    String? saleableAreaUnitId,
    String? netAreaUnitId,
    String? propertyAreaUnitId,
    double? saleableArea,
    double? carpetArea,
    double? builtUpArea,
    double? netArea,
    double? propertyArea,
    bool updateCarpetArea = true,
    bool updateBuildUpArea = true,
    bool updateSalableArea = true,
    bool updateNetArea = true,
    bool updatePropertyArea = true,
    bool updateIsUntouchable = true,
    bool updateMinBudget = true,
    bool updateMaxBudget = true,
    List<String>? unitNames,
    List<String>? clusterNames,
    List<String>? nationality,
    List<PurposeEnum>? purposes,
    double? maxBuiltUpArea,
    double? minBuiltUpArea,
    double? maxSaleableArea,
    bool updatePossessionDateRange = true,
    double? minSaleAbleArea,
    double? maxCarpetArea,
    double? minCarpetArea,
    double? maxPropertyArea,
    double? minPropertyArea,
    double? fromMinBudget,
    double? toMinBudget,
    double? fromMaxBudget,
    double? toMaxBudget,
    double? minNetArea,
    double? maxNetArea,
    List<String>? excelSheets,
  }) {
    return DataFilterModel(
      prospectVisiblity: prospectVisiblity ?? this.prospectVisiblity,
      sources: sources ?? this.sources,
      subSources: subSources ?? this.subSources,
      statusIds: statusIds ?? this.statusIds,
      userIds: userIds ?? this.userIds,
      assignedFromIds: assignedFromIds ?? this.assignedFromIds,
      enquiryTypes: enquiryTypes ?? this.enquiryTypes,
      projects: projects ?? this.projects,
      properties: properties ?? this.properties,
      dateType: !dateRangeSelected ? dateType ?? this.dateType : null,
      dateRange: !dateRangeSelected ? dateRange ?? this.dateRange : null,
      possessionTypeDateRange: updatePossessionDateRange ? possessionTypeDateRange ?? possessionTypeDateRange : null,
      fromDate: !dateRangeSelected ? fromDate ?? this.fromDate : null,
      toDate: !dateRangeSelected ? toDate ?? this.toDate : null,
      noOfBHKs: noOfBHKs ?? this.noOfBHKs,
      bhkTypes: bhkTypes ?? this.bhkTypes,
      propertyTypes: propertyTypes ?? this.propertyTypes,
      propertySubTypes: propertySubTypes ?? this.propertySubTypes,
      locations: locations ?? this.locations,
      agencyNames: agencyNames ?? this.agencyNames,
      createdByIds: createdByIds ?? this.createdByIds,
      qualifiedByIds: qualifiedByIds ?? this.qualifiedByIds,
      convertedByIds: convertedByIds ?? this.convertedByIds,
      currency: !currencySelected ? currency ?? this.currency : null,
      furnished: furnished ?? this.furnished,
      beds: beds ?? this.beds,
      baths: baths ?? this.baths,
      floors: floors ?? this.floors,
      offerTypes: offerTypes ?? this.offerTypes,
      brs: brs ?? this.brs,
      communities: communities ?? this.communities,
      subCommunities: subCommunities ?? this.subCommunities,
      towerNames: towerNames ?? this.towerNames,
      countries: countries ?? this.countries,
      pincode: pincode ?? this.pincode,
      builtUpAreaUnitId: updateBuildUpArea ? builtUpAreaUnitId ?? this.builtUpAreaUnitId : null,
      carpetAreaUnitId: updateCarpetArea ? carpetAreaUnitId ?? this.carpetAreaUnitId : null,
      saleableArea: updateSalableArea ? saleableArea ?? this.saleableArea : null,
      saleableAreaUnitId: updateSalableArea ? saleableAreaUnitId ?? this.saleableAreaUnitId : null,
      carpetArea: updateCarpetArea ? carpetArea ?? this.carpetArea : null,
      builtUpArea: updateBuildUpArea ? builtUpArea ?? this.builtUpArea : null,
      netAreaUnitId: updateNetArea ? netAreaUnitId ?? this.netAreaUnitId : null,
      netArea: updateNetArea ? netArea ?? this.netArea : null,
      propertyAreaUnitId: updatePropertyArea ? propertyAreaUnitId ?? this.propertyAreaUnitId : null,
      propertyArea: updatePropertyArea ? propertyArea ?? this.propertyArea : null,
      unitNames: unitNames ?? this.unitNames,
      clusterNames: clusterNames ?? this.clusterNames,
      nationality: nationality ?? this.nationality,
      campaignNames: campaignNames ?? this.campaignNames,
      purposes: purposes ?? this.purposes,
      maxBuiltUpArea: updateBuildUpArea ? maxBuiltUpArea ?? this.maxBuiltUpArea : null,
      minBuiltUpArea: updateBuildUpArea ? minBuiltUpArea ?? this.minBuiltUpArea : null,
      maxSaleableArea: updateSalableArea ? maxSaleableArea ?? this.maxSaleableArea : null,
      minSaleAbleArea: updateSalableArea ? minSaleAbleArea ?? this.minSaleAbleArea : null,
      maxCarpetArea: updateCarpetArea ? maxCarpetArea ?? this.maxCarpetArea : null,
      minCarpetArea: updateCarpetArea ? minCarpetArea ?? this.minCarpetArea : null,
      maxPropertyArea: updatePropertyArea ? maxPropertyArea ?? this.maxPropertyArea : null,
      minPropertyArea: updatePropertyArea ? minPropertyArea ?? this.minPropertyArea : null,
      fromMinBudget: updateMinBudget ? fromMinBudget ?? this.fromMinBudget : null,
      toMinBudget: updateMinBudget ? toMinBudget ?? this.toMinBudget : null,
      toMaxBudget: updateMaxBudget ? toMaxBudget ?? this.toMaxBudget : null,
      fromMaxBudget: updateMaxBudget ? fromMaxBudget ?? this.fromMaxBudget : null,
      maxNetArea: updateNetArea ? maxNetArea ?? this.maxNetArea : null,
      minNetArea: updateNetArea ? minNetArea ?? this.minNetArea : null,
      excelSheets: excelSheets ?? this.excelSheets,
    );
  }

  factory DataFilterModel.fromJson(Map<String, dynamic> json) => _$DataFilterModelFromJson(json);

  Map<String, dynamic> toJson() => _$DataFilterModelToJson(this);
}
