import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fpdart/src/either.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/common/widgets/share_with_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/contact_type_enum.dart';
import 'package:leadrat/core_main/enums/common/module_name.dart';
import 'package:leadrat/core_main/errors/failure.dart';
import 'package:leadrat/features/data_management/domain/entities/template_entity.dart';
import 'package:leadrat/features/projects/domain/entities/get_project_entity.dart';
import 'package:leadrat/features/projects/domain/repository/project_repository.dart';
import 'package:leadrat/features/projects/domain/usecase/update_project_share_count_usecase.dart';
import 'package:leadrat/features/properties/data/models/update_property_share_count_model.dart';
import 'package:leadrat/features/properties/domain/entities/get_property_entity.dart';
import 'package:leadrat/features/properties/domain/repository/properties_repository.dart';
import 'package:leadrat/features/properties/domain/usecase/get_property_templates_use_case.dart';
import 'package:leadrat/features/properties/domain/usecase/update_property_share_count_usecase.dart';

part 'property_share_event.dart';
part 'property_share_state.dart';

class PropertyShareBloc extends Bloc<PropertyShareEvent, PropertyShareState> {
  final GetPropertyTemplatesUseCase _getPropertyTemplatesUseCase;
  final ProjectRepository _projectRepository;
  final PropertiesRepository _propertiesRepository;
  final UpdatePropertyShareCountUseCase _updatePropertyShareCountUseCase;
  final UpdateProjectShareCountUseCase _updateProjectShareCountUseCase;
  final UsersDataRepository _usersDataRepository;
  final TextEditingController messageTextEditingController = TextEditingController();
  late UserDetailsModel? userDetails;
  ModuleName? moduleName;
  List<String>? propertyIds;
  String? domain;

  PropertyShareBloc(this._getPropertyTemplatesUseCase, this._projectRepository, this._propertiesRepository, this._updateProjectShareCountUseCase, this._updatePropertyShareCountUseCase, this._usersDataRepository) : super(const PropertyShareState()) {
    on<PropertyShareInitialEvent>(_initPropertyShare);
    on<GetAllTemplateEvent>(_onGetAllTemplate);
    on<SelectTemplateEvent>(_onSelectTemplate);
    on<SendTemplateEvent>(_onSendTemplate);
    on<UpdatePropertyShareCountEvent>(_onUpdatePropertyShareCountEvent);
  }

  FutureOr<void> _initPropertyShare(PropertyShareInitialEvent event, Emitter<PropertyShareState> emit) async {
    domain =  getIt<UsersDataRepository>().getLoggedInUser()?.organizationName;
    messageTextEditingController.text = '';
    emit(state.copyWith(
      pageState: PageState.initial,
      errorMessage: '',
      updateSelectedTemplate: false,
      selectedTemplate: null,
      templates: [],
      contactNumber: event.contactNumber,
      emailAddress: event.emailAddress,
    ));
  }

  FutureOr<void> _onGetAllTemplate(GetAllTemplateEvent event, Emitter<PropertyShareState> emit) async {
    emit(state.copyWith(pageState: PageState.loading, errorMessage: ''));
    userDetails = await getIt<UsersDataRepository>().getUser();
    moduleName = event.moduleName;
    final result = await _getPropertyTemplatesUseCase(event.moduleName != null ? event.moduleName! : ModuleName.property);
    result.fold(
      (failure) {
        emit(state.copyWith(pageState: PageState.failure, errorMessage: failure.message, templates: []));
      },
      (success) {
        final templates = success?.map((template) => SelectableItem<TemplateEntity>(title: template?.title ?? '', value: template)).toList();
        emit(state.copyWith(pageState: PageState.success, templates: [...?templates], errorMessage: ''));
      },
    );
  }

  Future<void> _onSelectTemplate(SelectTemplateEvent event, Emitter<PropertyShareState> emit) async {
    messageTextEditingController.text = "";
    emit(state.copyWith(pageState: PageState.loading, errorMessage: 'fetching message templates...'));

    final updatedTemplates = state.templates?.map((template) {
      return template.copyWith(
        isSelected: template.title == event.selectedTemplate?.title,
      );
    }).toList();

    SelectableItem<TemplateEntity>? template = updatedTemplates?.firstWhereOrNull((item) => item.isSelected);
    Either<Failure, String>? messageStatus;
    if (moduleName == ModuleName.project) {
      messageStatus = await _projectRepository.getTemplateMessageByProject(footer: event.selectedTemplate?.value?.footer, message: event.selectedTemplate?.value?.message, header: event.selectedTemplate?.value?.header, projectEntity: event.propertyIds?.map((e) => GetProjectEntity(id: e)).toList(), domain: domain, userDetails: userDetails);
    } else {
      messageStatus = await _propertiesRepository.getTemplateMessageByProperty(footer: event.selectedTemplate?.value?.footer, message: event.selectedTemplate?.value?.message, header: event.selectedTemplate?.value?.header, propertyEntity: event.propertyIds?.map((e) => GetPropertyEntity(id: e)).toList(), domain: domain, userDetails: userDetails);
    }

    messageStatus.fold(
      (failure) {
        emit(state.copyWith(pageState: PageState.failure, templates: [...?updatedTemplates], selectedTemplate: template, errorMessage: 'unable to get  ${moduleName?.name ?? 'property'} template message'));
      },
      (success) {
        messageTextEditingController.text = success;
        emit(state.copyWith(pageState: PageState.success, templates: [...?updatedTemplates], selectedTemplate: template, errorMessage: 'successfully get ${moduleName?.name ?? 'property'} template message'));
      },
    );
  }

  FutureOr<void> _onSendTemplate(SendTemplateEvent event, Emitter<PropertyShareState> emit) async {
    if (messageTextEditingController.text.isNotEmpty) {
      try {
        // Open the share dialog
        final shareResult = await shareWithBottomSheet(message: messageTextEditingController.text, emailAddress: state.emailAddress, contactNumber: state.contactNumber);
        // //updating share count of property in API
        _updateStatusOfProperty(shareResult, emit);
      } catch (e) {
        // Handle any unexpected errors (like dismissing the popup)
        emit(state.copyWith(errorMessage: ''));
      }
    } else {
      // Emit an error if no template was selected
      emit(state.copyWith(errorMessage: 'Please select a message template'));
    }
  }

  FutureOr<void> _onUpdatePropertyShareCountEvent(UpdatePropertyShareCountEvent event, Emitter<PropertyShareState> emit) async {
    if (propertyIds != null && propertyIds!.isNotEmpty) {
      if (moduleName == ModuleName.property) {
        var loggedInUser = _usersDataRepository.getLoggedInUser();
        final updateStatus = await _updatePropertyShareCountUseCase.call(UpdatePropertyShareCountModel(lastModifiedBy: loggedInUser?.userId, contactType: event.contactType, ids: propertyIds));
        updateStatus.fold((fail) => emit(state.copyWith(pageState: PageState.failure, errorMessage: 'unable to send property share count')), (success) {
          if (success != null && success == true) {
            emit(state.copyWith(pageState: PageState.success, errorMessage: ''));
          }
        });
      } else if (moduleName == ModuleName.project) {
        var loggedInUser = _usersDataRepository.getLoggedInUser();
        final updateStatus = await _updateProjectShareCountUseCase.call(UpdatePropertyShareCountModel(lastModifiedBy: loggedInUser?.userId, contactType: event.contactType, ids: propertyIds));
        updateStatus.fold((fail) => emit(state.copyWith(pageState: PageState.failure, errorMessage: 'unable to send projects share count')), (success) {
          if (success != null && success == true) {
            emit(state.copyWith(pageState: PageState.success, errorMessage: ''));
          }
        });
      }
    }
  }

  void _updateStatusOfProperty(ShareToResult shareResult, Emitter<PropertyShareState> emit) {
    switch (shareResult) {
      case ShareToResult.whatsapp:
        add(UpdatePropertyShareCountEvent(contactType: ContactType.whatsApp));
        break;
      case ShareToResult.email:
        add(UpdatePropertyShareCountEvent(contactType: ContactType.email));
        break;
      case ShareToResult.others:
        break;
      case ShareToResult.failed:
        emit(state.copyWith(errorMessage: 'unable to send message'));
        break;
      case ShareToResult.sms:
        add(UpdatePropertyShareCountEvent(contactType: ContactType.sms));
        break;
    }
  }
}
