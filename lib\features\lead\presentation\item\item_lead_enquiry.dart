import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/common/purpose_enum.dart';
import 'package:leadrat/core_main/enums/leads/profession.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:leadrat/core_main/extensions/integer_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';
import 'package:leadrat/features/lead/presentation/bloc/lead_info_bloc/lead_info_bloc.dart';

class ItemLeadEnquiryModel {
  final String sectionTitle;
  final String sectionIcon;
  final List<ItemSimpleModel> columnItems;
  final List<ItemSimpleModel> gridItems;

  ItemLeadEnquiryModel({
    required this.sectionTitle,
    required this.sectionIcon,
    this.columnItems = const [],
    this.gridItems = const [],
  });

  static List<ItemLeadEnquiryModel> getLeadEnquires({
    GetLeadEntity? leadEntity,
    GlobalSettingModel? globalSettingModel,
    String? bhkType,
    String? noOfBHK,
    String? floors,
    String? beds,
    String? baths,
    String? furnishingStatus,
    String? offeringType,
    List<ItemSimpleModel>? locations,
  }) {
    List<ItemLeadEnquiryModel> leadEnquires = [];
    final defaultCurrency = getIt<LeadInfoBloc>().getDefaultCurrency();
    //details
    leadEnquires.add(
      ItemLeadEnquiryModel(
        sectionTitle: "details",
        sectionIcon: ImageResources.iconBuilding,
        columnItems: [
          ItemSimpleModel(title: "property${(leadEntity?.properties?.length ?? 0) > 1 ? '(s)' : ''}", items: leadEntity?.properties?.map((e) => ItemSimpleModel(title: e.title ?? '')).toList()),
          ItemSimpleModel(title: 'project${(leadEntity?.projects?.length ?? 0) > 1 ? '(s)' : ''}', items: leadEntity?.projects?.map((e) => ItemSimpleModel(title: e.name ?? '')).toList()),
          ItemSimpleModel(title: 'agencies', items: leadEntity?.agencies?.map((e) => ItemSimpleModel(title: e.name ?? '')).toList()),
          ItemSimpleModel(title: 'channel partner', items: leadEntity?.channelPartners?.map((e) => ItemSimpleModel(title: e.firmName ?? '')).toList()),
          ItemSimpleModel(title: 'campaigns', items: leadEntity?.campaigns?.map((e) => ItemSimpleModel(title: e.name ?? '')).toList()),
        ],
        gridItems: [
          ItemSimpleModel(title: "serial number", description: leadEntity?.serialNumber),
          ItemSimpleModel(title: "type", description: leadEntity?.enquiry?.propertyType?.displayName),
          ItemSimpleModel(title: "sub-type", description: leadEntity?.enquiry?.propertyTypes?.map((e) => e.childType?.displayName).join(",") ?? "--"),
          ItemSimpleModel(title: "min budget", description: leadEntity?.enquiry?.lowerBudget?.convertCurrencyFormat(currency: leadEntity.enquiry?.currency, isInternationalFormat: globalSettingModel?.isCustomLeadFormEnabled ?? false)),
          ItemSimpleModel(title: "max budget", description: leadEntity?.enquiry?.upperBudget?.convertCurrencyFormat(currency: leadEntity.enquiry?.currency ?? defaultCurrency, isInternationalFormat: globalSettingModel?.isCustomLeadFormEnabled ?? false)),
          if (!(globalSettingModel?.isCustomLeadFormEnabled ?? false)) ItemSimpleModel(title: !(globalSettingModel?.isCustomLeadFormEnabled ?? false) ? "bhk" : "br", description: noOfBHK),
          if (!(globalSettingModel?.isCustomLeadFormEnabled ?? false)) ItemSimpleModel(title: "bhk type", description: bhkType, isEnabled: (!(globalSettingModel?.isCustomLeadFormEnabled ?? true))),
          ItemSimpleModel(title: "beds", description: beds, isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          if (baths == '--') ItemSimpleModel(title: "floors", description: floors, isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          if (baths != '--') ItemSimpleModel(title: "baths", description: baths, isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          ItemSimpleModel(title: "furnished status", description: furnishingStatus, isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          ItemSimpleModel(title: "offering type", description: offeringType, isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          ItemSimpleModel(title: "company name", description: leadEntity?.companyName),
          ItemSimpleModel(title: "carpet area", description: leadEntity?.enquiry?.carpetArea != null ? '${leadEntity?.enquiry?.carpetArea == 0 ? '--' : leadEntity?.enquiry?.carpetArea?.toString()} ${leadEntity?.carpetAreaUnit ?? '--'}' : '--'),
          ItemSimpleModel(title: "possession needed by", description: leadEntity?.enquiry?.possessionDate?.convertDateFormatToPossessionDateFormat() ?? ''),
          ItemSimpleModel(title: "profession", description: leadEntity?.profession == null || leadEntity?.profession == Profession.none ? "" : leadEntity?.profession?.description),
          ItemSimpleModel(title: "designation", description: leadEntity?.designation),
          ItemSimpleModel(title: "built up area", description: leadEntity?.enquiry?.builtUpArea != null ? '${leadEntity?.enquiry?.builtUpArea == 0 ? '--' : leadEntity?.enquiry?.builtUpArea?.toString()} ${leadEntity?.builtUpAreaUnit ?? '--'}' : '--'),
          ItemSimpleModel(title: "saleable area", description: leadEntity?.enquiry?.saleableArea != null ? '${leadEntity?.enquiry?.saleableArea == 0 ? '--' : leadEntity?.enquiry?.saleableArea?.toString()} ${leadEntity?.saleableAreaUnit ?? '--'}' : '--'),
          ItemSimpleModel(title: "property area", description: leadEntity?.enquiry?.propertyArea != null ? '${leadEntity?.enquiry?.propertyArea == 0 ? '--' : leadEntity?.enquiry?.propertyArea?.toString()} ${leadEntity?.propertyAreaUnit ?? '--'}' : '--', isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          ItemSimpleModel(title: "net area", description: leadEntity?.enquiry?.netArea != null ? '${leadEntity?.enquiry?.netArea == 0 ? '--' : leadEntity?.enquiry?.netArea?.toString()} ${leadEntity?.netAreaUnit ?? '--'}' : '--', isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          ItemSimpleModel(title: "unit number/name", description: leadEntity?.enquiry?.unitName, isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          ItemSimpleModel(title: "cluster name", description: leadEntity?.enquiry?.clusterName, isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          ItemSimpleModel(title: "nationality", description: leadEntity?.nationality, isEnabled: globalSettingModel?.isCustomLeadFormEnabled),
          ItemSimpleModel(title: "sourcing manager", description: leadEntity?.sourcingManager?.fullName),
          ItemSimpleModel(title: "closing manager", description: leadEntity?.closingManagerUser?.fullName),
          ItemSimpleModel(title: "purpose", description: leadEntity?.enquiry?.purpose != PurposeEnum.none ? leadEntity?.enquiry?.purpose?.description : null),
          ItemSimpleModel(title: "referral name", description: leadEntity?.referralName),
          ItemSimpleModel(title: "referral contact", description: leadEntity?.referralContactNo),
          ItemSimpleModel(title: "referral email", description: leadEntity?.referralEmail),
        ],
      ),
    );

    //location
    if (!(globalSettingModel?.isCustomLeadFormEnabled ?? false)) {
      leadEnquires.add(
        ItemLeadEnquiryModel(sectionTitle: "location", sectionIcon: ImageResources.iconLocation, columnItems: [
          ItemSimpleModel(title: "locations", items: locations?.map((e) => ItemSimpleModel(title: e.title)).toList()),
        ]),
      );
    }

    //enquiry location
    if (globalSettingModel?.isCustomLeadFormEnabled ?? false) {
      leadEnquires.add(
        ItemLeadEnquiryModel(sectionTitle: "enquiry location", sectionIcon: ImageResources.iconLocation, gridItems: [
          ItemSimpleModel(title: "community", description: leadEntity?.enquiry?.addresses?.firstOrNull != null ? leadEntity?.enquiry?.addresses?.firstOrNull?.community ?? '--' : '--'),
          ItemSimpleModel(title: "sub community", description: leadEntity?.enquiry?.addresses?.firstOrNull != null ? leadEntity?.enquiry?.addresses?.firstOrNull?.subCommunity ?? '--' : '--'),
          ItemSimpleModel(title: "tower name", description: leadEntity?.enquiry?.addresses?.firstOrNull != null ? leadEntity?.enquiry?.addresses?.firstOrNull?.towerName ?? '--' : '--'),
          ItemSimpleModel(title: "state", description: leadEntity?.enquiry?.addresses?.firstOrNull != null ? leadEntity?.enquiry?.addresses?.firstOrNull?.state ?? '--' : '--'),
          ItemSimpleModel(title: "country", description: leadEntity?.enquiry?.addresses?.firstOrNull != null ? leadEntity?.enquiry?.addresses?.firstOrNull?.country ?? '--' : '--'),
          ItemSimpleModel(title: "pincode", description: leadEntity?.enquiry?.addresses?.firstOrNull != null ? leadEntity?.enquiry?.addresses?.firstOrNull?.postalCode ?? '--' : '--'),
        ]),
      );
    }

    //customer location
    leadEnquires.add(
      ItemLeadEnquiryModel(sectionTitle: "customer location", sectionIcon: ImageResources.iconLocation, gridItems: [
        ItemSimpleModel(title: "community", description: leadEntity?.address != null ? leadEntity?.address?.community ?? '--' : '--'),
        ItemSimpleModel(title: "sub community", description: leadEntity?.address != null ? leadEntity?.address?.subCommunity ?? '--' : '--'),
        ItemSimpleModel(title: "tower name", description: leadEntity?.address != null ? leadEntity?.address?.towerName ?? '--' : '--'),
        ItemSimpleModel(title: "state", description: leadEntity?.address != null ? leadEntity?.address?.state ?? '--' : '--'),
        ItemSimpleModel(title: "country", description: leadEntity?.address != null ? leadEntity?.address?.country ?? '--' : '--'),
        ItemSimpleModel(title: "pincode", description: leadEntity?.address != null ? leadEntity?.address?.postalCode ?? '--' : '--'),
      ]),
    );

    return leadEnquires;
  }
}
