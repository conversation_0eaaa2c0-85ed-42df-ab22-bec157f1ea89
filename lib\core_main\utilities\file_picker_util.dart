import 'dart:io';

import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/file_type.dart';
import 'package:leadrat/core_main/enums/app_enum/select_file_enum.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/services/native_implementation_service/native_implementation_service.dart';
import 'package:path/path.dart' as path;

class FilePickerUtil {
  static Future<List<XFile?>?> pickFile(SelectFileEnum source, {FileType fileType = FileType.all, bool compress = true, int minWidth = 800, int minHeight = 800, int quality = 95, bool isMultiSelection = false}) async {
    final nativeImplementationService = getIt<NativeImplementationService>();

    try {
      List<XFile?>? files;

      switch (source) {
        case SelectFileEnum.camera:
          files = [await nativeImplementationService.openCamera()];
          break;

        case SelectFileEnum.gallery:
          final pickedImage = await nativeImplementationService.openGallery(isMultiSelection: isMultiSelection);
          pickedImage.fold(
            (failure) => null,
            (result) => files = result,
          );
          break;

        case SelectFileEnum.files:
          if (isMultiSelection) {
            final result = await nativeImplementationService.openMultiDocumentsSelection(fileType: fileType);
            result.fold(
              (failure) => null,
              (result) => files = result,
            );
          } else {
            final result = await nativeImplementationService.openDocuments(fileType: fileType);
            result.fold(
              (failure) => null,
              (result) => files = [result],
            );
          }
          break;

        default:
          return null;
      }
      final compressedFiles = files?.map(
        (file) async {
          if (file != null && compress && _isImage(file.path)) {
            final compressedFile = await FlutterImageCompress.compressWithFile(file.path, minWidth: minWidth, minHeight: minHeight, quality: quality);

            if (compressedFile != null) {
              final originalExtension = path.extension(file.path);
              final tempPath = file.path.replaceFirst(originalExtension, '_compressed$originalExtension');
              final compressedFilePath = await _writeCompressedFile(compressedFile, tempPath);
              return XFile(compressedFilePath);
            }
          }
          return file;
        },
      ).toList();
      if (compressedFiles != null) {
        files = await Future.wait(compressedFiles);
      }

      return files;
    } catch (e, stackTrace) {
      'Error picking file: $e'.logException(stackTrace);
      return null;
    }
  }

  static Future<String> _writeCompressedFile(List<int> compressedData, String path) async {
    final file = File(path);
    await file.writeAsBytes(compressedData);
    return file.path;
  }

  static bool _isImage(String path) {
    final lowerCasePath = path.toLowerCase();
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'heic', 'webp'];
    return imageExtensions.any(lowerCasePath.endsWith);
  }
}
