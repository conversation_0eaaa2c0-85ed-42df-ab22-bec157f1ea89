part of 'appointments_bloc.dart';

@immutable
class AppointmentsState {
  final AppointmentsPageState pageState;
  final String? leadName;
  final String? description;
  final String? enquiryDescription;
  final String? price;
  final SelectableItem<String?>? dimension;
  final String? location;
  final List<SelectableItem<String?>>? leadProjects;
  final List<SelectableItem<String?>>? allProjects;
  final List<SelectableItem<String?>>? initialSelectedProjects;
  final List<SelectableItem<String?>>? duplicateAllProjects;
  final List<SelectableItem<String?>>? locations;
  final List<SelectableItem<String?>>? leadDocuments;
  final AddressModel? leadLocation;
  final int? selectedIndex;
  final GetLeadEntity? leadEntity;
  final GlobalSettingModel? globalSettingModel;
  final bool showDialogProgress;
  final List<ItemSimpleModel> selectedProjects;
  final SelectableItem<String?>? selectedProject;
  final bool isProjectMandatory;
  final bool isNotesMandatory;
  final List<SelectableItem<String>> assignToUsers;
  final SelectableItem<String>? selectedAssignedUser;
  final bool? isMeetingScheduled;

  const AppointmentsState({
    this.pageState = AppointmentsPageState.initial,
    this.leadName,
    this.description,
    this.enquiryDescription,
    this.price,
    this.dimension,
    this.location,
    this.leadProjects,
    this.allProjects,
    this.duplicateAllProjects,
    this.locations,
    this.leadDocuments,
    this.leadLocation,
    this.selectedIndex,
    this.leadEntity,
    this.globalSettingModel,
    this.showDialogProgress = false,
    this.selectedProjects = const [],
    this.selectedProject,
    this.initialSelectedProjects,
    this.isProjectMandatory = false,
    this.isNotesMandatory = false,
    this.assignToUsers = const [],
    this.selectedAssignedUser,
    this.isMeetingScheduled,
  });

  AppointmentsState copyWith({
    AppointmentsPageState? pageState,
    String? leadName,
    String? description,
    String? enquiryDescription,
    String? price,
    SelectableItem<String?>? dimension,
    String? location,
    List<SelectableItem<String?>>? leadProjects,
    List<SelectableItem<String?>>? allProjects,
    List<SelectableItem<String?>>? initialSelectedProjects,
    List<SelectableItem<String?>>? duplicateAllProjects,
    List<SelectableItem<String?>>? locations,
    List<SelectableItem<String?>>? leadDocuments,
    AddressModel? leadLocation,
    int? selectedIndex,
    GetLeadEntity? leadEntity,
    GlobalSettingModel? globalSettingModel,
    bool? showDialogProgress,
    List<ItemSimpleModel>? selectedProjects,
    SelectableItem<String?>? selectedProject,
    bool? isProjectMandatory,
    bool? isNotesMandatory,
    List<SelectableItem<String>>? assignToUsers,
    SelectableItem<String>? selectedAssignedUser,
    bool? isMeetingScheduled,
    bool canUpdateSelectedAssignedUser = false,
  }) {
    return AppointmentsState(
      pageState: pageState ?? this.pageState,
      leadName: leadName ?? this.leadName,
      description: description ?? this.description,
      enquiryDescription: enquiryDescription ?? this.enquiryDescription,
      price: price ?? this.price,
      dimension: dimension ?? this.dimension,
      location: location ?? this.location,
      leadProjects: leadProjects ?? this.leadProjects,
      allProjects: allProjects ?? this.allProjects,
      initialSelectedProjects: initialSelectedProjects ?? this.initialSelectedProjects,
      duplicateAllProjects: duplicateAllProjects ?? this.duplicateAllProjects,
      locations: locations ?? this.locations,
      leadDocuments: leadDocuments ?? this.leadDocuments,
      leadLocation: leadLocation ?? this.leadLocation,
      selectedIndex: selectedIndex ?? this.selectedIndex,
      leadEntity: leadEntity ?? this.leadEntity,
      globalSettingModel: globalSettingModel ?? this.globalSettingModel,
      showDialogProgress: showDialogProgress ?? false,
      selectedProjects: selectedProjects ?? this.selectedProjects,
      selectedProject: selectedProject ?? this.selectedProject,
      isProjectMandatory: isProjectMandatory ?? this.isProjectMandatory,
      isNotesMandatory: isNotesMandatory ?? this.isNotesMandatory,
      assignToUsers: assignToUsers ?? this.assignToUsers,
      selectedAssignedUser: canUpdateSelectedAssignedUser ? (selectedAssignedUser ?? this.selectedAssignedUser) : null,
      isMeetingScheduled: isMeetingScheduled ?? this.isMeetingScheduled,
    );
  }

  AppointmentsState clearState({
    AppointmentsPageState? pageState,
    String? leadName,
    String? description,
    String? enquiryDescription,
    String? price,
    SelectableItem<String?>? dimension,
    String? location,
    List<SelectableItem<String?>>? leadProjects,
    List<SelectableItem<String?>>? allProjects,
    List<SelectableItem<String?>>? duplicateAllProjects,
    List<SelectableItem<String?>>? locations,
    List<SelectableItem<String?>>? leadDocuments,
    AddressModel? leadLocation,
    int? selectedIndex,
    GetLeadEntity? leadEntity,
    GlobalSettingModel? globalSettingModel,
    List<SelectableItem<String>>? assignToUsers,
    SelectableItem<String>? selectedAssignedUser,
    bool? isMeetingScheduled,
    SelectableItem<String>? loggedInUser,
  }) {
    return AppointmentsState(
      pageState: AppointmentsPageState.initial,
      leadName: leadName,
      description: description,
      enquiryDescription: enquiryDescription,
      price: price,
      dimension: dimension,
      location: location,
      leadProjects: leadProjects,
      allProjects: allProjects,
      duplicateAllProjects: duplicateAllProjects,
      locations: locations,
      leadDocuments: leadDocuments,
      leadLocation: leadLocation,
      selectedIndex: selectedIndex,
      leadEntity: leadEntity,
      globalSettingModel: globalSettingModel,
      showDialogProgress: false,
      selectedProjects: const [],
      selectedProject: null,
      initialSelectedProjects: const [],
      isProjectMandatory: false,
      isNotesMandatory: false,
      assignToUsers: const [],
      selectedAssignedUser: null,
      isMeetingScheduled: null,
    );
  }
}

enum AppointmentsPageState {
  initial,
  loading,
  failure,
  success,
  fetchingLocation,
  fetchingLocationError,
  updating,
}
