import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/enums/leads/lead_filter_keys.dart';
import 'package:leadrat/core_main/extensions/enum_extension.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';

class ItemLeadFilterCategoryModel {
  final LeadFilterKey filterKey;
  final bool hasCustomHeaderView;
  final bool hasSearch;
  final String? searchHintText;
  final List<ItemLeadFilterModel>? filters;
  final bool isSelected;
  final bool hasSelectAll;
  final bool hasMultiSelect;
  final bool isInitialized;
  final bool isCustomCategoryName;
  final String? categoryName;
  String? displayName;
  late bool isAllSelected;
  final bool isAreaRelated;
  final TextEditingController? searchController;
  final TextEditingController? pinCodeController;
  final bool isReorderAbleList;

  ItemLeadFilterCategoryModel({
    required this.filterKey,
    this.categoryName,
    this.hasCustomHeaderView = false,
    this.hasSearch = false,
    this.isSelected = false,
    this.hasMultiSelect = true,
    this.hasSelectAll = false,
    this.isInitialized = false,
    this.isAreaRelated = false,
    this.searchHintText,
    this.searchController,
    this.pinCodeController,
    this.filters,
    this.isCustomCategoryName = false,
    this.isReorderAbleList = false,
  }) {
    displayName = displayName ?? _setDisplayName();
    isAllSelected = filters?.every((element) => element.isSelected) ?? false;
  }

  String _setDisplayName() {
    final name = isCustomCategoryName ? categoryName ?? '' : filterKey.description;
    final selectedFilterLength = filters?.where((element) => element.isSelected).length ?? 0;
    if (selectedFilterLength > 0 && hasMultiSelect) {
      return "$name ($selectedFilterLength)";
    } else {
      return name;
    }
  }

  ItemLeadFilterCategoryModel copyWith({
    LeadFilterKey? filterKey,
    bool? hasCustomView,
    bool? hasSearch,
    String? searchHintText,
    List<ItemLeadFilterModel>? filters,
    bool? isSelected,
    bool? hasSelectAll,
    bool? hasMultiSelect,
    bool? isInitialized,
    bool? isAllSelected,
    TextEditingController? searchController,
    TextEditingController? pinCodeController,
    bool? isCustomCategoryName,
    bool? hasCustomHeaderView,
    String? categoryName,
    bool? isAreaRelated,
    bool? isReorderAbleList,
  }) {
    return ItemLeadFilterCategoryModel(
      filterKey: filterKey ?? this.filterKey,
      hasCustomHeaderView: hasCustomView ?? this.hasCustomHeaderView,
      hasSearch: hasSearch ?? this.hasSearch,
      searchHintText: searchHintText ?? this.searchHintText,
      filters: filters ?? this.filters,
      isSelected: isSelected ?? this.isSelected,
      hasSelectAll: hasSelectAll ?? this.hasSelectAll,
      hasMultiSelect: hasMultiSelect ?? this.hasMultiSelect,
      isInitialized: isInitialized ?? this.isInitialized,
      searchController: searchController ?? this.searchController,
      pinCodeController: pinCodeController ?? this.pinCodeController,
      isCustomCategoryName: isCustomCategoryName ?? this.isCustomCategoryName,
      categoryName: categoryName ?? this.categoryName,
      isAreaRelated: isAreaRelated ?? this.isAreaRelated,
      isReorderAbleList: isReorderAbleList ?? this.isReorderAbleList,
    );
  }
}

class ItemLeadFilterModel<T> {
  final LeadFilterKey? filterKey;
  final String displayName;
  final String? description;
  final bool isActive;
  final bool isSelectAll;
  final bool hasSubFilters;
  final bool isRequired;
  final dynamic category;
  final bool isSelected;
  final bool hasCustomView;
  final bool hasMonthFilter;
  final List<ItemSimpleModel>? subFilters;
  final T? value;
  final int? itemIndex;

  ItemLeadFilterModel({
    this.filterKey,
    required this.displayName,
    this.description,
    this.isActive = true,
    this.isSelectAll = false,
    this.hasSubFilters = false,
    this.isRequired = false,
    this.isSelected = false,
    this.hasCustomView = false,
    this.category,
    this.subFilters,
    this.value,
    this.itemIndex,
    this.hasMonthFilter = false,
  });

  ItemLeadFilterModel<T> copyWith({
    LeadFilterKey? filterKey,
    String? displayName,
    String? description,
    bool? isActive,
    bool? isSelectAll,
    bool? hasSubFilters,
    bool? isRequired,
    dynamic category,
    bool? isSelected,
    bool? hasCustomView,
    List<ItemSimpleModel>? subFilters,
    T? value,
    int? itemIndex,
    bool? hasMonthFilter,
  }) {
    return ItemLeadFilterModel<T>(
      filterKey: filterKey ?? this.filterKey,
      displayName: displayName ?? this.displayName,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      isSelectAll: isSelectAll ?? this.isSelectAll,
      hasSubFilters: hasSubFilters ?? this.hasSubFilters,
      isRequired: isRequired ?? this.isRequired,
      category: category ?? this.category,
      isSelected: isSelected ?? this.isSelected,
      hasCustomView: hasCustomView ?? this.hasCustomView,
      subFilters: subFilters ?? this.subFilters,
      value: value ?? this.value,
      itemIndex: itemIndex ?? this.itemIndex,
      hasMonthFilter: hasMonthFilter ?? this.hasMonthFilter,
    );
  }

  static List<T>? getSelectedEnums<T extends Enum>(List<ItemLeadFilterCategoryModel> categories, LeadFilterKey key, List<T> enumValues, {bool useDescription = false}) {
    try {
      var selectedItems = categories.firstWhereOrNull((category) => category.filterKey == key)?.filters?.where((filter) => filter.isSelected).toList();
      if (selectedItems?.isEmpty ?? true) return null;

      return selectedItems!.map((item) => useDescription ? getEnumFromValue<T>(enumValues, int.tryParse(item.description ?? "")) : getEnumFromDescription<T>(enumValues, item.displayName)).whereType<T>().toList();
    } catch (exception) {
      "Error while selecting the SelectedEnums, ${exception.toString()}".printInConsole();
      return null;
    }
  }

  static List<String>? getSelectedDisplayNames(List<ItemLeadFilterCategoryModel> categories, LeadFilterKey key) {
    try {
      var selectedItems = categories.firstWhereOrNull((category) => category.filterKey == key)?.filters?.where((filter) => filter.isSelected).toList();
      return selectedItems?.isNotEmpty ?? false ? selectedItems!.map((e) => e.displayName).toList() : null;
    } catch (exception) {
      "Error while selecting the displayNames, ${exception.toString()}".printInConsole();
      return null;
    }
  }

  static List<String>? getSelectedDescription(List<ItemLeadFilterCategoryModel> categories, LeadFilterKey key) {
    try {
      var selectedItems = categories.firstWhereOrNull((category) => category.filterKey == key)?.filters?.where((filter) => filter.isSelected).toList();
      return selectedItems?.isNotEmpty ?? false ? selectedItems!.map((e) => e.description ?? "").toList() : null;
    } catch (exception) {
      "Error while selecting the descriptions, ${exception.toString()}".printInConsole();
      return null;
    }
  }

  static List<T>? getSelectedValue<T>(List<ItemLeadFilterCategoryModel> categories, LeadFilterKey key) {
    try {
      var selectedItems = categories.firstWhereOrNull((category) => category.filterKey == key)?.filters?.where((filter) => filter.isSelected).toList();
      return selectedItems?.isNotEmpty ?? false ? selectedItems!.map<T>((e) => e.value).toList() : null;
    } catch (exception) {
      "Error while selecting the values, ${exception.toString()}".printInConsole();
      return null;
    }
  }
}
