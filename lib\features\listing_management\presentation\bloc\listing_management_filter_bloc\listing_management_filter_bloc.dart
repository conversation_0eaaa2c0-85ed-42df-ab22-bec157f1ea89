import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/usecase/use_case.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_amenity_model.dart';
import 'package:leadrat/core_main/common/data/global_settings/models/global_setting_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_type_model.dart';
import 'package:leadrat/core_main/common/data/master_data/repository/masterdata_repository.dart';
import 'package:leadrat/core_main/common/data/user/models/get_all_users_model.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/common/models/budget_range_model.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/common/date_range.dart';
import 'package:leadrat/core_main/enums/common/no_of_br.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/property_type_enum.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/property_enums/completion_status.dart';
import 'package:leadrat/core_main/enums/property_enums/facing.dart';
import 'package:leadrat/core_main/enums/property_enums/feature.dart';
import 'package:leadrat/core_main/enums/property_enums/floors.dart';
import 'package:leadrat/core_main/enums/property_enums/furnish_status.dart';
import 'package:leadrat/core_main/enums/property_enums/listing_management_filter_key.dart';
import 'package:leadrat/core_main/enums/property_enums/listing_status.dart';
import 'package:leadrat/core_main/enums/property_enums/property_date_type.dart';
import 'package:leadrat/core_main/enums/property_enums/property_status.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:leadrat/core_main/extensions/enum_extension.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/mapper/property_mapper.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/features/lead/domain/usecase/get_project_name_with_id_use_case.dart';
import 'package:leadrat/features/listing_management/data/models/listing_management_filter_model.dart';
import 'package:leadrat/features/listing_management/domain/repository/listing_management_repository.dart';
import 'package:leadrat/features/listing_management/presentation/item/item_property_listing_filter_model.dart';
import 'package:leadrat/features/projects/domain/entities/project_entity.dart';
import 'package:leadrat/features/properties/domain/usecase/get_community_and_sub_community_usecase.dart';
import 'package:leadrat/features/properties/domain/usecase/get_owner_names_or_addresses_usecase.dart';
import 'package:leadrat/features/properties/presentation/widgets/range_input_widget.dart';

part 'listing_management_filter_event.dart';
part 'listing_management_filter_state.dart';

class ListingManagementFilterBloc extends Bloc<ListingManagementFilterEvent, ListingManagementFilterState> {
  List<ItemListingPropertyFilterCategoryModel> listingManagementFilterCategories = [];
  late ListingManagementFilterModel _selectedListingManagementFilterModel;
  late GlobalSettingModel? _globalSettingModel;
  final UsersDataRepository _usersDataRepository;
  final ListingManagementRepository _listingManagementRepository;
  final GetCommunityUseCase _getCommunitiesUseCase;
  final GetSubCommunityUseCase _getSubCommunities;
  final MasterDataRepository _masterDataRepository;
  final GetOwnerNamesOrAddressesUseCase _getOwnerNamesOrAddressesUseCase;
  final GetProjectNameWithIdUseCase _getProjectNameWithIdUseCase;
  UserDetailsModel? _currentUser;
  bool isSearching = false;
  List<MasterPropertyTypeModel> _allPropertyTypes = [];
  List<MasterAreaUnitsModel?>? _allAreaUnits = [];

  ItemListingPropertyFilterModel? selectedFilterItem;

  ListingManagementFilterBloc(
    this._usersDataRepository,
    this._getCommunitiesUseCase,
    this._masterDataRepository,
    this._getOwnerNamesOrAddressesUseCase,
    this._getProjectNameWithIdUseCase,
    this._getSubCommunities,
    this._listingManagementRepository,
  ) : super(const ListingManagementFilterState()) {
    on<InitListingManagementFilterEvent>(_onInitListingManagementFilter);
    on<FilterCategorySelectEvent>(_onFilterCategorySelect);
    on<SelectFilterEvent>(_onSelectFilter);
    on<ApplyListingManagementFilterEvent>(_onApplyListingManagementFilter);
    on<SearchFiltersItemsEvent>(_onSearchFiltersItems);
    on<ResetFilterEvent>(_onResetFilter);
    on<SelectFromDateEvent>(_onSelectFromDate);
    on<SelectToDateEvent>(_onSelectToDate);
    on<CustomBudgetChangeEvent>(_onCustomBudgetChange);
    on<ChangeCurrencyEvent>(_onChangeCurrency);
    on<SelectDateTypeEvent>(_onSelectDateType);
    on<AreaUnitChangedEvent>(_onAreaUnitChanged);
    on<AreaSizeInputChangeEvent>(_onAreaSizeInputChangeEvent);
    on<InitAreaUnitsEvent>(_onInitAreaUnits);
    on<OnMinBudgetChangeEvent>(_onOnMinBudgetChangeEvent);
    on<OnMaxBudgetChangeEvent>(_onOnMaxBudgetChangeEvent);
  }

  FutureOr<void> _onInitListingManagementFilter(InitListingManagementFilterEvent event, Emitter<ListingManagementFilterState> emit) async {
    _globalSettingModel = event.globalSettingModel;
    _selectedListingManagementFilterModel = event.listingFilterModel ?? ListingManagementFilterModel(propertyVisibility: PropertyVisibility.all);
    listingManagementFilterCategories = _setUpListingManagementFilters();
    _currentUser = _usersDataRepository.getLoggedInUser();
    _updateState(
      emit,
      listingManagementFilterCategories: listingManagementFilterCategories,
      listingManagementFilterModel: _selectedListingManagementFilterModel,
      pageState: PageState.initial,
      selectedToDate: null,
      selectedFromDate: null,
      customBudgetRange: state.customBudget,
      selectedCurrency: null,
      allCurrencies: [],
      selectedDateType: null,
    );
    add(FilterCategorySelectEvent(selectedCategoryIndex: state.selectedCategoryIndex, itemListingFilterCategoryModel: listingManagementFilterCategories[state.selectedCategoryIndex]));
    await _initSelectedFilters(emit);
    add(InitAreaUnitsEvent());
  }

  FutureOr<void> _onFilterCategorySelect(FilterCategorySelectEvent event, Emitter<ListingManagementFilterState> emit) async {
    var selectedItem = event.itemListingFilterCategoryModel;
    var updatedLeadCategories = state.listingManagementFilterCategories
        .map((category) => category.filterKey == selectedItem.filterKey
            ? selectedItem.copyWith(isSelected: true, maxCountController: category.hasCount ? category.maxCountController : null, minCountController: category.hasCount ? category.minCountController : null, isInitialized: (category.minCountController != null || (category.minCountController != null)) || (selectedItem.filters?.isNotEmpty ?? false) || category.hasCustomFilters, searchController: TextEditingController())
            : category.copyWith(
                isSelected: false,
                searchController: TextEditingController(),
              ))
        .toList();
    _updateState(emit, listingManagementFilterCategories: updatedLeadCategories, selectedCategoryIndex: event.selectedCategoryIndex);

    if (((selectedItem.filters?.isEmpty ?? true) && !selectedItem.hasCustomFilters) || [ListingManagementFilterKey.minBudget].contains(selectedItem.filterKey)) {
      await _initializeFilter(selectedItem.filterKey, null, emit);
    }
  }

  FutureOr<void> _onSelectFilter(SelectFilterEvent event, Emitter<ListingManagementFilterState> emit) async {
    var selectedFilterCategory = state.listingManagementFilterCategories.firstWhereOrNull((element) => element.filterKey == event.selectedFilterCategory.filterKey);
    List<ItemListingPropertyFilterModel>? updatedFilterList;

    //check for date range filter
    if (selectedFilterCategory?.filterKey == ListingManagementFilterKey.dateRange && state.selectedDateType?.value == null) {
      emit(state.copyWith(pageState: PageState.failure, errorMessage: "Please select the date type"));
      return null;
    }

    if (selectedFilterCategory == null) return;
    selectedFilterItem = event.itemListingFilterModel;
    if (event.isSelectAll) {
      if (selectedFilterCategory.searchController?.text.isNullOrEmpty() ?? true) {
        updatedFilterList = selectedFilterCategory.filters?.map((e) => e.copyWith(isSelected: !(selectedFilterCategory.isAllSelected))).toList();
      } else {
        final searchedFilters = state.searchFilteredCategories.firstWhereOrNull((element) => element.filterKey == event.selectedFilterCategory.filterKey);
        updatedFilterList = selectedFilterCategory.filters?.map((item) {
          if (searchedFilters?.filters?.any((element) => element.displayName == item.displayName && element.description == item.description) ?? false) {
            return item.copyWith(isSelected: !(selectedFilterCategory.isAllSelected));
          }
          return item;
        }).toList();
      }
    } else if (event.itemListingFilterModel != null) {
      var selectedFilterItem = event.itemListingFilterModel!;
      updatedFilterList = selectedFilterCategory.filters?.map((item) {
        if (item.displayName == selectedFilterItem.displayName && item.description == selectedFilterItem.description) {
          return selectedFilterItem.copyWith(isSelected: !selectedFilterItem.isSelected);
        }
        return selectedFilterCategory.hasMultiSelect ? item : item.copyWith(isSelected: false);
      }).toList();
    }

    if (updatedFilterList != null) {
      var updatedCategoryList = state.listingManagementFilterCategories.map((e) => e.filterKey == selectedFilterCategory.filterKey ? selectedFilterCategory.copyWith(filters: updatedFilterList) : e).toList();
      _updateState(emit, listingManagementFilterCategories: updatedCategoryList);
    }

    if (selectedFilterCategory.filterKey == ListingManagementFilterKey.propertyType) {
      final selectedPropertyTypes = updatedFilterList?.where((element) => element.isSelected).toList();
      await _updatePropertySubTypes(emit, selectedPropertyTypes);
    }
    if (selectedFilterCategory.searchController?.text.isNotNullOrEmpty() ?? false) {
      add(SearchFiltersItemsEvent(searchText: selectedFilterCategory.searchController?.text, filterKey: event.selectedFilterCategory.filterKey));
    }
  }

  FutureOr<void> _onSearchFiltersItems(SearchFiltersItemsEvent event, Emitter<ListingManagementFilterState> emit) async {
    if (event.searchText == null) return;
    final selectedFilterCategoryFilters = state.listingManagementFilterCategories.firstWhereOrNull((category) => category.filterKey == event.filterKey)?.filters;
    final filteredList = selectedFilterCategoryFilters?.where((element) => element.displayName.toLowerCase().contains(event.searchText!.toLowerCase())).toList();
    final filteredCategories = state.listingManagementFilterCategories.map((category) {
      if (category.filterKey == event.filterKey) return category.copyWith(filters: filteredList);
      return category;
    }).toList();
    emit(state.copyWith(searchFilteredCategories: filteredCategories));
  }

  FutureOr<void> _onSelectFromDate(SelectFromDateEvent event, Emitter<ListingManagementFilterState> emit) {
    emit(state.copyWith(selectedFromDate: event.selectedFromDate));
  }

  FutureOr<void> _onSelectToDate(SelectToDateEvent event, Emitter<ListingManagementFilterState> emit) {
    emit(state.copyWith(selectedToDate: event.selectedToDate));
  }

  FutureOr<void> _onApplyListingManagementFilter(ApplyListingManagementFilterEvent event, Emitter<ListingManagementFilterState> emit) {
    var listingManagementFilterModel = ListingManagementFilterModel();
    var selectedFilterCategories = state.listingManagementFilterCategories.where((category) => category.filters?.any((filter) => filter.isSelected) ?? false).toList();
    final selectedPossessionDateRange = ItemListingPropertyFilterModel.getSelectedEnums<PossessionType>(selectedFilterCategories, ListingManagementFilterKey.dateRange, PossessionType.values)?.firstOrNull;
    final selectedDateRange = ItemListingPropertyFilterModel.getSelectedEnums(selectedFilterCategories, ListingManagementFilterKey.dateRange, DateRange.values)?.firstOrNull;

    String? fromDate, toDate;
    if (selectedDateRange != null && selectedDateRange == DateRange.customDate) {
      fromDate = state.selectedFromDate?.getBasedOnTimeZone().toString() ?? DateTime.now().getBasedOnTimeZone().toString();
      toDate = state.selectedToDate?.getBasedOnTimeZone().toString() ?? DateTime.now().getBasedOnTimeZone().toString();
    } else if (selectedDateRange != null) {
      var selectedDate = _getDateTimeFromRange(selectedDateRange);
      fromDate = selectedDate.$2;
      toDate = selectedDate.$1;
    }
    if (selectedPossessionDateRange != null && selectedPossessionDateRange == PossessionType.customDate) {
      fromDate = utcToDateFormat(state.selectedFromDate?.toUniversalTimeStartOfDay() ?? DateTime.now().toUserTimeZone());
      toDate = utcToDateFormat(state.selectedToDate?.toUniversalTimeStartOfDay() ?? DateTime.now().toUserTimeZone());
    }

    BudgetRangeModel? selectedBudget;

    final isCustomBudgetRange = selectedBudget?.isCustomBudget ?? false;
    final budgetFilter = isCustomBudgetRange ? state.customBudget : selectedBudget;
    listingManagementFilterModel = listingManagementFilterModel.copyWith(
      enquiryType: ItemListingPropertyFilterModel.getSelectedEnums<EnquiryType>(selectedFilterCategories, ListingManagementFilterKey.lookingFor, EnquiryType.values)?.firstOrNull,
      propertyTypes: ItemListingPropertyFilterModel.getSelectedEnums<PropertyType>(selectedFilterCategories, ListingManagementFilterKey.propertyType, PropertyType.values),
      propertySubTypes: ItemListingPropertyFilterModel.getSelectedValue<MasterPropertyTypeModel>(selectedFilterCategories, ListingManagementFilterKey.propertySubType),
      listingLevel: ItemListingPropertyFilterModel.getSelectedEnums<ListingLevel>(selectedFilterCategories, ListingManagementFilterKey.listingLevel, ListingLevel.values)?.firstOrNull,
      offeringType: ItemListingPropertyFilterModel.getSelectedEnums<OfferingType>(selectedFilterCategories, ListingManagementFilterKey.offeringType, OfferingType.values)?.firstOrNull,
      facing: ItemListingPropertyFilterModel.getSelectedEnums<Facing>(selectedFilterCategories, ListingManagementFilterKey.facing, Facing.values)?.firstOrNull,
      furnishStatus: ItemListingPropertyFilterModel.getSelectedEnums<FurnishStatus>(selectedFilterCategories, ListingManagementFilterKey.furnishingStatus, FurnishStatus.values),
      noOfBedrooms: ItemListingPropertyFilterModel.getSelectedEnums<Feature>(selectedFilterCategories, ListingManagementFilterKey.noOfBedrooms, Feature.values),
      noOfUtilities: ItemListingPropertyFilterModel.getSelectedEnums<Feature>(selectedFilterCategories, ListingManagementFilterKey.noOfUtilities, Feature.values),
      noOfKitchens: ItemListingPropertyFilterModel.getSelectedEnums<Feature>(selectedFilterCategories, ListingManagementFilterKey.noOfKitchens, Feature.values),
      noOfLivingRooms: ItemListingPropertyFilterModel.getSelectedEnums<Feature>(selectedFilterCategories, ListingManagementFilterKey.noOfLivingRooms, Feature.values),
      noOfBalconies: ItemListingPropertyFilterModel.getSelectedEnums<Feature>(selectedFilterCategories, ListingManagementFilterKey.noOfBalconies, Feature.values),
      noOfBathrooms: ItemListingPropertyFilterModel.getSelectedEnums<Feature>(selectedFilterCategories, ListingManagementFilterKey.noOfBathrooms, Feature.values),
      noOfBrs: ItemListingPropertyFilterModel.getSelectedEnums<NoOfPropertiesBr>(selectedFilterCategories, ListingManagementFilterKey.noOfBr, NoOfPropertiesBr.values),
      communities: ItemListingPropertyFilterModel.getSelectedDescription(selectedFilterCategories, ListingManagementFilterKey.community),
      subCommunities: ItemListingPropertyFilterModel.getSelectedDescription(selectedFilterCategories, ListingManagementFilterKey.subCommunity),
      locations: ItemListingPropertyFilterModel.getSelectedDescription(selectedFilterCategories, ListingManagementFilterKey.locations),
      ownerName: ItemListingPropertyFilterModel.getSelectedDescription(selectedFilterCategories, ListingManagementFilterKey.ownerNames),
      amenities: ItemListingPropertyFilterModel.getSelectedValue<CustomAmenityModel>(selectedFilterCategories, ListingManagementFilterKey.amenities),
      assignTo: ItemListingPropertyFilterModel.getSelectedValue<GetAllUsersModel>(selectedFilterCategories, ListingManagementFilterKey.listingBy),
      listingOnBehalf: ItemListingPropertyFilterModel.getSelectedValue<GetAllUsersModel>(selectedFilterCategories, ListingManagementFilterKey.listingOnBehalf),
      floors: ItemListingPropertyFilterModel.getSelectedEnums<Floors>(selectedFilterCategories, ListingManagementFilterKey.floors, Floors.values),
      projects: ItemListingPropertyFilterModel.getSelectedValue<ProjectEntity>(selectedFilterCategories, ListingManagementFilterKey.projects),
      completionStatus: ItemListingPropertyFilterModel.getSelectedEnums<CompletionStatus>(selectedFilterCategories, ListingManagementFilterKey.completionStatus, CompletionStatus.values)?.firstOrNull,
      builtUpAreaUnit: state.selectedBuiltUpAreaUnit?.value,
      carpetAreaUnit: state.selectedCarpetAreaUnit?.value,
      netAreaUnit: state.selectedNetAreaUnit?.value,
      propertySizeUnit: state.selectedPropertySizeAreaUnit?.value,
      saleableAreaUnit: state.selectedSaleableAreaUnit?.value,
      minBuiltUpArea: state.builtUpArea?.min,
      maxBuiltUpArea: state.builtUpArea?.max,
      minCarpetArea: state.carpetArea?.min,
      maxCarpetArea: state.carpetArea?.max,
      minNetArea: state.netArea?.min,
      maxNetArea: state.netArea?.max,
      minSaleableArea: state.saleableArea?.min,
      maxSaleableArea: state.saleableArea?.max,
      minPropertySize: state.propertySize?.min,
      maxPropertySize: state.propertySize?.max,
      budgetFilters: budgetFilter,
      currency: state.selectedCurrency?.value,
      fromDate: fromDate,
      toDate: toDate,
      dateType: state.selectedDateType?.value,
      possessionTypeDateRange: selectedPossessionDateRange,
      toMinBudget: state.minBudget?.max,
      fromMinBudget: state.minBudget?.min,
      toMaxBudget: state.maxBudget?.max,
      fromMaxBudget: state.maxBudget?.min,
      maxLeadCount: _getLeadOrProspectCount(propertyFilterKey: ListingManagementFilterKey.leadCount, isMinCount: false),
      minLeadCount: _getLeadOrProspectCount(propertyFilterKey: ListingManagementFilterKey.leadCount, isMinCount: true),
      maxProspectCount: _getLeadOrProspectCount(propertyFilterKey: ListingManagementFilterKey.prospectCount, isMinCount: false),
      minProspectCount: _getLeadOrProspectCount(propertyFilterKey: ListingManagementFilterKey.prospectCount, isMinCount: true),
      dateRange: ItemListingPropertyFilterModel.getSelectedEnums<DateRange>(selectedFilterCategories, ListingManagementFilterKey.dateRange, DateRange.values)?.firstOrNull,
    );

    _updateState(emit, pageState: PageState.success, listingManagementFilterModel: listingManagementFilterModel);
  }

  String? _getLeadOrProspectCount({required ListingManagementFilterKey propertyFilterKey, bool isMinCount = false}) {
    final selectedFilterCategoryModel = state.listingManagementFilterCategories.where((element) => element.filterKey == propertyFilterKey).firstOrNull;

    return isMinCount ? selectedFilterCategoryModel?.minCountController?.text : selectedFilterCategoryModel?.maxCountController?.text;
  }

  FutureOr<void> _onResetFilter(ResetFilterEvent event, Emitter<ListingManagementFilterState> emit) {
    emit(state.copyWith(
      updateSelectedDateType: false,
      updateSelectedToDate: false,
      updateSelectedFromDate: false,
      updateCustomBudget: false,
      selectedCategoryIndex: 0,
      updateSelectMaxBudget: false,
      updateSelectMinBudget: false,
    ));
    final updatedListingManagementFilterCategories = state.listingManagementFilterCategories.map((category) {
      return category.copyWith(resetCount: true, filters: category.filters?.map((filter) => filter.copyWith(isSelected: false)).toList());
    }).toList();
    _updateState(
      emit,
      listingManagementFilterCategories: updatedListingManagementFilterCategories,
      listingManagementFilterModel: ListingManagementFilterModel(propertyVisibility: PropertyVisibility.all),
      pageState: PageState.initial,
      selectedToDate: null,
      selectedFromDate: null,
      customBudgetRange: state.customBudget,
      selectedCurrency: null,
      allCurrencies: [],
      selectedDateType: null,
      resetValues: true,
    );
    add(FilterCategorySelectEvent(selectedCategoryIndex: state.selectedCategoryIndex, itemListingFilterCategoryModel: listingManagementFilterCategories[state.selectedCategoryIndex]));
  }

  FutureOr<void> _onCustomBudgetChange(CustomBudgetChangeEvent event, Emitter<ListingManagementFilterState> emit) {
    emit(state.copyWith(customBudget: state.customBudget.copyWith(minBudget: event.startValue, maxBudget: event.endValue)));
  }

  Future<void> _initSelectedFilters(Emitter<ListingManagementFilterState> emit) async {
    if (_selectedListingManagementFilterModel.enquiryType != null) await _initializeFilter(ListingManagementFilterKey.lookingFor, _initLookingFor, emit);
    if (_selectedListingManagementFilterModel.propertyTypes?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.propertyType, _initPropertyTypes, emit);
    if (_selectedListingManagementFilterModel.propertySubTypes?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.propertySubType, _initPropertySubTypes, emit);
    if (_selectedListingManagementFilterModel.listingLevel != null) await _initializeFilter(ListingManagementFilterKey.listingLevel, _initListingLevel, emit);
    if (_selectedListingManagementFilterModel.offeringType != null) await _initializeFilter(ListingManagementFilterKey.offeringType, _initOfferingTypes, emit);
    if (_selectedListingManagementFilterModel.completionStatus != null) await _initializeFilter(ListingManagementFilterKey.completionStatus, _initCompletionStatus, emit);
    if (_selectedListingManagementFilterModel.communities?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.community, _initCommunities, emit);
    if (_selectedListingManagementFilterModel.subCommunities?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.subCommunity, _initSubCommunities, emit);
    if (_selectedListingManagementFilterModel.facing != null) await _initializeFilter(ListingManagementFilterKey.facing, _initFacings, emit);
    if (_selectedListingManagementFilterModel.furnishStatus?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.furnishingStatus, _initFurnishingStatus, emit);
    // if (_selectedListingManagementFilterModel.bhkTypes?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.brType, _initBRTypes, emit);
    if (_selectedListingManagementFilterModel.noOfBrs?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.noOfBr, _initBrs, emit);
    if (_selectedListingManagementFilterModel.floors?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.floors, _initNoOfFloors, emit);
    if (_selectedListingManagementFilterModel.ownerName?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.ownerNames, _initOwnerNames, emit);
    if (_selectedListingManagementFilterModel.projects?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.projects, _initProjects, emit);
    if (_selectedListingManagementFilterModel.listingOnBehalf?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.listingOnBehalf, _initListingOnBehalf, emit);
    if (_selectedListingManagementFilterModel.assignTo?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.listingBy, _initAssignedToUsers, emit);
    if (_selectedListingManagementFilterModel.locations?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.locations, _initLocations, emit);
    if (_selectedListingManagementFilterModel.noOfBathrooms?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.noOfBathrooms, _initNoOfBaths, emit);
    if (_selectedListingManagementFilterModel.noOfLivingRooms?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.noOfLivingRooms, _initNoOfLivingRooms, emit);
    if (_selectedListingManagementFilterModel.noOfBedrooms?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.noOfBedrooms, _initNoOfBedroom, emit);
    if (_selectedListingManagementFilterModel.noOfKitchens?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.noOfKitchens, _initNoOfKitchen, emit);
    if (_selectedListingManagementFilterModel.noOfUtilities?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.noOfUtilities, _initNoOfUtilities, emit);
    if (_selectedListingManagementFilterModel.amenities?.isNotEmpty ?? false) await _initializeFilter(ListingManagementFilterKey.amenities, _initAmenities, emit);

    if (_selectedListingManagementFilterModel.possessionTypeDateRange != null) await _initializeFilter(ListingManagementFilterKey.dateRange, _initDateRanges, emit);
    if (_selectedListingManagementFilterModel.carpetAreaUnit == null && _selectedListingManagementFilterModel.minCarpetArea == null && _selectedListingManagementFilterModel.maxCarpetArea == null) emit(state.copyWith(removeCarpetArea: true));
    if (_selectedListingManagementFilterModel.netAreaUnit == null && _selectedListingManagementFilterModel.minNetArea == null && _selectedListingManagementFilterModel.maxNetArea == null) emit(state.copyWith(removeNetArea: true));
    if (_selectedListingManagementFilterModel.saleableAreaUnit == null && _selectedListingManagementFilterModel.minSaleableArea == null && _selectedListingManagementFilterModel.maxSaleableArea == null) emit(state.copyWith(removeSaleableArea: true));
    if (_selectedListingManagementFilterModel.builtUpAreaUnit == null && _selectedListingManagementFilterModel.minBuiltUpArea == null && _selectedListingManagementFilterModel.maxBuiltUpArea == null) emit(state.copyWith(removeBuiltUpArea: true));
    if (_selectedListingManagementFilterModel.propertySizeUnit == null && _selectedListingManagementFilterModel.minPropertySize == null && _selectedListingManagementFilterModel.maxPropertySize == null) emit(state.copyWith(removePropertySize: true));
    if (_selectedListingManagementFilterModel.fromMinBudget != null || _selectedListingManagementFilterModel.toMinBudget != null) await _initializeFilter(ListingManagementFilterKey.minBudget, _initMinBudgetFilters, emit);

    if (_selectedListingManagementFilterModel.maxLeadCount != null || _selectedListingManagementFilterModel.minLeadCount != null) {
      _makeLeadOrProspectCountNull(propertyFilterKey: ListingManagementFilterKey.leadCount, emit: emit, minCount: _selectedListingManagementFilterModel.minLeadCount, maxCount: _selectedListingManagementFilterModel.maxLeadCount);
    }
    if (_selectedListingManagementFilterModel.maxProspectCount != null || _selectedListingManagementFilterModel.minProspectCount != null) {
      _makeLeadOrProspectCountNull(propertyFilterKey: ListingManagementFilterKey.prospectCount, emit: emit, minCount: _selectedListingManagementFilterModel.minProspectCount, maxCount: _selectedListingManagementFilterModel.maxProspectCount);
    }
  }

  void _makeLeadOrProspectCountNull({required ListingManagementFilterKey propertyFilterKey, String? minCount, String? maxCount, required Emitter<ListingManagementFilterState> emit}) {
    var minCountController = TextEditingController();
    if (minCount != null) minCountController.text = minCount;
    var maxCountController = TextEditingController();
    if (maxCount != null) maxCountController.text = maxCount;
    final filteredCategoryModels = state.listingManagementFilterCategories.map((element) => element.filterKey == propertyFilterKey ? element.copyWith(maxCountController: maxCountController, minCountController: minCountController) : element).toList();

    emit(state.copyWith(listingManagementFilterCategories: filteredCategoryModels));
  }

  List<ItemListingPropertyFilterCategoryModel> _setUpListingManagementFilters() {
    return [
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.lookingFor, filters: [], hasMultiSelect: false),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.propertyType, filters: [], hasMultiSelect: true, hasSelectAll: true),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.propertySubType, filters: [], hasSelectAll: true, searchController: TextEditingController(), searchHintText: "search property sub types", hasSearch: true),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.listingLevel, filters: [], hasSelectAll: false, hasMultiSelect: false),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.offeringType, filters: [], hasSelectAll: false, hasMultiSelect: false),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.completionStatus, filters: [], hasSelectAll: true),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.community, filters: [], hasSelectAll: false, hasSearch: true, searchHintText: "search community names", searchController: TextEditingController()),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.subCommunity, filters: [], hasSelectAll: false, hasSearch: true, searchHintText: "search sub-community names", searchController: TextEditingController()),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.minBudget, filters: [], hasCustomFilters: true),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.facing, filters: []),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.furnishingStatus, filters: [], hasSelectAll: true),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.noOfBr, filters: []),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.floors, filters: []),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.ownerNames, filters: [], hasSearch: true, searchHintText: "search owners", searchController: TextEditingController()),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.projects, filters: [], hasSearch: true, searchHintText: "search projects", searchController: TextEditingController()),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.listingOnBehalf, filters: [], hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.listingBy, filters: [], hasSearch: true, searchHintText: "search users", searchController: TextEditingController()),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.dateRange, filters: [], hasCustomHeaderView: true, hasSelectAll: false, hasMultiSelect: false),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.propertySize, filters: [], hasCustomFilters: true),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.saleableArea, filters: [], hasCustomFilters: true),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.builtUpArea, filters: [], hasCustomFilters: true),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.carpetArea, filters: [], hasCustomFilters: true),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.netArea, filters: [], hasCustomFilters: true),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.locations, filters: [], hasSearch: true, searchHintText: "search locations", searchController: TextEditingController()),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.noOfBathrooms, filters: []),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.noOfLivingRooms, filters: []),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.noOfBedrooms, filters: []),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.noOfKitchens, filters: []),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.noOfUtilities, filters: []),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.amenities, filters: [], hasSearch: true, searchHintText: "search amenities", searchController: TextEditingController()),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.leadCount, filters: [], hasCount: true, hasMultiSelect: false, minCountController: TextEditingController(), maxCountController: TextEditingController()),
      ItemListingPropertyFilterCategoryModel(filterKey: ListingManagementFilterKey.prospectCount, filters: [], hasCount: true, hasMultiSelect: false, minCountController: TextEditingController(), maxCountController: TextEditingController()),
    ];
  }

  Future<void> _initializeFilter(ListingManagementFilterKey filterKey, Function? initFunction, Emitter<ListingManagementFilterState> emit) async {
    try {
      if (initFunction != null) {
        await initFunction(emit);
      } else {
        switch (filterKey) {
          case ListingManagementFilterKey.lookingFor:
            _initLookingFor(emit);
            break;
          case ListingManagementFilterKey.propertyType:
            _initPropertyTypes(emit);
            break;
          case ListingManagementFilterKey.propertySubType:
            await _initPropertySubTypes(emit);
            break;
          case ListingManagementFilterKey.listingLevel:
            _initListingLevel(emit);
            break;
          case ListingManagementFilterKey.offeringType:
            _initOfferingTypes(emit);
            break;
          case ListingManagementFilterKey.completionStatus:
            _initCompletionStatus(emit);
            break;
          case ListingManagementFilterKey.community:
            await _initCommunities(emit);
            break;
          case ListingManagementFilterKey.subCommunity:
            await _initSubCommunities(emit);
            break;

          case ListingManagementFilterKey.minBudget:
            await _initMinBudgetFilters(emit);
            break;

          case ListingManagementFilterKey.listingBy:
            await _initAssignedToUsers(emit);
            break;
          case ListingManagementFilterKey.listingOnBehalf:
            await _initListingOnBehalf(emit);
            break;
          case ListingManagementFilterKey.facing:
            _initFacings(emit);
            break;
          case ListingManagementFilterKey.furnishingStatus:
            _initFurnishingStatus(emit);
            break;

          case ListingManagementFilterKey.noOfBr:
            _initBrs(emit);
            break;
          case ListingManagementFilterKey.floors:
            _initNoOfFloors(emit);
            break;
          case ListingManagementFilterKey.ownerNames:
            await _initOwnerNames(emit);
            break;
          case ListingManagementFilterKey.projects:
            await _initProjects(emit);
            break;
          case ListingManagementFilterKey.propertySize:
            _initBrs(emit);
            break;
          case ListingManagementFilterKey.dateRange:
            _initDateRanges(emit);
            break;
          case ListingManagementFilterKey.locations:
            await _initLocations(emit);
            break;
          case ListingManagementFilterKey.noOfBathrooms:
            _initNoOfBaths(emit);
            break;
          case ListingManagementFilterKey.noOfLivingRooms:
            _initNoOfLivingRooms(emit);
            break;
          case ListingManagementFilterKey.noOfBalconies:
            _initNoOfBalconies(emit);
            break;
          case ListingManagementFilterKey.noOfBedrooms:
            _initNoOfBedroom(emit);
            break;
          case ListingManagementFilterKey.noOfKitchens:
            _initNoOfKitchen(emit);
            break;
          case ListingManagementFilterKey.noOfUtilities:
            _initNoOfUtilities(emit);
            break;
          case ListingManagementFilterKey.amenities:
            await _initAmenities(emit);
            break;
          default:
            break;
        }
      }
    } catch (ex) {
      "Error while initializing $filterKey filter: ${ex.toString()}".printInConsole();
    }
  }

  void _initLookingFor(Emitter<ListingManagementFilterState> emit) {
    final initialSelectedEnquiryType = _selectedListingManagementFilterModel.enquiryType;
    var lookingFor = EnquiryType.values
        .whereNot((element) => [EnquiryType.none, EnquiryType.buy].contains(element))
        .map((e) => ItemListingPropertyFilterModel(
              displayName: e.description,
              filterKey: ListingManagementFilterKey.lookingFor,
              isSelected: initialSelectedEnquiryType == e,
            ))
        .toList();
    _updateFilterCategory(ListingManagementFilterKey.lookingFor, lookingFor, emit);
  }

  void _initListingLevel(Emitter<ListingManagementFilterState> emit) {
    var initialSelectedListingLevel = _selectedListingManagementFilterModel.listingLevel;
    var listingLevels = ListingLevel.values.whereNot((element) => element == ListingLevel.none).map((e) => ItemListingPropertyFilterModel(displayName: e.description, filterKey: ListingManagementFilterKey.listingLevel, isSelected: initialSelectedListingLevel == e)).toList();
    _updateFilterCategory(ListingManagementFilterKey.listingLevel, listingLevels, emit);
  }

  void _initCompletionStatus(Emitter<ListingManagementFilterState> emit) {
    final initialSelectedEnquiryType = _selectedListingManagementFilterModel.completionStatus;
    var completionStatus = CompletionStatus.values
        .whereNot((element) => element == CompletionStatus.none)
        .map((e) => ItemListingPropertyFilterModel(
              displayName: e.description,
              filterKey: ListingManagementFilterKey.completionStatus,
              isSelected: initialSelectedEnquiryType == e,
            ))
        .toList();
    _updateFilterCategory(ListingManagementFilterKey.completionStatus, completionStatus, emit);
  }

  Future<void> _initMinBudgetFilters(Emitter<ListingManagementFilterState> emit) async {
    try {
      await _initializeCurrency(emit, _selectedListingManagementFilterModel.currency);

      final initialSelectMinBudget = _selectedListingManagementFilterModel.fromMinBudget ?? _selectedListingManagementFilterModel.toMinBudget;

      emit(state.copyWith(
        updateSelectMinBudget: initialSelectMinBudget != null ? true : false,
      ));
    } catch (ex) {
      rethrow;
    }
  }

  Future<void> _initializeCurrency(Emitter<ListingManagementFilterState> emit, String? selectedCurrency) async {
    try {
      if (state.currencies.isEmpty) {
        var currencies = await _listingManagementRepository.getCurrencies();
        List<SelectableItem<String>> allCurrencies = [];
        SelectableItem<String>? selectableItem;
        currencies?.forEach((item) => allCurrencies.add(SelectableItem<String>(title: item, value: item)));
        if (_globalSettingModel != null) {
          var defaultSymbol = _globalSettingModel?.countries?.firstOrNull?.defaultCurrency ?? "INR";
          selectableItem = allCurrencies.firstWhereOrNull((element) => element.value == defaultSymbol);
        }
        _updateState(emit, allCurrencies: allCurrencies, selectedCurrency: selectableItem);
      }
      if (selectedCurrency != null && state.currencies.isNotEmpty) {
        final selectedItem = state.currencies.firstWhereOrNull((element) => element.value == selectedCurrency);
        _updateState(emit, selectedCurrency: selectedItem);
      }
    } catch (ex, stackTrace) {
      ex.logException(stackTrace);
      "Error while initializing currency".printInConsole();
    }
  }

  Future<void> _initAssignedToUsers(Emitter<ListingManagementFilterState> emit) async {
    try {
      final initialSelectedSecondaryUsersIds = _selectedListingManagementFilterModel.assignTo?.map((e) => e.id).whereNotNull().toList();
      final getAllUsers = await _usersDataRepository.getAllReportees();
      final currentUserAsGetAllUsersModel = getAllUsers?.where((element) => element?.id == _currentUser?.userId).firstOrNull;
      final assignToUsers = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => ItemListingPropertyFilterModel(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                value: e,
                filterKey: ListingManagementFilterKey.listingBy,
                isSelected: initialSelectedSecondaryUsersIds?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      assignToUsers?.insert(0, ItemListingPropertyFilterModel(displayName: "You", description: _currentUser?.userId ?? "", value: currentUserAsGetAllUsersModel, filterKey: ListingManagementFilterKey.listingBy, isSelected: initialSelectedSecondaryUsersIds?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => assignToUsers?.add(ItemListingPropertyFilterModel(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: ListingManagementFilterKey.listingBy, isSelected: initialSelectedSecondaryUsersIds?.contains(e?.id ?? "") ?? false)));
      if (assignToUsers?.isNotEmpty ?? false) {
        _updateFilterCategory(ListingManagementFilterKey.listingBy, assignToUsers!, emit);
      }
    } catch (ex) {
      "Error while initializing assigned to users ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initListingOnBehalf(Emitter<ListingManagementFilterState> emit) async {
    try {
      final initialSelectedSecondaryUsersIds = _selectedListingManagementFilterModel.listingOnBehalf?.map((e) => e.id).whereNotNull().toList();
      final getAllUsers = await _usersDataRepository.getAllReportees();
      final currentUserAsGetAllUsersModel = getAllUsers?.where((element) => element?.id == _currentUser?.userId).firstOrNull;
      final listingOnBehalf = getAllUsers
          ?.whereNot((element) => element?.id == _currentUser?.userId || !(element?.isActive ?? true))
          .map((e) => ItemListingPropertyFilterModel(
                displayName: e?.toBaseUserModel().fullName ?? "",
                description: e?.id ?? "",
                value: e,
                filterKey: ListingManagementFilterKey.listingOnBehalf,
                isSelected: initialSelectedSecondaryUsersIds?.contains(e?.id ?? "") ?? false,
              ))
          .toList();
      listingOnBehalf?.insert(0, ItemListingPropertyFilterModel(displayName: "You", description: _currentUser?.userId ?? "", value: currentUserAsGetAllUsersModel, filterKey: ListingManagementFilterKey.listingOnBehalf, isSelected: initialSelectedSecondaryUsersIds?.contains(_currentUser?.userId ?? "") ?? false));
      getAllUsers?.where((element) => !(element?.isActive ?? true)).forEach((e) => listingOnBehalf?.add(ItemListingPropertyFilterModel(isActive: false, displayName: e?.toBaseUserModel().fullName ?? "", description: e?.id ?? "", filterKey: ListingManagementFilterKey.listingOnBehalf, isSelected: initialSelectedSecondaryUsersIds?.contains(e?.id ?? "") ?? false)));
      if (listingOnBehalf?.isNotEmpty ?? false) {
        _updateFilterCategory(ListingManagementFilterKey.listingOnBehalf, listingOnBehalf!, emit);
      }
    } catch (ex) {
      "Error while initializing listing on behalf ${ex.toString()}".printInConsole();
    }
  }

  void _initFurnishingStatus(Emitter<ListingManagementFilterState> emit) {
    try {
      final initialSelectedFurnishingStatus = _selectedListingManagementFilterModel.furnishStatus;
      final furnishingStatuses = FurnishStatus.values.where((e) => e != FurnishStatus.none).map((e) => ItemListingPropertyFilterModel(displayName: e.description, value: e, filterKey: ListingManagementFilterKey.furnishingStatus, isSelected: initialSelectedFurnishingStatus?.contains(e) ?? false)).toList();
      _updateFilterCategory(ListingManagementFilterKey.furnishingStatus, furnishingStatuses, emit);
    } catch (ex) {
      "Error while initializing Furnishing Status ${ex.toString()}".printInConsole();
    }
  }

  void _initNoOfBaths(Emitter<ListingManagementFilterState> emit) {
    try {
      final initialSelectedNoOfBaths = _selectedListingManagementFilterModel.noOfBathrooms;
      final baths = Feature.values.map((e) => ItemListingPropertyFilterModel(displayName: e.description, value: e, filterKey: ListingManagementFilterKey.noOfBathrooms, isSelected: initialSelectedNoOfBaths?.contains(e) ?? false)).toList();
      _updateFilterCategory(ListingManagementFilterKey.noOfBathrooms, baths, emit);
    } catch (ex) {
      "Error while initializing Baths ${ex.toString()}".printInConsole();
    }
  }

  void _initNoOfLivingRooms(Emitter<ListingManagementFilterState> emit) {
    try {
      final initialSelectedNoOfBaths = _selectedListingManagementFilterModel.noOfLivingRooms;
      final livingRooms = Feature.values.map((e) => ItemListingPropertyFilterModel(displayName: e.description, value: e, filterKey: ListingManagementFilterKey.noOfLivingRooms, isSelected: initialSelectedNoOfBaths?.contains(e) ?? false)).toList();
      _updateFilterCategory(ListingManagementFilterKey.noOfLivingRooms, livingRooms, emit);
    } catch (ex) {
      "Error while initializing livingRooms ${ex.toString()}".printInConsole();
    }
  }

  void _initNoOfBalconies(Emitter<ListingManagementFilterState> emit) {
    try {
      final initialSelectedNoOfBaths = _selectedListingManagementFilterModel.noOfLivingRooms;
      final balconies = Feature.values.map((e) => ItemListingPropertyFilterModel(displayName: e.description, value: e, filterKey: ListingManagementFilterKey.noOfBalconies, isSelected: initialSelectedNoOfBaths?.contains(e) ?? false)).toList();
      _updateFilterCategory(ListingManagementFilterKey.noOfBalconies, balconies, emit);
    } catch (ex) {
      "Error while initializing balconies ${ex.toString()}".printInConsole();
    }
  }

  void _initNoOfBedroom(Emitter<ListingManagementFilterState> emit) {
    try {
      final initialSelectedNoOfBaths = _selectedListingManagementFilterModel.noOfBedrooms;
      final bedrooms = Feature.values.map((e) => ItemListingPropertyFilterModel(displayName: e.description, value: e, filterKey: ListingManagementFilterKey.noOfBedrooms, isSelected: initialSelectedNoOfBaths?.contains(e) ?? false)).toList();
      _updateFilterCategory(ListingManagementFilterKey.noOfBedrooms, bedrooms, emit);
    } catch (ex) {
      "Error while initializing bedrooms ${ex.toString()}".printInConsole();
    }
  }

  void _initNoOfKitchen(Emitter<ListingManagementFilterState> emit) {
    try {
      final initialSelectedNoOfBaths = _selectedListingManagementFilterModel.noOfKitchens;
      final kitchens = Feature.values.map((e) => ItemListingPropertyFilterModel(displayName: e.description, value: e, filterKey: ListingManagementFilterKey.noOfKitchens, isSelected: initialSelectedNoOfBaths?.contains(e) ?? false)).toList();
      _updateFilterCategory(ListingManagementFilterKey.noOfKitchens, kitchens, emit);
    } catch (ex) {
      "Error while initializing kitchens ${ex.toString()}".printInConsole();
    }
  }

  void _initNoOfUtilities(Emitter<ListingManagementFilterState> emit) {
    try {
      final initialSelectedNoOfBaths = _selectedListingManagementFilterModel.noOfUtilities;
      final utilities = Feature.values.map((e) => ItemListingPropertyFilterModel(displayName: e.description, value: e, filterKey: ListingManagementFilterKey.noOfUtilities, isSelected: initialSelectedNoOfBaths?.contains(e) ?? false)).toList();
      _updateFilterCategory(ListingManagementFilterKey.noOfUtilities, utilities, emit);
    } catch (ex) {
      "Error while initializing Baths ${ex.toString()}".printInConsole();
    }
  }

  void _initOfferingTypes(Emitter<ListingManagementFilterState> emit) {
    try {
      final initialSelectedOfferingType = _selectedListingManagementFilterModel.offeringType;
      final offerTypes = OfferingType.values.where((e) => e != OfferingType.none).map((e) => ItemListingPropertyFilterModel(displayName: e.description, value: e, filterKey: ListingManagementFilterKey.noOfBathrooms, isSelected: initialSelectedOfferingType == e)).toList();
      _updateFilterCategory(ListingManagementFilterKey.offeringType, offerTypes, emit);
    } catch (ex) {
      "Error while initializing Offering Types ${ex.toString()}".printInConsole();
    }
  }

  void _initBrs(Emitter<ListingManagementFilterState> emit) {
    try {
      final initialSelectedBrs = _selectedListingManagementFilterModel.noOfBrs;
      final brs = NoOfPropertiesBr.values.map((e) => ItemListingPropertyFilterModel<double>(displayName: e.description, value: e.noOfBr, filterKey: ListingManagementFilterKey.noOfBr, isSelected: initialSelectedBrs?.contains(e) ?? false)).toList();
      _updateFilterCategory(ListingManagementFilterKey.noOfBr, brs, emit);
    } catch (ex) {
      "Error while initializing BRS ${ex.toString()}".printInConsole();
    }
  }

  void _initPropertyTypes(Emitter<ListingManagementFilterState> emit) {
    try {
      final initialSelectedPropertyTypes = _selectedListingManagementFilterModel.propertyTypes?.map((e) => e.description);
      final propertyTypes = PropertyType.values.map((e) => ItemListingPropertyFilterModel(displayName: e.description, description: e.baseId, filterKey: ListingManagementFilterKey.propertyType, isSelected: initialSelectedPropertyTypes?.contains(e.description) ?? false)).toList();
      _updateFilterCategory(ListingManagementFilterKey.propertyType, propertyTypes, emit);
    } catch (ex) {
      "Error while initializing propertyTypes ${ex.toString()}".printInConsole();
    }
  }

  void _initFacings(Emitter<ListingManagementFilterState> emit) {
    try {
      final initialSelectedPropertyTypes = _selectedListingManagementFilterModel.facing;
      final facings = Facing.values.whereNot((element) => element == Facing.unknown).map((e) => ItemListingPropertyFilterModel(displayName: e.description, description: e.description, filterKey: ListingManagementFilterKey.facing, isSelected: initialSelectedPropertyTypes?.description == e.description)).toList();
      _updateFilterCategory(ListingManagementFilterKey.facing, facings, emit);
    } catch (ex) {
      "Error while initializing facings ${ex.toString()}".printInConsole();
    }
  }

  void _initNoOfFloors(Emitter<ListingManagementFilterState> emit) {
    try {
      final initialSelectedPropertyTypes = _selectedListingManagementFilterModel.floors;
      final floors = Floors.values.map((e) => ItemListingPropertyFilterModel(displayName: e.description, description: e.description, filterKey: ListingManagementFilterKey.floors, isSelected: initialSelectedPropertyTypes?.contains(e) ?? false)).toList();
      _updateFilterCategory(ListingManagementFilterKey.floors, floors, emit);
    } catch (ex) {
      "Error while initializing propertyTypes ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initPropertySubTypes(Emitter<ListingManagementFilterState> emit) async {
    try {
      final initialSelectedPropertySubTypes = _selectedListingManagementFilterModel.propertySubTypes?.map((e) => e.id);
      final masterPropertyTypes = await _masterDataRepository.getCustomPropertyTypes(isPropertyListingEnabled: getIt<LeadratHomeBloc>().isPropertyListingEnabled);
      if (masterPropertyTypes == null) return;
      _allPropertyTypes = masterPropertyTypes;
      List<ItemListingPropertyFilterModel> subTypes = [];
      for (var item in masterPropertyTypes) {
        item.childTypes?.forEach((element) => subTypes.add(
              ItemListingPropertyFilterModel<MasterPropertyTypeModel>(
                displayName: element.displayName ?? "",
                description: element.id,
                value: element,
                filterKey: ListingManagementFilterKey.propertySubType,
                isSelected: initialSelectedPropertySubTypes?.contains(element.id) ?? false,
              ),
            ));
      }
      subTypes.sort((a, b) => a.displayName.compareTo(b.displayName));
      _updateFilterCategory(ListingManagementFilterKey.propertySubType, subTypes, emit);
    } catch (ex) {
      "Error while initializing property Sub types ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _updatePropertySubTypes(Emitter<ListingManagementFilterState> emit, List<ItemListingPropertyFilterModel>? selectedPropertyTypes) async {
    if (_allPropertyTypes.isEmpty) await _initPropertySubTypes(emit);
    if (selectedPropertyTypes != null) {
      if (selectedPropertyTypes.isNotEmpty) {
        List<ItemListingPropertyFilterModel> subTypes = [];
        for (var item in selectedPropertyTypes) {
          if (item.isSelected) {
            final selectedPropertySubTypes = _allPropertyTypes.firstWhereOrNull((e) => e.displayName == item.displayName);
            selectedPropertySubTypes?.childTypes?.forEach((element) => subTypes.add(ItemListingPropertyFilterModel(displayName: element.displayName ?? "", description: element.id, filterKey: ListingManagementFilterKey.propertySubType)));
          }
        }
        _updateFilterCategory(ListingManagementFilterKey.propertySubType, subTypes, emit);
      } else {
        _initPropertySubTypes(emit);
      }
    }
  }

  Future<void> _initLocations(Emitter<ListingManagementFilterState> emit) async {
    try {
      final initialSelectedLocations = _selectedListingManagementFilterModel.locations;
      final result = await _getOwnerNamesOrAddressesUseCase(GetOwnerNamesOrAddressesUseCaseParams.address);
      result.fold(
        (failure) => null,
        (addresses) {
          if (addresses == null) return;
          final locations = addresses?.map((address) => ItemListingPropertyFilterModel(displayName: address, description: address, filterKey: ListingManagementFilterKey.locations, isSelected: initialSelectedLocations?.contains(address) ?? false)).toList();
          if (locations?.isNotEmpty ?? false) {
            _updateFilterCategory(ListingManagementFilterKey.locations, locations!, emit);
          }
        },
      );
    } catch (ex) {
      "Error while initializing locations ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initCommunities(Emitter<ListingManagementFilterState> emit) async {
    try {
      final initialSelectedCommunities = _selectedListingManagementFilterModel.communities;
      final communities = await _getCommunitiesUseCase(NoParams());
      communities.fold(
        (l) => null,
        (success) {
          if (success != null) {
            final communities = success.map((community) => ItemListingPropertyFilterModel(displayName: community, description: community, filterKey: ListingManagementFilterKey.community, isSelected: initialSelectedCommunities?.contains(community) ?? false)).toList();
            communities.sort((a, b) => a.displayName.compareTo(b.displayName));
            if (communities.isNotEmpty) {
              _updateFilterCategory(ListingManagementFilterKey.community, communities, emit);
            }
          }
        },
      );
    } catch (ex) {
      "Error while initializing Communities ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initSubCommunities(Emitter<ListingManagementFilterState> emit) async {
    try {
      final initialSelectedSubCommunities = _selectedListingManagementFilterModel.subCommunities;
      final subCommunities = await _getSubCommunities(NoParams());
      subCommunities.fold(
        (l) => null,
        (success) {
          if (success != null) {
            final subCommunitiesFilters = success.map((subCommunity) => ItemListingPropertyFilterModel(displayName: subCommunity, description: subCommunity, filterKey: ListingManagementFilterKey.subCommunity, isSelected: initialSelectedSubCommunities?.contains(subCommunity) ?? false)).toList();
            if (subCommunitiesFilters.isNotEmpty) {
              subCommunitiesFilters.sort((a, b) => a.displayName.compareTo(b.displayName));
              _updateFilterCategory(ListingManagementFilterKey.subCommunity, subCommunitiesFilters!, emit);
            }
          }
        },
      );
    } catch (ex) {
      "Error while initializing sub communities ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initAmenities(Emitter<ListingManagementFilterState> emit) async {
    try {
      final initialSelectedProjects = _selectedListingManagementFilterModel.amenities?.map((e) => e.id);
      var amenities = getIt<PropertyEntityMapper>().getAllCustomAmenities();
      final amenitiesFilters = amenities?.map((item) => ItemListingPropertyFilterModel<CustomAmenityModel>(displayName: item.amenityDisplayName ?? "", value: item, description: item.id, filterKey: ListingManagementFilterKey.amenities, isSelected: initialSelectedProjects?.contains(item.id) ?? false)).toList();
      _updateFilterCategory(ListingManagementFilterKey.amenities, amenitiesFilters!, emit);
    } catch (ex) {
      "Error while initializing amenities ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initProjects(Emitter<ListingManagementFilterState> emit) async {
    try {
      final initialSelectedProjects = _selectedListingManagementFilterModel.projects?.map((e) => e.id);
      final result = await _getProjectNameWithIdUseCase(NoParams());
      result.fold(
        (failure) => null,
        (success) {
          if (success == null) return;
          final projectFilters = success.map((item) => ItemListingPropertyFilterModel<ProjectEntity>(displayName: item.name ?? "", value: item, description: item.id, filterKey: ListingManagementFilterKey.projects, isSelected: initialSelectedProjects?.contains(item.id) ?? false)).toList();
          _updateFilterCategory(ListingManagementFilterKey.projects, projectFilters, emit);
        },
      );
    } catch (ex) {
      "Error while initializing projects ${ex.toString()}".printInConsole();
    }
  }

  Future<void> _initOwnerNames(Emitter<ListingManagementFilterState> emit) async {
    try {
      final initialSelectedProjects = _selectedListingManagementFilterModel.ownerName;
      final result = await _getOwnerNamesOrAddressesUseCase(GetOwnerNamesOrAddressesUseCaseParams.owner);
      result.fold(
        (failure) => null,
        (success) {
          if (success == null) return;
          final ownerNameFilters = success.map((item) => ItemListingPropertyFilterModel(displayName: item, description: item, filterKey: ListingManagementFilterKey.ownerNames, isSelected: initialSelectedProjects?.contains(item) ?? false)).toList();
          _updateFilterCategory(ListingManagementFilterKey.ownerNames, ownerNameFilters, emit);
        },
      );
    } catch (ex) {
      "Error while initializing owners ${ex.toString()}".printInConsole();
    }
  }

  void _initDateRanges(Emitter<ListingManagementFilterState> emit) {
    try {
      final initialSelectedDateType = _selectedListingManagementFilterModel.dateType;
      final dateTypes = PropertyDateType.values
          .map((type) {
            return SelectableItem<PropertyDateType>(title: type.description, value: type);
          })
          .whereNotNull()
          .toList();
      final selectedDateType = dateTypes.firstWhereOrNull((element) => element.value == initialSelectedDateType);
      emit(state.copyWith(dateTypes: dateTypes, updateSelectedDateType: selectedDateType != null ? false : true, updateSelectedFromDate: _selectedListingManagementFilterModel.fromDate != '' ? true : false, updateSelectedToDate: _selectedListingManagementFilterModel.toDate != '' ? true : false));

      final initialSelectedDateRange = _selectedListingManagementFilterModel.dateRange ?? _selectedListingManagementFilterModel.possessionTypeDateRange;

      if (state.selectedDateType?.value != PropertyDateType.possessionDate) {
        var dateRangeFilters = DateRange.values.map((e) => ItemListingPropertyFilterModel<DateRange>(displayName: e.description, value: e, filterKey: ListingManagementFilterKey.dateRange, isSelected: initialSelectedDateRange == getEnumFromDescription(DateRange.values, e.description))).toList();
        dateRangeFilters = dateRangeFilters.map((item) => item.displayName == DateRange.customDate.description ? item.copyWith(hasCustomView: true) : item).toList();
        _updateFilterCategory(ListingManagementFilterKey.dateRange, dateRangeFilters, emit);
      } else {
        var dateRangeFilters = PossessionType.values
            .where((e) => e.value != 0) // or use e.value if you have a custom getter
            .map((e) => ItemListingPropertyFilterModel<PossessionType>(
                  displayName: e.description,
                  value: e,
                  filterKey: ListingManagementFilterKey.dateRange,
                  isSelected: initialSelectedDateRange == getEnumFromDescription(PossessionType.values, e.description),
                ))
            .toList();

        dateRangeFilters = dateRangeFilters.map((item) => item.displayName == PossessionType.customDate.description ? item.copyWith(hasCustomView: true, hasMonthFilter: true) : item).toList();
        _updateFilterCategory(ListingManagementFilterKey.dateRange, dateRangeFilters, emit);
      }
    } catch (ex) {
      "Error while initializing date range ${ex.toString()}".printInConsole();
    }
  }

  void updateDateRange(Emitter<ListingManagementFilterState> emit) {
    if (state.selectedDateType?.value != PropertyDateType.possessionDate) {
      var dateRangeFilters = DateRange.values
          .map((e) => ItemListingPropertyFilterModel(
                displayName: e.description,
                filterKey: ListingManagementFilterKey.dateRange,
              ))
          .toList();
      dateRangeFilters = dateRangeFilters.map((item) => item.displayName == DateRange.customDate.description ? item.copyWith(hasCustomView: true) : item).toList();
      _updateFilterCategory(ListingManagementFilterKey.dateRange, dateRangeFilters, emit);
    } else {
      List<ItemListingPropertyFilterModel> dateRangeFilters = PossessionType.values
          .where((e) => e.value != 0)
          .map((e) => ItemListingPropertyFilterModel<PossessionType>(
                displayName: e.description,
                value: e,
                filterKey: ListingManagementFilterKey.dateRange,
              ))
          .toList();
      dateRangeFilters = dateRangeFilters.map((item) => item.displayName == PossessionType.customDate.description ? item.copyWith(hasCustomView: true, hasMonthFilter: true) : item).toList();
      _updateFilterCategory(ListingManagementFilterKey.dateRange, dateRangeFilters, emit);
    }
  }

  void _updateFilterCategory(ListingManagementFilterKey filterKey, List<ItemListingPropertyFilterModel> filters, Emitter<ListingManagementFilterState> emit) {
    var updatedCategories = state.listingManagementFilterCategories.map((category) => category.filterKey == filterKey ? category.copyWith(filters: filters, isInitialized: true) : category).toList();
    _updateState(emit, listingManagementFilterCategories: updatedCategories);
  }

  void _updateState(
    Emitter<ListingManagementFilterState> emit, {
    List<ItemListingPropertyFilterCategoryModel>? listingManagementFilterCategories,
    ListingManagementFilterModel? listingManagementFilterModel,
    PageState? pageState,
    int? selectedCategoryIndex,
    DateTime? selectedFromDate,
    DateTime? selectedToDate,
    BudgetRangeModel? customBudgetRange,
    List<SelectableItem<String>>? allCurrencies,
    SelectableItem<String>? selectedCurrency,
    List<SelectableItem<PropertyDateType>>? dateTypes,
    SelectableItem<PropertyDateType>? selectedDateType,
    bool resetValues = false,
  }) {
    emit(state.copyWith(
      listingManagementFilterCategories: listingManagementFilterCategories ?? state.listingManagementFilterCategories,
      listingManagementFilterModel: listingManagementFilterModel ?? state.listingManagementFilterModel,
      pageState: pageState ?? state.pageState,
      selectedCategoryIndex: selectedCategoryIndex ?? state.selectedCategoryIndex,
      searchFilteredCategories: _updateSearchCategory(selectedCategoryIndex, listingManagementFilterCategories),
      selectedFromDate: selectedFromDate,
      selectedToDate: selectedToDate,
      customBudget: state.customBudget,
      currencies: allCurrencies,
      selectedCurrency: selectedCurrency,
      errorMessage: "",
      dateTypes: dateTypes,
      selectedDateType: selectedDateType,
      resetValues: resetValues,
    ));
  }

  List<ItemListingPropertyFilterCategoryModel>? _updateSearchCategory(int? selectedCategoryIndex, List<ItemListingPropertyFilterCategoryModel>? listingManagementFilterCategories) {
    final isSearching = state.searchFilteredCategories.isNotEmpty ? state.searchFilteredCategories[selectedCategoryIndex ?? state.selectedCategoryIndex].searchController?.text.isNotNullOrEmpty() : false;

    if (selectedFilterItem != null && (isSearching ?? false)) {
      final selectedSearchFilteredCategories = state.searchFilteredCategories[selectedCategoryIndex ?? state.selectedCategoryIndex];
      List<ItemListingPropertyFilterModel>? updatedSearchedFilterList = selectedSearchFilteredCategories.filters?.map((e) {
        if (e.displayName == selectedFilterItem!.displayName) {
          return selectedFilterItem!.copyWith(isSelected: !selectedFilterItem!.isSelected);
        }
        return selectedSearchFilteredCategories.hasMultiSelect ? e : e.copyWith(isSelected: false);
      }).toList();
      var updatedCategoryList = state.searchFilteredCategories.map((e) => e.filterKey == selectedSearchFilteredCategories.filterKey ? selectedSearchFilteredCategories.copyWith(filters: updatedSearchedFilterList) : e).toList();
      return updatedCategoryList;
    }
    return listingManagementFilterCategories;
  }

  String? utcToDateFormat(DateTime? date) {
    if (date == null) return null;
    DateTime utcDate = date.toUniversalTimeStartOfDay();
    return utcDate.toString();
  }

  FutureOr<void> _onChangeCurrency(ChangeCurrencyEvent event, Emitter<ListingManagementFilterState> emit) {
    _updateState(emit, selectedCurrency: event.selectedCurrency);
  }

  FutureOr<void> _onSelectDateType(SelectDateTypeEvent event, Emitter<ListingManagementFilterState> emit) {
    emit(state.copyWith(selectedDateType: event.selectedDateType,updateSelectedFromDate:false,updateSelectedToDate: false));
    updateDateRange(emit);
  }

  (String?, String?) _getDateTimeFromRange(DateRange selectedDateRange) {
    switch (selectedDateRange) {
      case DateRange.today:
        return (utcToDateFormat(DateTime.now().toUserTimeZone()), utcToDateFormat(DateTime.now().toUserTimeZone()));
      case DateRange.yesterday:
        return (utcToDateFormat(DateTime.now().toUserTimeZone()!.subtract(const Duration(days: 1))), utcToDateFormat(DateTime.now().toUserTimeZone()!.subtract(const Duration(days: 1))));
      case DateRange.lastSevenDays:
        return (utcToDateFormat(DateTime.now().toUserTimeZone()), utcToDateFormat(DateTime.now().toUserTimeZone()!.subtract(const Duration(days: 7))));
      case DateRange.lastTwentyEightDays:
        return (utcToDateFormat(DateTime.now().toUserTimeZone()), utcToDateFormat(DateTime.now().toUserTimeZone()!.subtract(const Duration(days: 28))));
      case DateRange.customDate:
        return (null, null);
    }
  }

  FutureOr<void> _onAreaUnitChanged(AreaUnitChangedEvent event, Emitter<ListingManagementFilterState> emit) {
    if (event.filterKey == ListingManagementFilterKey.carpetArea) {
      if (event.selectedAreaUnit?.isSelected ?? false) {
        emit(state.copyWith(selectedCarpetAreaUnit: event.selectedAreaUnit));
      } else {
        emit(state.copyWith(updateCarpetAreaUnit: true));
      }
    }
    if (event.filterKey == ListingManagementFilterKey.netArea) {
      if (event.selectedAreaUnit?.isSelected ?? false) {
        emit(state.copyWith(selectedNetAreaUnit: event.selectedAreaUnit));
      } else {
        emit(state.copyWith(updateNetAreaUnit: true));
      }
    }
    if (event.filterKey == ListingManagementFilterKey.propertySize) {
      if (event.selectedAreaUnit?.isSelected ?? false) {
        emit(state.copyWith(selectedPropertySizeAreaUnit: event.selectedAreaUnit));
      } else {
        emit(state.copyWith(updatePropertyAreaUnit: true));
      }
    }
    if (event.filterKey == ListingManagementFilterKey.builtUpArea) {
      if (event.selectedAreaUnit?.isSelected ?? false) {
        emit(state.copyWith(selectedBuiltUpAreaUnit: event.selectedAreaUnit));
      } else {
        emit(state.copyWith(updateBuiltUpAreaUnit: true));
      }
    }
    if (event.filterKey == ListingManagementFilterKey.saleableArea) {
      if (event.selectedAreaUnit?.isSelected ?? false) {
        emit(state.copyWith(selectedSaleableAreaUnit: event.selectedAreaUnit));
      } else {
        emit(state.copyWith(updateSaleableAreaUnit: true));
      }
    }
  }

  FutureOr<void> _onAreaSizeInputChangeEvent(AreaSizeInputChangeEvent event, Emitter<ListingManagementFilterState> emit) {
    if (event.filterKey == ListingManagementFilterKey.carpetArea) {
      emit(state.copyWith(carpetArea: event.rangeInput));
    }
    if (event.filterKey == ListingManagementFilterKey.netArea) {
      emit(state.copyWith(netArea: event.rangeInput));
    }
    if (event.filterKey == ListingManagementFilterKey.propertySize) {
      emit(state.copyWith(propertySize: event.rangeInput));
    }
    if (event.filterKey == ListingManagementFilterKey.builtUpArea) {
      emit(state.copyWith(builtUpArea: event.rangeInput));
    }
    if (event.filterKey == ListingManagementFilterKey.saleableArea) {
      emit(state.copyWith(saleableArea: event.rangeInput));
    }
  }

  FutureOr<void> _onInitAreaUnits(InitAreaUnitsEvent event, Emitter<ListingManagementFilterState> emit) async {
    _allAreaUnits = (_allAreaUnits?.isNotEmpty ?? false) ? _allAreaUnits : await _masterDataRepository.getAreaUnits();
    final areaUnits = _allAreaUnits?.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit)).toList();
    emit(state.copyWith(areaUnits: areaUnits));
  }

  FutureOr<void> _onOnMinBudgetChangeEvent(OnMinBudgetChangeEvent event, Emitter<ListingManagementFilterState> emit) {
    emit(state.copyWith(minBudget: event.rangeInput));
  }

  FutureOr<void> _onOnMaxBudgetChangeEvent(OnMaxBudgetChangeEvent event, Emitter<ListingManagementFilterState> emit) {
    emit(state.copyWith(maxBudget: event.rangeInput));
  }
}
