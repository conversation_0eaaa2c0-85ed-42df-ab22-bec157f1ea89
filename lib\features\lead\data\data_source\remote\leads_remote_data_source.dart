import 'package:leadrat/core_main/remote/rest_response/paged_rest_response.dart';
import 'package:leadrat/features/lead/data/models/add_lead_model.dart';
import 'package:leadrat/features/lead/data/models/appointment_project_model.dart';
import 'package:leadrat/features/lead/data/models/custom_filter_model.dart';
import 'package:leadrat/features/lead/data/models/delete_document_model.dart';
import 'package:leadrat/features/lead/data/models/get_all_leads_wrapper_model.dart';
import 'package:leadrat/features/lead/data/models/get_lead_model.dart';
import 'package:leadrat/features/lead/data/models/lead_call_log_model.dart';
import 'package:leadrat/features/lead/data/models/lead_category_model.dart';
import 'package:leadrat/features/lead/data/models/lead_document_model.dart';
import 'package:leadrat/features/lead/data/models/lead_filter_model.dart';
import 'package:leadrat/features/lead/data/models/lead_history_model.dart';
import 'package:leadrat/features/lead/data/models/lead_link_model.dart';
import 'package:leadrat/features/lead/data/models/projects_dto.dart';
import 'package:leadrat/features/lead/data/models/update_appointments_model.dart';
import 'package:leadrat/features/lead/data/models/update_custom_status_model.dart';
import 'package:leadrat/features/lead/data/models/update_lead_status_model.dart';
import 'package:leadrat/features/projects/data/models/project_unit_info_model.dart';
import 'package:leadrat/features/properties/data/models/get_property_model.dart';

import '../../models/booked_lead_model.dart';
import '../../models/get_booked_lead_model.dart';
import '../../models/re_assign_lead_model.dart';

abstract class LeadsRemoteDataSource {
  Future<GetAllLeadsWrapperModel?> getAllInitialLeads(LeadFilterModel? leadFilterModel, {int pageNumber = 0, int pageSize = 10});

  Future<GetAllLeadsWrapperCustomModel?> getAllInitialCustomLeads(LeadFilterModel? leadFilterModel, {int pageNumber = 0, int pageSize = 10});

  Future<Map<String, Map<String, int>>?> getLeadsCommunication(List<String> leadIds);

  Future<GetLeadModel?> getLeadDetails(String leadId);

  Future<Map<String, Map<String, List<LeadHistoryModel>>>?> getLeadHistory(String leadId);

  Future<Iterable<LeadDocumentModel>?> getLeadDocuments(String leadId);

  Future<bool?> updateLeadFlag(UpdateLeadFlagModel updateLeadFlagModel);

  Future<List<String>?> addLeadDocuments(AddLeadDocumentsModel addLeadDocumentsModel);

  Future<Map<DateTime, List<LeadHistoryModel>>?> getLeadNoteHistory(String leadId);

  Future<List<LeadHistoryModel>?> getLeadHistoryBasedOnTimeZone(String leadId);

  Future<bool?> updateLeadNote(String leadId, String note);

  Future<bool?> deleteLeadDocument(DeleteDocumentModel deleteDocumentModel);

  Future<bool?> updateLeadContactCount(LeadContactCountModel leadContactCountModel);

  Future<bool?> updateLeadTemplate(LeadCommunicationModel leadCommunicationModel);

  Future<List<LeadSubSourceModel>?> getLeadSubSource();

  Future<bool?> updateLeadStatus(UpdateLeadStatusModel? updateLeadStatusModel);

  Future<bool?> updateAppointments(UpdateAppointmentsModel? updateAppointmentsModel);

  Future<bool?> reAssignLead(ReAssignLeadModel? reAssignModel);

  Future<String?> addLeadAsync(AddLeadModel addLeadModel);

  Future<String?> updateLead(UpdateLeadModel updateLeadModel);

  Future<LeadContactModel?> getLeadByContactNo(String countryCode, String contactNo);

  Future<LeadCategoryModel?> searchLeads({String? keyword, int pageNumber = 1, int pageSize = 10});

  Future<PagedResponse<PropertyWithDegreeMatchedModel?, String>?> matchingProperties({String? leadId, String? searchText, int pageNumber = 1, int pageSize = 10, double? radiusInKms});

  Future<PagedResponse<ProjectWithDegreeMatchedModel?, String>?> matchingProjects({String? leadId, String? searchText, int pageNumber = 1, int pageSize = 10, double? radiusInKms});

  Future<List<AppointmentProjectModel>?> getAppointmentsByProjects(ProjectDto projectDto);

  Future<bool?> addProjectsInLead(ProjectDto projectDto);

  Future<List<String>?> getChannelPartnerNames();

  Future<List<CustomFilterModel>?> getCustomStatusFilter();

  Future<String?> bookLead(BookedLeadModel bookedLeadModel);

  Future<GetBookedLeadModel?> getBookedLead(String? leadId);

  Future<List<String>?> getCommunities();

  Future<List<String>?> getSubCommunities();

  Future<List<String>?> getTowerNames();

  Future<List<String>?> getCountries();

  Future<List<String>?> getCurrencies();

  Future<bool?> checkLeadAssignedByLeadId(String leadId);

  Future<List<String>?> getCampaignNames();

  Future<bool?> updateClickedLink(LeadLinkModel leadLinkModel);

  Future<List<String>?> getLeadExcelData();

  Future<List<String>?> getAdditionalPropertyKeys();

  Future<List<String>?> getAdditionalPropertyValues(String key);

  Future<List<String>?> getLeadNationality();

  Future<List<String>?> getLeadClusterNames();

  Future<List<String>?> getLeadUnitNames();

  Future<bool?> updateLeadCallLog(LeadCallLogModel leadCallLogModel);

  Future<bool>? customFilterBulkUpdate(UpdateCustomStatusModel updateCustomStatusModel);

  Future<List<String?>?> getAllLeadCities();

  Future<List<String?>?> getAllLeadZones();

  Future<List<String?>?> getAllLeadStates();

  Future<List<String?>?> getAllLeadLocality();
}
