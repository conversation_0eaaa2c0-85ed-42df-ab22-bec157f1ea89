// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'enquiry_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EnquiryModel _$EnquiryModelFromJson(Map<String, dynamic> json) => EnquiryModel(
      id: json['id'] as String?,
      enquiryType: (json['enquiryType'] as num?)?.toInt(),
      enquiryTypes: (json['enquiryTypes'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$EnquiryTypeEnumEnumMap, e))
          .toList(),
      bhKType: (json['bhKType'] as num?)?.toInt(),
      noOfBhks: (json['noOfBhks'] as num?)?.toDouble(),
      bhkTypes: (json['bhkTypes'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$BHKTypeEnumMap, e))
          .toList(),
      bhKs: (json['bhKs'] as List<dynamic>?)
          ?.map((e) => (e as num?)?.toDouble())
          .toList(),
      subSource: json['subSource'] as String?,
      lowerBudget: (json['lowerBudget'] as num?)?.toInt(),
      upperBudget: (json['upperBudget'] as num?)?.toInt(),
      carpetArea: (json['carpetArea'] as num?)?.toInt(),
      conversionFactor: json['conversionFactor'] as String?,
      carpetAreaUnit: json['carpetAreaUnit'] as String?,
      carpetAreaUnitId: json['carpetAreaUnitId'] as String?,
      address: json['address'] == null
          ? null
          : AddressModel.fromJson(json['address'] as Map<String, dynamic>),
      addresses: (json['addresses'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : AddressModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      currency: json['currency'] as String?,
      prospectSource: json['prospectSource'] == null
          ? null
          : ProspectSourceModel.fromJson(
              json['prospectSource'] as Map<String, dynamic>),
      propertyType: json['propertyType'] == null
          ? null
          : PropertyTypeModel.fromJson(
              json['propertyType'] as Map<String, dynamic>),
      propertyTypes: (json['propertyTypes'] as List<dynamic>?)
          ?.map((e) => PropertyTypeModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      saleableArea: (json['saleableArea'] as num?)?.toDouble(),
      builtUpArea: (json['builtUpArea'] as num?)?.toDouble(),
      bHKs: (json['bHKs'] as List<dynamic>?)
          ?.map((e) => (e as num).toDouble())
          .toList(),
      baths: (json['baths'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      builtUpAreaConversionFactor:
          (json['builtUpAreaConversionFactor'] as num?)?.toDouble(),
      saleableAreaConversionFactor:
          (json['saleableAreaConversionFactor'] as num?)?.toDouble(),
      floors:
          (json['floors'] as List<dynamic>?)?.map((e) => e as String).toList(),
      builtUpAreaUnitId: json['builtUpAreaUnitId'] as String?,
      saleableAreaUnitId: json['saleableAreaUnitId'] as String?,
      offerType: (json['offerType'] as num?)?.toInt(),
      furnished: (json['furnished'] as num?)?.toInt(),
      beds: (json['beds'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$BedsEnumMap, e))
          .toList(),
      propertyAreaUnitId: json['propertyAreaUnitId'] as String?,
      propertyArea: (json['propertyArea'] as num?)?.toDouble(),
      netAreaUnitId: json['netAreaUnitId'] as String?,
      netArea: (json['netArea'] as num?)?.toDouble(),
      propertyAreaConversionFactor:
          (json['propertyAreaConversionFactor'] as num?)?.toDouble(),
      netAreaConversionFactor:
          (json['netAreaConversionFactor'] as num?)?.toDouble(),
      unitName: json['unitName'] as String?,
      clusterName: json['clusterName'] as String?,
      purpose: $enumDecodeNullable(_$PurposeEnumEnumMap, json['purpose']) ??
          PurposeEnum.none,
      possesionType:
          $enumDecodeNullable(_$PossessionTypeEnumMap, json['possesionType']),
    );

Map<String, dynamic> _$EnquiryModelToJson(EnquiryModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'enquiryType': instance.enquiryType,
      'enquiryTypes': instance.enquiryTypes
          ?.map((e) => _$EnquiryTypeEnumEnumMap[e]!)
          .toList(),
      'bhKType': instance.bhKType,
      'noOfBhks': instance.noOfBhks,
      'bhkTypes': instance.bhkTypes?.map((e) => _$BHKTypeEnumMap[e]!).toList(),
      'bhKs': instance.bhKs,
      'subSource': instance.subSource,
      'lowerBudget': instance.lowerBudget,
      'upperBudget': instance.upperBudget,
      'carpetArea': instance.carpetArea,
      'conversionFactor': instance.conversionFactor,
      'carpetAreaUnit': instance.carpetAreaUnit,
      'carpetAreaUnitId': instance.carpetAreaUnitId,
      'address': instance.address?.toJson(),
      'addresses': instance.addresses?.map((e) => e?.toJson()).toList(),
      'currency': instance.currency,
      'prospectSource': instance.prospectSource?.toJson(),
      'propertyType': instance.propertyType?.toJson(),
      'propertyTypes': instance.propertyTypes?.map((e) => e.toJson()).toList(),
      'saleableArea': instance.saleableArea,
      'builtUpArea': instance.builtUpArea,
      'furnished': instance.furnished,
      'offerType': instance.offerType,
      'saleableAreaUnitId': instance.saleableAreaUnitId,
      'builtUpAreaUnitId': instance.builtUpAreaUnitId,
      'saleableAreaConversionFactor': instance.saleableAreaConversionFactor,
      'builtUpAreaConversionFactor': instance.builtUpAreaConversionFactor,
      'floors': instance.floors,
      'beds': instance.beds?.map((e) => _$BedsEnumMap[e]!).toList(),
      'bHKs': instance.bHKs,
      'baths': instance.baths,
      'propertyAreaUnitId': instance.propertyAreaUnitId,
      'propertyArea': instance.propertyArea,
      'netAreaUnitId': instance.netAreaUnitId,
      'netArea': instance.netArea,
      'propertyAreaConversionFactor': instance.propertyAreaConversionFactor,
      'netAreaConversionFactor': instance.netAreaConversionFactor,
      'unitName': instance.unitName,
      'clusterName': instance.clusterName,
      'purpose': _$PurposeEnumEnumMap[instance.purpose],
      'possesionType': _$PossessionTypeEnumMap[instance.possesionType],
    };

const _$EnquiryTypeEnumEnumMap = {
  EnquiryTypeEnum.none: 0,
  EnquiryTypeEnum.buy: 1,
  EnquiryTypeEnum.sale: 2,
  EnquiryTypeEnum.rent: 3,
};

const _$BHKTypeEnumMap = {
  BHKType.none: 0,
  BHKType.simplex: 1,
  BHKType.duplex: 2,
  BHKType.pentHouse: 3,
  BHKType.others: 4,
};

const _$BedsEnumMap = {
  Beds.studio: 0,
  Beds.oneBed: 1,
  Beds.twoBed: 2,
  Beds.threeBed: 3,
  Beds.fourBed: 4,
  Beds.fiveBed: 5,
  Beds.sixBed: 6,
  Beds.sevenBed: 7,
  Beds.eightBed: 8,
  Beds.nineBed: 9,
  Beds.tenBed: 10,
};

const _$PurposeEnumEnumMap = {
  PurposeEnum.none: 0,
  PurposeEnum.investment: 1,
  PurposeEnum.selfUse: 2,
};

const _$PossessionTypeEnumMap = {
  PossessionType.none: 0,
  PossessionType.underConstruction: 1,
  PossessionType.sixMonths: 2,
  PossessionType.oneYear: 3,
  PossessionType.twoYear: 4,
  PossessionType.customDate: 5,
};
