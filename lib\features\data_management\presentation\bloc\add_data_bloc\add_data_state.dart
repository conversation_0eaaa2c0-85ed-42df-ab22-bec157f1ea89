part of 'add_data_bloc.dart';

@immutable
class AddDataState {
  final PageState pageState;
  final String? errorMessage;
  final String? successMessage;
  final List<ItemSimpleModel<EnquiryTypeEnum>> enquiredFor;
  final List<ItemSimpleModel<PropertyType>> propertyTypes;
  final List<ItemSimpleModel<String>> propertySubTypes;
  final List<ItemSimpleModel<BHKType>> bhkTypes;
  final List<ItemSimpleModel<double>> noOfBhk;
  final List<ItemSimpleModel<AddressModel>> locations;
  final List<ItemSimpleModel<Profession>> professions;
  final List<SelectableItem<String>> dataSource;
  final SelectableItem<String>? selectedDataSource;
  final List<SelectableItem<String>> dataSubSource;
  final SelectableItem<String>? selectedDataSubSource;
  final List<SelectableItem<String>> agencyNames;
  final List<SelectableItem<String>>? selectedAgencyNames;
  final List<SelectableItem<String>> campaignNames;
  final List<SelectableItem<String>>? selectedCampaignNames;
  final List<SelectableItem<String>> assignToUsers;
  final SelectableItem<String>? selectedAssignedUser;
  final List<SelectableItem<MasterAreaUnitsModel>> carpetAreas;
  final List<SelectableItem<MasterAreaUnitsModel>> saleableAreas;
  final List<SelectableItem<MasterAreaUnitsModel>> builtUpAreas;
  final SelectableItem<MasterAreaUnitsModel>? selectedCarpetArea;
  final SelectableItem<MasterAreaUnitsModel>? selectedSaleableArea;
  final SelectableItem<MasterAreaUnitsModel>? selectedBuiltUpArea;
  final List<SelectableItem<String>> properties;
  final List<SelectableItem<String>>? selectedProperties;
  final List<SelectableItem<String>> projects;
  final List<SelectableItem<String>>? selectedProjects;
  final bool isSubTypesExpanded;
  final bool isNoOfBhkExpanded;
  final bool isEmailFieldVisible;
  final bool isAltPhoneFieldVisible;
  final bool isPossessionDateVisible;
  final DateTime? possessionDate;
  final String dialogMessage;
  final bool isDataAlreadyExits;
  final bool isDuplicateLeadBottomSheetVisible;
  final String? existingDataId;
  final String? contactNumber;
  final String? altContactNumber;
  final List<SelectableItem<String>> currencies;
  final SelectableItem<String>? selectedCurrency;
  final String? defaultCountryCode;
  final bool showDialogProgress;
  final String primaryOrSecondary;
  final List<SelectableItem<PurposeEnum>> purposes;
  final SelectableItem<PurposeEnum>? selectedPurpose;
  final bool isDataAlreadyExitsOnAltNumber;
  final bool isDuplicateDataBottomSheetVisibleForAltNumber;
  final List<SelectableItem<String>> sourcingManager;
  final SelectableItem<String>? selectedSourcingManager;
  final List<SelectableItem<String>> closingManager;
  final SelectableItem<String>? selectedClosingManager;
  final ItemSimpleModel<AddressModel>? customerLocations;
  final String? executiveContact;
  final bool? isAssignedLoggedInUser;
  final bool isReferralDetailsVisible;
  final String? referralContact;
  final List<SelectableItem<PossessionType?>>? possessionTypeSelectableItems;
  final SelectableItem<PossessionType?>? possessionTypeSelectedItem;
  final bool isPossessionDateCustomSelected;

  const AddDataState({
    this.pageState = PageState.initial,
    this.errorMessage,
    this.successMessage,
    this.enquiredFor = const [],
    this.propertyTypes = const [],
    this.propertySubTypes = const [],
    this.bhkTypes = const [],
    this.noOfBhk = const [],
    this.properties = const [],
    this.projects = const [],
    this.locations = const [],
    this.professions = const [],
    this.dataSource = const [],
    this.dataSubSource = const [],
    this.agencyNames = const [],
    this.assignToUsers = const [],
    this.carpetAreas = const [],
    this.saleableAreas = const [],
    this.builtUpAreas = const [],
    this.selectedSaleableArea,
    this.selectedBuiltUpArea,
    this.isSubTypesExpanded = false,
    this.isNoOfBhkExpanded = false,
    this.selectedDataSource,
    this.selectedDataSubSource,
    this.selectedAgencyNames,
    this.campaignNames = const [],
    this.selectedCampaignNames,
    this.selectedAssignedUser,
    this.selectedCarpetArea,
    this.selectedProperties,
    this.selectedProjects,
    this.isAltPhoneFieldVisible = false,
    this.isEmailFieldVisible = false,
    this.isPossessionDateVisible = false,
    this.isDataAlreadyExits = false,
    this.existingDataId,
    this.possessionDate,
    this.dialogMessage = "loading",
    this.contactNumber,
    this.altContactNumber,
    this.currencies = const [],
    this.selectedCurrency,
    this.defaultCountryCode,
    this.showDialogProgress = false,
    this.isDuplicateLeadBottomSheetVisible = false,
    this.primaryOrSecondary = '',
    this.selectedPurpose,
    this.purposes = const [],
    this.isDataAlreadyExitsOnAltNumber = false,
    this.isDuplicateDataBottomSheetVisibleForAltNumber = false,
    this.sourcingManager = const [],
    this.selectedSourcingManager,
    this.closingManager = const [],
    this.selectedClosingManager,
    this.customerLocations,
    this.executiveContact,
    this.isAssignedLoggedInUser,
    this.isReferralDetailsVisible = false,
    this.referralContact,
    this.possessionTypeSelectableItems,
    this.possessionTypeSelectedItem,
    this.isPossessionDateCustomSelected = false,
  });

  AddDataState initialState() => const AddDataState(
        pageState: PageState.initial,
        errorMessage: null,
        successMessage: null,
        enquiredFor: [],
        propertyTypes: [],
        propertySubTypes: [],
        bhkTypes: [],
        noOfBhk: [],
        properties: [],
        projects: [],
        locations: [],
        professions: [],
        dataSource: [],
        dataSubSource: [],
        agencyNames: [],
        assignToUsers: [],
        carpetAreas: [],
        saleableAreas: [],
        builtUpAreas: [],
        isSubTypesExpanded: false,
        isNoOfBhkExpanded: false,
        selectedDataSource: null,
        selectedDataSubSource: null,
        selectedAgencyNames: null,
        campaignNames: [],
        selectedCampaignNames: null,
        selectedAssignedUser: null,
        selectedCarpetArea: null,
        selectedBuiltUpArea: null,
        selectedSaleableArea: null,
        selectedProperties: null,
        selectedProjects: null,
        isEmailFieldVisible: false,
        isPossessionDateVisible: false,
        isAltPhoneFieldVisible: false,
        possessionDate: null,
        dialogMessage: "loading",
        contactNumber: null,
        altContactNumber: null,
        existingDataId: null,
        currencies: [],
        selectedCurrency: null,
        defaultCountryCode: null,
        showDialogProgress: false,
        purposes: [],
        sourcingManager: [],
        selectedSourcingManager: null,
        closingManager: [],
        selectedClosingManager: null,
        customerLocations: null,
        executiveContact: null,
        isAssignedLoggedInUser: null,
        isReferralDetailsVisible: false,
        referralContact: null,
      );

  AddDataState copyWith({
    PageState? pageState,
    String? errorMessage,
    String? successMessage,
    List<ItemSimpleModel<EnquiryTypeEnum>>? enquiredFor,
    List<ItemSimpleModel<PropertyType>>? propertyTypes,
    List<ItemSimpleModel<String>>? propertySubTypes,
    List<ItemSimpleModel<BHKType>>? bhkTypes,
    List<ItemSimpleModel<double>>? noOfBhk,
    List<ItemSimpleModel<AddressModel>>? locations,
    List<ItemSimpleModel<Profession>>? professions,
    List<SelectableItem<String>>? dataSource,
    SelectableItem<String>? selectedDataSource,
    List<SelectableItem<String>>? dataSubSource,
    SelectableItem<String>? selectedDataSubSource,
    bool updateSubSource = true, // added to make selectedSubSource null
    bool updatePossessionDate = true, // added to make selectedSubSource null
    List<SelectableItem<String>>? agencyNames,
    List<SelectableItem<String>>? selectedAgencyNames,
    List<SelectableItem<String>>? campaignNames,
    List<SelectableItem<String>>? selectedCampaignNames,
    List<SelectableItem<String>>? assignToUsers,
    SelectableItem<String>? selectedAssignedUser,
    List<SelectableItem<MasterAreaUnitsModel>>? carpetAreas,
    List<SelectableItem<MasterAreaUnitsModel>>? saleableAreas,
    List<SelectableItem<MasterAreaUnitsModel>>? builtUpAreas,
    SelectableItem<MasterAreaUnitsModel>? selectedCarpetArea,
    SelectableItem<MasterAreaUnitsModel>? selectedSaleableArea,
    SelectableItem<MasterAreaUnitsModel>? selectedBuiltUpArea,
    List<SelectableItem<String>>? properties,
    List<SelectableItem<String>>? selectedProperties,
    List<SelectableItem<String>>? projects,
    List<SelectableItem<String>>? selectedProjects,
    bool? isSubTypesExpanded,
    bool? isNoOfBhkExpanded,
    bool? isEmailFieldVisible,
    bool? isAltPhoneFieldVisible,
    bool? isPossessionDateVisible,
    DateTime? possessionDate,
    String? dialogMessage,
    bool? isDataAlreadyExits,
    String? existingDataId,
    String? contactNumber,
    String? altContactNumber,
    List<SelectableItem<String>>? currencies,
    SelectableItem<String>? selectedCurrency,
    String? defaultCountryCode,
    bool? showDialogProgress,
    bool? isDuplicateDataBottomSheetVisible,
    String? primaryOrSecondary,
    List<SelectableItem<PurposeEnum>>? purposes,
    SelectableItem<PurposeEnum>? selectedPurpose,
    bool? isDataAlreadyExitsOnAltNumber,
    bool? isDuplicateDataBottomSheetVisibleForAltNumber,
    List<SelectableItem<String>>? sourcingManager,
    SelectableItem<String>? selectedSourcingManager,
    List<SelectableItem<String>>? closingManager,
    SelectableItem<String>? selectedClosingManager,
    ItemSimpleModel<AddressModel>? customerLocations,
    String? executiveContact,
    bool? isAssignedLoggedInUser,
    bool? isReferralDetailsVisible,
    String? referralContact,
    List<SelectableItem<PossessionType?>>? possessionTypeSelectableItems,
    SelectableItem<PossessionType?>? possessionTypeSelectedItem,
    bool? isPossessionDateCustomSelected,
  }) {
    return AddDataState(
      pageState: pageState ?? this.pageState,
      errorMessage: errorMessage,
      successMessage: successMessage,
      enquiredFor: enquiredFor ?? this.enquiredFor,
      propertyTypes: propertyTypes ?? this.propertyTypes,
      propertySubTypes: propertySubTypes ?? this.propertySubTypes,
      bhkTypes: bhkTypes ?? this.bhkTypes,
      noOfBhk: noOfBhk ?? this.noOfBhk,
      locations: locations ?? this.locations,
      professions: professions ?? this.professions,
      dataSource: dataSource ?? this.dataSource,
      selectedDataSource: selectedDataSource ?? this.selectedDataSource,
      dataSubSource: dataSubSource ?? this.dataSubSource,
      selectedDataSubSource: updateSubSource ? selectedDataSubSource ?? this.selectedDataSubSource : null,
      agencyNames: agencyNames ?? this.agencyNames,
      selectedAgencyNames: selectedAgencyNames ?? this.selectedAgencyNames,
      campaignNames: campaignNames ?? this.campaignNames,
      selectedCampaignNames: selectedCampaignNames ?? this.selectedCampaignNames,
      assignToUsers: assignToUsers ?? this.assignToUsers,
      selectedAssignedUser: selectedAssignedUser ?? this.selectedAssignedUser,
      carpetAreas: carpetAreas ?? this.carpetAreas,
      saleableAreas: saleableAreas ?? this.saleableAreas,
      builtUpAreas: builtUpAreas ?? this.builtUpAreas,
      selectedCarpetArea: selectedCarpetArea ?? this.selectedCarpetArea,
      selectedSaleableArea: selectedSaleableArea ?? this.selectedSaleableArea,
      selectedBuiltUpArea: selectedBuiltUpArea ?? this.selectedBuiltUpArea,
      properties: properties ?? this.properties,
      selectedProperties: selectedProperties ?? this.selectedProperties,
      projects: projects ?? this.projects,
      selectedProjects: selectedProjects ?? this.selectedProjects,
      isSubTypesExpanded: isSubTypesExpanded ?? this.isSubTypesExpanded,
      isNoOfBhkExpanded: isNoOfBhkExpanded ?? this.isNoOfBhkExpanded,
      isAltPhoneFieldVisible: isAltPhoneFieldVisible ?? this.isAltPhoneFieldVisible,
      isEmailFieldVisible: isEmailFieldVisible ?? this.isEmailFieldVisible,
      isPossessionDateVisible: isPossessionDateVisible ?? this.isPossessionDateVisible,
      dialogMessage: dialogMessage ?? this.dialogMessage,
      possessionDate: updatePossessionDate ? possessionDate ?? this.possessionDate : null,
      isDataAlreadyExits: isDataAlreadyExits ?? this.isDataAlreadyExits,
      contactNumber: contactNumber ?? this.contactNumber,
      altContactNumber: altContactNumber ?? this.altContactNumber,
      existingDataId: existingDataId ?? this.existingDataId,
      currencies: currencies ?? this.currencies,
      selectedCurrency: selectedCurrency ?? this.selectedCurrency,
      defaultCountryCode: defaultCountryCode ?? this.defaultCountryCode,
      showDialogProgress: showDialogProgress ?? this.showDialogProgress,
      isDuplicateLeadBottomSheetVisible: isDuplicateDataBottomSheetVisible ?? this.isDuplicateLeadBottomSheetVisible,
      primaryOrSecondary: primaryOrSecondary ?? this.primaryOrSecondary,
      purposes: purposes ?? this.purposes,
      selectedPurpose: selectedPurpose ?? this.selectedPurpose,
      isDataAlreadyExitsOnAltNumber: isDataAlreadyExitsOnAltNumber ?? this.isDataAlreadyExitsOnAltNumber,
      isDuplicateDataBottomSheetVisibleForAltNumber: isDuplicateDataBottomSheetVisibleForAltNumber ?? this.isDuplicateDataBottomSheetVisibleForAltNumber,
      sourcingManager: sourcingManager ?? this.sourcingManager,
      selectedSourcingManager: selectedSourcingManager ?? this.selectedSourcingManager,
      closingManager: closingManager ?? this.closingManager,
      selectedClosingManager: selectedClosingManager ?? this.selectedClosingManager,
      customerLocations: customerLocations ?? this.customerLocations,
      executiveContact: executiveContact ?? this.executiveContact,
      isAssignedLoggedInUser: isAssignedLoggedInUser ?? this.isAssignedLoggedInUser,
      isReferralDetailsVisible: isReferralDetailsVisible ?? this.isReferralDetailsVisible,
      referralContact: referralContact ?? this.referralContact,
      possessionTypeSelectableItems: possessionTypeSelectableItems ?? this.possessionTypeSelectableItems,
      possessionTypeSelectedItem: possessionTypeSelectedItem ?? this.possessionTypeSelectedItem,
      isPossessionDateCustomSelected: isPossessionDateCustomSelected ?? this.isPossessionDateCustomSelected,
    );
  }
}
