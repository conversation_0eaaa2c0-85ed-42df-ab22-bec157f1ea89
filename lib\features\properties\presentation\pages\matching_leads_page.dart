import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:leadrat/core_main/common/pages/message_template_page.dart';
import 'package:leadrat/core_main/common/shapes/sharp_divider_painter.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/debouncer.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';
import 'package:leadrat/features/lead/presentation/pages/lead_info_page.dart';
import 'package:leadrat/features/properties/presentation/bloc/matching_leads_bloc/matching_leads_bloc.dart';
import 'package:leadrat/features/properties/presentation/items/item_matching_lead_model.dart';

import '../../../../core_main/common/base/presentation/leadrat_statefull_widget.dart';

class MatchingLeadsPage extends LeadratStatefulWidget {
  const MatchingLeadsPage({super.key, this.matchingLeadPropertyModel});

  final ItemMatchingPropertyDetails? matchingLeadPropertyModel;

  @override
  State<MatchingLeadsPage> createState() => _MatchingLeadsPageState();
}

class _MatchingLeadsPageState extends LeadratState<MatchingLeadsPage> {
  final isEnabled = getIt<LeadratHomeBloc>().globalSettings?.shouldEnablePropertyListing ?? false;

  @override
  void initState() {
    controller.addListener(() => matchingLeadsBloc.add(ScrollEvent(controller: controller)));
    super.initState();
  }

  ScrollController controller = ScrollController();
  TextEditingController textEditingController = TextEditingController();
  final matchingLeadsBloc = getIt<MatchingLeadsBloc>();

  @override
  void onDispose() {
    controller.dispose();
    textEditingController.dispose();
    super.onDispose();
  }

  final debouncer = Debouncer(milliseconds: 300);

  @override
  Widget buildContent(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: BlocConsumer<MatchingLeadsBloc, MatchingLeadsState>(
          listener: (context, state) {},
          builder: (context, state) {
            return GestureDetector(
              onTap: () {
                if (state.isPropertyDetailsVisible == true) matchingLeadsBloc.add(TogglePropertyDetailsEvent());
              },
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Positioned(
                      right: 0,
                      child: SvgPicture.asset(
                        ImageResources.imageMatchingLeadsBackground,
                      )),
                  Padding(
                    padding: const EdgeInsets.only(
                      left: 24,
                      top: 20,
                      right: 14,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _appBar(state),
                        RichText(
                          text: TextSpan(text: 'property:\n', style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.lightBackground), children: <TextSpan>[
                            TextSpan(text: widget.matchingLeadPropertyModel?.title ?? '--', style: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.tertiaryTextColor)),
                          ]),
                        ),
                        _search(),
                        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                          RichText(
                            text: TextSpan(
                              children: <TextSpan>[TextSpan(text: state.totalCount.doubleToWord(), style: LexendTextStyles.lexend12Bold.copyWith(color: ColorPalette.tertiaryTextColor)), TextSpan(text: ' matching leads found', style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.tertiaryTextColor))],
                            ),
                          ),
                          if (state.selectedLeads != null && state.selectedLeads!.isNotEmpty)
                            RichText(
                              text: TextSpan(
                                children: <TextSpan>[
                                  TextSpan(text: 'selected ', style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.tertiaryTextColor)),
                                  TextSpan(text: '${state.selectedLeads?.length ?? 0}', style: LexendTextStyles.lexend10Bold.copyWith(color: ColorPalette.tertiaryTextColor)),
                                  TextSpan(text: ' of ', style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.tertiaryTextColor)),
                                  TextSpan(text: state.totalCount.doubleToWord(), style: LexendTextStyles.lexend10Bold.copyWith(color: ColorPalette.tertiaryTextColor)),
                                ],
                              ),
                            ),
                        ]),
                        Expanded(
                          child: ListView.builder(
                            padding: const EdgeInsets.only(top: 10),
                            itemBuilder: (context, index) => _matchingItemWidget(state.itemMatchingLeadModels?[index], state),
                            itemCount: state.itemMatchingLeadModels?.length ?? 0,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Positioned(
                    top: 130,
                    child: _animationContainer(state.isPropertyDetailsVisible ?? false),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  _matchingItemWidget(ItemMatchingLeadModel? itemMatchingLeadModel, MatchingLeadsState state) {
    return itemMatchingLeadModel == null || (itemMatchingLeadModel.isVisible != null && itemMatchingLeadModel.isVisible == false)
        ? Container()
        : GestureDetector(
            onLongPress: () => matchingLeadsBloc.add(LongPressSelectEvent(matchingLead: itemMatchingLeadModel)),
            onTap: () {
              if (state.selectedLeads != null && state.selectedLeads!.isNotEmpty) {
                matchingLeadsBloc.add(SelectedMatchingLeadEvent(matchingLead: itemMatchingLeadModel));
              } else {
                Navigator.push(context, MaterialPageRoute(builder: (context) => LeadInfoPage(GetLeadEntity(id: itemMatchingLeadModel.matchingLeadEntity?.leadEntity?.id), null)));
              }
            },
            child: Container(
              height: 110,
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              margin: const EdgeInsets.only(bottom: 13),
              decoration: BoxDecoration(color: ColorPalette.primaryLightColor, borderRadius: BorderRadius.circular(2), border: Border.all(color: itemMatchingLeadModel.isSelected! ? ColorPalette.primaryGreen : ColorPalette.transparent)),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(itemMatchingLeadModel.title ?? '--', style: LexendTextStyles.lexend12Bold.copyWith(color: ColorPalette.primaryGreen)),
                      if (itemMatchingLeadModel.isSelected!)
                        SvgPicture.asset(ImageResources.iconSelectedCheckBox)
                      else
                        GestureDetector(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(builder: (context) => MessageTemplatePage(getLeadEntities: [itemMatchingLeadModel.matchingLeadEntity?.leadEntity])),
                              );
                            },
                            child: Container(
                                padding: const EdgeInsets.only(bottom: 5, left: 5),
                                child: SvgPicture.asset(
                                  ImageResources.iconShareGreen,
                                  height: 22,
                                  width: 22,
                                )))
                    ],
                  ),
                  RichText(
                    text: TextSpan(
                      text: itemMatchingLeadModel.subTitle == null ? "" : 'for',
                      style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.tertiaryTextColor),
                      children: <TextSpan>[
                        TextSpan(text: itemMatchingLeadModel.subTitle ?? '--', style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.tertiaryTextColor)),
                      ],
                    ),
                  ),
                  if (itemMatchingLeadModel.match != null)
                    RichText(
                      text: TextSpan(
                        text: 'match: ',
                        style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.tertiaryTextColor),
                        children: <TextSpan>[
                          TextSpan(text: itemMatchingLeadModel.match, style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.white)),
                        ],
                      ),
                    ),
                  IntrinsicHeight(
                    child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                      _budgetAndLocalityWidget(head: 'min budget:', desc: '${itemMatchingLeadModel.minBudget ?? '--'}'),
                      const SizedBox(height: 2, width: 12, child: Divider(color: ColorPalette.tertiaryTextColor, thickness: 2)),
                      _budgetAndLocalityWidget(head: 'max budget:', desc: '${itemMatchingLeadModel.maxBudget ?? '--'}'),
                      const VerticalDivider(color: ColorPalette.tertiaryTextColor, thickness: 2),
                      _budgetAndLocalityWidget(head: 'locality:', desc: itemMatchingLeadModel.locality ?? '--'),
                    ]),
                  )
                ],
              ),
            ),
          );
  }

  _budgetAndLocalityWidget({required String head, String? desc}) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text(head, style: LexendTextStyles.lexend10Regular.copyWith(color: ColorPalette.tertiaryTextColor)),
      SizedBox(width: 80, child: Text(desc?.trim() ?? '--', maxLines: 1, style: LexendTextStyles.lexend10Bold.copyWith(color: ColorPalette.white))),
    ]);
  }

  _animationContainer(bool isSeePropertyDetails) {
    var propertyDetails = widget.matchingLeadPropertyModel;
    return GestureDetector(
      onTap: () {
        matchingLeadsBloc.add(TogglePropertyDetailsEvent());
      },
      child: !isSeePropertyDetails
          ? Text(
              "see more",
              style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.primaryGreen),
            )
          : AnimatedContainer(
              height: context.height(100),
              width: context.width(100),
              color: ColorPalette.primaryLightColor.withOpacity(0.96),
              duration: const Duration(milliseconds: 400),
              curve: Curves.fastEaseInToSlowEaseOut,
              child: ListView(
                children: [
                  Container(
                    height: 200,
                    width: context.width(100),
                    color: ColorPalette.darkToneInk,
                    padding: const EdgeInsets.only(left: 24, right: 14),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomPaint(size: const Size(double.infinity, 4), painter: SharpDividerPainter(width: 1)),
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, crossAxisAlignment: CrossAxisAlignment.start, children: [
                            Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                              _budgetAndLocalityWidget(head: 'sale type:', desc: propertyDetails?.enquiryType),
                              Padding(padding: const EdgeInsets.symmetric(vertical: 14), child: _budgetAndLocalityWidget(head: 'price:', desc: propertyDetails?.subPropertyType)),
                              _budgetAndLocalityWidget(head: isEnabled ? 'Br' : 'BHK:', desc: propertyDetails?.noOfBhk),
                            ]),
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 50),
                              child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [_budgetAndLocalityWidget(head: 'Property type:', desc: propertyDetails?.propertyType), Padding(padding: const EdgeInsets.symmetric(vertical: 14), child: _budgetAndLocalityWidget(head: 'sub_property type:', desc: propertyDetails?.subPropertyType)), if (!isEnabled) _budgetAndLocalityWidget(head: 'BHK Type:', desc: propertyDetails?.bhkType)]),
                            ),
                            Column(crossAxisAlignment: CrossAxisAlignment.start, children: [_budgetAndLocalityWidget(head: 'property size:', desc: propertyDetails?.propertySize), Padding(padding: const EdgeInsets.only(top: 14), child: _budgetAndLocalityWidget(head: 'location:', desc: propertyDetails?.location))])
                          ]),
                        ),
                        Container(height: 5, width: 40, margin: const EdgeInsets.only(bottom: 19), decoration: BoxDecoration(color: ColorPalette.primaryLightColor, borderRadius: BorderRadius.circular(5)))
                      ],
                    ),
                  ),
                  Container(
                      decoration: BoxDecoration(
                    color: ColorPalette.gray800.withOpacity(0.95),
                  ))
                ],
              ),
            ),
    );
  }

  _appBar(MatchingLeadsState state) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(onTap: () => Navigator.pop(context), child: SvgPicture.asset(ImageResources.iconArrowLeftOut)),
          Text('matching leads', style: LexendTextStyles.lexend16SemiBold.copyWith(color: ColorPalette.primary)),
          GestureDetector(
            onTap: () {
              if (state.selectedLeads != null && state.selectedLeads!.isNotEmpty) {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => MessageTemplatePage(getLeadEntities: state.selectedLeads ?? [])),
                );
              } else {
                LeadratCustomSnackbar.show(context: context, message: "Kindly select the leads to share", type: SnackbarType.error);
              }
            },
            child: Container(height: 32, width: 32, padding: const EdgeInsets.all(7), decoration: BoxDecoration(color: ColorPalette.white.withOpacity(0.2), borderRadius: BorderRadius.circular(20), border: Border.all(color: ColorPalette.white.withOpacity(0.4), width: 1)), child: SvgPicture.asset(ImageResources.iconShareGreen, colorFilter: const ColorFilter.mode(ColorPalette.white, BlendMode.srcIn))),
          ),
        ],
      ),
    );
  }

  _search() {
    return Container(
      margin: const EdgeInsets.only(top: 56, bottom: 10),
      child: TextField(
        controller: textEditingController,
        decoration: InputDecoration(
            fillColor: ColorPalette.black,
            filled: true,
            hintText: 'search by lead name/number',
            hintStyle: LexendTextStyles.lexend11Regular.copyWith(color: ColorPalette.tertiaryTextColor),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4.0), // Rounded corners
              borderSide: const BorderSide(
                color: ColorPalette.tertiaryTextColor, // Border color
                width: 1.0,
              ),
            ),
            suffixIcon: UnconstrainedBox(
                child: SvgPicture.asset(
              ImageResources.iconSearch,
              height: 36,
            )),
            enabled: true),
        onChanged: (text) {
          debouncer.run(() => matchingLeadsBloc.add(SearchMatchingLeadEvent(searchText: text)));
        },
        style: LexendTextStyles.lexend12Regular.copyWith(color: ColorPalette.white),
      ),
    );
  }
}
