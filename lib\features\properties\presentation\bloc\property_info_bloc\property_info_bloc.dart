import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_model.dart';
import 'package:leadrat/core_main/common/models/base_user_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/features/properties/data/models/re_assign_property_model.dart';
import 'package:leadrat/features/properties/data/models/update_property_assignedTo_model.dart';
import 'package:leadrat/features/properties/domain/usecase/delete_property_use_case.dart';
import 'package:leadrat/features/properties/domain/usecase/get_matching_associate_leads_count_usecase.dart';
import 'package:leadrat/features/properties/domain/usecase/get_property_usecase.dart';
import 'package:leadrat/features/properties/domain/usecase/re_assign_property_use_case.dart';
import 'package:leadrat/features/properties/domain/usecase/update_property_assignedTo_usecase.dart';
import 'package:leadrat/features/properties/presentation/bloc/matching_leads_bloc/matching_leads_bloc.dart';
import 'package:leadrat/features/properties/presentation/items/item_property_info_model.dart';

import '../../../../../core_main/common/data/user/models/get_all_users_model.dart';
import '../../../../../core_main/common/data/user/repository/users_repository.dart';
import '../../../../../core_main/common/widgets/selectable_item_bottom_sheet.dart';

part 'property_info_event.dart';

part 'property_info_state.dart';

class PropertyInfoBloc extends Bloc<PropertyInfoEvent, PropertyInfoState> {
  final GetPropertyUseCase _getPropertyByIdUseCase;
  final GetMatchingAssociateLeadsCountUseCase _getMatchingAssociateLeadsCountUseCase;
  final ReAssignPropertyUseCase _reAssignPropertyUseCase;
  final UpdatePropertyAssignedToUseCase _updatePropertyAssignedToUseCase;
  final UsersDataRepository _userDataRepository;
  final DeletePropertyUseCase _deletePropertyUseCase;
  String propertyId = '';

  bool get isPropertyListingEnabled => getIt<LeadratHomeBloc>().isPropertyListingEnabled;

  List<GetAllUsersModel?>? allUsers = [];

  PropertyInfoBloc(this._getPropertyByIdUseCase, this._deletePropertyUseCase, this._getMatchingAssociateLeadsCountUseCase, this._userDataRepository, this._reAssignPropertyUseCase, this._updatePropertyAssignedToUseCase) : super(const PropertyInfoState()) {
    on<PropertyInfoInitialEvent>(_onPropertyInfoInitialEvent);
    on<PropertyImageToggleEvent>(_togglePropertyImageEvent);
    on<AmenitiesToggleEvent>(_amenitiesToggleEvent);
    on<PageStateResetEvent>(_onStatusResetEvent);
    on<PropertyDeleteEvent>(_onPropertyDeleteEvent);
    on<GetMatchingLeadsEvent>(_onGetMatchingLeadsEvent);
    on<GetAssociateMatchingLeadsCountEvent>(_onGetAssociateMatchingLeadsCountEvent);
    on<ReAssignPropertyEvent>(_onReAssignProperty);
    on<ReListingPropertyEvent>(_onReListingPropertyEvent);
  }

  FutureOr<void> _onPropertyInfoInitialEvent(PropertyInfoInitialEvent event, Emitter<PropertyInfoState> emit) async {
    emit(state.reset());
    propertyId = event.propertyId;
    emit(state.copyWith(pageState: PropertyInfoPageState.initial, propertyImageIndex: 0, associateLeadsCount: 0, matchingLeadsCount: 0));
    getIt<MatchingLeadsBloc>().add(MatchingLeadsInitialEvent(propertyId: event.propertyId));
    add(GetAssociateMatchingLeadsCountEvent(propertyId: event.propertyId));
    List<SelectableItem<String>>? users = [];
    List<SelectableItem<String>>? listingOnBehalfAllUsers = [];
    allUsers = await _userDataRepository.getAllUsers();
    UserDetailsModel? loggedInUser = _userDataRepository.getLoggedInUser();

    final result = await _getPropertyByIdUseCase(event.propertyId);
    result.fold(
      (failure) {
        emit(state.copyWith(pageState: PropertyInfoPageState.error));
      },
      (property) {
        if (property != null) {
          ItemPropertyInfoModel itemPropertyInfoModel = ItemPropertyInfoModel(propertyByIdEntity: property, isPropertyListingEnabled: isPropertyListingEnabled);
          final intiAssignedUserId = property.assignedTo?.map((e) => e?.id);
          final intiListingUserId = property.listingOnBehalf?.map((e) => e?.id);
          allUsers?.forEach((user) {
            if (user?.isActive ?? false) {
              if (loggedInUser?.userId != user?.id) {
                users.add(SelectableItem(
                    title: '${user?.firstName ?? ''} '
                        '${user?.lastName ?? ''}',
                    value: user?.id,
                    isSelected: intiAssignedUserId?.contains(user?.id) ?? false));
                listingOnBehalfAllUsers.add(SelectableItem(
                    title: '${user?.firstName ?? ''} '
                        '${user?.lastName ?? ''}',
                    value: user?.id,
                    isSelected: intiListingUserId?.contains(user?.id) ?? false));
              } else {
                users.insert(0, SelectableItem(title: 'You', value: user?.id, isSelected: intiAssignedUserId?.contains(user?.id) ?? false));
                listingOnBehalfAllUsers.insert(
                    0,
                    SelectableItem(
                        title: '${user?.firstName ?? ''} '
                            '${user?.lastName ?? ''}',
                        value: user?.id,
                        isSelected: intiListingUserId?.contains(user?.id) ?? false));
              }
            }
          });

          emit(state.copyWith(pageState: PropertyInfoPageState.success, itemPropertyInfoModel: itemPropertyInfoModel, allUsers: users, listingOnBehalfAllUsers: listingOnBehalfAllUsers, isHighlighted: property.tagInfo?.isFeatured, assignUsers: users.where((element) => element.isSelected).toList(), listingOnBehalf: users.where((element) => intiListingUserId?.contains(element.value) ?? false).toList()));
        }
      },
    );
  }

  FutureOr<void> _onStatusResetEvent(PageStateResetEvent event, Emitter<PropertyInfoState> emit) async {
    emit(state.copyWith(pageState: event.pageState));
  }

  FutureOr<void> _onGetMatchingLeadsEvent(GetMatchingLeadsEvent event, Emitter<PropertyInfoState> emit) async {
    emit(state.copyWith(matchingLeadsCount: event.matchingLeads));
  }

  FutureOr<void> _amenitiesToggleEvent(AmenitiesToggleEvent event, Emitter<PropertyInfoState> emit) async {
    emit(state.copyWith(isAmenitiesSelected: event.isAmenitiesToggled ? true : false));
  }

  FutureOr<void> _togglePropertyImageEvent(PropertyImageToggleEvent event, Emitter<PropertyInfoState> emit) async {
    emit(state.copyWith(propertyImageIndex: event.index));
  }

  FutureOr<void> _onPropertyDeleteEvent(PropertyDeleteEvent event, Emitter<PropertyInfoState> emit) async {
    emit(state.copyWith(pageState: PropertyInfoPageState.loading, statusMessage: 'archiving Project...'));
    var response = await _deletePropertyUseCase.call(event.propertyId);
    response.fold(
        (failure) => {
              emit(state.copyWith(pageState: PropertyInfoPageState.error, statusMessage: 'Unable to delete Property.')),
            },
        (result) => {
              emit(state.copyWith(pageState: PropertyInfoPageState.deleteSuccessful, statusMessage: 'Deleted Property Successfully.')),
            });
  }

  FutureOr<void> _onGetAssociateMatchingLeadsCountEvent(GetAssociateMatchingLeadsCountEvent event, Emitter<PropertyInfoState> emit) async {
    final result = await _getMatchingAssociateLeadsCountUseCase(event.propertyId);
    result.fold((failure) {
      emit(state.copyWith(pageState: PropertyInfoPageState.error));
    }, (associateLeadsCount) {
      emit(state.copyWith(associateLeadsCount: associateLeadsCount != null ? associateLeadsCount.first?.leadCount : 0));
    });
  }

  FutureOr<void> _onReAssignProperty(ReAssignPropertyEvent event, Emitter<PropertyInfoState> emit) async {
    emit(state.copyWith(pageState: PropertyInfoPageState.loading, statusMessage: "reassigning property"));

    var reAssignModel = ReAssignPropertyModel(propertiesId: event.propertyIds ?? [], userIds: event.selectedUsers);
    final response = await _reAssignPropertyUseCase.call(reAssignModel);
    await response.fold(
      (failure) async {
        // Check if emit is done before emitting
        if (!emit.isDone) {
          emit(state.copyWith(pageState: PropertyInfoPageState.failure, statusMessage: 'failed to assign the property'));
        }
      },
      (result) async {
        if (result) {
          final (List<BaseUserModel>? users, List<SelectableItem<String>>? temporaryAllUsers) selectableItemAndBaseUserModelUsers = await getSelectableItemAndBaseUserModelUsers(selectedUsers: event.selectedUsers);
          var property = state.itemPropertyInfoModel;
          property?.propertyByIdEntity?.assignedTo = selectableItemAndBaseUserModelUsers.$1;
          // Ensure emit is done check before emitting
          if (!emit.isDone) {
            emit(state.copyWith(
              pageState: PropertyInfoPageState.reAssignSuccessful,
              itemPropertyInfoModel: property,
              statusMessage: 'property assignment successful',
              assignUsers: selectableItemAndBaseUserModelUsers.$2?.where((element) => element.isSelected).toList(),
              allUsers: selectableItemAndBaseUserModelUsers.$2,
            ));
          }
        } else {
          if (!emit.isDone) {
            emit(state.copyWith(pageState: PropertyInfoPageState.failure, statusMessage: 'failed to assign the property'));
          }
        }
      },
    );
  }

  FutureOr<void> _onReListingPropertyEvent(ReListingPropertyEvent event, Emitter<PropertyInfoState> emit) async {
    emit(state.copyWith(pageState: PropertyInfoPageState.loading, statusMessage: "reassigning property"));
    var updatePropertyAssignedToModel = UpdatePropertyAssignedToModel(
      assignedTo: event.listingBy,
      listingOnBehalf: event.listingOnBehalf,
      propertyId: event.propertyIds,
    );
    final response = await _updatePropertyAssignedToUseCase.call(updatePropertyAssignedToModel);

    // Ensure the fold is awaited properly
    await response.fold(
      (failure) async {
        // Check if emit is done before emitting
        if (!emit.isDone) {
          emit(state.copyWith(pageState: PropertyInfoPageState.failure, statusMessage: 'failed to assign the property'));
        }
      },
      (result) async {
        if (result) {
          final (List<BaseUserModel>? users, List<SelectableItem<String>>? temporaryAllUsers) selectableItemAndBaseUserModelUsersListingBy = await getSelectableItemAndBaseUserModelUsers(selectedUsers: event.listingBy);
          final (List<BaseUserModel>? users, List<SelectableItem<String>>? temporaryAllUsers) selectableItemAndBaseUserModelUsersListingBehalf = await getSelectableItemAndBaseUserModelUsers(selectedUsers: event.listingOnBehalf);
          var property = state.itemPropertyInfoModel;
          property?.propertyByIdEntity?.assignedTo = selectableItemAndBaseUserModelUsersListingBy.$1;
          property?.propertyByIdEntity?.listingOnBehalf = selectableItemAndBaseUserModelUsersListingBehalf.$1;
          // Ensure emit is done check before emitting
          if (!emit.isDone) {
            emit(state.copyWith(
              pageState: PropertyInfoPageState.reAssignSuccessful,
              itemPropertyInfoModel: property,
              statusMessage: 'property assignment successful',
              assignUsers: selectableItemAndBaseUserModelUsersListingBy.$2?.where((element) => element.isSelected).toList(),
              listingOnBehalf: selectableItemAndBaseUserModelUsersListingBehalf.$2?.where((element) => element.isSelected).toList(),
              allUsers: selectableItemAndBaseUserModelUsersListingBy.$2,
              listingOnBehalfAllUsers: selectableItemAndBaseUserModelUsersListingBehalf.$2,
            ));
          }
        } else {
          if (!emit.isDone) {
            emit(state.copyWith(pageState: PropertyInfoPageState.failure, statusMessage: 'failed to assign the property'));
          }
        }
      },
    );
  }

  Future<(List<BaseUserModel>, List<SelectableItem<String>>)> getSelectableItemAndBaseUserModelUsers({List<String>? selectedUsers}) async {
    List<SelectableItem<String>>? temporaryAllUsers = [];
    List<GetAllUsersModel>? assignedUsers = [];
    allUsers = await _userDataRepository.getAllUsers();
    UserDetailsModel? loggedInUser = _userDataRepository.getLoggedInUser();
    List<BaseUserModel>? selectedUsersBaseUserModelType = [];
    final intiAssignedUserId = selectedUsers;
    for (var user in allUsers ?? []) {
      if (user?.isActive ?? false) {
        if (loggedInUser?.userId != user?.id) {
          temporaryAllUsers.add(SelectableItem(
            title: '${user?.firstName ?? ''} ${user?.lastName ?? ''}',
            value: user?.id,
            isSelected: intiAssignedUserId?.contains(user?.id) ?? false,
          ));
        } else {
          temporaryAllUsers.insert(0, SelectableItem(title: 'You', value: user?.id, isSelected: intiAssignedUserId?.contains(user?.id) ?? false));
        }
      }
    }
    for (var assignedUser in selectedUsers ?? []) {
      assignedUsers.add(allUsers?.firstWhere((user) => user?.id == assignedUser) ?? GetAllUsersModel(id: '', firstName: '', lastName: '', userName: '', imageUrl: '', isActive: false, isMFAEnabled: false));
    }
    for (var assignedUser in assignedUsers) {
      selectedUsersBaseUserModelType.add(BaseUserModel(
        id: assignedUser.id,
        userName: assignedUser.userName,
        isMFAEnabled: assignedUser.isMFAEnabled,
        isActive: assignedUser.isActive,
        imageUrl: assignedUser.imageUrl,
        firstName: assignedUser.firstName,
        lastName: assignedUser.lastName,
        name: '${assignedUser.firstName} ${assignedUser.lastName}',
      ));
    }
    return (selectedUsersBaseUserModelType, temporaryAllUsers);
  }
}
