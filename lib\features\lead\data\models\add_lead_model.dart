import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/models/address_model.dart';
import 'package:leadrat/core_main/common/models/dto_with_name_model.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/leads/profession.dart';
import 'package:leadrat/features/lead/data/models/create_enquiry_model.dart';
import 'package:leadrat/features/lead/data/models/get_lead_model.dart';
import 'package:leadrat/features/lead/data/models/lead_tag_model.dart';

part 'add_lead_model.g.dart';

@JsonSerializable(includeIfNull: false, explicitToJson: true)
class AddLeadModel {
  final String? id;
  final String? leadStatusId;
  final CreateLeadEnquiryModel? enquiry;
  final String? name;
  final String? contactNo;
  final String? alternateContactNo;
  final String? email;
  final String? notes;
  final DateTime? scheduledDate;
  final DateTime? revertDate;
  final String? chosenProject;
  final String? chosenProperty;
  final String? bookedUnderName;
  final String? leadNumber;
  final int? shareCount;
  final String? soldPrice;
  final String? rating;
  final LeadTagModel? leadTags;
  final List<TempProjectModel>? projects;
  final List<TempPropertyModel>? properties;
  final List<String>? projectsList;
  final List<String>? propertiesList;
  final String? assignTo;
  final String? assignedFrom;
  final String? referralName;
  final String? referralContactNo;
  final String? agencyName;
  final List<DTOWithNameModel>? agencies;
  final List<DTOWithNameModel>? campaigns;
  final String? campaignName;
  final String? companyName;
  final int? unmatchedBudget;
  final String? purchasedFrom;
  final String? closingManager;
  final String? sourcingManager;
  final AddressModel? address;
  final Profession? profession;
  final String? channelPartnerName;
  final List<String>? channelPartnerList;
  final String? channelPartnerExecutiveName;
  final String? channelPartnerContactNo;
  final List<ChannelPartnerModel>? channelPartners;
  final List<String>? existingProjects;
  final String? designation;
  final String? secondaryUserId;
  final String? statusId;
  final DateTime? bookedDate;
  final String? unitTypeId;
  final double? agreementValue;
  final String? serialNumber;
  final String? duplicateLeadVersion;
  final String? confidentialNotes;
  final Map<String, String?>? additionalProperties;
  final String? nationality;
  final String? referralEmail;
  final PossessionType? possesionType;

  AddLeadModel({
    this.id,
    this.leadStatusId,
    this.enquiry,
    this.name,
    this.contactNo,
    this.alternateContactNo,
    this.email,
    this.notes,
    this.scheduledDate,
    this.revertDate,
    this.chosenProject,
    this.chosenProperty,
    this.bookedUnderName,
    this.leadNumber,
    this.shareCount,
    this.soldPrice,
    this.rating,
    this.leadTags,
    this.projects,
    this.properties,
    this.projectsList,
    this.propertiesList,
    this.assignTo,
    this.assignedFrom,
    this.referralName,
    this.referralContactNo,
    this.agencyName,
    this.agencies,
    this.campaigns,
    this.campaignName,
    this.companyName,
    this.unmatchedBudget,
    this.purchasedFrom,
    this.closingManager,
    this.sourcingManager,
    this.address,
    this.profession,
    this.channelPartnerName,
    this.channelPartnerList,
    this.channelPartnerExecutiveName,
    this.channelPartnerContactNo,
    this.channelPartners,
    this.existingProjects,
    this.designation,
    this.secondaryUserId,
    this.statusId,
    this.bookedDate,
    this.unitTypeId,
    this.agreementValue,
    this.serialNumber,
    this.duplicateLeadVersion,
    this.confidentialNotes,
    this.additionalProperties,
    this.nationality,
    this.referralEmail,
    this.possesionType,
  });

  factory AddLeadModel.fromJson(Map<String, dynamic> json) => _$AddLeadModelFromJson(json);

  Map<String, dynamic> toJson() => _$AddLeadModelToJson(this);

  AddLeadModel copyWith({
    String? id,
    String? leadStatusId,
    CreateLeadEnquiryModel? enquiry,
    String? name,
    String? contactNo,
    String? alternateContactNo,
    String? email,
    String? notes,
    DateTime? scheduledDate,
    DateTime? revertDate,
    String? chosenProject,
    String? chosenProperty,
    String? bookedUnderName,
    String? leadNumber,
    int? shareCount,
    String? soldPrice,
    String? rating,
    LeadTagModel? leadTags,
    List<TempProjectModel>? projects,
    List<TempPropertyModel>? properties,
    List<String>? projectsList,
    List<String>? propertiesList,
    String? assignTo,
    String? assignedFrom,
    String? referralName,
    String? referralContactNo,
    String? agencyName,
    List<DTOWithNameModel>? agencies,
    List<DTOWithNameModel>? campaigns,
    String? campaignName,
    String? companyName,
    int? unmatchedBudget,
    String? purchasedFrom,
    String? closingManager,
    String? sourcingManager,
    AddressModel? address,
    Profession? profession,
    String? channelPartnerName,
    List<String>? channelPartnerList,
    String? channelPartnerExecutiveName,
    String? channelPartnerContactNo,
    List<ChannelPartnerModel>? channelPartners,
    List<String>? existingProjects,
    String? designation,
    String? secondaryUserId,
    String? statusId,
    DateTime? bookedDate,
    String? unitTypeId,
    double? agreementValue,
    String? serialNumber,
    String? duplicateLeadVersion,
    String? confidentialNotes,
    Map<String, String?>? additionalProperties,
    String? nationality,
    PossessionType? possesionType,
    String? referralEmail,
  }) {
    return AddLeadModel(
      id: id ?? this.id,
      leadStatusId: leadStatusId ?? this.leadStatusId,
      enquiry: enquiry ?? this.enquiry,
      name: name ?? this.name,
      contactNo: contactNo ?? this.contactNo,
      alternateContactNo: alternateContactNo ?? this.alternateContactNo,
      email: email ?? this.email,
      notes: notes ?? this.notes,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      revertDate: revertDate ?? this.revertDate,
      chosenProject: chosenProject ?? this.chosenProject,
      chosenProperty: chosenProperty ?? this.chosenProperty,
      bookedUnderName: bookedUnderName ?? this.bookedUnderName,
      leadNumber: leadNumber ?? this.leadNumber,
      shareCount: shareCount ?? this.shareCount,
      soldPrice: soldPrice ?? this.soldPrice,
      rating: rating ?? this.rating,
      leadTags: leadTags ?? this.leadTags,
      projects: projects ?? this.projects,
      properties: properties ?? this.properties,
      projectsList: projectsList ?? this.projectsList,
      propertiesList: propertiesList ?? this.propertiesList,
      assignTo: assignTo ?? this.assignTo,
      assignedFrom: assignedFrom ?? this.assignedFrom,
      referralName: referralName ?? this.referralName,
      referralContactNo: referralContactNo ?? this.referralContactNo,
      agencyName: agencyName ?? this.agencyName,
      agencies: agencies ?? this.agencies,
      campaigns: campaigns ?? this.campaigns,
      campaignName: campaignName ?? this.campaignName,
      companyName: companyName ?? this.companyName,
      unmatchedBudget: unmatchedBudget ?? this.unmatchedBudget,
      purchasedFrom: purchasedFrom ?? this.purchasedFrom,
      closingManager: closingManager ?? this.closingManager,
      sourcingManager: sourcingManager ?? this.sourcingManager,
      address: address ?? this.address,
      profession: profession ?? this.profession,
      channelPartnerName: channelPartnerName ?? this.channelPartnerName,
      channelPartnerList: channelPartnerList ?? this.channelPartnerList,
      channelPartnerExecutiveName: channelPartnerExecutiveName ?? this.channelPartnerExecutiveName,
      channelPartnerContactNo: channelPartnerContactNo ?? this.channelPartnerContactNo,
      channelPartners: channelPartners ?? this.channelPartners,
      existingProjects: existingProjects ?? this.existingProjects,
      designation: designation ?? this.designation,
      secondaryUserId: secondaryUserId ?? this.secondaryUserId,
      statusId: statusId ?? this.statusId,
      bookedDate: bookedDate ?? this.bookedDate,
      unitTypeId: unitTypeId ?? this.unitTypeId,
      agreementValue: agreementValue ?? this.agreementValue,
      serialNumber: serialNumber ?? this.serialNumber,
      duplicateLeadVersion: duplicateLeadVersion ?? this.duplicateLeadVersion,
      confidentialNotes: confidentialNotes ?? this.confidentialNotes,
      additionalProperties: additionalProperties ?? this.additionalProperties,
      nationality: nationality ?? this.nationality,
      referralEmail: referralEmail ?? this.referralEmail,
        possesionType: possesionType ?? this.possesionType,
    );
  }

  UpdateLeadModel toUpdateLeadModel() {
    return UpdateLeadModel(
      id: id,
      leadStatusId: leadStatusId,
      enquiry: enquiry,
      name: name,
      contactNo: contactNo,
      alternateContactNo: alternateContactNo,
      email: email,
      notes: notes,
      scheduledDate: scheduledDate,
      revertDate: revertDate,
      chosenProject: chosenProject,
      chosenProperty: chosenProperty,
      bookedUnderName: bookedUnderName,
      leadNumber: leadNumber,
      shareCount: shareCount,
      soldPrice: soldPrice,
      rating: rating,
      leadTags: leadTags,
      projects: projects,
      properties: properties,
      projectsList: projectsList,
      propertiesList: propertiesList,
      assignTo: assignTo,
      assignedFrom: assignedFrom,
      referralName: referralName,
      referralContactNo: referralContactNo,
      agencyName: agencyName,
      agencies: agencies,
      campaigns: campaigns,
      campaignName: campaignName,
      companyName: companyName,
      unmatchedBudget: unmatchedBudget,
      purchasedFrom: purchasedFrom,
      closingManager: closingManager,
      sourcingManager: sourcingManager,
      address: address,
      profession: profession,
      channelPartnerName: channelPartnerName,
      channelPartnerList: channelPartnerList,
      channelPartnerExecutiveName: channelPartnerExecutiveName,
      channelPartnerContactNo: channelPartnerContactNo,
      channelPartners: channelPartners,
      existingProjects: existingProjects,
      designation: designation,
      secondaryUserId: secondaryUserId,
      statusId: statusId,
      bookedDate: bookedDate,
      unitTypeId: unitTypeId,
      agreementValue: agreementValue,
      serialNumber: serialNumber,
      duplicateLeadVersion: duplicateLeadVersion,
      confidentialNotes: confidentialNotes,
      additionalProperties: additionalProperties,
      nationality: nationality,
      possesionType: possesionType,
      referralEmail: referralEmail,
    );
  }
}

@JsonSerializable(includeIfNull: false, explicitToJson: true)
class UpdateLeadModel extends AddLeadModel {
  UpdateLeadModel({
    String? id,
    String? leadStatusId,
    CreateLeadEnquiryModel? enquiry,
    String? name,
    String? contactNo,
    String? alternateContactNo,
    String? email,
    String? notes,
    DateTime? scheduledDate,
    DateTime? revertDate,
    String? chosenProject,
    String? chosenProperty,
    String? bookedUnderName,
    String? leadNumber,
    int? shareCount,
    String? soldPrice,
    String? rating,
    LeadTagModel? leadTags,
    List<TempProjectModel>? projects,
    List<TempPropertyModel>? properties,
    List<String>? projectsList,
    List<String>? propertiesList,
    String? assignTo,
    String? assignedFrom,
    String? referralName,
    String? referralContactNo,
    String? agencyName,
    List<DTOWithNameModel>? agencies,
    List<DTOWithNameModel>? campaigns,
    String? campaignName,
    String? companyName,
    int? unmatchedBudget,
    String? purchasedFrom,
    String? closingManager,
    String? sourcingManager,
    AddressModel? address,
    Profession? profession,
    String? channelPartnerName,
    List<String>? channelPartnerList,
    String? channelPartnerExecutiveName,
    String? channelPartnerContactNo,
    List<ChannelPartnerModel>? channelPartners,
    List<String>? existingProjects,
    String? designation,
    String? secondaryUserId,
    String? statusId,
    DateTime? bookedDate,
    String? unitTypeId,
    double? agreementValue,
    String? serialNumber,
    String? duplicateLeadVersion,
    String? confidentialNotes,
    Map<String, String?>? additionalProperties,
    String? nationality,
    PossessionType? possesionType,
    String? referralEmail,
  }) : super(
          id: id,
          leadStatusId: leadStatusId,
          enquiry: enquiry,
          name: name,
          contactNo: contactNo,
          alternateContactNo: alternateContactNo,
          email: email,
          notes: notes,
          scheduledDate: scheduledDate,
          revertDate: revertDate,
          chosenProject: chosenProject,
          chosenProperty: chosenProperty,
          bookedUnderName: bookedUnderName,
          leadNumber: leadNumber,
          shareCount: shareCount,
          soldPrice: soldPrice,
          rating: rating,
          leadTags: leadTags,
          projects: projects,
          properties: properties,
          projectsList: projectsList,
          propertiesList: propertiesList,
          assignTo: assignTo,
          assignedFrom: assignedFrom,
          referralName: referralName,
          referralContactNo: referralContactNo,
          agencyName: agencyName,
          agencies: agencies,
          campaigns: campaigns,
          campaignName: campaignName,
          companyName: companyName,
          unmatchedBudget: unmatchedBudget,
          purchasedFrom: purchasedFrom,
          closingManager: closingManager,
          sourcingManager: sourcingManager,
          address: address,
          profession: profession,
          channelPartnerName: channelPartnerName,
          channelPartnerList: channelPartnerList,
          channelPartnerExecutiveName: channelPartnerExecutiveName,
          channelPartnerContactNo: channelPartnerContactNo,
          channelPartners: channelPartners,
          existingProjects: existingProjects,
          designation: designation,
          secondaryUserId: secondaryUserId,
          statusId: statusId,
          bookedDate: bookedDate,
          unitTypeId: unitTypeId,
          agreementValue: agreementValue,
          serialNumber: serialNumber,
          duplicateLeadVersion: duplicateLeadVersion,
          confidentialNotes: confidentialNotes,
          additionalProperties: additionalProperties,
          nationality: nationality,
          referralEmail: referralEmail,
      possesionType: possesionType,
        );

  factory UpdateLeadModel.fromJson(Map<String, dynamic> json) => _$UpdateLeadModelFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$UpdateLeadModelToJson(this);
}
