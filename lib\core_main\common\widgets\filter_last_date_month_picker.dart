import 'package:flutter/material.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_statefull_widget.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';

class FilterCustomMonthPicker extends LeadratStatefulWidget {
  final String labelText;
  final DateTime? selectedDate;
  final DateTime? minDate;
  final DateTime? maxDate;
  final Function(DateTime?) onDateSelected;

  const FilterCustomMonthPicker({
    Key? key,
    required this.labelText,
    required this.selectedDate,
    required this.onDateSelected,
    this.minDate,
    this.maxDate,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _FilterCustomMonthPickerState();
}

class _FilterCustomMonthPickerState extends LeadratState<FilterCustomMonthPicker> {
  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.selectedDate;
  }

  Future<void> _selectDate(BuildContext context) async {
    DateTime now = DateTime.now();
    int tempYear = _selectedDate?.year ?? now.year;
    int tempMonth = _selectedDate?.month ?? now.month;

    final DateTime? picked = await showDialog<DateTime>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            List<int> years = List.generate(50, (index) => now.year - 25 + index);

            return AlertDialog(
              title: const Text('Select Month and Year'),
              content: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  /// Month Picker
                  DropdownButton<int>(
                    value: tempMonth,
                    items: List.generate(12, (index) {
                      final month = index + 1;
                      final tempDate = DateTime(tempYear, month, 1);

                      final isBeforeMin = widget.minDate != null && tempDate.isBefore(DateTime(widget.minDate!.year, widget.minDate!.month));
                      final isAfterMax = widget.maxDate != null && tempDate.isAfter(DateTime(widget.maxDate!.year, widget.maxDate!.month));
                      final isDisabled = isBeforeMin || isAfterMax;

                      return DropdownMenuItem(
                        value: month,
                        enabled: !isDisabled,
                        child: Text(
                          "${month.toString().padLeft(2, '0')}",
                          style: TextStyle(
                            color: isDisabled ? Colors.grey : Colors.black,
                          ),
                        ),
                      );
                    }),
                    onChanged: (value) {
                      if (value != null) {
                        final chosen = DateTime(tempYear, value, 1);
                        final isBeforeMin = widget.minDate != null && chosen.isBefore(DateTime(widget.minDate!.year, widget.minDate!.month));
                        final isAfterMax = widget.maxDate != null && chosen.isAfter(DateTime(widget.maxDate!.year, widget.maxDate!.month));
                        if (!isBeforeMin && !isAfterMax) {
                          Navigator.of(context).pop(DateTime(tempYear, value + 1, 0));
                        }
                      }
                    },
                  ),

                  /// Year Picker
                  DropdownButton<int>(
                    value: tempYear,
                    items: years.map((year) {
                      final isDisabled = (widget.minDate != null && year < widget.minDate!.year) || (widget.maxDate != null && year > widget.maxDate!.year);
                      return DropdownMenuItem(
                        value: year,
                        enabled: !isDisabled,
                        child: Text(
                          year.toString(),
                          style: TextStyle(
                            color: isDisabled ? Colors.grey : Colors.black,
                          ),
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          tempYear = value;
                        });

                        final chosen = DateTime(tempYear, tempMonth, 1);
                        final isBeforeMin = widget.minDate != null && chosen.isBefore(DateTime(widget.minDate!.year, widget.minDate!.month));
                        final isAfterMax = widget.maxDate != null && chosen.isAfter(DateTime(widget.maxDate!.year, widget.maxDate!.month));
                        if (!isBeforeMin && !isAfterMax) {
                          Navigator.of(context).pop(DateTime(tempYear, tempMonth + 1, 0));
                        }
                      }
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
              ],
            );
          },
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      widget.onDateSelected(picked);
    }
  }

  @override
  Widget buildContent(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          widget.labelText,
          style: LexendTextStyles.lexend10Medium.copyWith(color: ColorPalette.gray600),
        ),
        const SizedBox(width: 10),
        InkWell(
          onTap: () => _selectDate(context),
          child: Container(
            padding: const EdgeInsets.fromLTRB(10, 8, 8, 8),
            decoration: BoxDecoration(
              color: ColorPalette.primaryColor,
              border: Border.all(color: ColorPalette.primaryLightColor),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.max,
              children: [
                Text(
                  _selectedDate == null ? "select month" : "${_selectedDate!.month.toString().padLeft(2, '0')}/${_selectedDate!.year}",
                  style: LexendTextStyles.lexend10Medium.copyWith(
                    color: _selectedDate == null ? ColorPalette.gray600 : ColorPalette.white,
                  ),
                ),
                const SizedBox(width: 20),
                Image.asset(ImageResources.icCalendarMark),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
