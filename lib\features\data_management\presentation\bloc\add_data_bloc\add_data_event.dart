part of 'add_data_bloc.dart';

@immutable
sealed class AddDataEvent {}

final class AddDataInitialEvent extends AddDataEvent {
  final ProspectEntity? prospectEntity;

  AddDataInitialEvent(this.prospectEntity);
}

final class ToggleEnquiredForEvent extends AddDataEvent {
  final ItemSimpleModel<EnquiryTypeEnum>? selectedEnquiredFor;

  ToggleEnquiredForEvent(this.selectedEnquiredFor);
}

final class TogglePropertyTypeEvent extends AddDataEvent {
  final ItemSimpleModel<PropertyType> selectedPropertyType;

  TogglePropertyTypeEvent(this.selectedPropertyType);
}

final class TogglePropertySubTypeEvent extends AddDataEvent {
  final ItemSimpleModel<String> selectedPropertySubType;

  TogglePropertySubTypeEvent(this.selectedPropertySubType);
}

final class ToggleNoOfBhkEvent extends AddDataEvent {
  final ItemSimpleModel<double> selectedNoOfBhk;

  ToggleNoOfBhkEvent(this.selectedNoOfBhk);
}

final class ToggleBhkTypeEvent extends AddDataEvent {
  final ItemSimpleModel<BHKType> selectedBhkTypes;

  ToggleBhkTypeEvent(this.selectedBhkTypes);
}

final class ToggleProfessionEvent extends AddDataEvent {
  final ItemSimpleModel<Profession>? selectedProfession;

  ToggleProfessionEvent(this.selectedProfession);
}

final class SelectCarpetAreaEvent extends AddDataEvent {
  final SelectableItem<MasterAreaUnitsModel>? selectedCarpetArea;

  SelectCarpetAreaEvent(this.selectedCarpetArea);
}

final class SelectSaleableAreaEvent extends AddDataEvent {
  final SelectableItem<MasterAreaUnitsModel>? selectedSaleableArea;

  SelectSaleableAreaEvent(this.selectedSaleableArea);
}

final class SelectBuiltUpAreaEvent extends AddDataEvent {
  final SelectableItem<MasterAreaUnitsModel>? selectedBuiltUpArea;

  SelectBuiltUpAreaEvent(this.selectedBuiltUpArea);
}

final class SelectDataSourceEvent extends AddDataEvent {
  final SelectableItem<String> selectedLeadSource;

  SelectDataSourceEvent(this.selectedLeadSource);
}

final class SelectDataSubSourceEvent extends AddDataEvent {
  final SelectableItem<String> selectedLeadSubSource;

  SelectDataSubSourceEvent(this.selectedLeadSubSource);
}

final class SelectAgencyNameEvent extends AddDataEvent {
  final List<SelectableItem<String>> selectedAgencyName;

  SelectAgencyNameEvent(this.selectedAgencyName);
}

final class RemoveAgencyNameEvent extends AddDataEvent {
  final SelectableItem<String> selectedAgencyName;

  RemoveAgencyNameEvent(this.selectedAgencyName);
}

final class SelectPropertiesEvent extends AddDataEvent {
  final List<SelectableItem<String>> selectedProperties;

  SelectPropertiesEvent(this.selectedProperties);
}

final class RemovePropertyEvent extends AddDataEvent {
  final SelectableItem<String> selectedProperty;

  RemovePropertyEvent(this.selectedProperty);
}

final class SelectProjectsEvent extends AddDataEvent {
  final List<SelectableItem<String>> selectedProjects;

  SelectProjectsEvent(this.selectedProjects);
}

final class RemoveProjectsEvent extends AddDataEvent {
  final SelectableItem<String> selectedProjects;

  RemoveProjectsEvent(this.selectedProjects);
}

final class SelectAssignedUserEvent extends AddDataEvent {
  final SelectableItem<String> selectedUser;

  SelectAssignedUserEvent(this.selectedUser);
}

final class SelectCampaignNameEvent extends AddDataEvent {
  final List<SelectableItem<String>> selectedCampaignName;

  SelectCampaignNameEvent(this.selectedCampaignName);
}

final class RemoveCampaignNameEvent extends AddDataEvent {
  final SelectableItem<String> selectedCampaignName;

  RemoveCampaignNameEvent(this.selectedCampaignName);
}

final class ToggleSubTypesExpandedEvent extends AddDataEvent {}

final class ToggleNoOfBhkExpandedEvent extends AddDataEvent {}

final class ToggleEmailFieldEvent extends AddDataEvent {}

final class ToggleAltPhoneFieldEvent extends AddDataEvent {
  final bool? hideAltPhoneField;

  ToggleAltPhoneFieldEvent({this.hideAltPhoneField});
}

final class TogglePossessionDateEvent extends AddDataEvent {}

final class SelectPossessionDateEvent extends AddDataEvent {
  final DateTime selectedDate;

  SelectPossessionDateEvent(this.selectedDate);
}

final class AddLocationEvent extends AddDataEvent {
  final AddressModel? location;

  AddLocationEvent(this.location);
}

final class RemoveLocationEvent extends AddDataEvent {
  final ItemSimpleModel<AddressModel> selectedItem;

  RemoveLocationEvent(this.selectedItem);
}

final class CheckDataContactAlreadyExistsEvent extends AddDataEvent {
  final String countryCode;
  final String contactNo;

  CheckDataContactAlreadyExistsEvent(this.countryCode, this.contactNo);
}

final class CheckAltContactAlreadyExistsEvent extends AddDataEvent {
  final String countryCode;
  final String contactNo;

  CheckAltContactAlreadyExistsEvent(this.countryCode, this.contactNo);
}

final class CreateDataEvent extends AddDataEvent {}

final class ResetStateEvent extends AddDataEvent {}

final class OnDataContactChangedEvent extends AddDataEvent {
  final String countryCode;
  final String contactNumber;

  OnDataContactChangedEvent(this.countryCode, this.contactNumber);
}

final class OnAltContactChangedEvent extends AddDataEvent {
  final String countryCode;
  final String contactNumber;

  OnAltContactChangedEvent(this.countryCode, this.contactNumber);
}

final class PickContactEvent extends AddDataEvent {}

final class SelectCurrencyEvent extends AddDataEvent {
  final SelectableItem<String> selectedCurrency;

  SelectCurrencyEvent(this.selectedCurrency);
}

final class SelectPurposeEvent extends AddDataEvent {
  final SelectableItem<PurposeEnum>? selectedPurpose;

  SelectPurposeEvent(this.selectedPurpose);
}

final class AssignedToLoggedInUser extends AddDataEvent {}

final class SelectPossessionType extends AddDataEvent {
  final SelectableItem<PossessionType?>? selectPossessionType;

  SelectPossessionType(this.selectPossessionType);
}
final class SelectSourcingManagerEvent extends AddDataEvent {
  final SelectableItem<String> selectedSourcingManager;

  SelectSourcingManagerEvent(this.selectedSourcingManager);
}

final class SelectClosingManagerEvent extends AddDataEvent {
  final SelectableItem<String> selectedClosingManager;

  SelectClosingManagerEvent(this.selectedClosingManager);
}

final class AddCustomerLocationEvent extends AddDataEvent {
  final AddressModel? customerLocation;

  AddCustomerLocationEvent(this.customerLocation);
}

final class RemoveCustomerLocationEvent extends AddDataEvent {
  final ItemSimpleModel<AddressModel>? selectedItem;

  RemoveCustomerLocationEvent(this.selectedItem);
}

final class OnExecutiveContactChangedEvent extends AddDataEvent {
  final String countryCode;
  final String contactNumber;

  OnExecutiveContactChangedEvent(this.countryCode, this.contactNumber);
}

final class OnReferralContactChangedEvent extends AddDataEvent {
  final String countryCode;
  final String contactNumber;

  OnReferralContactChangedEvent(this.countryCode, this.contactNumber);
}

final class ToggleReferralFieldsEvent extends AddDataEvent {}
