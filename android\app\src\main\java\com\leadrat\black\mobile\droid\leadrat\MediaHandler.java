package com.leadrat.black.mobile.droid.leadrat;

import android.app.Activity;
import android.content.ContentResolver;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Environment;
import android.provider.MediaStore;

import androidx.annotation.Nullable;
import androidx.core.content.FileProvider;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

public class MediaHandler {
    private static final int CAMERA_REQUEST_CODE = 1001;
    private static final int GALLERY_REQUEST_CODE = 1002;
    private static final int DOCUMENT_REQUEST_CODE = 1003;

    private final Activity activity;
    private Uri photoURI;
    private MethodChannel.Result resultCallback;

    public MediaHandler(Activity activity, FlutterEngine flutterEngine) {
        this.activity = activity;
    }

    public void openCamera(MethodChannel.Result result) {
        this.resultCallback = result;
        Intent cameraIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        if (cameraIntent.resolveActivity(activity.getPackageManager()) != null) {
            try {
                // Create a file to save the image
                File photoFile = createImageFile();
                photoURI = FileProvider.getUriForFile(activity,
                        activity.getApplicationContext().getPackageName() + ".fileprovider", photoFile);
                cameraIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI);
                activity.startActivityForResult(cameraIntent, CAMERA_REQUEST_CODE);
            } catch (IOException ex) {
                result.error("FILE_ERROR", "Error creating file for image", null);
            }
        } else {
            result.error("CAMERA_ERROR", "No camera app found", null);
        }
    }

    public void openGallery(MethodChannel.Result result, boolean isMultiSelection) {
        this.resultCallback = result;
        Intent galleryIntent = new Intent(Intent.ACTION_GET_CONTENT);
        galleryIntent.setType("image/*");
        if (isMultiSelection) {
            galleryIntent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true); // Allow multiple image selection
        }
        galleryIntent.addCategory(Intent.CATEGORY_OPENABLE);

        if (galleryIntent.resolveActivity(activity.getPackageManager()) != null) {
            activity.startActivityForResult(Intent.createChooser(galleryIntent, "Select Images"), GALLERY_REQUEST_CODE);
        } else {
            result.error("GALLERY_ERROR", "No gallery app found", null);
        }
    }

    public void openDocuments(MethodCall call, MethodChannel.Result result, boolean isMultiSelection) {
        this.resultCallback = result;
        String fileType = call.argument("fileType");
        String mimeType;

        switch (fileType) {
            case "FileType.image":
                mimeType = "image/*";
                break;
            case "FileType.video":
                mimeType = "video/*";
                break;
            case "FileType.pdf":
                mimeType = "application/pdf";
                break;
            default:
                mimeType = "*/*";
                break;
        }

        Intent documentIntent = new Intent(Intent.ACTION_OPEN_DOCUMENT);
        documentIntent.addCategory(Intent.CATEGORY_OPENABLE);
        documentIntent.setType(mimeType);
        if (isMultiSelection) {
            documentIntent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true);
        }
        if (documentIntent.resolveActivity(activity.getPackageManager()) != null) {
            activity.startActivityForResult(documentIntent, DOCUMENT_REQUEST_CODE);
        } else {
            result.error("DOCUMENT_ERROR", "No document app found", null);
        }
    }

    public void getPathFromUri(Uri uri, MethodChannel.Result result) {
        try {
            String path = getFileFromUri(uri);
            result.success(path);
        } catch (IOException e) {
            result.error("IO_ERROR", "Error accessing file from URI", e.getMessage());
        }
    }

    private File createImageFile() throws IOException {
        // Create an image file name
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
        String imageFileName = "JPEG_" + timeStamp + "_";
        File storageDir = activity.getExternalFilesDir(Environment.DIRECTORY_PICTURES);
        return File.createTempFile(imageFileName, ".jpg", storageDir);
    }

    private String getFileFromUri(Uri uri) throws IOException {
        // Get the original file name
        String fileName = getFileName(uri);
        if (fileName == null) {
            fileName = "temp_file";  // Use a fallback name if unable to get the original name
        }

        // Create a file in the cache directory with the original file name
        File tempFile = new File(activity.getCacheDir(), fileName);
        try (InputStream inputStream = activity.getContentResolver().openInputStream(uri);
             OutputStream outputStream = new FileOutputStream(tempFile)) {

            if (inputStream == null) {
                throw new IOException("Unable to open InputStream from URI");
            }

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
        return tempFile.getAbsolutePath();
    }

    private String getFileName(Uri uri) {
        String result = null;
        if (uri.getScheme().equals("content")) {
            try (Cursor cursor = activity.getContentResolver().query(uri, null, null, null, null)) {
                if (cursor != null && cursor.moveToFirst()) {
                    int nameIndex = cursor.getColumnIndex(MediaStore.MediaColumns.DISPLAY_NAME);
                    if (nameIndex != -1) {
                        result = cursor.getString(nameIndex);
                    }
                }
            }
        }
        if (result == null) {
            result = uri.getLastPathSegment();
        }
        return result;
    }

    public void handleActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        if (resultCallback == null) {
            return;  // If resultCallback is null, avoid further actions
        }

        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == CAMERA_REQUEST_CODE) {
                if (photoURI != null) {
                    resultCallback.success(photoURI.toString()); // Return URI to Flutter
                } else {
                    resultCallback.error("URI_ERROR", "Photo URI is null", null);
                }
            } else if (requestCode == GALLERY_REQUEST_CODE) {
                if (data != null) {
                    List<String> filePaths = new ArrayList<>();

                    if (data.getClipData() != null) { // Multiple images selected
                        int count = data.getClipData().getItemCount();
                        for (int i = 0; i < count; i++) {
                            Uri imageUri = data.getClipData().getItemAt(i).getUri();
                            try {
                                filePaths.add(getFileFromUri(imageUri));
                            } catch (IOException e) {
                                resultCallback.error("IO_ERROR", "Failed to access file: " + e.getMessage(), null);
                                return;
                            }
                        }
                    } else if (data.getData() != null) { // Single image selected
                        Uri imageUri = data.getData();
                        try {
                            filePaths.add(getFileFromUri(imageUri));
                        } catch (IOException e) {
                            resultCallback.error("IO_ERROR", "Failed to access file: " + e.getMessage(), null);
                            return;
                        }
                    }

                    if (!filePaths.isEmpty()) {
                        resultCallback.success(filePaths); // Return list of file paths to Flutter
                    } else {
                        resultCallback.error("URI_ERROR", "No file selected", null);
                    }
                } else {
                    resultCallback.error("URI_ERROR", "No file selected", null);
                }
            } else if (requestCode == DOCUMENT_REQUEST_CODE) {

                if (data != null) {
                    List<String> documentUris = new ArrayList<>();

                    if (data.getClipData() != null) { // Multiple documents selected
                        int count = data.getClipData().getItemCount();
                        for (int i = 0; i < count; i++) {
                            Uri documentUri = data.getClipData().getItemAt(i).getUri();
                            documentUris.add(documentUri.toString());
                        }
                    } else if (data.getData() != null) { // Single document selected
                        documentUris.add(data.getData().toString());
                    }

                    if (!documentUris.isEmpty()) {
                        resultCallback.success(documentUris); // Return list of document URIs to Flutter
                    } else {
                        resultCallback.error("URI_ERROR", "No document selected", null);
                    }
                } else {
                    resultCallback.error("URI_ERROR", "No document selected", null);
                }
            }
        } else if (resultCode == Activity.RESULT_CANCELED) {
            resultCallback.success(null); // Return null to Flutter to indicate cancellation
        } else {
            resultCallback.error("RESULT_ERROR", "Unexpected resultCode: " + resultCode, null);
        }

        resultCallback = null;
    }
}