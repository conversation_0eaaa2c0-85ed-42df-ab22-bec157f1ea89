import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/common/models/address_model.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/purpose_enum.dart';
import 'package:leadrat/core_main/enums/data_management/enquiry_type_enum.dart';

import '../../../../core_main/enums/common/no_of_beds.dart';

part 'create_prospect_enquiry_model.g.dart';

@JsonSerializable(includeIfNull: false)
class CreateProspectEnquiryModel {
  final EnquiryTypeEnum? enquiryType;
  final List<EnquiryTypeEnum>? enquiryTypes;
  final BHKType? bhkType;
  final double? noOfBhks;
  final List<BHKType>? bhkTypes;
  final List<double>? bhks;
  final String? subSource;
  final int? lowerBudget;
  final int? upperBudget;
  final double? carpetArea;
  final double? saleableArea;
  final double? builtUpArea;
  final double? conversionFactor;
  final String? carpetAreaUnit;
  final String? carpetAreaUnitId;
  final AddressModel? address;
  final List<AddressModel>? addresses;
  final String? currency;
  final String? prospectSourceId;
  final String? propertyTypeId;
  final List<String>? propertyTypeIds;
  final PossessionType? possesionType;

  final int? furnished;
  final int? offerType;
  final String? saleableAreaUnitId;
  final String? builtUpAreaUnitId;
  final double? saleableAreaConversionFactor;
  final double? builtUpAreaConversionFactor;
  final List<String>? floors;
  final List<Beds>? beds;
  final List<double>? bHKs;
  final List<int>? baths;
  final String? propertyAreaUnitId;
  final double? propertyArea;
  final String? netAreaUnitId;
  final double? netArea;
  final double? propertyAreaConversionFactor;
  final double? netAreaConversionFactor;
  final String? unitName;
  final String? clusterName;

  @JsonKey(defaultValue: PurposeEnum.none)
  final PurposeEnum? purpose;

  CreateProspectEnquiryModel({
    this.enquiryType,
    this.enquiryTypes,
    this.bhkType,
    this.noOfBhks,
    this.bhkTypes,
    this.bhks,
    this.subSource,
    this.lowerBudget,
    this.upperBudget,
    this.carpetArea,
    this.builtUpArea,
    this.saleableArea,
    this.conversionFactor,
    this.carpetAreaUnit,
    this.carpetAreaUnitId,
    this.address,
    this.addresses,
    this.currency,
    this.prospectSourceId,
    this.propertyTypeId,
    this.propertyTypeIds,
    this.furnished,
    this.offerType,
    this.saleableAreaUnitId,
    this.builtUpAreaUnitId,
    this.builtUpAreaConversionFactor,
    this.saleableAreaConversionFactor,
    this.floors,
    this.beds,
    this.baths,
    this.bHKs,
    this.propertyAreaUnitId,
    this.propertyArea,
    this.netAreaUnitId,
    this.netArea,
    this.propertyAreaConversionFactor,
    this.netAreaConversionFactor,
    this.unitName,
    this.clusterName,
    this.purpose,
    this.possesionType,
  });

  factory CreateProspectEnquiryModel.fromJson(Map<String, dynamic> json) => _$CreateProspectEnquiryModelFromJson(json);

  Map<String, dynamic> toJson() => _$CreateProspectEnquiryModelToJson(this);

  CreateProspectEnquiryModel copyWith({
    EnquiryTypeEnum? enquiryType,
    List<EnquiryTypeEnum>? enquiryTypes,
    BHKType? bhkType,
    double? noOfBhks,
    List<BHKType>? bhkTypes,
    List<double>? bhks,
    String? subSource,
    int? lowerBudget,
    int? upperBudget,
    double? carpetArea,
    double? saleableArea,
    double? builtUpArea,
    double? conversionFactor,
    String? carpetAreaUnit,
    String? carpetAreaUnitId,
    AddressModel? address,
    List<AddressModel>? addresses,
    String? currency,
    String? prospectSourceId,
    String? propertyTypeId,
    List<String>? propertyTypeIds,
    int? furnished,
    int? offerType,
    String? saleableAreaUnitId,
    String? builtUpAreaUnitId,
    double? saleableAreaConversionFactor,
    double? builtUpAreaConversionFactor,
    List<String>? floors,
    List<Beds>? beds,
    List<double>? bHKs,
    List<int>? baths,
    String? propertyAreaUnitId,
    double? propertyArea,
    String? netAreaUnitId,
    double? netArea,
    double? propertyAreaConversionFactor,
    double? netAreaConversionFactor,
    String? unitName,
    bool resetCarpetAreaUnit = false,
    bool resetSaleableAreaUnit = false,
    bool resetBuiltUpAreaAreaUnit = false,
    bool resetNetAreaUnit = false,
    bool resetPropertyAreaUnit = false,
    String? clusterName,
    PurposeEnum? purpose,
    PossessionType? possesionType,
  }) {
    return CreateProspectEnquiryModel(
        enquiryType: enquiryType ?? this.enquiryType,
        enquiryTypes: enquiryTypes ?? this.enquiryTypes,
        bhkType: bhkType ?? this.bhkType,
        noOfBhks: noOfBhks ?? this.noOfBhks,
        bhkTypes: bhkTypes ?? this.bhkTypes,
        bhks: bhks ?? this.bhks,
        subSource: subSource ?? this.subSource,
        lowerBudget: lowerBudget ?? this.lowerBudget,
        upperBudget: upperBudget ?? this.upperBudget,
        carpetArea: carpetArea ?? this.carpetArea,
        builtUpArea: builtUpArea ?? this.builtUpArea,
        saleableArea: saleableArea ?? this.saleableArea,
        conversionFactor: conversionFactor ?? this.conversionFactor,
        carpetAreaUnit: carpetAreaUnit ?? this.carpetAreaUnit,
        carpetAreaUnitId: resetCarpetAreaUnit ? null : (carpetAreaUnitId ?? this.carpetAreaUnitId),
        address: address ?? this.address,
        addresses: addresses ?? this.addresses,
        currency: currency ?? this.currency,
        prospectSourceId: prospectSourceId ?? this.prospectSourceId,
        propertyTypeId: propertyTypeId ?? this.propertyTypeId,
        propertyTypeIds: propertyTypeIds ?? this.propertyTypeIds,
        furnished: furnished ?? this.furnished,
        offerType: offerType ?? this.offerType,
        saleableAreaUnitId: resetSaleableAreaUnit ? null : (saleableAreaUnitId ?? this.saleableAreaUnitId),
        builtUpAreaUnitId: resetBuiltUpAreaAreaUnit ? null : (builtUpAreaUnitId ?? this.builtUpAreaUnitId),
        netAreaUnitId: resetNetAreaUnit ? null : (netAreaUnitId ?? this.netAreaUnitId),
        propertyAreaUnitId: resetPropertyAreaUnit ? null : (propertyAreaUnitId ?? this.propertyAreaUnitId),
        saleableAreaConversionFactor: saleableAreaConversionFactor ?? this.saleableAreaConversionFactor,
        builtUpAreaConversionFactor: builtUpAreaConversionFactor ?? this.builtUpAreaConversionFactor,
        netAreaConversionFactor: netAreaConversionFactor ?? this.netAreaConversionFactor,
        propertyAreaConversionFactor: propertyAreaConversionFactor ?? this.propertyAreaConversionFactor,
        floors: floors ?? this.floors,
        beds: beds ?? this.beds,
        baths: baths ?? this.baths,
        bHKs: bhks ?? this.bhks,
        unitName: unitName ?? this.unitName,
        propertyArea: propertyArea ?? this.propertyArea,
        netArea: netArea ?? this.netArea,
        clusterName: clusterName ?? this.clusterName,
        purpose: purpose ?? this.purpose,
        possesionType: possesionType ?? this.possesionType);
  }
}
