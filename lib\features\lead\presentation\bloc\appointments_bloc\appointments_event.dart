part of 'appointments_bloc.dart';

@immutable
sealed class AppointmentsEvent {}

class InitAppointmentsPageEvent extends AppointmentsEvent {
  final GetLeadEntity? leadEntity;
  final GlobalSettingModel? globalSettingModel;

  InitAppointmentsPageEvent(this.leadEntity, this.globalSettingModel);
}

class GetAllProjectsEvent extends AppointmentsEvent {}

class UpdateLeadProjectsEvent extends AppointmentsEvent {
  final List<SelectableItem<String?>>? selectedProjects;

  UpdateLeadProjectsEvent({this.selectedProjects});
}

class ProjectSelectedEvent extends AppointmentsEvent {
  final SelectableItem<String?>? selectedProject;
  final bool? projectEdited;
  final int? index;

  ProjectSelectedEvent(this.selectedProject, this.projectEdited, {this.index});
}

class AddLeadDocumentsEvent extends AppointmentsEvent {
  final SelectFileEnum? fileType;
  final String? documentName;

  AddLeadDocumentsEvent(this.fileType, {this.documentName});
}

class AddLocationEvent extends AppointmentsEvent {
  final AddressModel? location;

  AddLocationEvent(this.location);
}

class AppointmentDoneEvent extends AppointmentsEvent {
  final AppointmentDoneItem? item;

  AppointmentDoneEvent(this.item);
}

class AppointmentNotDoneEvent extends AppointmentsEvent {
  final AppointmentDoneItem? item;

  AppointmentNotDoneEvent(this.item);
}

class ClearStateEvent extends AppointmentsEvent {}

final class SelectAssignedUserEvent extends AppointmentsEvent {
  final SelectableItem<String> selectedUser;

  SelectAssignedUserEvent(this.selectedUser);
}
