part of 'custom_add_data_bloc.dart';

@immutable
sealed class CustomAddDataEvent {}

final class AddDataInitialEvent extends CustomAddDataEvent {
  final GlobalSettingModel? globalSettingModel;
  final ProspectEntity? getDataEntity;

  AddDataInitialEvent({this.globalSettingModel, this.getDataEntity});
}

final class ToggleEnquiredForEvent extends CustomAddDataEvent {
  final ItemSimpleModel<EnquiryTypeEnum>? selectedEnquiredFor;

  ToggleEnquiredForEvent(this.selectedEnquiredFor);
}

final class TogglePropertyTypeEvent extends CustomAddDataEvent {
  final ItemSimpleModel<PropertyType> selectedPropertyType;

  TogglePropertyTypeEvent(this.selectedPropertyType);
}

final class ToggleFurnishStatusEvent extends CustomAddDataEvent {
  final ItemSimpleModel<FurnishStatus> selectedFurnishStatus;

  ToggleFurnishStatusEvent(this.selectedFurnishStatus);
}

final class ToggleOfferingTypeEvent extends CustomAddDataEvent {
  final ItemSimpleModel<OfferingType> selectedOfferingType;

  ToggleOfferingTypeEvent(this.selectedOfferingType);
}

final class TogglePropertySubTypeEvent extends CustomAddDataEvent {
  final ItemSimpleModel<String> selectedPropertySubType;

  TogglePropertySubTypeEvent(this.selectedPropertySubType);
}

final class ToggleBedsEvent extends CustomAddDataEvent {
  final ItemSimpleModel<Beds> selectedBeds;

  ToggleBedsEvent(this.selectedBeds);
}

final class ToggleBathsEvent extends CustomAddDataEvent {
  final ItemSimpleModel<int> selectedBaths;

  ToggleBathsEvent(this.selectedBaths);
}

final class ToggleProfessionEvent extends CustomAddDataEvent {
  final ItemSimpleModel<Profession>? selectedProfession;

  ToggleProfessionEvent(this.selectedProfession);
}

final class SelectCarpetAreaEvent extends CustomAddDataEvent {
  final SelectableItem<MasterAreaUnitsModel>? selectedCarpetArea;

  SelectCarpetAreaEvent(this.selectedCarpetArea);
}

final class SelectSaleableAreaEvent extends CustomAddDataEvent {
  final SelectableItem<MasterAreaUnitsModel>? selectedSaleableArea;

  SelectSaleableAreaEvent(this.selectedSaleableArea);
}

final class SelectBuiltUpAreaEvent extends CustomAddDataEvent {
  final SelectableItem<MasterAreaUnitsModel>? selectedBuiltUpArea;

  SelectBuiltUpAreaEvent(this.selectedBuiltUpArea);
}

final class SelectPropertyAreaEvent extends CustomAddDataEvent {
  final SelectableItem<MasterAreaUnitsModel>? selectedPropertyArea;

  SelectPropertyAreaEvent(this.selectedPropertyArea);
}

final class SelectNetAreaEvent extends CustomAddDataEvent {
  final SelectableItem<MasterAreaUnitsModel>? selectedNetArea;

  SelectNetAreaEvent(this.selectedNetArea);
}

final class SelectDataSourceEvent extends CustomAddDataEvent {
  final SelectableItem<String> selectedDataSource;

  SelectDataSourceEvent(this.selectedDataSource);
}

final class SelectDataSubSourceEvent extends CustomAddDataEvent {
  final SelectableItem<String> selectedDataSubSource;

  SelectDataSubSourceEvent(this.selectedDataSubSource);
}

final class SelectAgencyNameEvent extends CustomAddDataEvent {
  final List<SelectableItem<String>> selectedAgencyName;

  SelectAgencyNameEvent(this.selectedAgencyName);
}

final class RemoveCampaignNameEvent extends CustomAddDataEvent {
  final SelectableItem<String> selectedCampaignName;

  RemoveCampaignNameEvent(this.selectedCampaignName);
}

final class SelectCampaignNameEvent extends CustomAddDataEvent {
  final List<SelectableItem<String>> selectedCampaignName;

  SelectCampaignNameEvent(this.selectedCampaignName);
}

final class SelectFloorEvent extends CustomAddDataEvent {
  final List<SelectableItem<String>> selectedFloor;

  SelectFloorEvent(this.selectedFloor);
}

final class RemoveAgencyNameEvent extends CustomAddDataEvent {
  final SelectableItem<String> selectedAgencyName;

  RemoveAgencyNameEvent(this.selectedAgencyName);
}

final class RemoveChannelPartnerNameEvent extends CustomAddDataEvent {
  final SelectableItem<String> selectedChannelPartner;

  RemoveChannelPartnerNameEvent(this.selectedChannelPartner);
}

final class SelectPropertiesEvent extends CustomAddDataEvent {
  final List<SelectableItem<String>> selectedProperties;

  SelectPropertiesEvent(this.selectedProperties);
}

final class RemovePropertyEvent extends CustomAddDataEvent {
  final SelectableItem<String> selectedProperty;

  RemovePropertyEvent(this.selectedProperty);
}

final class SelectProjectsEvent extends CustomAddDataEvent {
  final List<SelectableItem<String>> selectedProjects;

  SelectProjectsEvent(this.selectedProjects);
}

final class RemoveProjectsEvent extends CustomAddDataEvent {
  final SelectableItem<String> selectedProjects;

  RemoveProjectsEvent(this.selectedProjects);
}

final class SelectAssignedUserEvent extends CustomAddDataEvent {
  final SelectableItem<String> selectedUser;

  SelectAssignedUserEvent(this.selectedUser);
}

final class SelectSourcingManagerEvent extends CustomAddDataEvent {
  final SelectableItem<String> selectedSourcingManager;

  SelectSourcingManagerEvent(this.selectedSourcingManager);
}

final class SelectClosingManagerEvent extends CustomAddDataEvent {
  final SelectableItem<String> selectedClosingManager;

  SelectClosingManagerEvent(this.selectedClosingManager);
}

// final class SelectSecondaryUserEvent extends CustomAddDataEvent {
//   final SelectableItem<String> selectedUser;
//
//   SelectSecondaryUserEvent(this.selectedUser);
// }

final class ToggleSubTypesExpandedEvent extends CustomAddDataEvent {}

final class ToggleNoOfBhkExpandedEvent extends CustomAddDataEvent {}

final class ToggleEmailFieldEvent extends CustomAddDataEvent {}

final class ToggleAltPhoneFieldEvent extends CustomAddDataEvent {
  final bool? hideAltPhoneField;
  ToggleAltPhoneFieldEvent({this.hideAltPhoneField});
}

final class ToggleReferralFieldsEvent extends CustomAddDataEvent {}

final class TogglePossessionDateEvent extends CustomAddDataEvent {}

final class SelectPossessionDateEvent extends CustomAddDataEvent {
  final DateTime selectedDate;

  SelectPossessionDateEvent(this.selectedDate);
}

final class AddLocationEvent extends CustomAddDataEvent {
  final AddressModel? location;

  AddLocationEvent(this.location);
}

final class RemoveLocationEvent extends CustomAddDataEvent {
  final ItemSimpleModel<AddressModel> selectedItem;

  RemoveLocationEvent(this.selectedItem);
}

final class CheckDataContactAlreadyExistsEvent extends CustomAddDataEvent {
  final String countryCode;
  final String contactNo;

  CheckDataContactAlreadyExistsEvent(this.countryCode, this.contactNo);
}

final class CheckAltContactAlreadyExistsEvent extends CustomAddDataEvent {
  final String countryCode;
  final String contactNo;

  CheckAltContactAlreadyExistsEvent(this.countryCode, this.contactNo);
}

final class CreateDataEvent extends CustomAddDataEvent {}

final class ResetStateEvent extends CustomAddDataEvent {}

final class OnDataContactChangedEvent extends CustomAddDataEvent {
  final String countryCode;
  final String contactNumber;

  OnDataContactChangedEvent(this.countryCode, this.contactNumber);
}

final class OnAltContactChangedEvent extends CustomAddDataEvent {
  final String countryCode;
  final String contactNumber;

  OnAltContactChangedEvent(this.countryCode, this.contactNumber);
}

final class OnReferralContactChangedEvent extends CustomAddDataEvent {
  final String countryCode;
  final String contactNumber;

  OnReferralContactChangedEvent(this.countryCode, this.contactNumber);
}

final class PickContactEvent extends CustomAddDataEvent {}

final class SelectCurrency extends CustomAddDataEvent {
  final SelectableItem<String> selectedCurrency;

  SelectCurrency(this.selectedCurrency);
}

final class SelectChannelPartnerEvent extends CustomAddDataEvent {
  final List<SelectableItem<String>> selectedChannelPartners;

  SelectChannelPartnerEvent(this.selectedChannelPartners);
}

final class AddCustomerLocationEvent extends CustomAddDataEvent {
  final AddressModel? customerLocation;

  AddCustomerLocationEvent(this.customerLocation);
}

final class RemoveCustomerLocationEvent extends CustomAddDataEvent {
  final ItemSimpleModel<AddressModel>? selectedItem;

  RemoveCustomerLocationEvent(this.selectedItem);
}

final class SelectLeadNationalityEvent extends CustomAddDataEvent {
  final SelectableItem<Country> selectedCountry;

  SelectLeadNationalityEvent(this.selectedCountry);
}

final class SelectPurposeEvent extends CustomAddDataEvent {
  final SelectableItem<PurposeEnum>? selectedPurpose;

  SelectPurposeEvent(this.selectedPurpose);
}
final class AssignedToLoggedInUser extends CustomAddDataEvent {}

final class SelectPossessionType extends CustomAddDataEvent {
  final SelectableItem<PossessionType?>? selectPossessionType;

  SelectPossessionType(this.selectPossessionType);
}

final class OnExecutiveContactChangedEvent extends CustomAddDataEvent {
  final String countryCode;
  final String contactNumber;

  OnExecutiveContactChangedEvent(this.countryCode, this.contactNumber);
}
