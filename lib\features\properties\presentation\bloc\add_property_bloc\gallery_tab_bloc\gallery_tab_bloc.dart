import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/file_type.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/features/properties/data/models/add_property_model.dart';
import 'package:leadrat/features/properties/data/models/property_brochure_model.dart';
import 'package:leadrat/features/properties/domain/usecase/update_property_use_case.dart';
import 'package:leadrat/main.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../../core_main/common/widgets/selectable_item_bottom_sheet.dart';
import '../../../../../../core_main/enums/app_enum/blob_folder_names.dart';
import '../../../../../../core_main/enums/app_enum/page_state_enum.dart';
import '../../../../../../core_main/enums/app_enum/select_file_enum.dart';
import '../../../../../../core_main/utilities/file_picker_util.dart';
import '../../../../../user_profile/domain/usecase/upload_document_usecase.dart';
import '../../../../data/models/property_image_model.dart';
import '../../../../domain/usecase/add_property_use_case.dart';

part 'gallery_tab_event.dart';
part 'gallery_tab_state.dart';

class GalleryTabBloc extends Bloc<GalleryTabEvent, GalleryTabState> {
  AddPropertyModel _addPropertyModel = AddPropertyModel();

  final UploadDocumentUseCase _uploadDocumentUseCase;
  final AddPropertyUseCase _addPropertyUseCase;
  final UpdatePropertyUseCase _updatePropertyUseCase;
  bool isOfficeSpaceSelected = false;
  bool isCoWorkingOfficeSpaceSelected = false;

  List<SelectableItem<File>>? photos = [];
  List<SelectableItem<XFile>>? brochures = [];

  bool get isPropertyListingEnabled => getIt<LeadratHomeBloc>().globalSettings?.shouldEnablePropertyListing ?? false;

  GalleryTabBloc(this._uploadDocumentUseCase, this._addPropertyUseCase, this._updatePropertyUseCase) : super(const GalleryTabState()) {
    on<InitGalleryTabEvent>(_intGalleryTab);
    on<AddPhotosEvent>(_onPhotoUpload);
    on<RemovePhotosEvent>(_onRemovePhotoClicked);
    on<AddBrochuresEvent>(_onBrochureAdded);
    on<RemoveBrochuresEvent>(_onBrochureRemoved);
    on<NavigateBackToAmenitiesEvent>(_onBackButtonPressed);
    on<AddPropertyEvent>(_onAddProperty);
    on<ClearGalleryTabStateEvent>(_clearState);
    on<Add360VideoUrlEvent>(_onAdd360VideoUrl);
    on<AddThirdPartyVideoUrlEvent>(_onAddThirdPartyVideoUrl);
    on<RemoveThirdPartyVideoUrlEvent>(_onRemoveThirdPartyVideoUrl);
    on<Remove360VideoUrlEvent>(_onRemove360VideoUrl);
    on<ToggleThirdPartyEvent>(_onToggleThirdParty);
    on<Toggle360VideoUrlEvent>(_onToggle360VideoUrl);
    on<WaterMarkEvent>(_onWaterMarkEvent);
    on<UpdatePhotoOrderEvent>(_onUpdatePhotoOrder);
  }

  FutureOr<void> _intGalleryTab(InitGalleryTabEvent event, Emitter<GalleryTabState> emit) {
    photos = [];
    brochures = [];
    _addPropertyModel = event.addPropertyModel;
    final video360Urls = _addPropertyModel.view360Url?.map((e) => ItemSimpleModel(title: e, value: e)).toList();
    final thirdPartyUrls = _addPropertyModel.links?.map((e) => ItemSimpleModel(title: e, value: e)).toList();
    emit(state.copyWith(pageState: PageState.initial, photos: [], brochures: [], errorMessage: '', videos: [], degree360VideoUrls: video360Urls, thirdPartyUrls: thirdPartyUrls));

    if (_addPropertyModel.id != null && _addPropertyModel.id != '00000000-0000-0000-0000-000000000000') {
      _addPropertyModel.images?.sort((a, b) {
        final rankA = a.orderRank ?? 0;
        final rankB = b.orderRank ?? 0;
        return rankA.compareTo(rankB);
      });
      if (_addPropertyModel.images?.isNotEmpty ?? false) {
        _addPropertyModel.images?.forEach((image) {
          photos?.add(SelectableItem<File>(title: image.name ?? '', value: File(image.imageFilePath ?? '')));
        });
      }
      _addPropertyModel.brochures?.forEach((brochure) {
        brochures?.add(SelectableItem<XFile>(title: brochure.name ?? '', value: XFile(brochure.url ?? '', name: brochure.name)));
      });
      emit(state.copyWith(pageState: PageState.initial, photos: photos, brochures: brochures, isWaterMarkEnabled: _addPropertyModel.isWaterMarkEnabled));
    }
  }

  FutureOr<void> _onPhotoUpload(AddPhotosEvent event, Emitter<GalleryTabState> emit) async {
    var selectedFiles = await FilePickerUtil.pickFile(event.selectFileEnum, isMultiSelection: true);
    if (selectedFiles == null || selectedFiles.isEmpty) return;
    for (var selectedFile in selectedFiles) {
      if (selectedFile?.path.isNotNullOrEmpty() ?? false) photos?.add(SelectableItem(title: selectedFile?.path ?? "", value: File(selectedFile?.path ?? ''), isEnabled: false)); //is enabled check as a in uploaded to s3 bucket
    }
    emit(state.copyWith(pageState: PageState.initial, photos: photos));
  }

  Future<String?> uploadImageToS3Bucket(XFile? capturedImage, {bool isDocument = false}) async {
    if (capturedImage != null) {
      String? uploadedImage;
      final base64File = base64Encode(await capturedImage.readAsBytes());
      final uploadedImageToS3Bucket = await _uploadDocumentUseCase(UploadDocumentParams(isDocument ? BlobFolderNameEnum.propertyDocument.description : BlobFolderNameEnum.propertyImage.description, capturedImage.path.split('/').last, base64File));
      uploadedImageToS3Bucket.fold(
          (failure) => {
                // show toast
              },
          (res) => {
                uploadedImage = res?.isNotEmpty ?? false ? res : null,
              });
      return uploadedImage;
    } else {
      return null;
    }
  }

  FutureOr<void> _onRemovePhotoClicked(RemovePhotosEvent event, Emitter<GalleryTabState> emit) {
    photos?.remove(photos?.firstWhereOrNull((photo) => photo.value?.path == event.photoPath));
    emit(state.copyWith(pageState: PageState.initial, photos: photos));
  }

  FutureOr<void> _onBackButtonPressed(NavigateBackToAmenitiesEvent event, Emitter<GalleryTabState> emit) {
    emit(state.copyWith(pageState: PageState.failure, addPropertyModel: _addPropertyModel));
  }

  FutureOr<void> _onWaterMarkEvent(WaterMarkEvent event, Emitter<GalleryTabState> emit) {
    emit(state.copyWith(isWaterMarkEnabled: !state.isWaterMarkEnabled));
  }

  FutureOr<void> _onAddProperty(AddPropertyEvent event, Emitter<GalleryTabState> emit) async {
    emit(state.copyWith(pageState: PageState.loading, errorMessage: ''));
    List<PropertyImageModel>? uploadedImages = [];
    List<PropertyBrochureModel>? uploadedBrochures = [];
    //water mark enabled filed
    // _addPropertyModel = _addPropertyModel.copyWith(isWaterMarkEnabled: state.isWaterMarkEnabled);
    if (photos != null && (photos?.isNotEmpty ?? false)) {
      for (var photo in photos!) {
        if (photo.isEnabled) {
          uploadedImages.add(PropertyImageModel(
            name: photo.title,
            galleryType: 1,
            imageKey: "images",
            imageFilePath: photo.value?.path,
            isCoverImage: photos?.first.value?.path == photo.value?.path,
          ));
        } else {
          var uploadedUrl = await uploadImageToS3Bucket(XFile(photo.value?.path ?? ''));
          uploadedImages.add(PropertyImageModel(
            name: photo.title,
            galleryType: 1,
            imageKey: "images",
            imageFilePath: uploadedUrl,
            isCoverImage: photos?.first.value?.path == photo.value?.path,
          ));
        }
      }
      final orderedUploadedImages = uploadedImages.asMap().entries.map(
        (entry) {
          final index = entry.key;
          final image = entry.value;
          return image.copyWith(orderRank: index);
        },
      ).toList();

      _addPropertyModel = _addPropertyModel.copyWith(images: orderedUploadedImages, canMakeImageUrlsNull: true);
    } else {
      _addPropertyModel = _addPropertyModel.copyWith(images: [], canMakeImageUrlsNull: true);
    }

    if (brochures != null && (brochures?.isNotEmpty ?? false)) {
      for (var brochure in brochures!) {
        if (brochure.isEnabled) {
          uploadedBrochures.add(PropertyBrochureModel(name: brochure.title, url: brochure.value?.path));
        } else {
          var uploadedUrl = await uploadImageToS3Bucket(XFile(brochure.value?.path ?? ''));
          uploadedBrochures.add(PropertyBrochureModel(name: brochure.value?.name, url: uploadedUrl));
        }
      }
      _addPropertyModel = _addPropertyModel.copyWith(brochures: uploadedBrochures);
    } else {
      _addPropertyModel = _addPropertyModel.copyWith(brochures: []);
    }

    _addPropertyModel = _addPropertyModel.copyWith(saleType: 1, status: 0, shareCount: 0, isGOListingEnabled: false, goPropertyId: '00000000-0000-0000-0000-000000000000', whatsAppShareCount: 0, callShareCount: 0, emailShareCount: 0, smsShareCount: 0);

    if (_addPropertyModel.id == null) {
      _addPropertyModel = _addPropertyModel.reset(id: '00000000-0000-0000-0000-000000000000', isOfficeSpaceSelected: isOfficeSpaceSelected, isCoWorkingOfficeSpaceSelected: isCoWorkingOfficeSpaceSelected);
      var response = await _addPropertyUseCase.call(_addPropertyModel);
      response.fold(
          (failure) => {
                emit(state.copyWith(pageState: PageState.loading, errorMessage: 'Some error occurred while adding the property! Please try again')),
              },
          (result) => {
                emit(state.copyWith(pageState: PageState.success, addPropertyModel: _addPropertyModel)),
              });
    } else if (_addPropertyModel.id != null && _addPropertyModel.id != '00000000-0000-0000-0000-000000000000') {
      _addPropertyModel = _addPropertyModel.reset(isOfficeSpaceSelected: isOfficeSpaceSelected, isCoWorkingOfficeSpaceSelected: isCoWorkingOfficeSpaceSelected);
      var response = await _updatePropertyUseCase.call(_addPropertyModel);
      response.fold(
          (failure) => {
                emit(state.copyWith(pageState: PageState.loading, errorMessage: 'Some error occurred while updating the property! Please try again')),
              },
          (result) => {
                emit(state.copyWith(pageState: PageState.success, addPropertyModel: _addPropertyModel)),
              });
    }
  }

  FutureOr<void> _onBrochureAdded(AddBrochuresEvent event, Emitter<GalleryTabState> emit) async {
    var selectedFiles = await FilePickerUtil.pickFile(event.selectFileEnum, fileType: FileType.pdf, isMultiSelection: true);
    if (selectedFiles == null) return;
    for (var selectedFile in selectedFiles) {
      brochures?.add(SelectableItem(title: selectedFile?.name ?? '', isEnabled: false, value: selectedFile));
    }
    emit(state.copyWith(pageState: PageState.initial, brochures: brochures));
  }

  FutureOr<void> _onBrochureRemoved(RemoveBrochuresEvent event, Emitter<GalleryTabState> emit) {
    brochures?.remove(brochures?.firstWhereOrNull((brochure) => brochure.value?.path == event.brochurePath));
    emit(state.copyWith(pageState: PageState.initial, brochures: brochures));
  }

  FutureOr<void> _clearState(ClearGalleryTabStateEvent event, Emitter<GalleryTabState> emit) {
    emit(state.clearState());
  }

  FutureOr<void> _onAdd360VideoUrl(Add360VideoUrlEvent event, Emitter<GalleryTabState> emit) async {
    try {
      if (event.videoUrl.isNullOrEmpty()) {
        LeadratCustomSnackbar.show(message: "Please enter a URL", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      } else if (await canLaunchUrl(Uri.parse(event.videoUrl))) {
        emit(state.copyWith(degree360VideoUrls: [ItemSimpleModel(title: event.videoUrl, value: event.videoUrl), ...state.degree360VideoUrls]));
        final video360Urls = (_addPropertyModel.view360Url ?? []) + [event.videoUrl];
        _addPropertyModel = _addPropertyModel.copyWith(view360Url: video360Urls);
      } else {
        LeadratCustomSnackbar.show(message: "Invalid URL, please enter valid URL", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      }
    } catch (ex) {
      LeadratCustomSnackbar.show(message: "Invalid URL, please enter valid URL", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
    }
  }

  FutureOr<void> _onAddThirdPartyVideoUrl(AddThirdPartyVideoUrlEvent event, Emitter<GalleryTabState> emit) async {
    try {
      if (event.thirdPartyUrl.isNullOrEmpty()) {
        LeadratCustomSnackbar.show(message: "Please enter a URL", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      } else if (await canLaunchUrl(Uri.parse(event.thirdPartyUrl))) {
        emit(state.copyWith(thirdPartyUrls: [ItemSimpleModel(title: event.thirdPartyUrl, value: event.thirdPartyUrl), ...state.thirdPartyUrls]));
        final links = (_addPropertyModel.links ?? []) + [event.thirdPartyUrl];
        _addPropertyModel = _addPropertyModel.copyWith(links: links);
      } else {
        LeadratCustomSnackbar.show(message: "Invalid URL, please enter valid URL", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      }
    } catch (ex) {
      LeadratCustomSnackbar.show(message: "Invalid URL, please enter valid URL", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
    }
  }

  FutureOr<void> _onRemoveThirdPartyVideoUrl(RemoveThirdPartyVideoUrlEvent event, Emitter<GalleryTabState> emit) {
    final updatedThirdPartyUrls = state.thirdPartyUrls.whereNot((element) => element.value == event.item.value).toList();
    emit(state.copyWith(thirdPartyUrls: updatedThirdPartyUrls));
    final updatedLinks = _addPropertyModel.links?.whereNot((element) => element == event.item.value).toList();
    _addPropertyModel.copyWith(links: updatedLinks);
  }

  FutureOr<void> _onRemove360VideoUrl(Remove360VideoUrlEvent event, Emitter<GalleryTabState> emit) {
    final updated360VideoUrls = state.degree360VideoUrls.whereNot((element) => element.value == event.item.value).toList();
    emit(state.copyWith(degree360VideoUrls: updated360VideoUrls));
    final video360Urls = _addPropertyModel.view360Url?.whereNot((element) => element == event.item.value).toList();
    _addPropertyModel.copyWith(view360Url: video360Urls);
  }

  FutureOr<void> _onToggleThirdParty(ToggleThirdPartyEvent event, Emitter<GalleryTabState> emit) async {
    try {
      if (event.item.value == null) return;
      await launchUrl(Uri.parse(event.item.value!));
    } catch (ex) {
      LeadratCustomSnackbar.show(message: "Invalid third party URL", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
    }
  }

  FutureOr<void> _onToggle360VideoUrl(Toggle360VideoUrlEvent event, Emitter<GalleryTabState> emit) async {
    try {
      if (event.item.value == null) return;
      await launchUrl(Uri.parse(event.item.value!));
    } catch (ex) {
      LeadratCustomSnackbar.show(message: "Invalid 360 degree video URL", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
    }
  }

  FutureOr<void> _onUpdatePhotoOrder(UpdatePhotoOrderEvent event, Emitter<GalleryTabState> emit) async {
    emit(state.copyWith(photos: event.photos));

    photos = event.photos;

    final orderedUploadedImages = state.photos?.asMap().entries.map((entry) {
      final index = entry.key;
      final photo = entry.value;
      return PropertyImageModel(name: photo.title, galleryType: 1, imageKey: "images", imageFilePath: photo.value?.path, isCoverImage: event.photos.first.value?.path == photo.value?.path, orderRank: index);
    }).toList();

    _addPropertyModel = _addPropertyModel.copyWith(images: orderedUploadedImages, canMakeImageUrlsNull: true);
  }
}
