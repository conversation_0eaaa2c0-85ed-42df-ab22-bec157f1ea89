import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/usecase/use_case.dart';
import 'package:leadrat/core_main/common/constants/constants.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/data_source/local/custom_amenities_and_attributes_local_data_source.dart';
import 'package:leadrat/core_main/common/data/custom_amenities_and_attributes/models/custom_attributes_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_area_unit_model.dart';
import 'package:leadrat/core_main/common/data/master_data/models/master_property_type_model.dart';
import 'package:leadrat/core_main/common/data/master_data/repository/masterdata_repository.dart';
import 'package:leadrat/core_main/common/models/copy_withvalue_model.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/taxation_mode.dart';
import 'package:leadrat/core_main/enums/leads/enquiry_type.dart';
import 'package:leadrat/core_main/enums/property_enums/facing.dart';
import 'package:leadrat/core_main/enums/property_enums/furnish_status.dart';
import 'package:leadrat/core_main/enums/property_enums/lockin_period_type.dart';
import 'package:leadrat/core_main/enums/property_enums/notice_period_type.dart';
import 'package:leadrat/core_main/enums/property_enums/payment_frequency.dart';
import 'package:leadrat/core_main/enums/property_enums/security_deposite_type.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/utilities/date_time_utils.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/features/lead/domain/usecase/get_project_name_with_id_use_case.dart';
import 'package:leadrat/features/properties/data/models/property_attribute_model.dart';
import 'package:leadrat/features/properties/data/models/view_listing_source_address_model.dart';
import 'package:leadrat/features/properties/domain/usecase/get_all_listing_site_addresses_usecase.dart';
import 'package:leadrat/features/properties/presentation/bloc/add_property_bloc/basic_info_bloc/basic_info_bloc.dart';
import 'package:leadrat/features/properties/presentation/bloc/add_property_bloc/gallery_tab_bloc/gallery_tab_bloc.dart';
import 'package:leadrat/features/properties/presentation/items/item_listing_source_address_model.dart';
import 'package:leadrat/features/properties/presentation/items/item_property_info_tab.dart';
import 'package:leadrat/main.dart';

import '../../../../../../core_main/common/data/global_settings/models/global_setting_model.dart';
import '../../../../../../core_main/common/data/global_settings/repository/global_setting_repository.dart';
import '../../../../../../core_main/common/models/address_model.dart';
import '../../../../../../core_main/common/models/booked_details_model.dart';
import '../../../../../../core_main/common/widgets/selectable_item_bottom_sheet.dart';
import '../../../../../../core_main/enums/app_enum/page_state_enum.dart';
import '../../../../../../core_main/enums/common/brokerage_unit.dart';
import '../../../../data/models/add_property_model.dart';

part 'property_info_tab_event.dart';
part 'property_info_tab_state.dart';

class PropertyInfoTabBloc extends Bloc<PropertyInfoTabEvent, PropertyInfoTabState> {
  AddPropertyModel _addPropertyModel = AddPropertyModel();
  List<OwnerDetailsStorage>? propertyOwnerDetails = [];
  late PropertyMonetaryInfoModel _propertyMonetaryInfo;
  GlobalSettingModel? _globalSettingModel;
  PropertyOwnerDetailsModel _propertyOwnerDetailsModel = PropertyOwnerDetailsModel();

  TextEditingController? totalPriceController = TextEditingController();
  TextEditingController? maintennaceCostController = TextEditingController();
  TextEditingController? brokerageController = TextEditingController();
  TextEditingController? notesController = TextEditingController();
  TextEditingController? ownerNameController = TextEditingController();
  TextEditingController? ownerNumberController = TextEditingController();
  TextEditingController? ownerEmailController = TextEditingController();
  TextEditingController? depositAndSecurityController;

  TextEditingController? escalationController;

  TextEditingController? pocNameController;

  TextEditingController? pocNumberController;

  TextEditingController? pocDesignationController;
  TextEditingController? commonAreaSizeController;
  TextEditingController? coWorkingOperatorName;
  TextEditingController? coWorkingOperatorNumber;
  TextEditingController? serviceChargesController;
  TextEditingController? permitNumberController;

  final GlobalSettingRepository _globalSettingsRepository;
  final MasterDataRepository _masterDataRepository;

  // final ProjectRepository _projectRepository;
  final GetProjectNameWithIdUseCase _getProjectNameWithIdUseCase;
  final GetAllListingSourceAndSiteAddressesUseCase _getAllListingSourceAndSiteAddressesUseCase;
  MasterPropertyTypeModel? selectedPropertyType;

  bool get isPropertyListingEnabled => getIt<LeadratHomeBloc>().globalSettings?.shouldEnablePropertyListing ?? false;

  bool get isRentOrLeaseSelected => (getIt<BasicInfoBloc>().isRentOrLeaseSelected ?? false);

  Map<int, String> tempListingAddressMap = {};

  PropertyInfoTabBloc(
    this._globalSettingsRepository,
    this._getProjectNameWithIdUseCase,
    this._masterDataRepository,
    this._getAllListingSourceAndSiteAddressesUseCase,
  ) : super(const PropertyInfoTabState()) {
    on<InitPropertyInfoTabEvent>(_initPropertyInfoTab);
    on<SelectPossessionDateEvent>(_onSelectPossessionDate);
    on<SelectCurrencyEvent>(_onCurrencySelected);
    on<GetProjectsEvent>(_onGetProjectsEvent);
    on<ToggleIsNegotiableEvent>(_onIsNegotiableSelected);
    on<SelectProjectEvent>(_onProjectSelected);
    on<SelectBrokerageUnit>(_onBrokerageUnitSelected);
    on<SelectLocationEvent>(_onLocationSelected);
    on<NavigateBackToBasicInfoTabEvent>(_onBackPressed);
    on<NavigateToAttributeTabEvent>(_onContinuePressed);
    on<ClearPropertyInfoTabStateEvent>(_clearState);
    on<ClearProjectEvent>(_clearProject);
    on<SelectLockInPeriodEvent>(_onSelectLockInPeriodEvent);
    on<SelectNoticePeriodEvent>(_onSelectNoticePeriodEvent);
    on<SelectSecurityDepositEvent>(_onSelectSecurityDepositEvent);
    on<SelectCommonAreaUnitsEvent>(_onSelectCommonAreaUnitsEvent);
    on<InitListingSourceAndAddresses>(_onInitListingSourceAndAddresses);
    on<SelectListingSiteAddress>(_onSelectListingSiteAddress);
    on<SelectListingSourceEvent>(_onSelectListingSource);
    on<ChangePermitValueEvent>(_onChangePermitValue);
    on<SelectPaymentFrequency>(_onSelectPaymentFrequency);
    on<SelectNoOfChequeEvent>(_onSelectedNoOfCheque);
    on<RemoveNewListingSourceAndAddressEvent>(_onRemoveNewListingSourceAndAddress);
    on<AddNewListingSourceAndAddressEvent>(_onAddNewListingSourceAndAddress);
    on<ChangeTaxationModeEvent>(_onChangeTaxationMode);
    on<OnBuilderContactChangedEvent>(_onBuilderContactChanged);
    on<SelectPossessionType>(_onSelectPossessionType);
    on<ToggleSecurityAmount>(_onToggleSecurityAmount);
    on<SelectDepositCurrencyEvent>(_onSelectDepositCurrencyEvent);
    on<ToggleRatingsEvent>(_onToggleRatingsEvent);
    on<InitAttributesEvent>(_onInitAttributesEvent);
    on<AttachOrDeAttachCustomAttributeEvent>(_onAttachOrDeAttachCustomAttributeEvent);
    on<ToggleFacingEvent>(_onToggleFacingEvent);
    on<ToggleFurnishStatusEvent>(_onToggleFurnishStatusEvent);
    on<AddContactEvent>(_onAddContactEvent);
    on<RemoveContactEvent>(_onRemoveContactEvent);
    on<OwnerContactChangedEvent>(_onOwnerContactChangedEvent);
    on<CollapseOwnerContactEvent>(_onCollapseOwnerContactEvent);
    on<SelectNumberOfFloorsOccupiedEvent>(_onSelectNumberOfFloorsOccupiedEvent);
    on<UpdateOfficeAndCoWorkingSpaceSelected>(_onUpdateOfficeAndCoWorkingSpaceSelected);
  }

  void initTextController() {
    totalPriceController = TextEditingController();
    maintennaceCostController = TextEditingController();
    brokerageController = TextEditingController();
    notesController = TextEditingController();
    ownerNameController = TextEditingController();
    ownerNumberController = TextEditingController();
    ownerEmailController = TextEditingController();
    depositAndSecurityController = TextEditingController();
    escalationController = TextEditingController();
    pocNameController = TextEditingController();
    pocNumberController = TextEditingController();
    pocDesignationController = TextEditingController();
    commonAreaSizeController = TextEditingController();
    coWorkingOperatorName = TextEditingController();
    coWorkingOperatorNumber = TextEditingController();
    serviceChargesController = TextEditingController();
    permitNumberController = TextEditingController();
  }

  void disposeTextController() {
    totalPriceController?.dispose();
    maintennaceCostController?.dispose();
    brokerageController?.dispose();
    notesController?.dispose();
    ownerNameController?.dispose();
    ownerNumberController?.dispose();
    ownerEmailController?.dispose();
    depositAndSecurityController?.dispose();
    escalationController?.dispose();
    pocNameController?.dispose();
    pocNumberController?.dispose();
    pocDesignationController?.dispose();
    commonAreaSizeController?.dispose();
    coWorkingOperatorNumber?.dispose();
    coWorkingOperatorName?.dispose();
    serviceChargesController?.dispose();
    permitNumberController?.dispose();
  }

  void _initData() {
    totalPriceController?.text = _addPropertyModel.monetaryInfo?.expectedPrice?.toString() ?? '';
    maintennaceCostController?.text = _addPropertyModel.monetaryInfo?.maintenanceCost?.toString() ?? '';
    brokerageController?.text = _addPropertyModel.monetaryInfo?.brokerage?.toString() ?? '';
    notesController?.text = _addPropertyModel.notes ?? '';
    ownerNameController?.text = _addPropertyModel.ownerDetails?.name ?? '';
    ownerNumberController?.text = _addPropertyModel.ownerDetails?.phone ?? '';
    ownerEmailController?.text = _addPropertyModel.ownerDetails?.email ?? '';
    depositAndSecurityController?.text = _addPropertyModel.securityDepositAmount?.toString() ?? '';
    escalationController?.text = _addPropertyModel.monetaryInfo?.escalationPercentage?.toString() ?? '';
    var poc = _addPropertyModel.tenantContactInfo;
    pocNameController?.text = poc?.name?.toString() ?? '';
    pocNumberController?.text = poc?.phone?.toString() ?? '';
    pocDesignationController?.text = poc?.designation?.toString() ?? '';
    coWorkingOperatorName?.text = _addPropertyModel.coWorkingOperatorName?.toString() ?? '';
    coWorkingOperatorNumber?.text = _addPropertyModel.coWorkingOperatorPhone?.toString() ?? '';
    commonAreaSizeController?.text = _addPropertyModel.dimension?.commonAreaCharges?.toString() ?? '';
    serviceChargesController?.text = _addPropertyModel.monetaryInfo?.serviceChange?.toString().replaceAll(".0", "") ?? '';
    permitNumberController?.text = _addPropertyModel.dtcmPermit ?? _addPropertyModel.dldPermitNumber ?? '';
    if (_addPropertyModel.dtcmPermit.isNotNullOrEmpty()) {
      add(ChangePermitValueEvent("DTMC"));
    } else if (_addPropertyModel.dldPermitNumber.isNotNullOrEmpty()) {
      add(ChangePermitValueEvent("DLD"));
    }
    if (_addPropertyModel.securityDepositUnit == null && state.depositCurrency == null) {
      add(
        SelectDepositCurrencyEvent(
          state.currencies?.firstWhereOrNull(
            (currency) => currency.title == (state.globalSettings?.countries?.firstOrNull?.defaultCurrency ?? 'INR'),
          ),
        ),
      );
    }

    if (_addPropertyModel.securityDepositUnit == '%') {
      add(ToggleSecurityAmount(true));
    } else {
      add(SelectDepositCurrencyEvent(state.currencies?.firstWhereOrNull((currency) => currency.title == _addPropertyModel.securityDepositUnit)));
    }

    add(SelectCurrencyEvent(state.currencies?.firstWhereOrNull((currency) => currency.title == _addPropertyModel.monetaryInfo?.currency)));
    add(SelectProjectEvent(state.projects?.firstWhereOrNull((project) => project.title == _addPropertyModel.project)));
    add(SelectBrokerageUnit(state.brokerageUnits?.firstWhereOrNull((brokerageUnit) => brokerageUnit.title == _addPropertyModel.monetaryInfo?.brokerageCurrency)));
    add(SelectLocationEvent(_addPropertyModel.address));
    final selectedPaymentFrequency = state.selectablePaymentFrequency.firstWhereOrNull((element) => element.value == _addPropertyModel.monetaryInfo?.paymentFrequency);
    if (selectedPaymentFrequency != null) add(SelectPaymentFrequency(selectedPaymentFrequency));
  }

  FutureOr<void> _initPropertyInfoTab(InitPropertyInfoTabEvent event, Emitter<PropertyInfoTabState> emit) async {
    _addPropertyModel = event.addPropertyModel;
    _propertyMonetaryInfo = _addPropertyModel.monetaryInfo ?? PropertyMonetaryInfoModel();
    add(InitListingSourceAndAddresses());
    _initPossessionType(emit);

    _globalSettingModel = await _globalSettingsRepository.getGlobalSettings();
    List<SelectableItem<String>>? allCurrencies = [];
    _globalSettingModel?.countries?.firstOrNull?.currencies?.forEach((currency) {
      allCurrencies.add(SelectableItem(title: currency.currency ?? '', value: currency.currency ?? ''));
    });
    final defaultDialCode = _globalSettingModel?.countries?.firstOrNull?.defaultCallingCode;

    var selectableNoticePeriodTypes = NoticePeriodType.values.where((element) => element != NoticePeriodType.none).map((e) => SelectableItem(title: e.description, value: e)).toList();
    var selectableLockInPeriodType = LockInPeriodType.values.where((element) => element != LockInPeriodType.none).map((e) => SelectableItem(title: e.description, value: e)).toList();
    var selectablePaymentFrequency = PaymentFrequency.values.whereNot((element) => element == PaymentFrequency.none).map((e) => SelectableItem<PaymentFrequency>(title: e.description, value: e)).toList();
    var noOfCheques = List.generate(12, (index) => SelectableItem<int>(title: "${index + 1}", value: index + 1));
    emit(state.copyWith(
      pageState: PageState.initial,
      currencies: allCurrencies,
      globalSettings: _globalSettingModel,
      brokerageUnits: _getBrokerageUnits(),
      selectedEnquiredType: EnquiryType.values.where((e) => e.value == _addPropertyModel.enquiredFor).firstOrNull,
      selectedPropertySubType: _addPropertyModel.propertyTypeId,
      selectableNoticePeriodType: selectableNoticePeriodTypes,
      selectableLockInPeriodType: selectableLockInPeriodType,
      selectablePaymentFrequency: selectablePaymentFrequency,
      defaultCountryCode: (_globalSettingModel?.hasInternationalSupport ?? false)
          ? defaultDialCode
          : isPropertyListingEnabled
              ? "+971"
              : "+91",
      numberOfCheques: noOfCheques,
      selectedNumberOfCheque: noOfCheques.firstWhereOrNull((element) => element.value == _addPropertyModel.monetaryInfo?.noOfChequesAllowed),
      taxationMode: _addPropertyModel.taxationMode,
    ));
    await _addAreaUnits(emit);
    initOwnerDetails(emit);
    add(InitAttributesEvent());
    add(UpdateOfficeAndCoWorkingSpaceSelected());
    if (_addPropertyModel.id != null && _addPropertyModel.id != '00000000-0000-0000-0000-000000000000') {
      _initData();
      emit(state.copyWith(
        isNegotiable: _addPropertyModel.monetaryInfo?.isNegotiable,
      ));
    }
  }

  void _initPossessionType(Emitter<PropertyInfoTabState> emit) {
    PossessionType? initialPossessionType = _addPropertyModel.possesionType;
    if (_addPropertyModel.possessionDate != null) initialPossessionType ??= PossessionType.customDate;
    List<SelectableItem<PossessionType?>> possessionTypeItem = PossessionType.values.where((e) => e.description != 'None').map((e) => SelectableItem<PossessionType?>(title: e.description, value: e)).toList();
    emit(state.copyWith(possessionTypeSelectableItems: possessionTypeItem));
    if (possessionTypeItem.isNotEmpty) add(SelectPossessionType(possessionTypeItem.firstWhereOrNull((element) => element.value == initialPossessionType)));
    if (initialPossessionType == PossessionType.customDate) add(SelectPossessionDateEvent(_addPropertyModel.possessionDate ?? DateTime.now()));
  }

  FutureOr<void> _onGetProjectsEvent(GetProjectsEvent event, Emitter<PropertyInfoTabState> emit) async {
    try {
      var response = await _getProjectNameWithIdUseCase(NoParams());
      List<SelectableItem<String>>? projectsList = [];
      response.fold((failure) => {}, (result) {
        result?.forEach((project) {
          if (project.name.isNotNullOrEmpty()) {
            projectsList.add(SelectableItem(title: project.name?.trim() ?? '', value: project.id ?? ''));
          }
        });
        projectsList.sort((a, b) => a.title.compareTo(b.title));
        emit(state.copyWith(pageState: PageState.initial, projects: projectsList));
        if (_addPropertyModel.project != null) {
          add(SelectProjectEvent(state.projects?.firstWhereOrNull((project) => project.title == _addPropertyModel.project)));
        }
      });
    } catch (ex, stackTrace) {
      ex.logException(stackTrace);
    }
  }

  List<SelectableItem<String>> _getBrokerageUnits() {
    List<SelectableItem<String>> brokerageUnits = [];
    BrokerageUnit.values.map((e) => e).toList().forEach((brokerageUnit) {
      if (brokerageUnit.description == 'INR') {
        String defaultCurrency = (isPropertyListingEnabled ? _globalSettingModel?.countries?.firstOrNull?.defaultCurrency ?? "AED" : _globalSettingModel?.countries?.firstOrNull?.defaultCurrency ?? "INR");
        brokerageUnits.add(SelectableItem(title: state.currency ?? defaultCurrency, value: state.currency ?? defaultCurrency));
      } else {
        brokerageUnits.add(SelectableItem(title: brokerageUnit.description, value: brokerageUnit.description));
      }
    });
    brokerageUnits.removeWhere((element) => element.value == BrokerageUnit.none.description);
    return brokerageUnits;
  }

  FutureOr<void> _onSelectPossessionDate(SelectPossessionDateEvent event, Emitter<PropertyInfoTabState> emit) {
    emit(state.copyWith(pageState: PageState.initial, possessionAvailablity: event.selectedDate.toUserTimeZone(), isPossessionDateCustomSelected: true));
    _addPropertyModel = _addPropertyModel.copyWith(
      possessionDate: event.selectedDate.getBasedOnTimeZone().toUtcFormat(),
    );
  }

  FutureOr<void> _onCurrencySelected(SelectCurrencyEvent event, Emitter<PropertyInfoTabState> emit) {
    emit(state.copyWith(pageState: PageState.initial, currency: event.currency?.value));
    _propertyMonetaryInfo = _propertyMonetaryInfo.copyWith(currency: event.currency?.value);
    _addPropertyModel = _addPropertyModel.copyWith(monetaryInfo: _propertyMonetaryInfo);
  }

  Future<void> _onUpdateOfficeAndCoWorkingSpaceSelected(UpdateOfficeAndCoWorkingSpaceSelected event, Emitter<PropertyInfoTabState> emit) async {
    final masterPropertyTypes = await _masterDataRepository.getCustomPropertyTypes(isPropertyListingEnabled: isPropertyListingEnabled);
    selectedPropertyType = masterPropertyTypes!
        .where((type) => type.childTypes != null)
        .expand((type) => type.childTypes!)
        .where(
          (subType) => subType.id == _addPropertyModel.propertyTypeId,
        )
        .firstOrNull;
    final isOfficeSpaceSelected = state.selectedEnquiredType == EnquiryType.rent && (selectedPropertyType?.displayName?.toLowerCase().replaceAll(' ', '') == 'officespace');
    final isCoWorkingOfficeSpaceSelected = state.selectedEnquiredType == EnquiryType.rent && (selectedPropertyType?.displayName?.toLowerCase().replaceAll(' ', '') == 'coworkingofficespace');
    getIt<GalleryTabBloc>().isOfficeSpaceSelected = isOfficeSpaceSelected;
    getIt<GalleryTabBloc>().isCoWorkingOfficeSpaceSelected = isCoWorkingOfficeSpaceSelected;
    emit(state.copyWith(isOfficeSpaceSelected: isOfficeSpaceSelected, isCoWorkingOfficeSpaceSelected: isCoWorkingOfficeSpaceSelected));
  }

  FutureOr<void> _onIsNegotiableSelected(ToggleIsNegotiableEvent event, Emitter<PropertyInfoTabState> emit) {
    emit(state.copyWith(pageState: PageState.initial, isNegotiable: event.isNegotiable ?? false));
    _propertyMonetaryInfo = _propertyMonetaryInfo.copyWith(isNegotiable: event.isNegotiable ?? false);
    _addPropertyModel = _addPropertyModel.copyWith(monetaryInfo: _propertyMonetaryInfo);
  }

  FutureOr<void> _onProjectSelected(SelectProjectEvent event, Emitter<PropertyInfoTabState> emit) {
    emit(state.copyWith(pageState: PageState.initial, selectedProject: event.project));
    _addPropertyModel = _addPropertyModel.copyWith(project: event.project?.title);
  }

  FutureOr<void> _onBrokerageUnitSelected(SelectBrokerageUnit event, Emitter<PropertyInfoTabState> emit) {
    emit(state.copyWith(pageState: PageState.initial, brokerageUnit: event.brokerageUnit));
    _propertyMonetaryInfo = _propertyMonetaryInfo.copyWith(brokerageCurrency: event.brokerageUnit?.title);
    String defaultCurrency = (isPropertyListingEnabled ? "AED" : _globalSettingModel?.countries?.firstOrNull?.defaultCurrency ?? "INR");
    // String? selectedBrokerageUnit = BrokerageUnit.values.map((e) => e).toList().firstWhereOrNull((brokerageUnit) => brokerageUnit.description == event.brokerageUnit?.title)?.description;
    String? selectedBrokerageUnit = event.brokerageUnit?.value;
    selectedBrokerageUnit ??= defaultCurrency == event.brokerageUnit?.title ? defaultCurrency : null;
    _propertyMonetaryInfo = _propertyMonetaryInfo.copyWith(brokerageCurrency: selectedBrokerageUnit);
    _addPropertyModel = _addPropertyModel.copyWith(monetaryInfo: _propertyMonetaryInfo);
  }

  FutureOr<void> _onLocationSelected(SelectLocationEvent event, Emitter<PropertyInfoTabState> emit) {
    emit(state.copyWith(pageState: PageState.initial, location: event.location));
    _addPropertyModel = _addPropertyModel.copyWith(address: event.location, placeId: CopyWithValue(value: null, canUpdateValue: true));
  }

  FutureOr<void> _onBackPressed(NavigateBackToBasicInfoTabEvent event, Emitter<PropertyInfoTabState> emit) {
    emit(state.copyWith(pageState: PageState.loading, addPropertyModel: _addPropertyModel));
  }

  FutureOr<void> _onContinuePressed(NavigateToAttributeTabEvent event, Emitter<PropertyInfoTabState> emit) {
    final selectedCurrency = state.currency ?? (isPropertyListingEnabled ? "AED" : _globalSettingModel?.countries?.firstOrNull?.defaultCurrency ?? "INR");
    _propertyMonetaryInfo = _propertyMonetaryInfo.copyWith(
        id: '00000000-0000-0000-0000-000000000000',
        serviceChange: double.tryParse(serviceChargesController?.text ?? ''),
        expectedPrice: int.tryParse(totalPriceController?.text ?? ''),
        maintenanceCost: int.tryParse(maintennaceCostController?.text ?? ''),
        brokerage: double.tryParse(brokerageController?.text ?? ''),
        isNegotiable: state.isNegotiable ?? false,
        currency: selectedCurrency,
        brokerageUnit: _addPropertyModel.monetaryInfo?.brokerageUnit ?? BrokerageUnit.none,
        brokerageCurrency: _addPropertyModel.monetaryInfo?.brokerageCurrency ?? 'INR',

        //this is for office subType ......
        escalationPercentage: state.selectedEnquiredType == EnquiryType.rent && (state.selectedPropertySubType == 'dd952882-ea9c-4416-a8f3-e2167f333906' || state.selectedPropertySubType == '12b936e3-df25-44bc-bcdb-9f6d3c3ccb84')
            ? escalationController != null && escalationController!.text.isNotEmpty
                ? int.parse(escalationController!.text.toString())
                : null
            : null);

    if (isRentOrLeaseSelected && state.selectedPaymentFrequency == null) {
      LeadratCustomSnackbar.show(message: "Please select the payment frequency", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
      return null;
    }
    if (state.isSecurityAmountPercentageSelected && (double.tryParse(depositAndSecurityController?.text ?? '') ?? 0) > 99) {
      LeadratCustomSnackbar.show(message: "The security amount percentage must be less than 100", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
      return null;
    }
    if (depositAndSecurityController != null && depositAndSecurityController!.text.isNotNullOrEmpty() && double.tryParse(depositAndSecurityController?.text ?? '') == null) {
      LeadratCustomSnackbar.show(message: "Enter correct security deposit amount", type: SnackbarType.error, navigatorKey: MyApp.navigatorKey);
      return null;
    }

    if (commonAreaSizeController?.text != null && commonAreaSizeController!.text.isNotEmpty && (state.selectedCommonAreaUnits == null)) {
      emit(state.copyWith(pageState: PageState.error, errorMessage: 'common area size units field required'));
      return null;
    }

    if ((state.isOfficeSpaceSelected ?? false) || (state.isCoWorkingOfficeSpaceSelected ?? false)) {
      _addPropertyModel = _addPropertyModel.copyWith(
          lockInPeriod: state.selectedLockInPeriodType?.value,
          noticePeriod: state.selectedNoticePeriodType?.value,
          dimension: _addPropertyModel.dimension?.copyWith(commonAreaCharges: commonAreaSizeController?.text != null && commonAreaSizeController!.text.isNotEmpty ? int.parse(commonAreaSizeController!.text) : null, commonAreaChargesId: state.selectedCommonAreaUnits?.value?.id),
          tenantContactInfo: TenantContactInfo(
            name: pocNameController?.text,
            phone: pocNumberController?.text,
            designation: (state.isOfficeSpaceSelected ?? false) ? pocDesignationController?.text : null,
          ));
    }
    if (ownerEmailController?.text != null && ownerEmailController!.text.isNotEmpty && (!isValidEmail(ownerEmailController!.text))) {
      emit(state.copyWith(pageState: PageState.error, errorMessage: 'Enter valid ownerEmail'));
      return null;
    }
    _propertyOwnerDetailsModel = _propertyOwnerDetailsModel.copyWith(
      id: '00000000-0000-0000-0000-000000000000',
      name: ownerNameController?.text,
      phone: state.builderContactNumber,
      email: ownerEmailController?.text,
    );
    _addPropertyModel = _addPropertyModel.copyWith(
      monetaryInfo: _propertyMonetaryInfo,
      notes: notesController?.text,
      ownerDetails: _propertyOwnerDetailsModel,
      coWorkingOperatorPhone: (state.isCoWorkingOfficeSpaceSelected ?? false) ? coWorkingOperatorNumber?.text : null,
      coWorkingOperatorName: (state.isCoWorkingOfficeSpaceSelected ?? false) ? coWorkingOperatorName?.text : null,
      dtcmPermit: state.selectedPermitValue == "DTMC" ? (permitNumberController?.text ?? '') : null,
      dldPermitNumber: state.selectedPermitValue == "DLD" ? (permitNumberController?.text ?? '') : null,
      taxationMode: isPropertyListingEnabled ? null : state.taxationMode,
      securityDepositAmount: double.tryParse(depositAndSecurityController?.text ?? ''),
    );

    if (state.possessionTypeSelectedItem?.value == PossessionType.customDate && (!isPropertyListingEnabled)) {
      if ((_addPropertyModel.possessionDate == null)) {
        if (state.possessionAvailablity != null) {
          _addPropertyModel = _addPropertyModel.copyWith(possessionDate: state.possessionAvailablity?.getBasedOnTimeZone().toUtcFormat());
        } else {
          emit(state.copyWith(pageState: PageState.error, errorMessage: 'kindly enter the possession availability date'));
          return null;
        }
      }
    }

    if (state.possessionTypeSelectedItem?.value == PossessionType.customDate) {
      if (state.possessionAvailablity != null) {
        _addPropertyModel = _addPropertyModel.copyWith(possessionDate: state.possessionAvailablity?.getBasedOnTimeZone().toUtcFormat());
      } else {
        emit(state.copyWith(pageState: PageState.error, errorMessage: 'kindly enter the possession availability date'));
        return null;
      }
    }

    if (totalPriceController == null || totalPriceController!.text.isEmpty) {
      emit(state.copyWith(pageState: PageState.error, errorMessage: isRentOrLeaseSelected ? 'kindly enter the Rent Amount' : 'kindly enter the total price'));
      return null;
    }

    if (_addPropertyModel.address == null && !isPropertyListingEnabled) {
      if (state.location != null) {
        _addPropertyModel = _addPropertyModel.copyWith(address: state.location);
      } else {
        emit(state.copyWith(pageState: PageState.error, errorMessage: 'kindly enter the location'));
        return null;
      }
    }

    if (brokerageController?.text != '') {
      if (state.brokerageUnit == null || state.brokerageUnit?.title == 'Select') {
        emit(state.copyWith(pageState: PageState.error, errorMessage: 'kindly select the brokerage unit'));
        return null;
      }

      if (state.brokerageUnit?.title == BrokerageUnit.percentage.description) {
        if ((double.tryParse(brokerageController?.text ?? '') ?? 0) > 100) {
          emit(state.copyWith(pageState: PageState.error, errorMessage: 'Brokerage cannot be more than 100 percent'));
          return null;
        }
      }
    }
    if ((maintennaceCostController?.text.isNotEmpty ?? false) && (totalPriceController?.text.isNotEmpty ?? false)) {
      if ((int.tryParse(maintennaceCostController!.text) ?? 0) > (int.tryParse(totalPriceController!.text) ?? 0)) {
        emit(state.copyWith(pageState: PageState.error, errorMessage: 'maintenance cost cannot be greater than total price'));
        return null;
      }
    }
    if (propertyOwnerDetails?.isNotEmpty ?? false) {
      final ownerDetails = propertyOwnerDetails!.map((e) => e.getOwnerDetailsModel()).toList();
      _addPropertyModel = _addPropertyModel.copyWith(propertyOwnerDetails: ownerDetails);
    }
    final additionalAttributes = getIt<CustomAmenitiesAndAttributesLocalDataSource>().getCustomAttributes()?.where((element) => element?.attributeType == "Additional").toList() ?? [];
    List<PropertyAttributeModel>? attributes = _addPropertyModel.attributes?.where((element) => additionalAttributes.any((e) => e?.attributeName == element.attributeName)).toList() ?? [];
    if (state.customBasicAttributes.isNotEmpty) attributes.addAll(state.customBasicAttributes.map((e) => PropertyAttributeModel(value: e.title, attributeName: e.value?.attributeName, attributeDisplayName: e.value?.attributeDisplayName, masterPropertyAttributeId: e.value?.id)));
    List<PropertyAttributeModel>? updatedAttributesValue = attributes.whereNot((element) => (element.value == null || element.value == "false" || element.value == "0")).toList();
    _addPropertyModel = _addPropertyModel.copyWith(attributes: updatedAttributesValue);

    if (state.selectedNoOfFloorsOccupied != null && state.selectedNoOfFloorsOccupied!.isNotEmpty) {
      var selectedNoOfFloorsOccupiedFloors = state.selectedNoOfFloorsOccupied?.map((e) => e.value ?? 0).toList();
      _addPropertyModel = _addPropertyModel.copyWith(noOfFloorsOccupied: selectedNoOfFloorsOccupiedFloors);
    }
    if (state.ratings != null || state.ratings != 0) {
      _addPropertyModel = _addPropertyModel.copyWith(rating: state.ratings?.toString());
    }

    if (_addPropertyModel.furnishStatus == null) {
      _addPropertyModel = _addPropertyModel.copyWith(furnishStatus: 0);
    }

    if (_addPropertyModel.facing == null) {
      _addPropertyModel = _addPropertyModel.copyWith(facing: 0);
    }
    emit(state.copyWith(pageState: PageState.success, addPropertyModel: _addPropertyModel));
  }

  FutureOr<void> _clearState(ClearPropertyInfoTabStateEvent event, Emitter<PropertyInfoTabState> emit) {
    emit(state.clearState());
  }

  FutureOr<void> _clearProject(ClearProjectEvent event, Emitter<PropertyInfoTabState> emit) {
    if (event.clearOption != null) {
      switch (event.clearOption) {
        case ClearOption.securityDeposit:
          emit(state.copyWith(pageState: PageState.initial, selectedSecurityDeposit: SelectableItem(title: 'Select', value: SecurityDepositType.none)));
          break;
        case ClearOption.noticePeriod:
          emit(state.copyWith(pageState: PageState.initial, selectedNoticePeriodType: SelectableItem(title: 'Select', value: NoticePeriodType.none)));
          break;
        case ClearOption.lockInPeriod:
          emit(state.copyWith(pageState: PageState.initial, selectedLockInPeriodType: SelectableItem(title: 'Select', value: LockInPeriodType.none)));
          break;
        case ClearOption.project:
        case null:
          _addPropertyModel = _addPropertyModel.copyWith(project: '');
          emit(state.copyWith(pageState: PageState.initial, selectedProject: SelectableItem(title: 'Select', value: '')));
      }
    }
  }

  FutureOr<void> _onSelectSecurityDepositEvent(SelectSecurityDepositEvent event, Emitter<PropertyInfoTabState> emit) {
    emit(state.copyWith(pageState: PageState.initial, selectedSecurityDeposit: event.selectableItem));
  }

  FutureOr<void> _onSelectNoticePeriodEvent(SelectNoticePeriodEvent event, Emitter<PropertyInfoTabState> emit) {
    emit(state.copyWith(pageState: PageState.initial, selectedNoticePeriodType: event.selectableItem));
  }

  FutureOr<void> _onSelectLockInPeriodEvent(SelectLockInPeriodEvent event, Emitter<PropertyInfoTabState> emit) {
    emit(state.copyWith(pageState: PageState.initial, selectedLockInPeriodType: event.selectableItem));
  }

  FutureOr<void> _onSelectCommonAreaUnitsEvent(SelectCommonAreaUnitsEvent event, Emitter<PropertyInfoTabState> emit) {
    emit(state.copyWith(pageState: PageState.initial, selectedCommonAreaUnits: event.selectableItem));
  }

  FutureOr<void> _onAddContactEvent(AddContactEvent event, Emitter<PropertyInfoTabState> emit) {
    List<OwnerDetailsStorage> allOwnerDetails = [];
    allOwnerDetails.addAll(state.ownerDetailsStorages);
    final newOwnerDetail = OwnerDetailsStorage(
      ownerNameController: TextEditingController(),
      phoneNumberController: TextEditingController(),
      alternateNumberController: TextEditingController(),
      emailController: TextEditingController(),
      altCountryCode: state.defaultCountryCode,
      primaryCountryCode: state.defaultCountryCode,
    );
    allOwnerDetails.add(newOwnerDetail);
    propertyOwnerDetails?.add(newOwnerDetail);
    emit(state.copyWith(pageState: PageState.initial, ownerDetailsStorages: allOwnerDetails));
  }

  FutureOr<void> _onRemoveContactEvent(RemoveContactEvent event, Emitter<PropertyInfoTabState> emit) {
    List<OwnerDetailsStorage>? allOwnerDetails = state.ownerDetailsStorages;
    allOwnerDetails.removeWhere((element) => element.uniqueId == event.uniqueId);
    propertyOwnerDetails?.removeWhere((element) => element.uniqueId == event.uniqueId);
    emit(state.copyWith(pageState: PageState.initial, ownerDetailsStorages: allOwnerDetails));
  }

  FutureOr<void> _onOwnerContactChangedEvent(OwnerContactChangedEvent event, Emitter<PropertyInfoTabState> emit) {
    for (int index = 0; index < (propertyOwnerDetails?.length ?? 0); index++) {
      if (propertyOwnerDetails![index].uniqueId == event.uniqueId) {
        if (event.isAlternateContactNumberChanged) {
          propertyOwnerDetails![index].altCountryCode = event.countryCode;
        } else {
          propertyOwnerDetails![index].primaryCountryCode = event.countryCode;
        }
        break;
      }
    }
  }

  FutureOr<void> _onCollapseOwnerContactEvent(CollapseOwnerContactEvent event, Emitter<PropertyInfoTabState> emit) {
    List<OwnerDetailsStorage> allOwnerDetails = state.ownerDetailsStorages ?? [];
    for (int index = 0; index < allOwnerDetails.length; index++) {
      if (allOwnerDetails[index].uniqueId == event.uniqueId) {
        allOwnerDetails[index].isCollapse = !allOwnerDetails[index].isCollapse;
        break;
      }
    }
    emit(state.copyWith(pageState: PageState.initial, ownerDetailsStorages: allOwnerDetails));
  }

  Future<void> _addAreaUnits(Emitter<PropertyInfoTabState> emit) async {
    var masterAreaUnits = await _masterDataRepository.getAreaUnits();
    if (masterAreaUnits?.isNotEmpty ?? false) {
      final areaUnits = masterAreaUnits!.map((areaUnit) => SelectableItem<MasterAreaUnitsModel>(title: areaUnit?.unit ?? '', value: areaUnit)).toList();
      if (_addPropertyModel.id != null && _addPropertyModel.id != '00000000-0000-0000-0000-000000000000') {
        final selectedCarpetAea = areaUnits.firstWhereOrNull((element) => element.value?.id == _addPropertyModel.dimension?.commonAreaChargesId);

        var noticePeriod = _addPropertyModel.noticePeriod != null && _addPropertyModel.noticePeriod != NoticePeriodType.none ? NoticePeriodType.values.where((e) => e == _addPropertyModel.noticePeriod).first : null;
        var lockInPeriod = _addPropertyModel.lockInPeriod != null && _addPropertyModel.lockInPeriod != LockInPeriodType.none ? LockInPeriodType.values.where((e) => e == _addPropertyModel.lockInPeriod).first : null;
        emit(state.copyWith(selectedCommonAreaUnits: selectedCarpetAea, selectableCommonAreaUnits: areaUnits, selectedNoticePeriodType: noticePeriod != null ? SelectableItem<NoticePeriodType>(title: noticePeriod.description, value: noticePeriod) : null, selectedLockInPeriodType: lockInPeriod != null ? SelectableItem<LockInPeriodType>(title: lockInPeriod.description, value: lockInPeriod) : null));

        emit(state.copyWith(selectedCommonAreaUnits: selectedCarpetAea, selectableCommonAreaUnits: areaUnits, selectedNoticePeriodType: noticePeriod != null ? SelectableItem<NoticePeriodType>(title: noticePeriod.description, value: noticePeriod) : null, selectedLockInPeriodType: lockInPeriod != null ? SelectableItem<LockInPeriodType>(title: lockInPeriod.description, value: lockInPeriod) : null));
      } else {
        emit(state.copyWith(
          selectableCommonAreaUnits: areaUnits,
        ));
      }
    }
  }

  FutureOr<void> _onInitListingSourceAndAddresses(InitListingSourceAndAddresses event, Emitter<PropertyInfoTabState> emit) async {
    final listingSourceResponse = await _getAllListingSourceAndSiteAddressesUseCase(NoParams());
    listingSourceResponse.fold(
      (failure) => null,
      (response) {
        final selectableListingSource = response.items.whereNot((element) => element.displayName == null || element.id == StringConstants.emptyGuidId).map((e) => SelectableItem<ViewAddressesModel>(title: e.displayName ?? '', value: e)).toList();

        final listingSitesAndLocations = List.generate((_addPropertyModel.listingAddresses?.length ?? 0) == 0 ? 1 : _addPropertyModel.listingAddresses!.length, (index) {
          final selectedSourceIds = _addPropertyModel.listingAddresses?.where((item) => item != _addPropertyModel.listingAddresses?[index]).map((item) => item.listingSourceId).toList() ?? [];
          final availableSources = selectableListingSource.where((source) => !selectedSourceIds.contains(source.value?.id)).toList();
          return ItemListingSourceAddressModel(listingSource: availableSources);
        });

        emit(state.copyWith(listingSiteAndLocations: listingSitesAndLocations, totalListingSiteAndAddress: response.items.length));

        int index = 0;
        for (var item in selectableListingSource) {
          if (_addPropertyModel.listingAddresses?.any((element) => item.value?.id == element.listingSourceId) ?? false) {
            add(SelectListingSourceEvent(item, index: index++, updateListingLocations: false));
          }
        }
      },
    );
  }

  FutureOr<void> _onSelectListingSource(SelectListingSourceEvent event, Emitter<PropertyInfoTabState> emit) {
    final selectableListingSiteAddress = event.selectedListingSource.value?.listingSourceAddresses?.map((e) => SelectableItem<ViewListingSourceAddressModel>(title: "${e.towerName ?? ''}, ${e.community ?? ''}, ${e.subCommunity ?? ''}", value: e)).toList();
    var updatedListingSitesAndLocations = state.listingSiteAndLocations;
    updatedListingSitesAndLocations[event.index] = updatedListingSitesAndLocations[event.index].copyWith(listingSiteAddress: selectableListingSiteAddress, selectedListingSource: event.selectedListingSource, resetSelectedListingSiteAddress: true);
    for (var i = 0; i < updatedListingSitesAndLocations.length; i++) {
      if (i != event.index) {
        final selectedSourceIds = updatedListingSitesAndLocations.asMap().entries.where((entry) => entry.key != i && entry.value.selectedListingSource != null).map((entry) => entry.value.selectedListingSource!.value?.id).toList();
        final availableSources = updatedListingSitesAndLocations[i].listingSource.where((source) => !selectedSourceIds.contains(source.value?.id)).toList();
        updatedListingSitesAndLocations[i] = updatedListingSitesAndLocations[i].copyWith(listingSource: availableSources);
      }
    }
    emit(state.copyWith(pageState: PageState.initial, listingSiteAndLocations: updatedListingSitesAndLocations));
    final selectedSiteAddress = state.listingSiteAndLocations[event.index].listingSiteAddress.firstWhereOrNull((element) => _addPropertyModel.listingAddresses?.any((item) => element.value?.id == item.locationId) ?? false);
    if (selectedSiteAddress != null) {
      add(SelectListingSiteAddress(selectedSiteAddress, index: event.index));
    }

    var listingAddresses = _addPropertyModel.listingAddresses;
    if (tempListingAddressMap.containsKey(event.index) && event.updateListingLocations) {
      listingAddresses?.removeWhere((element) => element.listingSourceId == tempListingAddressMap[event.index]);
      _addPropertyModel = _addPropertyModel.copyWith(listingAddresses: listingAddresses);
    } else {
      tempListingAddressMap[event.index] = event.selectedListingSource.value?.id ?? '';
      _addPropertyModel = _addPropertyModel.copyWith(listingAddresses: listingAddresses);
    }
  }

  FutureOr<void> _onSelectListingSiteAddress(SelectListingSiteAddress event, Emitter<PropertyInfoTabState> emit) {
    var updatedListingSitesAndLocations = state.listingSiteAndLocations;
    updatedListingSitesAndLocations[event.index] = updatedListingSitesAndLocations[event.index].copyWith(selectedListingSiteAddress: event.selectedListingSiteAddress);

    emit(state.copyWith(pageState: PageState.initial, listingSiteAndLocations: updatedListingSitesAndLocations));

    final List<CreateListingAddressModel> listingAddresses = {
      ...?_addPropertyModel.listingAddresses,
      CreateListingAddressModel(
        locationId: event.selectedListingSiteAddress.value?.id,
        listingSourceId: state.listingSiteAndLocations[event.index].selectedListingSource?.value?.id,
      ),
    }.toList();

    _addPropertyModel = _addPropertyModel.copyWith(listingAddresses: listingAddresses);
  }

  FutureOr<void> _onAddNewListingSourceAndAddress(AddNewListingSourceAndAddressEvent event, Emitter<PropertyInfoTabState> emit) {
    var updatedListingSitesAndLocations = List<ItemListingSourceAddressModel>.from(state.listingSiteAndLocations);
    final selectedSourceIds = updatedListingSitesAndLocations.where((item) => item.selectedListingSource != null).map((item) => item.selectedListingSource!.value?.id).toSet();
    final availableSources = updatedListingSitesAndLocations.first.listingSource.where((source) => !selectedSourceIds.contains(source.value?.id)).toList();
    updatedListingSitesAndLocations.add(ItemListingSourceAddressModel(listingSource: availableSources));
    emit(state.copyWith(listingSiteAndLocations: updatedListingSitesAndLocations, pageState: PageState.initial));
  }

  FutureOr<void> _onRemoveNewListingSourceAndAddress(RemoveNewListingSourceAndAddressEvent event, Emitter<PropertyInfoTabState> emit) async {
    if (event.index <= 0 || event.index >= state.listingSiteAndLocations.length) return;
    var updatedListingSitesAndLocations = List<ItemListingSourceAddressModel>.from(state.listingSiteAndLocations);
    final removedSource = updatedListingSitesAndLocations[event.index].selectedListingSource;
    updatedListingSitesAndLocations.removeAt(event.index);
    if (removedSource != null) {
      updatedListingSitesAndLocations = updatedListingSitesAndLocations.map((item) {
        final selectedSourceIds = updatedListingSitesAndLocations.where((otherItem) => otherItem != item && otherItem.selectedListingSource != null).map((otherItem) => otherItem.selectedListingSource!.value?.id).toSet();
        final updatedSources = [...item.listingSource];
        if (!selectedSourceIds.contains(removedSource.value?.id) && !updatedSources.any((source) => source.value?.id == removedSource.value?.id)) {
          updatedSources.add(removedSource);
        }
        return item.copyWith(listingSource: updatedSources);
      }).toList();
    }
    emit(state.copyWith(listingSiteAndLocations: updatedListingSitesAndLocations, pageState: PageState.initial));
    var listingAddresses = _addPropertyModel.listingAddresses;
    if (tempListingAddressMap.containsKey(event.index)) {
      listingAddresses?.removeWhere((element) => element.listingSourceId == tempListingAddressMap[event.index]);
      _addPropertyModel = _addPropertyModel.copyWith(listingAddresses: listingAddresses);
    }
  }

  FutureOr<void> _onChangePermitValue(ChangePermitValueEvent event, Emitter<PropertyInfoTabState> emit) {
    emit(state.copyWith(selectedPermitValue: event.selectedPermitValue, pageState: PageState.initial));
  }

  FutureOr<void> _onSelectPaymentFrequency(SelectPaymentFrequency event, Emitter<PropertyInfoTabState> emit) {
    emit(state.copyWith(selectedPaymentFrequency: event.selectPaymentFrequency, pageState: PageState.initial));
    _propertyMonetaryInfo = _propertyMonetaryInfo.copyWith(paymentFrequency: event.selectPaymentFrequency.value);
    _addPropertyModel = _addPropertyModel.copyWith(monetaryInfo: _propertyMonetaryInfo);
  }

  FutureOr<void> _onSelectedNoOfCheque(SelectNoOfChequeEvent event, Emitter<PropertyInfoTabState> emit) {
    _propertyMonetaryInfo = _propertyMonetaryInfo.copyWith(noOfChequesAllowed: event.selectedNoOfCheque.value);
    _addPropertyModel = _addPropertyModel.copyWith(monetaryInfo: _propertyMonetaryInfo);
    emit(state.copyWith(selectedNumberOfCheque: event.selectedNoOfCheque, pageState: PageState.initial));
  }

  FutureOr<void> _onChangeTaxationMode(ChangeTaxationModeEvent event, Emitter<PropertyInfoTabState> emit) async {
    emit(state.copyWith(taxationMode: event.taxationMode, pageState: PageState.initial));
  }

  FutureOr<void> _onBuilderContactChanged(OnBuilderContactChangedEvent event, Emitter<PropertyInfoTabState> emit) {
    final contactNumber = "+${event.countryCode}${event.contactNumber}";
    if (event.countryCode.isNotEmpty && event.contactNumber.isNotEmpty) emit(state.copyWith(builderContactNumber: contactNumber, pageState: PageState.initial));
  }

  bool isValidEmail(String? email) {
    if (email == null || email.trim().isEmpty) return false;
    return RegExp(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$").hasMatch(email);
  }

  FutureOr<void> _onSelectPossessionType(SelectPossessionType event, Emitter<PropertyInfoTabState> emit) async {
    _addPropertyModel = _addPropertyModel.copyWith(possesionType: event.selectPossessionType?.value, possessionDate: DateTimeUtils.getPossessionDate(event.selectPossessionType?.value ?? PossessionType.none).getBasedOnTimeZone().toUtcFormat(), updatePossessionDate: DateTimeUtils.getPossessionDate(event.selectPossessionType?.value ?? PossessionType.none) == null ? true : false);
    emit(state.copyWith(possessionTypeSelectedItem: event.selectPossessionType, isPossessionDateCustomSelected: event.selectPossessionType?.value == PossessionType.customDate));
  }

  FutureOr<void> _onToggleSecurityAmount(ToggleSecurityAmount event, Emitter<PropertyInfoTabState> emit) {
    emit(state.copyWith(isSecurityAmountPercentageSelected: event.isSecurityAmountPercentageSelected, depositCurrency: event.isSecurityAmountPercentageSelected ?? false ? '%' : state.globalSettings?.countries?.firstOrNull?.defaultCurrency ?? 'INR'));

    _addPropertyModel = _addPropertyModel.copyWith(securityDepositUnit: event.isSecurityAmountPercentageSelected ?? false ? '%' : state.depositCurrency);
  }

  FutureOr<void> _onSelectDepositCurrencyEvent(SelectDepositCurrencyEvent event, Emitter<PropertyInfoTabState> emit) {
    emit(state.copyWith(pageState: PageState.initial, depositCurrency: event.currency?.value));

    _addPropertyModel = _addPropertyModel.copyWith(securityDepositUnit: event.currency?.value);
  }

  void initOwnerDetails(Emitter<PropertyInfoTabState> emit) {
    final ownerDetails = _addPropertyModel.propertyOwnerDetails;
    final ownerDetailsStorage = ownerDetails
        ?.map((ownerDetail) => OwnerDetailsStorage(
              ownerNameController: TextEditingController(text: ownerDetail.name),
              phoneNumberController: TextEditingController(text: ownerDetail.phone),
              alternateNumberController: TextEditingController(text: ownerDetail.alternateContactNo),
              emailController: TextEditingController(text: ownerDetail.email),
              altCountryCode: state.defaultCountryCode,
              primaryCountryCode: state.defaultCountryCode,
              ownerId: ownerDetail.id,
            ))
        .toList();
    propertyOwnerDetails = ownerDetailsStorage ?? [];
    if (propertyOwnerDetails?.isEmpty ?? true) {
      add(AddContactEvent());
    } else {
      emit(state.copyWith(pageState: PageState.initial, ownerDetailsStorages: ownerDetailsStorage));
    }
  }

  FutureOr<void> _onInitAttributesEvent(InitAttributesEvent event, Emitter<PropertyInfoTabState> emit) async {
    final furnishStatuses = _getFurnishStatuses();
    final facings = _getFacings();
    emit(state.copyWith(pageState: PageState.initial, furnishStatuses: furnishStatuses, facings: facings, selectedEnquiredType: EnquiryType.values.where((e) => e.value == _addPropertyModel.enquiredFor).firstOrNull, selectedPropertySubType: _addPropertyModel.propertyTypeId));

    String? masterPropertyType = await _getPropertyType();
    final localAttributes = getIt<CustomAmenitiesAndAttributesLocalDataSource>().getCustomAttributes() ?? [];
    final propertyAttributes = _addPropertyModel.attributes ?? [];

    final updatedBasicAttributes = localAttributes
        .whereType<CustomAttributesModel>() // Removes any nulls
        .where((attr) => attr.attributeType == 'Basic')
        .map((attr) {
          final matched = propertyAttributes.firstWhereOrNull(
            (pa) => pa.attributeName == attr.attributeName,
          );

          if (attr.attributeDisplayName?.replaceAll(" ", '').toLowerCase().contains('totalfloors') ?? false) {
            var totalFloors = 0;
            try {
              totalFloors = int.parse(matched?.value ?? '0');
            } catch (ex) {}
            List<SelectableItem<int>>? selectableNoOfFloorsOccupied = [];
            if (totalFloors != 0) {
              for (int i = 0; i < totalFloors; i++) {
                selectableNoOfFloorsOccupied.add(SelectableItem<int>(title: 'Floor ${i + 1}', value: i + 1));
              }
            }
            List<SelectableItem<int>>? selectedNoOfFloorsOccupied = [];
            final propertySelectedNoOfFloorsOccupied = _addPropertyModel.noOfFloorsOccupied;
            if (propertySelectedNoOfFloorsOccupied?.isNotEmpty ?? false) {
              for (int number in propertySelectedNoOfFloorsOccupied!) {
                selectedNoOfFloorsOccupied.add(SelectableItem<int>(title: 'Floor $number', value: number));
              }
            }
            emit(state.copyWith(pageState: PageState.initial, selectableNoOfFloorsOccupied: selectableNoOfFloorsOccupied, selectedNoOfFloorsOccupied: selectedNoOfFloorsOccupied));
          }
          return attr.copyWith(matched?.value);
        })
        .where((attr) => attr.basePropertyType?.any((type) => type.description.toLowerCase() == masterPropertyType?.toLowerCase()) ?? false)
        .toList();

    final customBasicAttributes = updatedBasicAttributes
        .map((attr) => SelectableItem<CustomAttributesModel>(
              title: (attr.defaultValue?.isNotEmpty ?? false) ? attr.defaultValue! : '0',
              value: attr,
            ))
        .toList();

    emit(state.copyWith(customBasicAttributes: customBasicAttributes));

    var noOfRatings = int.tryParse(_addPropertyModel.rating ?? '') ?? 0;
    add(ToggleRatingsEvent(noOfRatings - 1));
  }

  List<SelectableItem<String>> _getFurnishStatuses() {
    final selectedFurnishStatus = _addPropertyModel.furnishStatus;
    List<SelectableItem<String>> furnishStatuses = [];
    FurnishStatus.values.map((e) => e).toList().forEach((furnishStatus) {
      if (furnishStatus.value != 0) {
        furnishStatuses.add(SelectableItem(title: furnishStatus.description, isSelected: furnishStatus.value == selectedFurnishStatus));
      }
    });

    return furnishStatuses;
  }

  List<SelectableItem<String>> _getFacings() {
    final selectedFurnishStatus = _addPropertyModel.facing;
    List<SelectableItem<String>> facings = [];
    Facing.values.map((e) => e).toList().forEach((face) {
      if (face.value != 0) {
        facings.add(SelectableItem(title: face.description, isSelected: face.value == selectedFurnishStatus));
      }
    });

    return facings;
  }

  FutureOr<void> _onToggleRatingsEvent(ToggleRatingsEvent event, Emitter<PropertyInfoTabState> emit) {
    emit(state.copyWith(pageState: PageState.initial, ratings: event.rating! + 1));
  }

  FutureOr<void> _onAttachOrDeAttachCustomAttributeEvent(AttachOrDeAttachCustomAttributeEvent event, Emitter<PropertyInfoTabState> emit) {
    List<SelectableItem<CustomAttributesModel>>? customAttributes;
    var totalFloors = 0;
    switch (event.isAttach) {
      case true:
        customAttributes = state.customBasicAttributes.map((attribute) {
          if (attribute.value == event.attribute && ((attribute.value?.attributeDisplayName ?? '').replaceAll(" ", '').toLowerCase().contains('totalfloors'))) {
            try {
              totalFloors = (int.parse(attribute.title) + 1);
            } catch (ex) {}
          }
          return attribute.value == event.attribute ? attribute.copyWith(title: (int.parse(attribute.title) + 1).toString()) : attribute;
        }).toList();
      case false:
        customAttributes = state.customBasicAttributes.map((attribute) {
          if (attribute.value == event.attribute && ((attribute.value?.attributeDisplayName ?? '').replaceAll(" ", '').toLowerCase().contains('totalfloors'))) {
            try {
              totalFloors = (int.parse(attribute.title) - 1);
            } catch (ex) {}
          }
          return attribute.value == event.attribute ? attribute.copyWith(title: int.parse(attribute.title) > 0 ? (int.parse(attribute.title) - 1).toString() : attribute.title) : attribute;
        }).toList();
    }
    List<SelectableItem<int>>? selectableNoOfFloorsOccupied = [];
    if (totalFloors != 0) {
      for (int i = 0; i < totalFloors; i++) {
        selectableNoOfFloorsOccupied.add(SelectableItem<int>(title: 'Floor ${i + 1}', value: i + 1));
      }
    }
    emit(state.copyWith(pageState: PageState.initial, customBasicAttributes: customAttributes, selectableNoOfFloorsOccupied: selectableNoOfFloorsOccupied, selectedNoOfFloorsOccupied: []));
  }

  FutureOr<void> _onToggleFacingEvent(ToggleFacingEvent event, Emitter<PropertyInfoTabState> emit) {
    final updatedFacing = state.facings!.map((e) {
      return e.title == event.selectedFacing.title ? e.copyWith(isSelected: true) : e.copyWith(isSelected: false);
    }).toList();
    emit(state.copyWith(pageState: PageState.initial, facings: updatedFacing));
    final selectedFacing = state.facings?.where((element) => element.isSelected).map((e) => e.title).whereNotNull().toList().firstOrNull;
    if (selectedFacing != null) {
      var selection = Facing.values.map((e) => e).toList().firstWhereOrNull((furnishStatus) => furnishStatus.description == selectedFacing);
      _addPropertyModel = _addPropertyModel.copyWith(facing: selection?.value);
    }
  }

  FutureOr<void> _onSelectNumberOfFloorsOccupiedEvent(SelectNumberOfFloorsOccupiedEvent event, Emitter<PropertyInfoTabState> emit) {
    emit(state.copyWith(selectedNoOfFloorsOccupied: event.item));
  }

  FutureOr<void> _onToggleFurnishStatusEvent(ToggleFurnishStatusEvent event, Emitter<PropertyInfoTabState> emit) {
    final updatedFurnishStatus = state.furnishStatuses!.map((e) {
      return e.title == event.selectedFurnishStatus.title ? e.copyWith(isSelected: true) : e.copyWith(isSelected: false);
    }).toList();
    emit(state.copyWith(pageState: PageState.initial, furnishStatuses: updatedFurnishStatus));
    final selectedFurnishStatus = state.furnishStatuses?.where((element) => element.isSelected).map((e) => e.title).whereNotNull().toList().firstOrNull;
    if (selectedFurnishStatus != null) {
      var selection = FurnishStatus.values.map((e) => e).toList().firstWhereOrNull((furnishStatus) => furnishStatus.description == selectedFurnishStatus);
      _addPropertyModel = _addPropertyModel.copyWith(furnishStatus: selection?.value);
    }
  }

  Future<String?> _getPropertyType() async {
    try {
      final propertyTypes = await _masterDataRepository.getCustomPropertyTypes(isPropertyListingEnabled: isPropertyListingEnabled);

      for (final propertyType in propertyTypes ?? []) {
        final childTypes = propertyType.childTypes ?? [];
        for (final child in childTypes) {
          if (child.id == _addPropertyModel.propertyTypeId && child.baseId == propertyType.id) {
            return propertyType.type;
          }
        }
      }
      return null;
    } catch (ex, stack) {
      return null;
    }
  }
}
