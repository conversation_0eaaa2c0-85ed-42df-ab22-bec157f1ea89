import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/constants/app_analytics_constants.dart';
import 'package:leadrat/core_main/common/data/app_analysis/repository/app_analysis_repository.dart';
import 'package:leadrat/core_main/common/data/user/data_source/local/user_local_data_source.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/blob_folder_names.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/app_enum/select_file_enum.dart';
import 'package:leadrat/core_main/extensions/datetime_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/mapper/lead_mapper.dart';
import 'package:leadrat/core_main/utilities/file_picker_util.dart';
import 'package:leadrat/features/lead/data/models/delete_document_model.dart';
import 'package:leadrat/features/lead/data/models/lead_document_model.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';
import 'package:leadrat/features/lead/domain/entities/lead_document_entity.dart';
import 'package:leadrat/features/lead/domain/usecase/add_lead_documents_usecase.dart';
import 'package:leadrat/features/lead/domain/usecase/delete_lead_document_use_case.dart';
import 'package:leadrat/features/lead/presentation/bloc/lead_info_bloc/lead_info_bloc.dart';
import 'package:leadrat/features/lead/presentation/item/item_lead_document_model.dart';
import 'package:leadrat/features/user_profile/domain/usecase/upload_document_usecase.dart';
import 'package:url_launcher/url_launcher.dart';

part 'lead_documents_event.dart';
part 'lead_documents_state.dart';

class LeadDocumentsBloc extends Bloc<LeadDocumentsEvent, LeadDocumentsState> {
  final UploadDocumentUseCase _uploadDocumentUseCase;
  final AddLeadDocumentsUseCase _addLeadDocumentsUseCase;
  final DeleteLeadDocumentUseCase _deleteLeadDocumentUseCase;
  GetLeadEntity? _leadEntity;

  TextEditingController fileNameController = TextEditingController();

  LeadDocumentsBloc(this._uploadDocumentUseCase, this._addLeadDocumentsUseCase, this._deleteLeadDocumentUseCase) : super(const LeadDocumentsState()) {
    on<LeadDocumentsInitialEvent>(_onLeadDocumentsInitialEvent);
    on<InitLeadDocumentsEvent>(_onInitLeadDocuments);
    on<ToggleUploadFileEvent>(_onToggleUploadFile);
    on<SelectFileEvent>(_onSelectAndUploadFile);
    on<UploadDocumentEvent>(_onUploadDocument);
    on<DeleteDocumentEvent>(_onDeleteDocument);
    on<DownloadDocumentEvent>(_onDownloadDocument);
    on<ShowLoadingDialogEvent>(_onShowLoadingDialog);
  }

  FutureOr<void> _onLeadDocumentsInitialEvent(LeadDocumentsInitialEvent event, Emitter<LeadDocumentsState> emit) {
    fileNameController.text = "";
    emit(state.copyWith(pageState: PageState.initial, leadDocuments: [], successMessage: null, errorMessage: null, canSelectFile: false, showLoadingProgress: false));
  }

  FutureOr<void> _onInitLeadDocuments(InitLeadDocumentsEvent event, Emitter<LeadDocumentsState> emit) {
    final leadDocuments = event.leadEntity.documents ?? [];
    _leadEntity = event.leadEntity;
    List<ItemLeadDocumentModel> documents = [];
    if (leadDocuments.isNotEmpty) {
      for (var document in leadDocuments) {
        documents.add(ItemLeadDocumentModel(
          name: document.documentName ?? "",
          path: document.filePath ?? "",
          addedByUser: getIt<LeadEntityMapper>().getUser(document.createdBy),
          document: document,
          uploadedOn: document.createdOn?.toLocal(),
        ));
      }
    }
    emit(state.copyWith(pageState: PageState.success, errorMessage: null, successMessage: null, leadDocuments: documents, showLoadingProgress: false));
  }

  FutureOr<void> _onToggleUploadFile(ToggleUploadFileEvent event, Emitter<LeadDocumentsState> emit) {
    emit(state.copyWith(canSelectFile: !state.canSelectFile));
    if (!state.canSelectFile) fileNameController.text = "";
    getIt<AppAnalysisRepository>().sendAppAnalysis(name: state.canSelectFile ? AppAnalyticsConstants.mobileLeadInfoButtonUploadFileClick : AppAnalyticsConstants.mobileLeadInfoButtonUploadFileCancelClick);
  }

  FutureOr<void> _onSelectAndUploadFile(SelectFileEvent event, Emitter<LeadDocumentsState> emit) async {
    if (fileNameController.text.isNullOrEmpty()) {
      emit(state.copyWith(errorMessage: "Please add the file name", pageState: PageState.failure));
      return;
    }
    final file = await FilePickerUtil.pickFile(event.source);
    if (file?.any((element) => element != null) ?? false) {
      final base64File = base64Encode(await file!.first!.readAsBytes());
      add(ShowLoadingDialogEvent(true));
      final uploadedResponse = await _uploadDocumentUseCase(UploadDocumentParams(BlobFolderNameEnum.leadDocuments.description, file.first!.path.split('/').last, base64File));
      uploadedResponse.fold((failure) => emit(state.copyWith(errorMessage: failure.message, showLoadingProgress: false)), (result) {
        if (result != null) {
          add(UploadDocumentEvent(result));
        }
      });
    }
  }

  FutureOr<void> _onUploadDocument(UploadDocumentEvent event, Emitter<LeadDocumentsState> emit) async {
    final documentName = fileNameController.text.isEmpty ? "default" : fileNameController.text;
    final leadDocument = LeadDocumentEntity(
      documentName: documentName,
      id: _leadEntity?.id,
      filePath: event.documentPath,
      createdOn: DateTime.now().getBasedOnTimeZone().toUtcFormat(),
      createdBy: "00000000-0000-0000-0000-000000000000",
      uploadedOn: DateTime.now().getBasedOnTimeZone().toUtcFormat(),
      lastModifiedBy: "00000000-0000-0000-0000-000000000000",
      lastModifiedOn: DateTime.now().getBasedOnTimeZone().toUtcFormat(),
    );
    AddLeadDocumentsModel addLeadDocumentsModel = AddLeadDocumentsModel(leadId: _leadEntity?.id, documents: [leadDocument.toModel()]);
    add(ShowLoadingDialogEvent(true));
    final result = await _addLeadDocumentsUseCase(addLeadDocumentsModel);
    result.fold(
      (failure) => _emitFailureState(emit, failure.message),
      (result) {
        final uploadedUser = getIt<UsersLocalDataSource>().getUser()?.toBaseUser();
        var oldDocuments = state.leadDocuments;
        oldDocuments?.insert(
          0,
          ItemLeadDocumentModel(name: documentName, path: event.documentPath ?? "", addedByUser: uploadedUser, document: leadDocument.copyWith(id: result?.firstOrNull), uploadedOn: DateTime.now().toUserTimeZone()),
        );
        fileNameController.text = "";
        emit(state.copyWith(leadDocuments: oldDocuments, showLoadingProgress: false, canSelectFile: false));
        if (_leadEntity?.id?.isNotNullOrEmpty() ?? false) {
          getIt<LeadInfoBloc>().add(GetLeadInfoInitialEvent(_leadEntity?.id ?? ''));
        }
      },
    );
  }

  FutureOr<void> _onDownloadDocument(DownloadDocumentEvent event, Emitter<LeadDocumentsState> emit) async {
    if (event.documentPath == null) return;
    final Uri url = Uri.parse(event.documentPath!);
    if (!await launchUrl(url)) {
      _emitFailureState(emit, "unable to open a file");
    }
  }

  FutureOr<void> _onDeleteDocument(DeleteDocumentEvent event, Emitter<LeadDocumentsState> emit) async {
    if (event.documentId == null) return;
    add(ShowLoadingDialogEvent(true));
    final deleteResponse = await _deleteLeadDocumentUseCase(DeleteDocumentModel(documentIds: [event.documentId!], leadId: _leadEntity?.id));
    deleteResponse.fold((failure) => _emitFailureState(emit, failure.message), (success) {
      if (state.leadDocuments != null) {
        final updatedDocumentsItems = state.leadDocuments!
            .map((item) {
              if (item.document.id != event.documentId) {
                return item;
              }
            })
            .whereType<ItemLeadDocumentModel>()
            .toList();
        emit(state.copyWith(showLoadingProgress: false, errorMessage: null, leadDocuments: updatedDocumentsItems, canSelectFile: false));
        fileNameController.text = "";
      }
      getIt<LeadInfoBloc>().add(LeadInfoInitialEvent(_leadEntity ?? GetLeadEntity(id: _leadEntity?.id), null));
      if (_leadEntity?.id?.isNotNullOrEmpty() ?? false) {
        getIt<LeadInfoBloc>().add(GetLeadInfoInitialEvent(_leadEntity?.id ?? ''));
      }
    });
  }

  _emitFailureState(Emitter<LeadDocumentsState> emit, String? errorMessage) {
    emit(state.copyWith(errorMessage: errorMessage, showLoadingProgress: false));
  }

  FutureOr<void> _onShowLoadingDialog(ShowLoadingDialogEvent event, Emitter<LeadDocumentsState> emit) {
    emit(state.copyWith(showLoadingProgress: event.isVisible));
  }
}
