part of 'add_lead_bloc.dart';

@immutable
sealed class AddLeadEvent {}

final class AddLeadInitialEvent extends AddLeadEvent {
  final GlobalSettingModel? globalSettingModel;
  final GetLeadEntity? getLeadEntity;

  AddLeadInitialEvent({this.globalSettingModel, this.getLeadEntity});
}

final class ToggleEnquiredForEvent extends AddLeadEvent {
  final ItemSimpleModel<EnquiryType>? selectedEnquiredFor;

  ToggleEnquiredForEvent(this.selectedEnquiredFor);
}

final class TogglePropertyTypeEvent extends AddLeadEvent {
  final ItemSimpleModel<PropertyType> selectedPropertyType;

  TogglePropertyTypeEvent(this.selectedPropertyType);
}

final class TogglePropertySubTypeEvent extends AddLeadEvent {
  final ItemSimpleModel<String> selectedPropertySubType;

  TogglePropertySubTypeEvent(this.selectedPropertySubType);
}

final class ToggleNoOfBhkEvent extends AddLeadEvent {
  final ItemSimpleModel<double> selectedNoOfBhk;

  ToggleNoOfBhkEvent(this.selectedNoOfBhk);
}

final class ToggleBhkTypeEvent extends AddLeadEvent {
  final ItemSimpleModel<BHKType> selectedBhkTypes;

  ToggleBhkTypeEvent(this.selectedBhkTypes);
}

final class ToggleProfessionEvent extends AddLeadEvent {
  final ItemSimpleModel<Profession>? selectedProfession;

  ToggleProfessionEvent(this.selectedProfession);
}

final class SelectCarpetAreaEvent extends AddLeadEvent {
  final SelectableItem<MasterAreaUnitsModel>? selectedCarpetArea;

  SelectCarpetAreaEvent(this.selectedCarpetArea);
}

final class SelectSaleableAreaEvent extends AddLeadEvent {
  final SelectableItem<MasterAreaUnitsModel>? selectedSaleableArea;

  SelectSaleableAreaEvent(this.selectedSaleableArea);
}

final class SelectBuiltUpAreaEvent extends AddLeadEvent {
  final SelectableItem<MasterAreaUnitsModel>? selectedBuiltUpArea;

  SelectBuiltUpAreaEvent(this.selectedBuiltUpArea);
}

final class SelectLeadSourceEvent extends AddLeadEvent {
  final SelectableItem<int> selectedLeadSource;

  SelectLeadSourceEvent(this.selectedLeadSource);
}

final class SelectLeadSubSourceEvent extends AddLeadEvent {
  final SelectableItem<String> selectedLeadSubSource;

  SelectLeadSubSourceEvent(this.selectedLeadSubSource);
}

final class SelectAgencyNameEvent extends AddLeadEvent {
  final List<SelectableItem<String>> selectedAgencyName;

  SelectAgencyNameEvent(this.selectedAgencyName);
}

final class SelectCampaignNameEvent extends AddLeadEvent {
  final List<SelectableItem<String>> selectedCampaignName;

  SelectCampaignNameEvent(this.selectedCampaignName);
}

final class RemoveAgencyNameEvent extends AddLeadEvent {
  final SelectableItem<String> selectedAgencyName;

  RemoveAgencyNameEvent(this.selectedAgencyName);
}

final class RemoveCampaignNameEvent extends AddLeadEvent {
  final SelectableItem<String> selectedCampaignName;

  RemoveCampaignNameEvent(this.selectedCampaignName);
}

final class RemoveChannelPartnerNameEvent extends AddLeadEvent {
  final SelectableItem<String> selectedChannelPartner;

  RemoveChannelPartnerNameEvent(this.selectedChannelPartner);
}

final class SelectPropertiesEvent extends AddLeadEvent {
  final List<SelectableItem<String>> selectedProperties;

  SelectPropertiesEvent(this.selectedProperties);
}

final class RemovePropertyEvent extends AddLeadEvent {
  final SelectableItem<String> selectedProperty;

  RemovePropertyEvent(this.selectedProperty);
}

final class SelectProjectsEvent extends AddLeadEvent {
  final List<SelectableItem<String>> selectedProjects;

  SelectProjectsEvent(this.selectedProjects);
}

final class RemoveProjectsEvent extends AddLeadEvent {
  final SelectableItem<String> selectedProjects;

  RemoveProjectsEvent(this.selectedProjects);
}

final class SelectAssignedUserEvent extends AddLeadEvent {
  final SelectableItem<String> selectedUser;

  SelectAssignedUserEvent(this.selectedUser);
}

final class SelectSecondaryUserEvent extends AddLeadEvent {
  final SelectableItem<String> selectedUser;

  SelectSecondaryUserEvent(this.selectedUser);
}

final class ToggleSubTypesExpandedEvent extends AddLeadEvent {}

final class ToggleNoOfBhkExpandedEvent extends AddLeadEvent {}

final class ToggleEmailFieldEvent extends AddLeadEvent {}

final class ToggleAltPhoneFieldEvent extends AddLeadEvent {
  final bool? hideAltPhoneField;

  ToggleAltPhoneFieldEvent({this.hideAltPhoneField});
}

final class ToggleReferralFieldsEvent extends AddLeadEvent {}

final class TogglePossessionDateEvent extends AddLeadEvent {}

final class SelectPossessionDateEvent extends AddLeadEvent {
  final DateTime selectedDate;

  SelectPossessionDateEvent(this.selectedDate);
}

final class AddLocationEvent extends AddLeadEvent {
  final AddressModel? location;

  AddLocationEvent(this.location);
}

final class RemoveLocationEvent extends AddLeadEvent {
  final ItemSimpleModel<AddressModel> selectedItem;

  RemoveLocationEvent(this.selectedItem);
}

final class CheckLeadContactAlreadyExistsEvent extends AddLeadEvent {
  final String countryCode;
  final String contactNo;

  CheckLeadContactAlreadyExistsEvent(this.countryCode, this.contactNo);
}

final class CheckAltContactAlreadyExistsEvent extends AddLeadEvent {
  final String countryCode;
  final String contactNo;

  CheckAltContactAlreadyExistsEvent(this.countryCode, this.contactNo);
}

final class CreateLeadEvent extends AddLeadEvent {}

final class ResetStateEvent extends AddLeadEvent {}

final class OnLeadContactChangedEvent extends AddLeadEvent {
  final String countryCode;
  final String contactNumber;

  OnLeadContactChangedEvent(this.countryCode, this.contactNumber);
}

final class OnAltContactChangedEvent extends AddLeadEvent {
  final String countryCode;
  final String contactNumber;

  OnAltContactChangedEvent(this.countryCode, this.contactNumber);
}

final class OnReferralContactChangedEvent extends AddLeadEvent {
  final String countryCode;
  final String contactNumber;

  OnReferralContactChangedEvent(this.countryCode, this.contactNumber);
}

final class PickContactEvent extends AddLeadEvent {}

final class SelectCurrency extends AddLeadEvent {
  final SelectableItem<String> selectedCurrency;

  SelectCurrency(this.selectedCurrency);
}

final class SelectChannelPartnerEvent extends AddLeadEvent {
  final List<SelectableItem<String>> selectedChannelPartners;

  SelectChannelPartnerEvent(this.selectedChannelPartners);
}

final class SelectPurposeEvent extends AddLeadEvent {
  final SelectableItem<PurposeEnum>? selectedPurpose;

  SelectPurposeEvent(this.selectedPurpose);
}

final class AssignedToLoggedInUser extends AddLeadEvent {}

final class SelectPossessionType extends AddLeadEvent {
  final SelectableItem<PossessionType?>? selectPossessionType;

  SelectPossessionType(this.selectPossessionType);
}

final class SelectSourcingManagerEvent extends AddLeadEvent {
  final SelectableItem<String> selectedSourcingManager;

  SelectSourcingManagerEvent(this.selectedSourcingManager);
}

final class SelectClosingManagerEvent extends AddLeadEvent {
  final SelectableItem<String> selectedClosingManager;

  SelectClosingManagerEvent(this.selectedClosingManager);
}

final class AddCustomerLocationEvent extends AddLeadEvent {
  final AddressModel? customerLocation;

  AddCustomerLocationEvent(this.customerLocation);
}

final class RemoveCustomerLocationEvent extends AddLeadEvent {
  final ItemSimpleModel<AddressModel>? selectedItem;

  RemoveCustomerLocationEvent(this.selectedItem);
}

final class OnExecutiveContactChangedEvent extends AddLeadEvent {
  final String countryCode;
  final String contactNumber;

  OnExecutiveContactChangedEvent(this.countryCode, this.contactNumber);
}
