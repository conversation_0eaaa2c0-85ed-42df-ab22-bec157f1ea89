part of 'gallery_tab_bloc.dart';

@immutable
class GalleryTabState {
  final PageState pageState;
  final String? errorMessage;
  final AddPropertyModel? addPropertyModel;
  final List<SelectableItem<File>>? photos;
  final List<SelectableItem<String>>? videos;
  final List<SelectableItem<XFile>>? brochures;
  final List<ItemSimpleModel<String>> degree360VideoUrls;
  final List<ItemSimpleModel<String>> thirdPartyUrls;
  final bool isWaterMarkEnabled;

  const GalleryTabState({
    this.pageState = PageState.initial,
    this.errorMessage,
    this.addPropertyModel,
    this.photos,
    this.videos,
    this.brochures,
    this.isWaterMarkEnabled = false,
    this.degree360VideoUrls = const [],
    this.thirdPartyUrls = const [],
  });

  GalleryTabState copyWith({
    PageState? pageState,
    String? errorMessage,
    AddPropertyModel? addPropertyModel,
    List<SelectableItem<File>>? photos,
    List<SelectableItem<String>>? videos,
    List<SelectableItem<XFile>>? brochures,
    List<ItemSimpleModel<String>>? degree360VideoUrls,
    List<ItemSimpleModel<String>>? thirdPartyUrls,
    bool? isWaterMarkEnabled,
  }) {
    return GalleryTabState(pageState: pageState ?? this.pageState, errorMessage: errorMessage ?? this.errorMessage, addPropertyModel: addPropertyModel ?? this.addPropertyModel, photos: photos ?? this.photos, videos: videos ?? this.videos, brochures: brochures ?? this.brochures, degree360VideoUrls: degree360VideoUrls ?? this.degree360VideoUrls, thirdPartyUrls: thirdPartyUrls ?? this.thirdPartyUrls, isWaterMarkEnabled: isWaterMarkEnabled ?? this.isWaterMarkEnabled);
  }

  GalleryTabState clearState({
    PageState? pageState,
    String? errorMessage,
    AddPropertyModel? addPropertyModel,
    List<SelectableItem<File>>? photos,
    List<SelectableItem<String>>? videos,
    List<SelectableItem<XFile>>? brochures,
    List<ItemSimpleModel<String>> degree360VideoUrls = const [],
    List<ItemSimpleModel<String>> thirdPartyUrls = const [],
    bool isWaterMarkEnabled = false,
  }) {
    return GalleryTabState(pageState: pageState ?? PageState.initial, errorMessage: errorMessage, addPropertyModel: addPropertyModel, photos: photos, videos: videos, brochures: brochures, degree360VideoUrls: degree360VideoUrls, thirdPartyUrls: thirdPartyUrls, isWaterMarkEnabled: isWaterMarkEnabled);
  }
}
