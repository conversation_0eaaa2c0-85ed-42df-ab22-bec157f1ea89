import 'package:json_annotation/json_annotation.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/common/bhk_type.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/common/purpose_enum.dart';
import 'package:leadrat/core_main/enums/data_management/enquiry_type_enum.dart';
import 'package:leadrat/core_main/mapper/prospect_mapper.dart';
import 'package:leadrat/features/data_management/data/models/property_type_model.dart';
import 'package:leadrat/features/data_management/data/models/prospect_source_model.dart';
import 'package:leadrat/features/data_management/domain/entities/enquiry_entity.dart';

import '../../../../core_main/common/models/address_model.dart';
import '../../../../core_main/enums/common/no_of_beds.dart';

part 'enquiry_model.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: true)
class EnquiryModel {
  final String? id;
  final int? enquiryType;
  final List<EnquiryTypeEnum>? enquiryTypes;
  final int? bhKType;
  final double? noOfBhks;
  final List<BHKType>? bhkTypes;
  final List<double?>? bhKs;
  final String? subSource;
  final int? lowerBudget;
  final int? upperBudget;
  final int? carpetArea;
  final String? conversionFactor;
  final String? carpetAreaUnit;
  final String? carpetAreaUnitId;
  final AddressModel? address;
  final List<AddressModel?>? addresses;
  final String? currency;
  final ProspectSourceModel? prospectSource;
  final PropertyTypeModel? propertyType;
  final List<PropertyTypeModel>? propertyTypes;
  final double? saleableArea;
  final double? builtUpArea;
  final int? furnished;
  final int? offerType;
  final String? saleableAreaUnitId;
  final String? builtUpAreaUnitId;
  final double? saleableAreaConversionFactor;
  final double? builtUpAreaConversionFactor;
  final List<String>? floors;
  final List<Beds>? beds;
  final List<double>? bHKs;
  final List<int>? baths;
  final String? propertyAreaUnitId;
  final double? propertyArea;
  final String? netAreaUnitId;
  final double? netArea;
  final double? propertyAreaConversionFactor;
  final double? netAreaConversionFactor;
  final String? unitName;
  final String? clusterName;
  @JsonKey(defaultValue: PurposeEnum.none)
  final PurposeEnum? purpose;
  final PossessionType? possesionType;

  EnquiryModel({
    this.id,
    this.enquiryType,
    this.enquiryTypes,
    this.bhKType,
    this.noOfBhks,
    this.bhkTypes,
    this.bhKs,
    this.subSource,
    this.lowerBudget,
    this.upperBudget,
    this.carpetArea,
    this.conversionFactor,
    this.carpetAreaUnit,
    this.carpetAreaUnitId,
    this.address,
    this.addresses,
    this.currency,
    this.prospectSource,
    this.propertyType,
    this.propertyTypes,
    this.saleableArea,
    this.builtUpArea,
    this.bHKs,
    this.baths,
    this.builtUpAreaConversionFactor,
    this.saleableAreaConversionFactor,
    this.floors,
    this.builtUpAreaUnitId,
    this.saleableAreaUnitId,
    this.offerType,
    this.furnished,
    this.beds,
    this.propertyAreaUnitId,
    this.propertyArea,
    this.netAreaUnitId,
    this.netArea,
    this.propertyAreaConversionFactor,
    this.netAreaConversionFactor,
    this.unitName,
    this.clusterName,
    this.purpose,
    this.possesionType,
  });

  factory EnquiryModel.fromJson(Map<String, dynamic> json) => _$EnquiryModelFromJson(json);

  Map<String, dynamic> toJson() => _$EnquiryModelToJson(this);

  EnquiryEntity toEntity() {
    final prospectEntityMapper = getIt<ProspectEntityMapper>();
    return EnquiryEntity(
      id: id,
      enquiryType: enquiryType,
      enquiryTypes: enquiryTypes,
      bhKType: bhKType,
      noOfBhks: noOfBhks,
      bhkTypes: bhkTypes,
      bhKs: bhKs,
      subSource: subSource,
      lowerBudget: lowerBudget,
      upperBudget: upperBudget,
      carpetArea: carpetArea,
      conversionFactor: conversionFactor,
      carpetAreaUnit: carpetAreaUnit,
      carpetAreaUnitId: prospectEntityMapper.getArea(carpetAreaUnitId),
      address: address?.toEntity(),
      addresses: addresses?.map((e) => e?.toEntity()).toList(),
      currency: currency,
      prospectSource: prospectSource?.toEntity(),
      propertyType: propertyType?.toEntity(),
      propertyTypes: propertyTypes?.map((e) => e.toEntity()).toList(),
      beds: beds,
      floors: floors,
      offerType: offerType,
      baths: baths,
      furnished: furnished,
      saleableArea: saleableArea,
      bHKs: bHKs,
      builtUpArea: builtUpArea,
      builtUpAreaUnitId: prospectEntityMapper.getArea(builtUpAreaUnitId),
      saleableAreaUnitId: prospectEntityMapper.getArea(saleableAreaUnitId),
      propertyAreaUnitId: prospectEntityMapper.getArea(propertyAreaUnitId),
      netAreaUnitId: prospectEntityMapper.getArea(netAreaUnitId),
      netArea: netArea,
      propertyArea: propertyArea,
      unitName: unitName,
      clusterName: clusterName,
      purpose: purpose,
      possesionType: possesionType,
    );
  }
}
