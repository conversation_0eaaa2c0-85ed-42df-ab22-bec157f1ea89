package com.leadrat.black.mobile.droid.leadrat;

import io.flutter.embedding.android.FlutterActivity;
import androidx.annotation.NonNull;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodChannel;
import android.content.Intent;
import android.os.Bundle;
import android.net.Uri;

public class MainActivity extends FlutterActivity {
    private static final String CHANNEL = "native_actions";
    private static final String DEEP_LINK_CHANNEL = "deepLink";
    private static final String APP_CONFIG_CHANNEL = "app_config";

    private MediaHandler mediaHandler;
    private CommunicationHandler communicationHandler;
    private AppConfigHandler appConfigHandler;
    private DeepLinkHandler deepLinkHandler;

    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);

        // Initialize handlers
        mediaHandler = new MediaHandler(this, flutterEngine);
        communicationHandler = new CommunicationHandler(this, flutterEngine);
        appConfigHandler = new AppConfigHandler(this, flutterEngine);
        deepLinkHandler = new DeepLinkHandler(this, flutterEngine);

        // Configure method channels
        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), CHANNEL)
                .setMethodCallHandler((call, result) -> {
                    switch (call.method) {
                        // Media handling methods
                        case "openCamera":
                            mediaHandler.openCamera(result);
                            break;
                        case "openGallery":
                            mediaHandler.openGallery(result, false);
                            break;
                        case "openGalleryMultipleImageSelection":
                            mediaHandler.openGallery(result, true);
                            break;
                        case "openDocuments":
                            mediaHandler.openDocuments(call, result,false);
                            break;
                        case "openDocumentsMultipleDocumentSelection":
                            mediaHandler.openDocuments(call, result,true);
                            break;
                        case "getPathFromUri":
                            String uriPath = call.argument("uri");
                            if (uriPath != null) {
                                Uri uri = Uri.parse(uriPath);
                                mediaHandler.getPathFromUri(uri, result);
                            } else {
                                result.error("URI_ERROR", "URI is null", null);
                            }
                            break;

                        // Communication methods
                        case "launchSMS":
                            String smsUriString = call.argument("uri");
                            String smsMessage = call.argument("message");
                            communicationHandler.launchSMS(smsUriString, smsMessage, result);
                            break;
                        case "checkBusinessWhatsappInstalled":
                            communicationHandler.checkBusinessWhatsappInstalled(result);
                            break;
                        case "launchWhatsApp":
                            String phoneNumber = call.argument("phoneNumber");
                            String whatsappMessage = call.argument("message");
                            String packageName = call.argument("packageName");
                            communicationHandler.launchWhatsApp(phoneNumber, whatsappMessage, packageName, result);
                            break;
                        case "sendIntent":
                            String tenantId = call.argument("tenantId");
                            String userId = call.argument("userId");
                            String isLoggedIn = call.argument("isLoggedIn");
                            communicationHandler.sendIntentToBroadcast(tenantId, userId, isLoggedIn);
                            result.success("Intent sent to services application");
                            break;
                        default:
                            result.notImplemented();
                            break;
                    }
                });

        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), DEEP_LINK_CHANNEL);

        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), APP_CONFIG_CHANNEL)
                .setMethodCallHandler((call, result) -> {
                    switch (call.method) {
                        case "changeAppIcon":
                            String iconName = call.argument("iconName");
                            if (iconName == null) {
                                iconName = "default";
                            }
                            boolean success = appConfigHandler.changeAppIcon(iconName);
                            result.success(success);
                            break;
                        case "changeAppName":
                            String appName = call.argument("appName");
                            if (appName == null) {
                                appName = "";
                            }
                            boolean nameChangeSuccess = appConfigHandler.saveAppName(appName);
                            result.success(nameChangeSuccess);
                            break;
                        case "confirmDisableMainActivity":
                            appConfigHandler.disableMainActivity();
                            break;
                        default:
                            result.notImplemented();
                            break;
                    }
                });
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (deepLinkHandler == null) {
            deepLinkHandler = new DeepLinkHandler(this, getFlutterEngine());
        }
        deepLinkHandler.handleDeepLinkIntent(getIntent());
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        deepLinkHandler.handleDeepLinkIntent(intent);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (mediaHandler != null) {
            mediaHandler.handleActivityResult(requestCode, resultCode, data);
        }
    }
}