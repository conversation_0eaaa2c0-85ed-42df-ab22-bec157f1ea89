enum ListingManagementFilterKey {
  lookingFor("Looking For"),
  propertyType("Property Type"),
  propertySubType("Property Sub Type"),
  listingLevel("Listing Level"),
  offeringType("Offering Type"),
  completionStatus("Completion Status"),
  community("Community"),
  subCommunity("Sub Community"),
  facing("Facing"),
  furnishingStatus("Furnishing Status"),
  noOfBr("BR"),
  floors("Floors"),
  ownerNames("Owner Names"),
  projects("Projects"),
  propertySize("Property Area"),
  dateRange("Date Range"),
  saleableArea("Saleable Area"),
  builtUpArea("Built-Up Area"),
  carpetArea("Carpet Area"),
  netArea("Net Area"),
  listingBy("Listing By"),
  listingOnBehalf("Listing On Behalf"),
  locations("Locations"),
  noOfBathrooms("No Of Bathrooms"),
  prospectCount("Prospect Count"),
  leadCount("Lead Count"),
  noOfLivingRooms("No Of LivingRooms"),
  noOfBalconies("No Of Balconies"),
  noOfBedrooms("No Of Bedrooms"),
  noOfKitchens("No Of Kitchens"),
  noOfUtilities("No Of Utilities"),
  minBudget("Price"),
  // maxBudget("Max Price"),
  amenities("Amenities");

  final String description;

  const ListingManagementFilterKey(this.description);
}
